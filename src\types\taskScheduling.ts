/**
 * 路径规划参数
 */
export interface PathPlanningParams {
  startPoint: Coordinate
  endPoint: Coordinate
  fieldId: string
  avoidanceLevel: number // 避障灵敏度，1-10
  deviceType: 'robotDog' | 'drone' // 设备类型
  routeType?: 'shortest' | 'safest' | 'optimal' // 路径类型
}

/**
 * 地图坐标
 */
export interface Coordinate {
  x: number
  y: number
  z?: number
}

/**
 * 障碍物信息
 */
export interface Obstacle {
  id: string
  position: Coordinate
  type: string // 障碍物类型，如：高大作物、灌溉设施等
  size: { width: number; height: number; length: number }
  description?: string
}

/**
 * 路径点
 */
export interface PathPoint {
  position: Coordinate
  timestamp?: number // 预计到达时间戳
  type?: 'normal' | 'key' | 'warning' // 路径点类型
  info?: string // 路径点附加信息
}

/**
 * 路径规划结果
 */
export interface PathPlanningResult {
  id: string
  path: PathPoint[]
  distance: number // 总距离
  estimatedTime: number // 预计用时（秒）
  obstacleCount: number // 绕过障碍物数量
  createdAt: string
  fieldId: string
  deviceType: 'robotDog' | 'drone'
}

/**
 * 执行周期类型
 */
export type CycleType = 'once' | 'daily' | 'weekly' | 'monthly'

/**
 * 任务优先级
 */
export type TaskPriority = 'low' | 'medium' | 'high' | 'emergency'

/**
 * 任务状态
 */
export type TaskStatus = 'pending' | 'running' | 'paused' | 'completed' | 'failed'

/**
 * 周期性巡航任务
 */
export interface PeriodicTask {
  id: string
  name: string
  description?: string
  type: 'patrol' | 'spray' | 'inspection' | 'other'
  deviceIds: string[] // 关联的设备ID列表
  cycleType: CycleType
  cycleValue: number // 周期值，如每7天执行一次，此处为7
  startTime: string // ISO时间字符串
  endTime?: string // ISO时间字符串，可选
  priority: TaskPriority
  status: TaskStatus
  path?: PathPlanningResult // 关联的路径
  enabled: boolean // 是否启用
  dependencies?: string[] // 依赖的任务ID列表
  createdAt: string
  updatedAt: string
  lastExecutedAt?: string // 上次执行时间
  nextExecutionTime?: string // 下次执行时间
}

/**
 * 应急消杀任务
 */
export interface EmergencyTask {
  id: string
  name: string
  description?: string
  pestType: string // 病虫害类型
  severity: 'low' | 'medium' | 'high' // 严重程度
  affectedArea: number // 影响面积，单位平方米
  location: {
    fieldId: string
    coordinates: Coordinate[]
  }
  deviceIds: string[] // 执行任务的设备ID列表
  status: TaskStatus
  estimatedResourceUsage: {
    pesticide: number // 农药用量，单位升
    battery: number // 电池消耗，单位百分比
  }
  createdAt: string
  startedAt?: string
  completedAt?: string
}

/**
 * 设备信息
 */
export interface DeviceInfo {
  id: string
  name: string
  type: 'robotDog' | 'drone' | 'other'
  status: 'online' | 'offline' | 'busy' | 'charging' | 'error'
  battery: number // 电池电量，0-100
  position: Coordinate // 当前位置
  lastActiveTime: string // 最后活跃时间
  capabilities: string[] // 设备能力，如：patrol、spray、camera
  connectionStrength?: number // 连接信号强度，0-100
  errorCode?: string // 错误代码
  errorMessage?: string // 错误消息
}

/**
 * 设备实时数据
 */
export interface DeviceRealTimeData {
  deviceId: string
  timestamp: string
  position: Coordinate
  speed: number // 速度，单位米/秒
  batteryRemaining: number // 剩余电量百分比
  status: string // 设备状态
  sensorData?: Record<string, any> // 传感器数据
}

/**
 * 协同事件
 */
export interface CollaborationEvent {
  id: string
  taskId: string
  deviceId: string
  eventType: string // 事件类型：detection、spray、completion等
  timestamp: string
  data: any // 事件相关数据
  importance: 'normal' | 'important' // 事件重要性
}

/**
 * 任务进度信息
 */
export interface TaskProgress {
  taskId: string
  completionPercentage: number // 完成百分比，0-100
  currentStage?: string // 当前阶段
  remainingTime?: number // 剩余时间，单位秒
  startTime: string
  estimatedEndTime?: string
  actualEndTime?: string
  keyIndicators: {
    [key: string]: number | string // 关键指标，如：已巡逻区域、已喷洒药剂量
  }
  deviceStatuses: {
    deviceId: string
    status: string
    batteryRemaining: number
    position?: Coordinate
  }[]
}

/**
 * 任务事件日志
 */
export interface TaskEventLog {
  id: string
  taskId: string
  timestamp: string
  eventType: string
  deviceId?: string
  message: string
  data?: any
  level: 'info' | 'warning' | 'error' // 事件级别
}

/**
 * 历史轨迹数据
 */
export interface TrajectoryData {
  trajectoryId: string
  deviceId: string
  taskId?: string
  startTime: string
  endTime: string
  points: TrajectoryPoint[]
  totalDistance: number // 总距离，单位米
  averageSpeed: number // 平均速度，单位米/秒
  duration: number // 持续时间，单位秒
}

/**
 * 轨迹点
 */
export interface TrajectoryPoint {
  position: Coordinate
  timestamp: string
  speed?: number // 速度
  orientation?: number // 方向角度，0-360
  status?: string // 设备状态
}

/**
 * 轨迹关键点
 */
export interface TrajectoryKeyPoint {
  pointIndex: number // 在轨迹点数组中的索引
  position: Coordinate
  timestamp: string
  eventType: string // 事件类型
  description: string // 事件描述
  data?: any // 事件数据
}

// 任务进度信息
export interface TaskProgressInfo {
  id: string;
  name: string;
  type: string;
  status: string;
  priority: number;
  workArea: string;
  deviceIds: string[];
  progress: number;
  startTime: string;
  estimatedEndTime: string;
  estimatedDuration: number;
  description?: string;
  resources: {
    name: string;
    value: number;
    total: number;
    unit: string;
    type: string;
  }[];
}

// 任务事件
export interface TaskEvent {
  id: string;
  taskId: string;
  deviceId: string;
  eventType: string;
  timestamp: string;
  data: any;
  importance: 'normal' | 'important';
  description?: string;
}

// 多机协同任务
export interface CollaborationTask {
  id: string;
  name: string;
  description: string;
  workArea: string;
  taskType: string;
  deviceIds: string[];
  strategy: string;
  status: string;
  progress: number;
  startTime: string;
  endTime: string | null;
  estimatedTime: number;
  recentEvents: {
    type: string;
    time: string;
    description: string;
  }[];
  events: CollaborationEvent[];
}

// 协同事件
export interface CollaborationEvent {
  type: string;
  time: string;
  title: string;
  description: string;
  deviceId: string;
}

// 任务执行记录类型
export interface TaskExecutionRecord {
  id: string;
  taskId: string;
  startTime: string; // ISO格式日期字符串
  endTime?: string; // ISO格式日期字符串，可选
  status: TaskStatus;
  result?: string; // 执行结果，可选
  deviceId: string;
  executionData?: Record<string, any>; // 执行数据，可选
} 