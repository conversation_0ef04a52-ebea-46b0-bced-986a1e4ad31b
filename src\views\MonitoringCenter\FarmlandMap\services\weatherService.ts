/**
 * 天气服务 - 负责天气和环境数据
 */
import type { EnvironmentData, SoilData } from '../types';

export class WeatherService {
  // 环境数据
  private environmentData: EnvironmentData = {
    temperature: '26',
    humidity: '65',
    windSpeed: '2.3',
    lightIntensity: '50000'
  };
  
  // 土壤数据
  private soilData: SoilData = {
    moisture: '68',
    nutrition: '75',
    pH: '6.8'
  };
  
  // 天气状态
  private weather: string = '晴';
  
  // 获取环境数据
  public getEnvironmentData(): EnvironmentData {
    return { ...this.environmentData };
  }
  
  // 获取土壤数据
  public getSoilData(): SoilData {
    return { ...this.soilData };
  }
  
  // 获取天气状态
  public getWeather(): string {
    return this.weather;
  }
  
  // 更新天气信息（模拟数据）
  public updateWeatherInfo(): void {
    try {
      // 实际项目中，这里应该调用真实的天气API
      // 这里使用模拟数据
      const weatherConditions = ['晴', '多云', '小雨', '阴'];
      this.weather = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
      
      this.environmentData = {
        temperature: (20 + Math.random() * 10).toFixed(1),
        humidity: (50 + Math.random() * 30).toFixed(0),
        windSpeed: (1 + Math.random() * 4).toFixed(1),
        lightIntensity: (30000 + Math.random() * 50000).toFixed(0)
      };
    } catch (error) {
      console.error('获取天气信息失败:', error);
    }
  }
  
  // 更新土壤数据（模拟）
  public updateSoilData(): void {
    this.soilData = {
      moisture: (60 + Math.random() * 20).toFixed(0),
      nutrition: (65 + Math.random() * 25).toFixed(0),
      pH: (6.5 + Math.random() * 0.8).toFixed(1)
    };
  }
  
  // 获取土壤湿度颜色
  public getSoilMoistureColor(): string {
    const value = parseInt(this.soilData.moisture);
    if (value < 30) return '#ff4d4f';
    if (value < 50) return '#faad14';
    return '#52c41a';
  }
  
  // 获取土壤养分颜色
  public getSoilNutritionColor(): string {
    const value = parseInt(this.soilData.nutrition);
    if (value < 30) return '#ff4d4f';
    if (value < 50) return '#faad14';
    return '#52c41a';
  }
}

// 导出单例实例
export const weatherService = new WeatherService(); 