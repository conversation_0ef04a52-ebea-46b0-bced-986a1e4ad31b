import { ref, computed, watch, onUnmounted } from 'vue'

// 路径点接口
export interface PathPoint {
  x: number
  y: number
  id?: string
  name?: string
}

// 巡航状态枚举
export enum CruiseStatus {
  IDLE = 'idle',           // 空闲
  PLANNING = 'planning',   // 路径规划中
  CRUISING = 'cruising',   // 巡航中
  PAUSED = 'paused',       // 暂停
  RETURNING = 'returning', // 返回起点
  ERROR = 'error'          // 错误状态
}

// 巡航配置接口
export interface CruiseConfig {
  speed: number           // 移动速度 (0.1-1.0)
  pauseTime: number      // 每个点的停留时间(秒)
  loopMode: boolean      // 是否循环巡航
  returnToStart: boolean // 是否返回起点
  safeDistance: number   // 安全距离(米)
  maxDistance: number    // 最大移动距离(米)
  timeoutSeconds: number // 移动超时时间(秒)
  enableBoundaryCheck: boolean // 是否启用边界检查
  // 新增角度控制配置
  enableAngleControl: boolean    // 启用角度控制
  rotationSpeed: number          // 转向速度 (度/秒)
  angleTolerance: number         // 角度容差 (度)
  rotationTimeout: number        // 转向超时时间 (秒)
}

// 安全边界接口
export interface SafeBoundary {
  minX: number
  maxX: number
  minY: number
  maxY: number
}

// 默认配置
const DEFAULT_CONFIG: CruiseConfig = {
  speed: 0.3,
  pauseTime: 2,
  loopMode: true,
  returnToStart: true,
  safeDistance: 0.5, // 到达目标点的判定距离，匹配基站定位系统±0.3米精度
  maxDistance: 10,
  timeoutSeconds: 30,
  enableBoundaryCheck: true,
  // 角度控制默认配置
  enableAngleControl: true,
  rotationSpeed: 45,        // 45度/秒 (提高转向速度)
  angleTolerance: 15,       // 15度容差 (放宽容差，提高成功率)
  rotationTimeout: 20       // 20秒转向超时 (增加超时时间)
}

// 自动巡航Hook (已禁用WebRTC功能)
export function useAutoCruise(rtcInstance?: () => any | null) {
  // 状态管理
  const status = ref<CruiseStatus>(CruiseStatus.IDLE)
  const config = ref<CruiseConfig>({ ...DEFAULT_CONFIG })
  const pathPoints = ref<PathPoint[]>([])
  const currentPointIndex = ref(0)
  const currentPosition = ref<PathPoint>({ x: 0, y: 0 })
  const targetPosition = ref<PathPoint>({ x: 0, y: 0 })

  // 运行时状态
  const startTime = ref<Date | null>(null)
  const totalDistance = ref(0)
  const completedLoops = ref(0)
  const lastError = ref<string>('')
  const safeBoundary = ref<SafeBoundary | null>(null)
  const lastMoveTime = ref<Date | null>(null)
  const stuckCount = ref(0)

  // 角度控制状态
  const currentYaw = ref<number>(0)        // 当前偏航角 (-180 到 180)
  const targetYaw = ref<number>(0)         // 目标偏航角
  const isRotating = ref<boolean>(false)   // 是否正在转向
  const rotationStartTime = ref<Date | null>(null)

  // 定时器
  let moveTimer: number | null = null
  let pauseTimer: number | null = null
  let timeoutTimer: number | null = null
  let safetyCheckTimer: number | null = null

  // 计算属性
  const isActive = computed(() =>
    status.value === CruiseStatus.CRUISING ||
    status.value === CruiseStatus.RETURNING
  )

  const progress = computed(() => {
    if (pathPoints.value.length === 0) return 0
    return Math.round((currentPointIndex.value / pathPoints.value.length) * 100)
  })

  const runningTime = computed(() => {
    if (!startTime.value) return '00:00:00'
    const now = new Date()
    const diff = now.getTime() - startTime.value.getTime()
    const hours = Math.floor(diff / 3600000)
    const minutes = Math.floor((diff % 3600000) / 60000)
    const seconds = Math.floor((diff % 60000) / 1000)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  })

  // 计算两点间距离
  const calculateDistance = (p1: PathPoint, p2: PathPoint): number => {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2))
  }

  // 检查点是否在基站边界内
  const isPointInBounds = (point: PathPoint): boolean => {
    if (!config.value.enableBoundaryCheck || !safeBoundary.value) {
      return true
    }

    const boundary = safeBoundary.value
    return point.x >= boundary.minX &&
           point.x <= boundary.maxX &&
           point.y >= boundary.minY &&
           point.y <= boundary.maxY
  }

  // 检查移动距离是否安全
  const isMoveDistanceSafe = (from: PathPoint, to: PathPoint): boolean => {
    const distance = calculateDistance(from, to)
    return distance <= config.value.maxDistance
  }

  // 检查是否卡住（位置长时间未变化）
  const checkIfStuck = (): boolean => {
    if (!lastMoveTime.value) return false

    const now = new Date()
    const timeDiff = (now.getTime() - lastMoveTime.value.getTime()) / 1000

    // 简单的时间判断：如果超过设定时间还在移动中，可能卡住了
    const isTimeOut = timeDiff > config.value.timeoutSeconds

    if (isTimeOut) {
      console.warn(`🚨 机器狗可能卡住了！`)
      console.warn(`⏰ 已经 ${timeDiff.toFixed(1)} 秒没有到达目标点`)
      console.warn(`📍 当前位置: (${currentPosition.value.x.toFixed(2)}, ${currentPosition.value.y.toFixed(2)})`)
      console.warn(`🎯 目标位置: (${targetPosition.value.x.toFixed(2)}, ${targetPosition.value.y.toFixed(2)})`)
      console.warn(`📏 距离目标: ${calculateDistance(currentPosition.value, targetPosition.value).toFixed(2)}米`)
    }

    return isTimeOut
  }

  // 设置安全边界
  const setSafeBoundary = (boundary: SafeBoundary) => {
    safeBoundary.value = boundary
  }

  // 基于基站设置边界（使用完整覆盖范围）
  const setSafeBoundaryFromAnchors = (anchors: Array<{x: number, y: number}>) => {
    if (anchors.length < 4) return

    const minX = Math.min(...anchors.map(a => a.x))
    const maxX = Math.max(...anchors.map(a => a.x))
    const minY = Math.min(...anchors.map(a => a.y))
    const maxY = Math.max(...anchors.map(a => a.y))

    setSafeBoundary({ minX, maxX, minY, maxY })
  }

  /**
   * 角度控制相关函数
   */

  // 计算两点之间的角度 (返回-180到180度)
  const calculateAngle = (from: PathPoint, to: PathPoint): number => {
    const dx = to.x - from.x
    const dy = to.y - from.y
    // 使用atan2计算角度，0度为东方(右)，正角度为逆时针
    const angleRad = Math.atan2(dy, dx)
    return angleRad * 180 / Math.PI
  }

  // 计算角度差 (考虑-180到180度的循环)
  const calculateAngleDifference = (current: number, target: number): number => {
    let diff = target - current
    // 确保角度差在-180到180度之间
    while (diff > 180) diff -= 360
    while (diff < -180) diff += 360
    return diff
  }

  // 发送转向命令 (已禁用WebRTC功能)
  const sendRotationCommand = (angularVelocity: number) => {
    console.warn('⚠️ 自动巡航功能已禁用 - WebRTC连接不可用')
    return false
  }

  // 更新当前偏航角
  const updateCurrentYaw = (yaw: number) => {
    currentYaw.value = yaw
  }

  // 检查是否需要转向
  const needsRotation = (targetAngle: number): boolean => {
    if (!config.value.enableAngleControl) return false

    const angleDiff = Math.abs(calculateAngleDifference(currentYaw.value, targetAngle))
    return angleDiff > config.value.angleTolerance
  }

  // 执行转向到目标角度
  const rotateToAngle = (targetAngle: number): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!config.value.enableAngleControl) {
        resolve(true)
        return
      }

      targetYaw.value = targetAngle
      isRotating.value = true
      rotationStartTime.value = new Date()

      console.log(`🔄 开始转向: 当前角度=${currentYaw.value.toFixed(1)}°, 目标角度=${targetAngle.toFixed(1)}°`)

      const rotationTimer = setInterval(() => {
        const angleDiff = calculateAngleDifference(currentYaw.value, targetAngle)
        const absAngleDiff = Math.abs(angleDiff)

        // 检查是否已到达目标角度
        if (absAngleDiff <= config.value.angleTolerance) {
          clearInterval(rotationTimer)
          sendRotationCommand(0) // 停止转向
          isRotating.value = false

          const elapsedTime = rotationStartTime.value ? (Date.now() - rotationStartTime.value.getTime()) / 1000 : 0
          console.log(`✅ 转向完成: 当前角度=${currentYaw.value.toFixed(1)}°, 目标角度=${targetAngle.toFixed(1)}°, 误差=${absAngleDiff.toFixed(1)}°, 用时=${elapsedTime.toFixed(1)}s`)
          console.log(`🎯 转向成功，准备开始直线移动`)
          resolve(true)
          return
        }

        // 检查转向超时
        if (rotationStartTime.value &&
            (Date.now() - rotationStartTime.value.getTime()) > config.value.rotationTimeout * 1000) {
          clearInterval(rotationTimer)
          sendRotationCommand(0) // 停止转向
          isRotating.value = false
          console.warn(`⚠️ 转向超时: 当前角度=${currentYaw.value.toFixed(1)}°, 目标角度=${targetAngle.toFixed(1)}°`)
          resolve(false)
          return
        }

        // 计算转向速度 (优化自适应速度算法)
        const rotationDirection = angleDiff > 0 ? 1 : -1

        // 改进的速度因子计算：
        // - 大角度差(>30°): 使用最大速度
        // - 中等角度差(10-30°): 使用70%速度
        // - 小角度差(<10°): 使用50%速度，但不低于最小速度
        let speedFactor: number
        if (absAngleDiff > 30) {
          speedFactor = 1.0  // 最大速度
        } else if (absAngleDiff > 10) {
          speedFactor = 0.7  // 中等速度
        } else {
          speedFactor = Math.max(0.5, absAngleDiff / 20)  // 最小50%速度
        }

        const angularVelocity = rotationDirection * config.value.rotationSpeed * speedFactor * Math.PI / 180 // 转换为弧度/秒

        sendRotationCommand(angularVelocity)

        // 增强调试信息
        const currentTime = Date.now()
        const elapsedTime = rotationStartTime.value ? (currentTime - rotationStartTime.value.getTime()) / 1000 : 0
        console.log(`🔄 转向中: 角度差=${angleDiff.toFixed(1)}°, 速度因子=${speedFactor.toFixed(2)}, 角速度=${(angularVelocity * 180 / Math.PI).toFixed(1)}°/s, 已用时=${elapsedTime.toFixed(1)}s`)

      }, 100) // 每100ms检查一次
    })
  }

  // 发送移动命令 (已禁用WebRTC功能)
  const sendMoveCommand = (x: number, y: number, z: number) => {
    console.warn('⚠️ 自动巡航功能已禁用 - WebRTC连接不可用')
    return false
  }

  // 发送停止命令
  const sendStopCommand = () => {
    return sendMoveCommand(0, 0, 0)
  }

  // 移动到目标点 (集成角度控制)
  const moveToTarget = async (target: PathPoint) => {
    const current = currentPosition.value

    console.log(`开始移动到目标点: (${target.x.toFixed(2)}, ${target.y.toFixed(2)})`)
    console.log(`当前位置: (${current.x.toFixed(2)}, ${current.y.toFixed(2)})`)

    // 安全检查
    if (!isPointInBounds(target)) {
      handleError(`目标点 (${target.x.toFixed(1)}, ${target.y.toFixed(1)}) 超出安全边界`)
      return
    }

    if (!isMoveDistanceSafe(current, target)) {
      handleError(`移动距离 ${calculateDistance(current, target).toFixed(1)}m 超过安全限制 ${config.value.maxDistance}m`)
      return
    }

    const distance = calculateDistance(current, target)
    console.log(`🎯 到目标点距离: ${distance.toFixed(3)}m, 判定精度: ±${config.value.safeDistance}m`)

    if (distance <= config.value.safeDistance) {
      // 已到达目标点（精度±0.3米）
      console.log(`✅ 已到达目标点！距离: ${distance.toFixed(3)}m ≤ ${config.value.safeDistance}m`)
      onPointReached()
      return
    }

    // 角度控制：先转向目标方向，再直线移动
    if (config.value.enableAngleControl) {
      const targetAngle = calculateAngle(current, target)
      console.log(`🧭 计算目标角度: ${targetAngle.toFixed(1)}°`)

      if (needsRotation(targetAngle)) {
        console.log(`🔄 需要转向: 当前=${currentYaw.value.toFixed(1)}°, 目标=${targetAngle.toFixed(1)}°`)
        const rotationSuccess = await rotateToAngle(targetAngle)

        if (!rotationSuccess) {
          handleError('转向失败或超时')
          return
        }

        // 转向完成后，重新检查距离（可能在转向过程中位置发生了变化）
        const newDistance = calculateDistance(currentPosition.value, target)
        if (newDistance <= config.value.safeDistance) {
          console.log(`✅ 转向后已到达目标点！距离: ${newDistance.toFixed(3)}m`)
          onPointReached()
          return
        }
      }
    }

    // 计算移动方向（直线移动）
    const dx = target.x - current.x
    const dy = target.y - current.y
    const length = Math.sqrt(dx * dx + dy * dy)

    if (length === 0) {
      console.log('目标点与当前位置相同')
      onPointReached()
      return
    }

    // 计算世界坐标系中的移动向量
    const worldVectorX = (dx / length) * config.value.speed
    const worldVectorY = (dy / length) * config.value.speed

    console.log(`🌍 世界坐标系移动向量: dx=${dx.toFixed(3)}, dy=${dy.toFixed(3)}, 距离=${length.toFixed(3)}m`)
    console.log(`🌍 归一化世界向量: x=${worldVectorX.toFixed(3)}, y=${worldVectorY.toFixed(3)}`)

    // 转换为机器狗本体坐标系
    // 如果启用角度控制，机器狗应该已经朝向目标方向，所以只需要前进
    let robotX: number, robotY: number, robotZ: number

    if (config.value.enableAngleControl) {
      // 角度控制模式：机器狗已经转向目标方向，只需要前进
      robotX = config.value.speed  // 前进
      robotY = 0                   // 不需要侧移
      robotZ = 0                   // 不需要转向
      console.log(`🤖 角度控制模式 - 机器狗坐标系: 前进=${robotX.toFixed(3)}, 侧移=${robotY.toFixed(3)}, 转向=${robotZ.toFixed(3)}`)
    } else {
      // 传统模式：将世界坐标系向量转换为机器狗本体坐标系
      // 需要根据机器狗当前朝向进行坐标变换
      const currentYawRad = (currentYaw.value * Math.PI) / 180

      // 坐标变换：世界坐标系 -> 机器狗本体坐标系
      robotX = worldVectorX * Math.cos(currentYawRad) + worldVectorY * Math.sin(currentYawRad)  // 前后移动
      robotY = -worldVectorX * Math.sin(currentYawRad) + worldVectorY * Math.cos(currentYawRad) // 左右移动
      robotZ = 0  // 不需要转向

      console.log(`🤖 传统模式 - 当前朝向=${currentYaw.value.toFixed(1)}°`)
      console.log(`🤖 机器狗坐标系: 前后=${robotX.toFixed(3)}, 左右=${robotY.toFixed(3)}, 转向=${robotZ.toFixed(3)}`)
    }

    // 发送移动命令
    if (sendMoveCommand(robotX, robotY, robotZ)) {
      targetPosition.value = target
      lastMoveTime.value = new Date()
      stuckCount.value = 0

      console.log('移动命令发送成功，启动监控定时器')

      // 启动移动监控定时器 - 持续发送移动命令
      if (moveTimer) {
        clearInterval(moveTimer)
      }

      moveTimer = window.setInterval(() => {
        // 持续发送移动命令，直到到达目标点
        const currentPos = currentPosition.value
        const targetPos = targetPosition.value
        const currentDistance = calculateDistance(currentPos, targetPos)

        console.log(`🔄 巡航检查 - 当前距离目标: ${currentDistance.toFixed(3)}m`)

        if (currentDistance <= config.value.safeDistance) {
          // 到达目标点（精度±0.3米）
          console.log(`✅ 到达目标点！距离: ${currentDistance.toFixed(3)}m ≤ ${config.value.safeDistance}m`)
          if (moveTimer) {
            clearInterval(moveTimer)
            moveTimer = null
          }
          if (timeoutTimer) {
            clearTimeout(timeoutTimer)
            timeoutTimer = null
          }
          onPointReached()
        } else {
          // 检查是否卡住
          if (checkIfStuck()) {
            stuckCount.value++
            console.warn(`⚠️ 检测到卡住 ${stuckCount.value}/3 次`)

            if (stuckCount.value >= 3) {
              console.error('❌ 机器狗确认卡住，停止巡航')
              handleError('机器狗卡住了，连续3次检测到位置长时间未变化')
              return
            }

            // 重置移动时间，给机器狗一次新的机会
            lastMoveTime.value = new Date()
          }

          // 继续移动，重新计算方向并进行坐标系转换
          const newDx = targetPos.x - currentPos.x
          const newDy = targetPos.y - currentPos.y
          const newLength = Math.sqrt(newDx * newDx + newDy * newDy)

          if (newLength > 0) {
            // 计算世界坐标系中的移动向量
            const worldVectorX = (newDx / newLength) * config.value.speed
            const worldVectorY = (newDy / newLength) * config.value.speed

            console.log(`🌍 继续移动 - 世界坐标系向量: x=${worldVectorX.toFixed(3)}, y=${worldVectorY.toFixed(3)}`)

            // 转换为机器狗本体坐标系
            let robotX: number, robotY: number, robotZ: number

            if (config.value.enableAngleControl) {
              // 角度控制模式：机器狗已经朝向目标方向，只需要前进
              robotX = config.value.speed  // 前进
              robotY = 0                   // 不需要侧移
              robotZ = 0                   // 不需要转向
              console.log(`🤖 继续移动 - 角度控制模式: 前进=${robotX.toFixed(3)}`)
            } else {
              // 传统模式：将世界坐标系向量转换为机器狗本体坐标系
              const currentYawRad = (currentYaw.value * Math.PI) / 180

              // 坐标变换：世界坐标系 -> 机器狗本体坐标系
              robotX = worldVectorX * Math.cos(currentYawRad) + worldVectorY * Math.sin(currentYawRad)  // 前后移动
              robotY = -worldVectorX * Math.sin(currentYawRad) + worldVectorY * Math.cos(currentYawRad) // 左右移动
              robotZ = 0  // 不需要转向

              console.log(`🤖 继续移动 - 传统模式: 前后=${robotX.toFixed(3)}, 左右=${robotY.toFixed(3)}`)
            }

            sendMoveCommand(robotX, robotY, robotZ)
          }
        }
      }, 1000) // 每1秒检查一次并发送移动命令

      // 启动超时检查定时器
      if (timeoutTimer) {
        clearTimeout(timeoutTimer)
      }

      timeoutTimer = window.setTimeout(() => {
        if (status.value === CruiseStatus.CRUISING || status.value === CruiseStatus.RETURNING) {
          handleError(`移动超时，可能机器狗卡住了`)
        }
      }, config.value.timeoutSeconds * 1000)

    } else {
      handleError('移动命令发送失败')
    }
  }



  // 到达路径点处理
  const onPointReached = () => {
    console.log(`到达路径点 ${currentPointIndex.value + 1}/${pathPoints.value.length}`)

    // 停止移动
    sendStopCommand()

    // 更新统计
    if (currentPointIndex.value > 0) {
      const prevPoint = pathPoints.value[currentPointIndex.value - 1]
      const currentPoint = pathPoints.value[currentPointIndex.value]
      totalDistance.value += calculateDistance(prevPoint, currentPoint)
    }

    // 暂停指定时间
    if (config.value.pauseTime > 0) {
      status.value = CruiseStatus.PAUSED
      pauseTimer = window.setTimeout(() => {
        if (status.value === CruiseStatus.PAUSED) {
          continueToNextPoint()
        }
      }, config.value.pauseTime * 1000)
    } else {
      continueToNextPoint()
    }
  }

  // 继续到下一个点
  const continueToNextPoint = () => {
    currentPointIndex.value++

    if (currentPointIndex.value >= pathPoints.value.length) {
      // 完成一轮巡航
      completedLoops.value++

      if (config.value.loopMode) {
        // 循环模式，重新开始
        currentPointIndex.value = 0
        status.value = CruiseStatus.CRUISING
        startNextMove()
      } else if (config.value.returnToStart && pathPoints.value.length > 0) {
        // 返回起点
        status.value = CruiseStatus.RETURNING
        moveToTarget(pathPoints.value[0])
      } else {
        // 完成巡航
        stop()
      }
    } else {
      // 继续下一个点
      status.value = CruiseStatus.CRUISING
      startNextMove()
    }
  }

  // 开始下一次移动
  const startNextMove = () => {
    if (currentPointIndex.value < pathPoints.value.length) {
      const nextPoint = pathPoints.value[currentPointIndex.value]
      moveToTarget(nextPoint)
    }
  }

  // 错误处理
  const handleError = (error: string) => {
    console.error('自动巡航错误:', error)
    lastError.value = error
    status.value = CruiseStatus.ERROR
    stop()
  }

  // 开始巡航
  const start = (points?: PathPoint[]) => {
    if (points && points.length > 0) {
      pathPoints.value = [...points]
    }

    if (pathPoints.value.length === 0) {
      handleError('没有设置巡航路径')
      return false
    }

    // 重置状态
    currentPointIndex.value = 0
    totalDistance.value = 0
    completedLoops.value = 0
    startTime.value = new Date()
    lastError.value = ''

    status.value = CruiseStatus.CRUISING
    startNextMove()

    console.log('开始自动巡航，路径点数量:', pathPoints.value.length)
    return true
  }

  // 停止巡航
  const stop = () => {
    // 清理所有定时器
    if (moveTimer) {
      clearInterval(moveTimer)
      moveTimer = null
    }
    if (pauseTimer) {
      clearTimeout(pauseTimer)
      pauseTimer = null
    }
    if (timeoutTimer) {
      clearTimeout(timeoutTimer)
      timeoutTimer = null
    }
    if (safetyCheckTimer) {
      clearInterval(safetyCheckTimer)
      safetyCheckTimer = null
    }

    // 发送停止命令
    sendStopCommand()

    // 重置状态
    status.value = CruiseStatus.IDLE
    currentPointIndex.value = 0
    startTime.value = null
    lastMoveTime.value = null
    stuckCount.value = 0

    console.log('自动巡航已停止')
  }

  // 暂停巡航
  const pause = () => {
    if (status.value === CruiseStatus.CRUISING) {
      sendStopCommand()
      status.value = CruiseStatus.PAUSED
      console.log('自动巡航已暂停')
    }
  }

  // 恢复巡航
  const resume = () => {
    if (status.value === CruiseStatus.PAUSED) {
      status.value = CruiseStatus.CRUISING
      startNextMove()
      console.log('自动巡航已恢复')
    }
  }

  // 设置路径点
  const setPathPoints = (points: PathPoint[]) => {
    if (isActive.value) {
      console.warn('巡航进行中，无法设置路径点')
      return false
    }
    pathPoints.value = [...points]
    return true
  }

  // 更新配置
  const updateConfig = (newConfig: Partial<CruiseConfig>) => {
    config.value = { ...config.value, ...newConfig }
  }

  // 更新当前位置（来自基站定位系统）
  const updateCurrentPosition = (position: PathPoint) => {
    const previousPosition = { ...currentPosition.value }
    currentPosition.value = position

    // 计算位移距离
    const displacement = calculateDistance(previousPosition, position)

    // 详细的位置更新调试信息
    if (isActive.value) {
      console.log(`📍 位置更新: (${position.x.toFixed(3)}, ${position.y.toFixed(3)})`)
      if (displacement > 0.01) { // 只有位移超过1cm才记录
        console.log(`   位移距离: ${displacement.toFixed(3)}m`)
        console.log(`   上次位置: (${previousPosition.x.toFixed(3)}, ${previousPosition.y.toFixed(3)})`)
      }

      // 如果有目标位置，显示到目标的距离
      if (targetPosition.value.x !== 0 || targetPosition.value.y !== 0) {
        const distanceToTarget = calculateDistance(position, targetPosition.value)
        console.log(`   到目标距离: ${distanceToTarget.toFixed(3)}m`)
      }
    }
  }

  // 获取基站围成的安全边界
  const getAnchorBounds = (anchors: Array<{x: number, y: number}>) => {
    if (anchors.length < 4) return null

    return {
      minX: Math.min(...anchors.map(a => a.x)),
      maxX: Math.max(...anchors.map(a => a.x)),
      minY: Math.min(...anchors.map(a => a.y)),
      maxY: Math.max(...anchors.map(a => a.y))
    }
  }

  // 检查点是否在基站边界内
  const isPointInAnchorBounds = (point: PathPoint, anchors: Array<{x: number, y: number}>, margin: number = 0.5): boolean => {
    const bounds = getAnchorBounds(anchors)
    if (!bounds) return false

    return point.x >= bounds.minX + margin &&
           point.x <= bounds.maxX - margin &&
           point.y >= bounds.minY + margin &&
           point.y <= bounds.maxY - margin
  }

  // 将点限制在基站边界内
  const constrainPointToAnchorBounds = (point: PathPoint, anchors: Array<{x: number, y: number}>): PathPoint => {
    const bounds = getAnchorBounds(anchors)
    if (!bounds) return point

    return {
      x: Math.max(bounds.minX, Math.min(bounds.maxX, point.x)),
      y: Math.max(bounds.minY, Math.min(bounds.maxY, point.y)),
      name: point.name
    }
  }

  // 生成默认巡航路径（基于基站边界的矩形路径）
  const generateDefaultPath = (center: PathPoint, anchors?: Array<{x: number, y: number}>): PathPoint[] => {
    if (!anchors || anchors.length < 4) {
      console.warn('需要基站数据来生成路径')
      return []
    }

    const bounds = getAnchorBounds(anchors)
    if (!bounds) return []

    // 使用基站完整边界
    const minX = bounds.minX
    const maxX = bounds.maxX
    const minY = bounds.minY
    const maxY = bounds.maxY

    console.log(`🏗️ 生成基于基站的矩形路径`)
    console.log(`📍 基站边界: X(${bounds.minX.toFixed(1)} ~ ${bounds.maxX.toFixed(1)}), Y(${bounds.minY.toFixed(1)} ~ ${bounds.maxY.toFixed(1)})`)
    console.log(`🎯 使用完整覆盖范围，无安全边距`)

    return [
      { x: minX, y: minY, name: '左下角' },
      { x: maxX, y: minY, name: '右下角' },
      { x: maxX, y: maxY, name: '右上角' },
      { x: minX, y: maxY, name: '左上角' }
    ]
  }

  // 生成圆形巡航路径（限制在基站边界内）
  const generateCircularPath = (center: PathPoint, anchors?: Array<{x: number, y: number}>): PathPoint[] => {
    if (!anchors || anchors.length < 4) {
      console.warn('需要基站数据来生成路径')
      return []
    }

    const bounds = getAnchorBounds(anchors)
    if (!bounds) return []

    // 计算在基站边界内的最大半径
    const maxRadiusX = Math.min(center.x - bounds.minX, bounds.maxX - center.x)
    const maxRadiusY = Math.min(center.y - bounds.minY, bounds.maxY - center.y)
    const radius = Math.min(maxRadiusX, maxRadiusY, 3.0) // 最大3米

    console.log(`🔵 生成基于基站的圆形路径，半径: ${radius.toFixed(1)}m`)

    const path: PathPoint[] = []
    const points = 8
    const angleStep = (2 * Math.PI) / points

    for (let i = 0; i < points; i++) {
      const angle = i * angleStep
      const x = center.x + radius * Math.cos(angle)
      const y = center.y + radius * Math.sin(angle)

      // 确保点在基站边界内
      const constrainedPoint = constrainPointToAnchorBounds({ x, y, name: `圆形点${i + 1}` }, anchors)
      path.push(constrainedPoint)
    }

    return path
  }

  // 基于基站位置生成巡航路径（使用完整覆盖范围）
  const generatePathFromAnchors = (anchors: Array<{x: number, y: number}>): PathPoint[] => {
    if (anchors.length < 4) {
      console.warn('基站数量不足，无法生成路径')
      return []
    }

    const bounds = getAnchorBounds(anchors)
    if (!bounds) return []

    // 使用基站完整边界
    const minX = bounds.minX
    const maxX = bounds.maxX
    const minY = bounds.minY
    const maxY = bounds.maxY

    console.log(`🏗️ 生成基站路径`)
    console.log(`📍 基站边界: X(${bounds.minX.toFixed(1)} ~ ${bounds.maxX.toFixed(1)}), Y(${bounds.minY.toFixed(1)} ~ ${bounds.maxY.toFixed(1)})`)
    console.log(`🎯 使用完整覆盖范围，无安全边距`)

    // 生成基站边界的巡航路径
    return [
      { x: minX, y: minY, name: '基站路径-左下' },
      { x: maxX, y: minY, name: '基站路径-右下' },
      { x: maxX, y: maxY, name: '基站路径-右上' },
      { x: minX, y: maxY, name: '基站路径-左上' }
    ]
  }

  // 生成S形巡航路径（适合农田巡检，使用基站完整边界）
  const generateSShapePath = (center: PathPoint, anchors?: Array<{x: number, y: number}>): PathPoint[] => {
    if (!anchors || anchors.length < 4) {
      console.warn('需要基站数据来生成路径')
      return []
    }

    const bounds = getAnchorBounds(anchors)
    if (!bounds) return []

    const minX = bounds.minX
    const maxX = bounds.maxX
    const minY = bounds.minY
    const maxY = bounds.maxY

    const width = maxX - minX
    const height = maxY - minY
    const rows = 3 // 3行S形路径

    if (width <= 0 || height <= 0) {
      console.warn('基站覆盖区域无效，无法生成S形路径')
      return []
    }

    console.log(`🔀 生成基于基站的S形路径，区域: ${width.toFixed(1)}m × ${height.toFixed(1)}m`)

    const path: PathPoint[] = []
    const rowHeight = height / (rows - 1)

    for (let i = 0; i < rows; i++) {
      const y = minY + i * rowHeight
      if (i % 2 === 0) {
        // 从左到右
        path.push({ x: minX, y, name: `S形-行${i + 1}-左` })
        path.push({ x: maxX, y, name: `S形-行${i + 1}-右` })
      } else {
        // 从右到左
        path.push({ x: maxX, y, name: `S形-行${i + 1}-右` })
        path.push({ x: minX, y, name: `S形-行${i + 1}-左` })
      }
    }

    return path
  }

  // 生成螺旋形巡航路径（使用基站完整边界）
  const generateSpiralPath = (center: PathPoint, anchors?: Array<{x: number, y: number}>): PathPoint[] => {
    if (!anchors || anchors.length < 4) {
      console.warn('需要基站数据来生成路径')
      return []
    }

    const bounds = getAnchorBounds(anchors)
    if (!bounds) return []

    // 计算在基站边界内的最大半径
    const maxRadiusX = Math.min(center.x - bounds.minX, bounds.maxX - center.x)
    const maxRadiusY = Math.min(center.y - bounds.minY, bounds.maxY - center.y)
    const maxRadius = Math.min(maxRadiusX, maxRadiusY, 4.0) // 最大4米

    if (maxRadius <= 0) {
      console.warn('基站覆盖区域无效，无法生成螺旋路径')
      return []
    }

    console.log(`🌀 生成基于基站的螺旋路径，最大半径: ${maxRadius.toFixed(1)}m`)

    const path: PathPoint[] = []
    const turns = 2
    const pointsPerTurn = 6
    const totalPoints = turns * pointsPerTurn
    const radiusStep = maxRadius / totalPoints
    const angleStep = (2 * Math.PI) / pointsPerTurn

    for (let i = 0; i <= totalPoints; i++) {
      const radius = i * radiusStep
      const angle = i * angleStep
      const x = center.x + radius * Math.cos(angle)
      const y = center.y + radius * Math.sin(angle)

      // 确保点在基站边界内
      const constrainedPoint = constrainPointToAnchorBounds({ x, y, name: `螺旋点${i + 1}` }, anchors)
      path.push(constrainedPoint)
    }

    return path
  }

  // 智能路径优化（避免急转弯）
  const optimizePath = (originalPath: PathPoint[]): PathPoint[] => {
    if (originalPath.length < 3) return originalPath

    const optimizedPath: PathPoint[] = [originalPath[0]]

    for (let i = 1; i < originalPath.length - 1; i++) {
      const prev = originalPath[i - 1]
      const current = originalPath[i]
      const next = originalPath[i + 1]

      // 计算转角
      const angle1 = Math.atan2(current.y - prev.y, current.x - prev.x)
      const angle2 = Math.atan2(next.y - current.y, next.x - current.x)
      const angleDiff = Math.abs(angle2 - angle1)

      // 如果转角过大，添加中间点平滑路径
      if (angleDiff > Math.PI / 2) {
        const midX = (prev.x + next.x) / 2
        const midY = (prev.y + next.y) / 2
        optimizedPath.push({ x: midX, y: midY, name: `平滑点${i}` })
      }

      optimizedPath.push(current)
    }

    optimizedPath.push(originalPath[originalPath.length - 1])
    return optimizedPath
  }

  // 清理资源
  onUnmounted(() => {
    stop()
  })

  return {
    // 状态
    status: computed(() => status.value),
    config: computed(() => config.value),
    pathPoints: computed(() => pathPoints.value),
    currentPointIndex: computed(() => currentPointIndex.value),
    currentPosition: computed(() => currentPosition.value),
    targetPosition: computed(() => targetPosition.value),

    // 统计信息
    isActive,
    progress,
    runningTime,
    totalDistance: computed(() => totalDistance.value),
    completedLoops: computed(() => completedLoops.value),
    lastError: computed(() => lastError.value),

    // 角度控制状态
    currentYaw: computed(() => currentYaw.value),
    targetYaw: computed(() => targetYaw.value),
    isRotating: computed(() => isRotating.value),

    // 方法
    start,
    stop,
    pause,
    resume,
    setPathPoints,
    updateConfig,
    updateCurrentPosition,
    generateDefaultPath,
    generateCircularPath,
    generatePathFromAnchors,
    generateSShapePath,
    generateSpiralPath,
    optimizePath,
    setSafeBoundary,
    setSafeBoundaryFromAnchors,

    // 角度控制方法
    updateCurrentYaw,
    rotateToAngle,
    calculateAngle,
    needsRotation
  }
}
