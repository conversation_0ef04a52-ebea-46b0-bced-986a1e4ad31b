<!-- 
  PesticideLedger.vue
  农药电子台账模块
  管理农药产品的库存、采购和使用信息
-->
<template>
  <div class="pesticide-ledger">
    <!-- 页面标题 -->
    <PageHeader
      title="农药电子台账"
      description="管理农药产品的库存、采购和使用信息，支持入库、出库和库存预警"
      icon="Notebook"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ pesticidesData.length }}</span>
            <span class="summary-label">农药种类</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ lowStockPesticides.length }}</span>
            <span class="summary-label">库存预警</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 操作工具栏 -->
    <div class="action-toolbar">
      <el-button type="primary" @click="showPurchaseDialog">
        <el-icon><Plus /></el-icon>
        采购入库
      </el-button>
      <el-button type="danger" @click="showUsageDialog">
        <el-icon><Remove /></el-icon>
        出库使用
      </el-button>
      
      <!-- 批量操作按钮 -->
      <div class="batch-actions" v-if="selectedPesticides.length > 0">
        <el-divider direction="vertical" />
        <el-button type="primary" @click="batchPurchase">
          批量入库 ({{ selectedPesticides.length }})
        </el-button>
        <el-button type="danger" @click="batchUsage">
          批量出库 ({{ selectedPesticides.length }})
        </el-button>
        <el-button @click="clearSelection">
          取消选择
        </el-button>
      </div>
      
      <div class="toolbar-spacer"></div>
      <el-input
        v-model="searchQuery"
        placeholder="搜索农药名称或登记号"
        prefix-icon="Search"
        clearable
        class="search-input"
      />
    </div>
    
    <!-- 农药数据表格 -->
    <div class="table-container" v-loading="loading">
      <el-table
        ref="pesticideTable"
        :data="filteredPesticides"
        style="width: 100%"
        :header-cell-style="{
          background: '#1a2234',
          color: '#e5e7eb',
          fontWeight: '600'
        }"
        row-class-name="table-row"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
        border
        table-layout="auto"
        :scrollbar-always-on="true"
      >
        <!-- 复选框列 -->
        <el-table-column
          type="selection"
          width="55"
          align="center"
          fixed="left"
        />
        
        <el-table-column prop="name" label="农药名称" min-width="120">
          <template #default="scope">
            <div class="pesticide-name-cell">
              <span class="pesticide-name">{{ scope.row.name }}</span>
              <el-tag 
                size="small" 
                effect="dark" 
                :type="getToxicityTagType(scope.row.toxicityLevel)"
              >
                {{ scope.row.toxicityLevel }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="registrationNumber" label="登记证号" min-width="120" />
        
        <el-table-column prop="specification" label="规格" min-width="150" />
        
        <el-table-column label="库存量" min-width="100" align="center">
          <template #default="scope">
            <div class="stock-cell" :class="{ 'low-stock': isLowStock(scope.row) }">
              <span>{{ scope.row.stockQuantity }} {{ scope.row.unit }}</span>
              <StatusIndicator 
                :type="getStockStatusType(scope.row.stockQuantity || 0, scope.row.lowStockWarning || 0)"
                :show-label="false"
                size="small"
              />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="expiryDate" label="有效期至" min-width="120" />
        
        <el-table-column prop="manufacturer" label="生产厂家" min-width="180" show-overflow-tooltip />
        
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <div class="table-actions">
              <el-button 
                type="primary" 
                link 
                @click.stop="handleViewDetails(scope.row)"
              >
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button 
                type="primary" 
                link 
                @click.stop="handlePurchase(scope.row)"
              >
                <el-icon><Plus /></el-icon>
                入库
              </el-button>
              <el-button 
                type="danger" 
                link 
                @click.stop="handleUsage(scope.row)"
              >
                <el-icon><Remove /></el-icon>
                出库
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 库存预警区域 -->
    <div class="status-indicators" v-if="lowStockPesticides.length > 0">
      <div class="indicator-group">
        <h3 class="indicators-title">库存预警</h3>
        <StatusIndicator v-for="pesticide in lowStockPesticides.slice(0, 3)" :key="pesticide.id" 
          :type="getStockStatusType(pesticide.stockQuantity || 0, pesticide.lowStockWarning || 0)"
          :label="`${pesticide.name}: ${pesticide.stockQuantity} ${pesticide.unit}`"
        />
        <el-button v-if="lowStockPesticides.length > 3" type="primary" link @click="showAllLowStock">
          查看更多 ({{ lowStockPesticides.length - 3 }})
        </el-button>
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailsDrawerVisible"
      title="农药详细信息"
      direction="rtl"
      size="35%"
    >
      <div class="details-content" v-if="selectedPesticide">
        <DataPanel :title="selectedPesticide.name">
          <template #actions>
            <el-tag :type="getToxicityTagType(selectedPesticide.toxicityLevel)" effect="dark" size="small">
              {{ selectedPesticide.toxicityLevel }}
            </el-tag>
          </template>
          
          <div class="detail-section">
            <h4 class="section-title">基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">登记号</span>
                <span class="detail-value">{{ selectedPesticide.registrationNumber }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">有效成分</span>
                <span class="detail-value">{{ selectedPesticide.activeIngredients }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">剂型</span>
                <span class="detail-value">{{ selectedPesticide.formulation }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">生产厂家</span>
                <span class="detail-value">{{ selectedPesticide.manufacturer }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">批准日期</span>
                <span class="detail-value">{{ selectedPesticide.approvalDate }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">有效期至</span>
                <span class="detail-value">{{ selectedPesticide.validUntil }}</span>
              </div>
            </div>
          </div>
          
          <div class="detail-section">
            <h4 class="section-title">库存信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">当前库存</span>
                <span class="detail-value" :class="{ 'warning-text': isLowStock(selectedPesticide) }">
                  {{ selectedPesticide.stockQuantity }} {{ selectedPesticide.unit }}
                </span>
              </div>
              <div class="detail-item">
                <span class="detail-label">预警阈值</span>
                <span class="detail-value">{{ selectedPesticide.lowStockWarning }} {{ selectedPesticide.unit }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">批次号</span>
                <span class="detail-value">{{ selectedPesticide.batchNumber }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">入库时间</span>
                <span class="detail-value">{{ selectedPesticide.inboundTime }}</span>
              </div>
            </div>
          </div>
          
          <div class="detail-section">
            <h4 class="section-title">使用信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">适用对象</span>
                <span class="detail-value">{{ selectedPesticide.usageTargets?.join('、') }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">使用剂量</span>
                <span class="detail-value">{{ selectedPesticide.dosageRange }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">安全间隔期</span>
                <span class="detail-value">{{ selectedPesticide.safetyInterval }} 天</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">存储要求</span>
                <span class="detail-value">{{ selectedPesticide.storageRequirements }}</span>
              </div>
            </div>
          </div>
          
          <div class="detail-section">
            <h4 class="section-title">备注</h4>
            <p class="detail-notes">{{ selectedPesticide.notes }}</p>
          </div>
          
          <template #footer>
            <div class="details-actions">
              <el-button type="primary" @click="handlePurchase(selectedPesticide)">
                <el-icon><Plus /></el-icon>
                采购入库
              </el-button>
              <el-button type="danger" @click="handleUsage(selectedPesticide)">
                <el-icon><Remove /></el-icon>
                出库使用
              </el-button>
            </div>
          </template>
        </DataPanel>
      </div>
    </el-drawer>
    
    <!-- 库存预警弹窗 -->
    <el-dialog
      v-model="lowStockDialogVisible"
      title="库存预警列表"
      width="600px"
    >
      <el-table :data="lowStockPesticides" style="width: 100%">
        <el-table-column prop="name" label="农药名称" min-width="120" />
        <el-table-column prop="specification" label="规格" min-width="120" />
        <el-table-column label="当前库存" min-width="120">
          <template #default="scope">
            <span class="warning-text">
              {{ scope.row.stockQuantity }} {{ scope.row.unit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="预警阈值" min-width="120">
          <template #default="scope">
            {{ scope.row.lowStockWarning }} {{ scope.row.unit }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="primary" link @click="handlePurchase(scope.row)">
              <el-icon><Plus /></el-icon>
              采购
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    
    <!-- 批量入库对话框 -->
    <el-dialog
      v-model="batchPurchaseDialogVisible"
      title="批量采购入库"
      width="650px"
    >
      <div class="batch-dialog-content">
        <div class="batch-info">
          <el-alert
            title="您正在对多个农药产品进行批量入库操作"
            type="info"
            description="批量操作将使用相同的供应商和采购日期，但可以为每种农药设置不同的采购数量"
            show-icon
            :closable="false"
          />
        </div>
        
        <div class="common-form">
          <el-form :model="batchPurchaseForm" label-position="top">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="供应商">
                  <el-input v-model="batchPurchaseForm.supplier" placeholder="请输入供应商名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="采购日期">
                  <el-date-picker
                    v-model="batchPurchaseForm.purchaseDate"
                    type="date"
                    placeholder="选择采购日期"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        
        <div class="batch-items">
          <h4 class="batch-subtitle">采购清单 ({{ selectedPesticides.length }}项)</h4>
          <el-table :data="selectedPesticides" style="width: 100%">
            <el-table-column prop="name" label="农药名称" min-width="120" />
            <el-table-column prop="specification" label="规格" min-width="100" />
            <el-table-column label="当前库存" width="100">
              <template #default="scope">
                {{ scope.row.stockQuantity }} {{ scope.row.unit }}
              </template>
            </el-table-column>
            <el-table-column label="采购数量" width="160">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.purchaseQuantity"
                  :min="1"
                  :step="1"
                  size="small"
                  controls-position="right"
                />
                {{ scope.row.unit }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchPurchaseDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchPurchase">
            确认入库
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 批量出库对话框 -->
    <el-dialog
      v-model="batchUsageDialogVisible"
      title="批量出库使用"
      width="650px"
    >
      <div class="batch-dialog-content">
        <div class="batch-info">
          <el-alert
            title="您正在对多个农药产品进行批量出库操作"
            type="warning"
            description="批量操作将使用相同的使用日期和使用地点，但可以为每种农药设置不同的使用数量"
            show-icon
            :closable="false"
          />
        </div>
        
        <div class="common-form">
          <el-form :model="batchUsageForm" label-position="top">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="使用日期">
                  <el-date-picker
                    v-model="batchUsageForm.usageDate"
                    type="date"
                    placeholder="选择使用日期"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="使用地点">
                  <el-input v-model="batchUsageForm.location" placeholder="请输入使用地点" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="使用备注">
              <el-input
                v-model="batchUsageForm.notes"
                type="textarea"
                placeholder="请输入使用备注信息"
                :rows="2"
              />
            </el-form-item>
          </el-form>
        </div>
        
        <div class="batch-items">
          <h4 class="batch-subtitle">出库清单 ({{ selectedPesticides.length }}项)</h4>
          <el-table :data="selectedPesticides" style="width: 100%">
            <el-table-column prop="name" label="农药名称" min-width="120" />
            <el-table-column prop="specification" label="规格" min-width="100" />
            <el-table-column label="当前库存" width="100">
              <template #default="scope">
                <span :class="{ 'warning-text': isLowStock(scope.row) }">
                  {{ scope.row.stockQuantity }} {{ scope.row.unit }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="使用数量" width="160">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.usageQuantity"
                  :min="1"
                  :max="scope.row.stockQuantity || 0"
                  :step="1"
                  size="small"
                  controls-position="right"
                />
                {{ scope.row.unit }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchUsageDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmBatchUsage">
            确认出库
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Notebook, 
  Plus, 
  Remove, 
  View, 
  Search, 
  Refresh 
} from '@element-plus/icons-vue';
import type { PesticideLedger } from '@/types/pesticide';
import { pesticideLedgerApi } from '@/api/pesticideApi';
import { 
  mapPesticideData, 
  getToxicityTagType, 
  getStockStatusType, 
  getMockPesticides 
} from '@/utils/pesticideMapper';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import StatusIndicator from './components/StatusIndicator.vue';
import DataPanel from './components/DataPanel.vue';
import PesticideCard from './components/PesticideCard.vue';

// 数据加载状态
const loading = ref(false);

// 农药数据
const pesticidesData = ref<PesticideLedger[]>([]);

// 选中的农药信息
const selectedPesticide = ref<PesticideLedger | null>(null);

// 表格实例引用
const pesticideTable = ref(null);

// 表格选中的行数据
const selectedPesticides = ref<PesticideLedger[]>([]);

// 详情抽屉状态
const detailsDrawerVisible = ref(false);

// 库存预警对话框状态
const lowStockDialogVisible = ref(false);

// 批量入库对话框状态
const batchPurchaseDialogVisible = ref(false);

// 批量出库对话框状态
const batchUsageDialogVisible = ref(false);

// 批量入库表单
const batchPurchaseForm = ref({
  supplier: '',
  purchaseDate: new Date(),
});

// 批量出库表单
const batchUsageForm = ref({
  usageDate: new Date(),
  location: '',
  notes: '',
});

// 搜索查询
const searchQuery = ref('');

// 最后更新时间
const lastUpdateTime = ref(new Date());

// 过滤后的农药列表
const filteredPesticides = computed(() => {
  if (!searchQuery.value) return pesticidesData.value;
  
  const query = searchQuery.value.toLowerCase();
  return pesticidesData.value.filter(p => 
    p.name.toLowerCase().includes(query) || 
    p.registrationNumber.toLowerCase().includes(query)
  );
});

// 库存预警列表
const lowStockPesticides = computed(() => {
  return pesticidesData.value.filter(p => 
    p.stockQuantity !== undefined && 
    p.lowStockWarning !== undefined && 
    p.stockQuantity <= p.lowStockWarning
  );
});

// 判断是否库存不足
const isLowStock = (pesticide: PesticideLedger) => {
  return pesticide.stockQuantity !== undefined && 
         pesticide.lowStockWarning !== undefined && 
         pesticide.stockQuantity <= pesticide.lowStockWarning;
};

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 初始化获取数据
onMounted(async () => {
  await fetchPesticides();
});

// 获取农药列表
const fetchPesticides = async () => {
  loading.value = true;
  try {
    // 这里使用实际 API 或 mock 数据
    // const data = await pesticideLedgerApi.getPesticides();
    // 使用模拟数据进行开发测试
    const data = getMockPesticides();
    pesticidesData.value = data.map(mapPesticideData);
    lastUpdateTime.value = new Date();
  } catch (error) {
    ElMessage.error('获取农药列表失败');
    console.error('Failed to fetch pesticides:', error);
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = async () => {
  await fetchPesticides();
  ElMessage.success('数据已更新');
  clearSelection();
};

// 处理表格行点击事件
const handleRowClick = (row: PesticideLedger, column: any, event: Event) => {
  // 如果点击的是复选框列，不执行查看详情操作
  if (column.type === 'selection') return;
  handleViewDetails(row);
};

// 处理表格选择改变事件
const handleSelectionChange = (selection: PesticideLedger[]) => {
  selectedPesticides.value = selection;
};

// 清除所有选择
const clearSelection = () => {
  if (pesticideTable.value) {
    (pesticideTable.value as any).clearSelection();
  }
  selectedPesticides.value = [];
};

// 处理查看详情
const handleViewDetails = (pesticide: PesticideLedger) => {
  selectedPesticide.value = pesticide;
  detailsDrawerVisible.value = true;
};

// 处理采购入库操作
const handlePurchase = (pesticide: PesticideLedger) => {
  selectedPesticide.value = pesticide;
  showPurchaseDialog();
};

// 处理出库使用操作
const handleUsage = (pesticide: PesticideLedger) => {
  selectedPesticide.value = pesticide;
  showUsageDialog();
};

// 显示采购对话框
const showPurchaseDialog = () => {
  ElMessage.info('采购入库功能尚未实现');
  // TODO: 实现采购入库对话框
};

// 显示出库对话框
const showUsageDialog = () => {
  ElMessage.info('出库使用功能尚未实现');
  // TODO: 实现出库使用对话框
};

// 显示所有库存预警
const showAllLowStock = () => {
  lowStockDialogVisible.value = true;
};

// 批量采购入库
const batchPurchase = () => {
  if (selectedPesticides.value.length === 0) {
    ElMessage.warning('请先选择需要入库的农药');
    return;
  }
  
  // 为每个选中的农药添加默认的采购数量
  selectedPesticides.value.forEach(pesticide => {
    // 使用TypeScript的类型断言来避免类型错误
    (pesticide as Pesticide).purchaseQuantity = 1;
  });
  
  // 重置表单
  batchPurchaseForm.value = {
    supplier: '',
    purchaseDate: new Date(),
  };
  
  // 显示批量入库对话框
  batchPurchaseDialogVisible.value = true;
};

// 确认批量入库
const confirmBatchPurchase = () => {
  if (!batchPurchaseForm.value.supplier) {
    ElMessage.warning('请输入供应商名称');
    return;
  }
  
  if (!batchPurchaseForm.value.purchaseDate) {
    ElMessage.warning('请选择采购日期');
    return;
  }
  
  // 这里执行批量入库逻辑
  // TODO: 调用API进行批量入库
  
  ElMessage.success(`已成功为 ${selectedPesticides.value.length} 种农药完成批量入库操作`);
  batchPurchaseDialogVisible.value = false;
  clearSelection();
};

// 批量出库使用
const batchUsage = () => {
  if (selectedPesticides.value.length === 0) {
    ElMessage.warning('请先选择需要出库的农药');
    return;
  }
  
  // 为每个选中的农药添加默认的出库数量
  selectedPesticides.value.forEach(pesticide => {
    // 使用TypeScript的类型断言来避免类型错误
    (pesticide as Pesticide).usageQuantity = 1;
  });
  
  // 重置表单
  batchUsageForm.value = {
    usageDate: new Date(),
    location: '',
    notes: '',
  };
  
  // 显示批量出库对话框
  batchUsageDialogVisible.value = true;
};

// 确认批量出库
const confirmBatchUsage = () => {
  if (!batchUsageForm.value.usageDate) {
    ElMessage.warning('请选择使用日期');
    return;
  }
  
  if (!batchUsageForm.value.location) {
    ElMessage.warning('请输入使用地点');
    return;
  }
  
  // 检查库存是否足够
  const insufficientStock = selectedPesticides.value.filter(
    p => ((p as Pesticide).usageQuantity || 0) > ((p as Pesticide).stockQuantity || 0)
  );
  
  if (insufficientStock.length > 0) {
    ElMessage.error(`${insufficientStock[0].name} 库存不足，无法完成出库操作`);
    return;
  }
  
  // 这里执行批量出库逻辑
  // TODO: 调用API进行批量出库
  
  ElMessage.success(`已成功为 ${selectedPesticides.value.length} 种农药完成批量出库操作`);
  batchUsageDialogVisible.value = false;
  clearSelection();
};
</script>

<style scoped>
.pesticide-ledger {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 操作工具栏 */
.action-toolbar {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
  flex-wrap: wrap;
}

/* 批量操作区域 */
.batch-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.toolbar-spacer {
  flex: 1;
}

.search-input {
  width: 250px;
}

/* 表格容器 */
.table-container {
  flex: 1;
  overflow: auto;
  margin-bottom: 20px;
  background: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 2px;
  border: 1px solid #3b4863;
  /* 确保容器可以水平滚动 */
  min-width: 100%;
  width: 100%;
}

:deep(.el-table--border) {
  border: 1px solid #3b4863;
}

:deep(.el-table--border .el-table__inner-wrapper::after) {
  background-color: #3b4863;
}

:deep(.el-scrollbar__bar) {
  background-color: rgba(60, 60, 60, 0.3);
}

:deep(.el-scrollbar__thumb) {
  background-color: rgba(144, 147, 153, 0.5);
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 10px;
  height: 10px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: rgba(144, 147, 153, 0.5);
  border-radius: 5px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background-color: rgba(60, 60, 60, 0.3);
  border-radius: 5px;
}

/* 表格行样式 */
:deep(.table-row) {
  background-color: #1f2937;
  border-bottom: 1px solid #3b4863;
  transition: all 0.2s ease;
}

:deep(.table-row:hover) {
  background-color: #2d3748;
}

:deep(.el-table) {
  --el-table-header-bg-color: #1a2234;
  --el-table-bg-color: #1f2937;
  --el-table-border-color: #3b4863;
  --el-table-text-color: #e5e7eb;
  --el-table-header-text-color: #e5e7eb;
  --el-table-row-hover-bg-color: #2d3748;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: #1a2234;
}

:deep(.el-table, .el-table__inner-wrapper, .el-table__header-wrapper, .el-table__body-wrapper) {
  background-color: transparent;
}

/* 名称单元格 */
.pesticide-name-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pesticide-name {
  font-weight: 500;
}

/* 库存单元格 */
.stock-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.stock-cell.low-stock {
  color: #ef4444;
}

/* 表格操作区 */
.table-actions {
  display: flex;
  gap: 8px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
  align-items: center;
}

.indicators-title {
  margin: 0;
  font-size: 16px;
  color: #e5e7eb;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 详情抽屉内容 */
.details-content {
  height: 100%;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  margin: 0 0 12px 0;
  color: #3b82f6;
  font-weight: 500;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #9ca3af;
}

.detail-value {
  font-size: 14px;
  color: #e5e7eb;
}

.detail-notes {
  color: #e5e7eb;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.warning-text {
  color: #ef4444;
}

.details-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style> 