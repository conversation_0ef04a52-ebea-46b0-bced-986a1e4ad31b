<!-- 
  SoilMoistureMonitoring.vue
  土壤墒情监测图层模块
  实时监控农田土壤水分含量和墒情分布，提供灌溉决策支持
  
  本组件功能：
  1. 通过3D地图展示农田土壤墒情分布情况
  2. 展示监测点列表及其数据状态
  3. 提供监测点详情查看，包括历史趋势图
  4. 提供智能灌溉决策支持
  
  注意事项：
  1. 需要安装echarts依赖用于图表展示
  2. 实际应用中需要集成地图库（如Cesium、Mapbox等）
  3. 页面布局和风格已与设备管理模块保持一致
-->
<template>
  <div class="soil-moisture-monitoring">
    <!-- 页面标题 -->
    <PageHeader
      title="智慧农场土壤墒情监测"
      description="实时监控农田土壤水分含量和墒情分布，提供灌溉决策支持"
      icon="MapLocation"
    >
      <template #actions>
        <div class="status-indicator-wrapper">
          <StatusIndicator type="success" label="数据实时采集中" size="large" />
        </div>
      </template>
    </PageHeader>
    
    <!-- 主内容区域 -->
    <div class="monitoring-content">
      <!-- 地图与监测点列表 -->
      <div class="main-section">
        <!-- 监测点列表面板 -->
        <DataPanel title="监测点列表">
          <template #actions>
            <div class="search-box">
              <el-input 
                v-model="searchQuery" 
                placeholder="搜索监测点" 
                prefix-icon="el-icon-search"
                clearable
              />
            </div>
          </template>
          <div class="panel-table-container">
            <el-table
              :data="filteredMonitoringPoints"
              style="width: 100%"
              @row-click="handlePointClick"
            >
              <el-table-column prop="name" label="名称" width="120" />
              <el-table-column prop="location" label="位置" width="120" />
              <el-table-column prop="moisture" label="含水量" width="100">
                <template #default="scope">
                  {{ scope.row.moisture }}%
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </DataPanel>
        
        <!-- 地图容器面板 -->
        <DataPanel title="土壤墒情分布图">
          <template #actions>
            <div class="layer-controller">
              <el-checkbox-group v-model="activeLayers" @change="handleLayerChange">
                <el-checkbox label="moisture">墒情图层</el-checkbox>
                <el-checkbox label="topographic">地形图层</el-checkbox>
                <el-checkbox label="management">管理区图层</el-checkbox>
              </el-checkbox-group>
            </div>
          </template>
          <div class="map-container" ref="mapContainer">
            <!-- 地图将在这里渲染 -->
            <div class="map-toolbar">
              <div class="view-control">
                <el-button-group>
                  <el-button type="primary" size="small" @click="changeView('top')">
                    <el-icon><Top /></el-icon> 俯视图
                  </el-button>
                  <el-button type="primary" size="small" @click="changeView('side')">
                    <el-icon><Right /></el-icon> 侧视图
                  </el-button>
                  <el-button type="primary" size="small" @click="changeView('3d')">
                    <el-icon><View /></el-icon> 三维视图
                  </el-button>
                </el-button-group>
              </div>
              <div class="operating-tools">
                <el-button-group>
                  <el-button size="small" @click="zoomIn"><el-icon><ZoomIn /></el-icon></el-button>
                  <el-button size="small" @click="zoomOut"><el-icon><ZoomOut /></el-icon></el-button>
                  <el-button size="small" @click="pan"><el-icon><Aim /></el-icon></el-button>
                  <el-button size="small" @click="rotate"><el-icon><RefreshRight /></el-icon></el-button>
                </el-button-group>
              </div>
            </div>
            
            <!-- 图例 -->
            <div class="map-legend">
              <div class="legend-title">土壤墒情图例</div>
              <div class="legend-content">
                <div class="legend-gradient"></div>
                <div class="legend-labels">
                  <span>高含水量</span>
                  <span>中等含水量</span>
                  <span>低含水量</span>
                </div>
              </div>
            </div>
          </div>
        </DataPanel>
      </div>
      
      <!-- 详情与决策支持区域 -->
      <div class="details-section">
        <!-- 监测点详情面板 -->
        <DataPanel v-if="selectedPoint" :title="`${selectedPoint.name} 详情`">
          <template #actions>
            <el-button type="text" @click="selectedPoint = null">
              <el-icon><Close /></el-icon>
            </el-button>
          </template>
          <div class="detail-items">
            <div class="detail-item">
              <span class="item-label">安装时间</span>
              <span class="item-value">{{ selectedPoint.installationTime }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">传感器类型</span>
              <span class="item-value">{{ selectedPoint.sensorType }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">校准状态</span>
              <span class="item-value">{{ selectedPoint.calibrationStatus }}</span>
            </div>
          </div>
          
          <div class="trend-chart-container" ref="trendChartContainer"></div>
          
          <div class="moisture-assessment">
            <div class="assessment-title">土壤墒情评估</div>
            <div class="assessment-content">{{ selectedPoint.assessment }}</div>
            <div class="suggested-actions">
              <div class="action-title">建议操作</div>
              <ul class="action-list">
                <li v-for="(action, index) in selectedPoint.suggestedActions" :key="index">
                  {{ action }}
                </li>
              </ul>
            </div>
          </div>
        </DataPanel>
        
        <!-- 灌溉决策支持面板 -->
        <DataPanel title="灌溉决策支持">
          <template #actions>
            <el-button type="text" @click="expandIrrigationArea">
              <el-icon :class="{ 'rotate-icon': irrigationAreaExpanded }"><ArrowUp /></el-icon>
            </el-button>
          </template>
          <div class="irrigation-recommendations">
            <div class="recommendation-item">
              <span class="item-label">灌溉区域</span>
              <span class="item-value">东北、西南区域</span>
            </div>
            <div class="recommendation-item">
              <span class="item-label">灌溉量</span>
              <span class="item-value">15mm</span>
            </div>
            <div class="recommendation-item">
              <span class="item-label">最佳灌溉时间</span>
              <span class="item-value">今日16:00-18:00</span>
            </div>
          </div>
          
          <div class="detailed-irrigation-plan" v-if="irrigationAreaExpanded">
            <div class="plan-title">详细灌溉计划</div>
            <el-tabs v-model="irrigationPlanTab">
              <el-tab-pane label="灌溉时间表" name="schedule">
                <div class="table-wrapper">
                  <el-table :data="irrigationSchedule" style="width: 100%">
                    <el-table-column prop="zone" label="区域" width="120" />
                    <el-table-column prop="startTime" label="开始时间" width="150" />
                    <el-table-column prop="endTime" label="结束时间" width="150" />
                    <el-table-column prop="amount" label="灌溉量" width="100" />
                  </el-table>
                </div>
              </el-tab-pane>
              <el-tab-pane label="设备需求" name="equipment">
                <div class="equipment-list">
                  <div class="equipment-item" v-for="(item, index) in equipmentRequirements" :key="index">
                    <div class="equipment-name">{{ item.name }}</div>
                    <div class="equipment-count">{{ item.count }}台</div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="预期效果" name="outcomes">
                <div class="outcomes-content">
                  <p v-for="(outcome, index) in expectedOutcomes" :key="index">
                    {{ outcome }}
                  </p>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </DataPanel>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="农田健康" />
        <StatusIndicator type="normal" label="数据监测中" />
        <StatusIndicator type="warning" label="AI分析中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { 
  Top, Right, View, ZoomIn, ZoomOut, Aim, RefreshRight, 
  Close, ArrowUp, Refresh, MapLocation 
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from '@/views/DeviceManagement/components/PageHeader.vue'
import StatusIndicator from '@/views/DeviceManagement/components/StatusIndicator.vue'
import DataPanel from '@/views/DeviceManagement/components/DataPanel.vue'

// 定义类型接口
interface MonitoringPoint {
  id: number
  name: string
  location: string
  moisture: number
  status: string
  installationTime: string
  sensorType: string
  calibrationStatus: string
  assessment: string
  suggestedActions: string[]
}

// 状态变量
const mapContainer = ref<HTMLElement | null>(null)
const trendChartContainer = ref<HTMLElement | null>(null)
const searchQuery = ref('')
const selectedPoint = ref<MonitoringPoint | null>(null)
const activeLayers = ref(['moisture'])
const irrigationAreaExpanded = ref(false)
const irrigationPlanTab = ref('schedule')
const lastUpdateTime = ref(new Date())

// 模拟监测点数据
const monitoringPoints = reactive<MonitoringPoint[]>([
  {
    id: 1,
    name: '监测点A1',
    location: '东北角',
    moisture: 42,
    status: '正常',
    installationTime: '2023-01-15',
    sensorType: 'TDR-315',
    calibrationStatus: '已校准',
    assessment: '土壤含水量适中，短期内无需灌溉。',
    suggestedActions: ['保持当前作物管理方式', '继续观察土壤墒情变化']
  },
  {
    id: 2,
    name: '监测点B2',
    location: '西南角',
    moisture: 28,
    status: '预警',
    installationTime: '2023-02-10',
    sensorType: 'TDR-315',
    calibrationStatus: '已校准',
    assessment: '土壤含水量偏低，建议近期安排灌溉。',
    suggestedActions: ['计划2-3天内进行灌溉', '每次灌溉15mm水量']
  },
  {
    id: 3,
    name: '监测点C3',
    location: '中心区',
    moisture: 38,
    status: '正常',
    installationTime: '2023-01-20',
    sensorType: 'TDR-315',
    calibrationStatus: '已校准',
    assessment: '土壤含水量适中，短期内无需灌溉。',
    suggestedActions: ['保持当前作物管理方式', '继续观察土壤墒情变化']
  },
  {
    id: 4,
    name: '监测点D4',
    location: '东南角',
    moisture: 45,
    status: '正常',
    installationTime: '2023-02-05',
    sensorType: 'TDR-315',
    calibrationStatus: '已校准',
    assessment: '土壤含水量充足，无需灌溉。',
    suggestedActions: ['注意排水情况', '防止土壤过湿']
  },
  {
    id: 5,
    name: '监测点E5',
    location: '西北角',
    moisture: 22,
    status: '严重',
    installationTime: '2023-02-20',
    sensorType: 'TDR-315',
    calibrationStatus: '已校准',
    assessment: '土壤含水量严重不足，需立即灌溉。',
    suggestedActions: ['立即安排灌溉', '每次灌溉20mm水量', '灌溉后重新检测含水量']
  }
])

// 模拟灌溉相关数据
const irrigationSchedule = reactive([
  { zone: '东北区域', startTime: '2023-05-21 16:00', endTime: '2023-05-21 17:00', amount: '15mm' },
  { zone: '西南区域', startTime: '2023-05-21 17:15', endTime: '2023-05-21 18:15', amount: '15mm' }
])

const equipmentRequirements = reactive([
  { name: '灌溉泵', count: 2 },
  { name: '喷头', count: 10 },
  { name: '水管', count: 200 }
])

const expectedOutcomes = reactive([
  '本次灌溉预计提升土壤含水量10-15%',
  '灌溉后土壤墒情将恢复至适宜水平，预计可维持7-10天',
  '灌溉总用水量约为45立方米',
  '预计能有效改善作物生长状态，提高产量5-8%'
])

// 计算过滤后的监测点
const filteredMonitoringPoints = computed(() => {
  if (!searchQuery.value) return monitoringPoints
  
  const query = searchQuery.value.toLowerCase()
  return monitoringPoints.filter(point => {
    return point.name.toLowerCase().includes(query) || 
           point.location.toLowerCase().includes(query) ||
           point.status.toLowerCase().includes(query)
  })
})

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 根据状态获取标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '正常': return 'success'
    case '预警': return 'warning'
    case '严重': return 'danger'
    default: return 'info'
  }
}

// 处理监测点点击
const handlePointClick = (row: MonitoringPoint) => {
  selectedPoint.value = row
  nextTick(() => {
    initTrendChart()
  })
}

// 处理图层变化
const handleLayerChange = () => {
  // 实际应用中，这里应该更新地图图层
  console.log('Active layers:', activeLayers.value)
}

// 更改地图视角
const changeView = (viewType: string) => {
  // 实际应用中，这里应该调用地图API更改视角
  console.log('Change view to:', viewType)
  ElMessage.success(`已切换到${viewType === 'top' ? '俯视图' : viewType === 'side' ? '侧视图' : '三维视图'}`)
}

// 地图操作函数
const zoomIn = () => {
  // 实际应用中调用地图API
  console.log('Zoom in')
  ElMessage.info('放大地图')
}

const zoomOut = () => {
  // 实际应用中调用地图API
  console.log('Zoom out')
  ElMessage.info('缩小地图')
}

const pan = () => {
  // 实际应用中调用地图API
  console.log('Pan mode')
  ElMessage.info('进入平移模式')
}

const rotate = () => {
  // 实际应用中调用地图API
  console.log('Rotate mode')
  ElMessage.info('进入旋转模式')
}

// 展开/收起灌溉决策支持区
const expandIrrigationArea = () => {
  irrigationAreaExpanded.value = !irrigationAreaExpanded.value
}

// 刷新数据
const refreshData = () => {
  // 模拟数据更新
  monitoringPoints.forEach(point => {
    point.moisture = Math.max(10, Math.min(60, point.moisture + (Math.random() * 10 - 5)))
  })
  
  lastUpdateTime.value = new Date()
  
  if (selectedPoint.value) {
    initTrendChart()
  }
  
  ElMessage.success('数据已更新')
}

// 初始化趋势图表
let trendChart: echarts.ECharts | null = null
const initTrendChart = () => {
  if (!trendChartContainer.value || !selectedPoint.value) return
  
  if (trendChart) {
    trendChart.dispose()
  }
  
  trendChart = echarts.init(trendChartContainer.value)
  
  // 生成模拟数据
  const dates = Array(14).fill(0).map((_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - 13 + i)
    return `${date.getMonth() + 1}-${date.getDate()}`
  })
  
  // 根据监测点ID生成不同的模拟数据
  const moistureData = dates.map((_, i) => {
    let baseMoisture = 0
    if (selectedPoint.value) {
      switch (selectedPoint.value.id) {
        case 1: baseMoisture = 40; break
        case 2: baseMoisture = 30; break
        case 3: baseMoisture = 35; break
        case 4: baseMoisture = 42; break
        case 5: baseMoisture = 25; break
        default: baseMoisture = 35
      }
    } else {
      baseMoisture = 35
    }
    
    return baseMoisture + Math.sin(i / 3) * 10 + Math.random() * 3
  })
  
  const option = {
    title: {
      text: '土壤含水量趋势 (近两周)',
      textStyle: {
        color: '#d1d5db',
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#374151'
        }
      },
      axisLabel: {
        color: '#d1d5db'
      }
    },
    yAxis: {
      type: 'value',
      name: '含水量(%)',
      nameTextStyle: {
        color: '#d1d5db'
      },
      axisLine: {
        lineStyle: {
          color: '#374151'
        }
      },
      axisLabel: {
        color: '#d1d5db'
      },
      splitLine: {
        lineStyle: {
          color: '#374151'
        }
      }
    },
    series: [
      {
        name: '含水量',
        type: 'line',
        data: moistureData,
        symbolSize: 6,
        itemStyle: {
          color: '#3b82f6'
        },
        lineStyle: {
          width: 2,
          color: '#3b82f6'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(59, 130, 246, 0.5)'
            },
            {
              offset: 1,
              color: 'rgba(59, 130, 246, 0.1)'
            }
          ])
        }
      },
      {
        name: '灌溉阈值',
        type: 'line',
        data: Array(14).fill(30),
        symbolSize: 0,
        lineStyle: {
          width: 1,
          type: 'dashed',
          color: '#ef4444'
        }
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 模拟数据自动更新
let dataUpdateInterval: number | null = null;

// 窗口大小变化时重新调整图表
const handleResize = () => {
  trendChart?.resize()
}

// 组件挂载
onMounted(() => {
  // 实际应用中，这里应该初始化三维地图
  console.log('Initializing 3D map...')
  
  window.addEventListener('resize', handleResize)
  
  // 启动数据更新定时器
  dataUpdateInterval = window.setInterval(() => {
    // 小幅度随机波动数据
    monitoringPoints.forEach(point => {
      point.moisture = Math.max(10, Math.min(60, point.moisture + (Math.random() * 4 - 2)))
    })
    
    lastUpdateTime.value = new Date()
    
    if (selectedPoint.value) {
      initTrendChart()
    }
  }, 30000); // 每30秒更新一次
})

// 组件销毁
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
  
  // 清除定时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval)
  }
})
</script>

<style scoped>
.soil-moisture-monitoring {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 主内容区域 */
.monitoring-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

.main-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 0;
}

.details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 300px;
}

/* 搜索框 */
.search-box {
  width: 200px;
}

:deep(.el-input__inner) {
  background-color: rgba(31, 41, 55, 0.5);
  border: 1px solid #374151;
  color: #d1d5db;
}

:deep(.el-input__inner::placeholder) {
  color: #6b7280;
}

/* 面板内表格容器 */
.panel-table-container {
  height: 250px;
  width: 100%;
}

/* 表格样式覆盖 */
:deep(.el-table) {
  background-color: transparent;
  color: #d1d5db;
  width: 100% !important; /* 强制表格宽度为100% */
}

:deep(.el-table__inner-wrapper) {
  width: 100%; /* 设置内部包装器宽度为100% */
}

:deep(.el-table__header), 
:deep(.el-table__body), 
:deep(.el-table__footer) {
  width: 100% !important; /* 强制表头和表体宽度为100% */
}

:deep(.el-table tr) {
  background-color: transparent;
}

:deep(.el-table--border, .el-table--group) {
  border-color: #374151;
}

:deep(.el-table td, .el-table th.is-leaf) {
  border-bottom-color: #374151;
}

:deep(.el-table--border th, .el-table--border td) {
  border-right-color: #374151;
}

:deep(.el-table thead th) {
  background-color: #1f2937;
  color: #d1d5db;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(31, 41, 55, 0.3);
}

:deep(.el-table__body tr.hover-row > td) {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

/* 地图容器 */
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
  background-color: #1e3a8a;
  border-radius: 8px;
  overflow: hidden;
}

.map-toolbar {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.layer-controller {
  display: flex;
  gap: 10px;
}

:deep(.el-checkbox__label) {
  color: #d1d5db;
}

.view-control, .operating-tools {
  display: flex;
  justify-content: flex-end;
}

.map-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: rgba(31, 41, 55, 0.8);
  border-radius: 6px;
  padding: 10px;
  z-index: 10;
  width: 200px;
}

.legend-title {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
}

.legend-gradient {
  height: 10px;
  width: 100%;
  background: linear-gradient(to right, #0f172a, #3b82f6, #fbbf24);
  border-radius: 2px;
  margin-bottom: 4px;
}

.legend-labels {
  display: flex;
  justify-content: space-between;
  color: #d1d5db;
  font-size: 12px;
}

/* 详情项 */
.detail-items {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.item-label {
  color: #9ca3af;
  font-size: 14px;
}

.item-value {
  color: #e5e7eb;
  font-size: 14px;
  font-weight: 500;
}

/* 趋势图表 */
.trend-chart-container {
  height: 200px;
  margin-bottom: 16px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
}

/* 土壤墒情评估 */
.moisture-assessment {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 6px;
  padding: 12px;
}

.assessment-title, .action-title, .plan-title {
  font-size: 14px;
  font-weight: bold;
  color: #d1d5db;
  margin-bottom: 8px;
}

.assessment-content {
  color: #e5e7eb;
  margin-bottom: 12px;
}

.action-list {
  padding-left: 20px;
  color: #d1d5db;
  margin: 0;
}

.action-list li {
  margin-bottom: 4px;
}

/* 表格包装器 */
.table-wrapper {
  width: 100%;
  overflow-x: auto;
}

/* 灌溉建议 */
.irrigation-recommendations {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.recommendation-item {
  flex: 1;
  min-width: 120px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 10px;
}

.detailed-irrigation-plan {
  margin-top: 15px;
  transition: all 0.3s ease;
  width: 100%;
}

:deep(.el-tabs) {
  width: 100%;
}

:deep(.el-tabs__content) {
  width: 100%;
}

:deep(.el-tab-pane) {
  width: 100%;
  overflow-x: auto;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #374151;
}

:deep(.el-tabs__item) {
  color: #d1d5db;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

:deep(.el-tabs__active-bar) {
  background-color: #3b82f6;
}

.equipment-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.equipment-item {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 6px;
  padding: 10px;
  text-align: center;
}

.equipment-name {
  color: #d1d5db;
  margin-bottom: 4px;
}

.equipment-count {
  color: #e5e7eb;
  font-weight: bold;
}

.outcomes-content {
  color: #d1d5db;
}

.outcomes-content p {
  margin: 0 0 8px 0;
}

.rotate-icon {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .monitoring-content {
    flex-direction: column;
  }
  
  .details-section {
    min-width: 100%;
  }
}

@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .irrigation-recommendations {
    flex-direction: column;
  }
  
  .equipment-list {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 480px) {
  .equipment-list {
    grid-template-columns: 1fr;
  }
}
</style>

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save
-->