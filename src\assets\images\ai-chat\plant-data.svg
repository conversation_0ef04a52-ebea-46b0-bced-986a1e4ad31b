<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
  <!-- 植物主干 -->
  <path d="M60,140 C60,140 60,90 60,50" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="2"
        stroke-linecap="round" />
  
  <!-- 植物根部/数据线 -->
  <path d="M60,140 C50,145 40,145 30,140" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="1.5"
        stroke-linecap="round" />
  
  <path d="M60,140 C70,145 80,145 90,140" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="1.5"
        stroke-linecap="round" />
  
  <!-- 数据连接节点 -->
  <circle cx="30" cy="140" r="2" fill="#00ffa3" />
  <circle cx="90" cy="140" r="2" fill="#00ffa3" />
  
  <!-- 叶子/数据面板1 -->
  <g transform="translate(0,0)">
    <path d="M60,90 C50,80 30,85 20,75 C30,70 40,75 60,65" 
          fill="none" 
          stroke="#00b8ff" 
          stroke-width="1.5"
          stroke-linecap="round" />
    
    <!-- 数据面板 -->
    <rect x="22" y="72" width="15" height="8" rx="1" 
          fill="none" 
          stroke="#00b8ff" 
          stroke-width="0.8" />
    
    <!-- 面板内数据点 -->
    <circle cx="25" cy="76" r="0.8" fill="#00b8ff" />
    <circle cx="28" cy="76" r="0.8" fill="#00b8ff" />
    <circle cx="31" cy="76" r="0.8" fill="#00b8ff" />
    <circle cx="34" cy="76" r="0.8" fill="#00b8ff" />
  </g>
  
  <!-- 叶子/数据面板2 -->
  <g transform="translate(0,0) scale(-1,1)" transform-origin="60 0">
    <path d="M60,90 C50,80 30,85 20,75 C30,70 40,75 60,65" 
          fill="none" 
          stroke="#00b8ff" 
          stroke-width="1.5"
          stroke-linecap="round" />
    
    <!-- 数据面板 -->
    <rect x="22" y="72" width="15" height="8" rx="1" 
          fill="none" 
          stroke="#00b8ff" 
          stroke-width="0.8" />
    
    <!-- 面板内数据点 -->
    <circle cx="25" cy="76" r="0.8" fill="#00b8ff" />
    <circle cx="28" cy="76" r="0.8" fill="#00b8ff" />
    <circle cx="31" cy="76" r="0.8" fill="#00b8ff" />
    <circle cx="34" cy="76" r="0.8" fill="#00b8ff" />
  </g>
  
  <!-- 叶子/数据面板3 -->
  <g transform="translate(0,20)">
    <path d="M60,90 C45,85 35,95 20,90 C35,80 45,90 60,80" 
          fill="none" 
          stroke="#00ffa3" 
          stroke-width="1.5"
          stroke-linecap="round" />
    
    <!-- 数据面板 -->
    <rect x="25" y="87" width="15" height="8" rx="1" 
          fill="none" 
          stroke="#00ffa3" 
          stroke-width="0.8" />
    
    <!-- 面板内数据点 -->
    <circle cx="28" cy="91" r="0.8" fill="#00ffa3" />
    <circle cx="31" cy="91" r="0.8" fill="#00ffa3" />
    <circle cx="34" cy="91" r="0.8" fill="#00ffa3" />
    <circle cx="37" cy="91" r="0.8" fill="#00ffa3" />
  </g>
  
  <!-- 叶子/数据面板4 -->
  <g transform="translate(0,20) scale(-1,1)" transform-origin="60 0">
    <path d="M60,90 C45,85 35,95 20,90 C35,80 45,90 60,80" 
          fill="none" 
          stroke="#00ffa3" 
          stroke-width="1.5"
          stroke-linecap="round" />
    
    <!-- 数据面板 -->
    <rect x="25" y="87" width="15" height="8" rx="1" 
          fill="none" 
          stroke="#00ffa3" 
          stroke-width="0.8" />
    
    <!-- 面板内数据点 -->
    <circle cx="28" cy="91" r="0.8" fill="#00ffa3" />
    <circle cx="31" cy="91" r="0.8" fill="#00ffa3" />
    <circle cx="34" cy="91" r="0.8" fill="#00ffa3" />
    <circle cx="37" cy="91" r="0.8" fill="#00ffa3" />
  </g>
  
  <!-- 顶部果实/数据中心 -->
  <g>
    <circle cx="60" cy="45" r="10" 
           fill="none" 
           stroke="url(#dataGradient)" 
           stroke-width="1.5" />
    
    <!-- 内部结构 -->
    <circle cx="60" cy="45" r="6" 
           fill="none" 
           stroke="url(#dataGradient)" 
           stroke-width="1" 
           opacity="0.8" />
    
    <circle cx="60" cy="45" r="3" 
           fill="url(#dataGradient)" 
           opacity="0.5" />
    
    <!-- 连接线 -->
    <line x1="50" y1="45" x2="70" y2="45" 
          stroke="url(#dataGradient)" 
          stroke-width="0.8" 
          opacity="0.8" />
    
    <line x1="60" y1="35" x2="60" y2="55" 
          stroke="url(#dataGradient)" 
          stroke-width="0.8" 
          opacity="0.8" />
    
    <!-- 数据点 -->
    <circle cx="55" cy="40" r="1" fill="#00ffa3" />
    <circle cx="65" cy="40" r="1" fill="#00ffa3" />
    <circle cx="55" cy="50" r="1" fill="#00ffa3" />
    <circle cx="65" cy="50" r="1" fill="#00ffa3" />
  </g>
  
  <!-- 主干上的小节点 -->
  <circle cx="60" cy="65" r="1.5" fill="#00ffa3" />
  <circle cx="60" cy="80" r="1.5" fill="#00ffa3" />
  <circle cx="60" cy="95" r="1.5" fill="#00ffa3" />
  <circle cx="60" cy="110" r="1.5" fill="#00ffa3" />
  <circle cx="60" cy="125" r="1.5" fill="#00ffa3" />
  
  <!-- 数据流线 -->
  <path d="M60,55 C58,60 62,60 60,65" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="0.8"
        stroke-dasharray="2,2"
        opacity="0.8" />
  
  <path d="M60,65 C58,70 62,70 60,80" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="0.8"
        stroke-dasharray="2,2"
        opacity="0.8" />
  
  <path d="M60,80 C58,85 62,90 60,95" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="0.8"
        stroke-dasharray="2,2"
        opacity="0.8" />
  
  <path d="M60,95 C58,100 62,105 60,110" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="0.8"
        stroke-dasharray="2,2"
        opacity="0.8" />
  
  <path d="M60,110 C58,115 62,120 60,125" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="0.8"
        stroke-dasharray="2,2"
        opacity="0.8" />
  
  <path d="M60,125 C58,130 62,135 60,140" 
        fill="none" 
        stroke="#00ffa3" 
        stroke-width="0.8"
        stroke-dasharray="2,2"
        opacity="0.8" />
  
  <!-- 定义渐变 -->
  <defs>
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00ffa3" />
      <stop offset="100%" stop-color="#00b8ff" />
    </linearGradient>
    
    <filter id="dataGlow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="1.5" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- 应用发光效果 -->
  <g filter="url(#dataGlow)">
    <circle cx="60" cy="45" r="12" 
           fill="none" 
           stroke="url(#dataGradient)" 
           stroke-width="0.3"
           opacity="0.3" />
  </g>
</svg> 