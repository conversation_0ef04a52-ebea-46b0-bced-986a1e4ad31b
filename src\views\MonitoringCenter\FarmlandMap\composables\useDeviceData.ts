/**
 * 设备数据的可复用逻辑 - 单例模式实现
 */
import { ref, onUnmounted } from 'vue';
import { deviceService } from '../services/deviceService';
import type { DeviceInfo, DeviceStats } from '../types';

// 将状态提升到模块级别，确保所有组件共享同一个状态
// 设备列表
const devices = ref<DeviceInfo[]>([]);

// 选中的设备
const selectedDevice = ref<DeviceInfo | null>(null);

// 设备统计数据
const onlineDevices = ref(0);
const standbyDevices = ref(0);
const offlineDevices = ref(0);

// 设备工作统计
const droneFlyingHours = ref('0');
const dogPatrolHours = ref('0');
const monitorWorkDays = ref('0');
const dataCollectionSize = ref('0');

// 定时器ID
let deviceStatusTimer: number | null = null;
let deviceMovementTimer: number | null = null;
let deviceStatsTimer: number | null = null;

// 标记是否已初始化
let isInitialized = false;

// 初始化设备数据
const initDeviceData = () => {
  // 如果已经初始化过，则不再重复初始化
  if (isInitialized) {
    console.log('设备数据已经初始化过，跳过重复初始化');
    return;
  }
  
  // 获取设备列表
  devices.value = deviceService.getDevices();
  
  // 更新设备统计数据
  updateDeviceStats();
  
  console.log('设备数据初始化完成，设备数量:', devices.value.length);
  isInitialized = true;
};

// 更新设备统计数据
const updateDeviceStats = () => {
  const stats = deviceService.getDeviceStats();
  
  onlineDevices.value = stats.onlineCount;
  standbyDevices.value = stats.standbyCount;
  offlineDevices.value = stats.offlineCount;
  
  droneFlyingHours.value = stats.droneFlyingHours;
  dogPatrolHours.value = stats.dogPatrolHours;
  monitorWorkDays.value = stats.monitorWorkDays;
  dataCollectionSize.value = stats.dataCollectionSize;
};

// 开始设备模拟
const startDeviceSimulation = () => {
  // 避免重复启动定时器
  if (deviceStatusTimer !== null || deviceMovementTimer !== null || deviceStatsTimer !== null) {
    console.log('设备模拟已经在运行，跳过重复启动');
    return;
  }
  
  console.log('开始设备数据模拟');
  
  // 设置设备状态变化模拟
  deviceStatusTimer = window.setInterval(() => {
    deviceService.simulateDeviceStatusChanges();
    devices.value = deviceService.getDevices();
    updateDeviceStats();
    
    // 如果有选中的设备，更新选中设备的信息
    if (selectedDevice.value) {
      const updatedDevice = devices.value.find(d => d.id === selectedDevice.value!.id);
      if (updatedDevice) {
        selectedDevice.value = updatedDevice;
      }
    }
  }, 10000);
  
  // 设置设备移动模拟
  deviceMovementTimer = window.setInterval(() => {
    deviceService.simulateDeviceMovement();
    devices.value = deviceService.getDevices();
    
    // 如果有选中的设备，更新选中设备的信息
    if (selectedDevice.value) {
      const updatedDevice = devices.value.find(d => d.id === selectedDevice.value!.id);
      if (updatedDevice) {
        selectedDevice.value = updatedDevice;
      }
    }
  }, 2000);
  
  // 设置设备工作时间统计更新
  deviceStatsTimer = window.setInterval(() => {
    deviceService.updateWorkStats();
    updateDeviceStats();
  }, 5000);
};

// 停止设备模拟
const stopDeviceSimulation = () => {
  if (deviceStatusTimer !== null) {
    window.clearInterval(deviceStatusTimer);
    deviceStatusTimer = null;
  }
  
  if (deviceMovementTimer !== null) {
    window.clearInterval(deviceMovementTimer);
    deviceMovementTimer = null;
  }
  
  if (deviceStatsTimer !== null) {
    window.clearInterval(deviceStatsTimer);
    deviceStatsTimer = null;
  }
  
  console.log('设备数据模拟已停止');
};

// 选择设备
const selectDevice = (deviceId: string | null) => {
  console.log('选择设备被调用 [单例模式]:', deviceId);
  
  if (deviceId === null) {
    console.log('清除选中设备');
    selectedDevice.value = null;
    return;
  }
  
  const device = devices.value.find(d => d.id === deviceId);
  if (device) {
    console.log('设备被选中 [单例模式]:', device.name, device.type);
    selectedDevice.value = device;
  } else {
    console.warn('未找到设备:', deviceId);
  }
};

// 刷新设备数据
const refreshDeviceData = () => {
  if (selectedDevice.value) {
    const updatedDevice = devices.value.find(d => d.id === selectedDevice.value!.id);
    if (updatedDevice) {
      selectedDevice.value = updatedDevice;
    }
  }
};

// 调试工具：获取所有设备ID
const getDeviceIds = () => {
  console.log('可用设备列表:');
  devices.value.forEach(d => {
    console.log(`${d.id}: ${d.name} (${d.type})`);
  });
  return devices.value.map(d => d.id);
};

// 调试工具：控制台手动选择设备
const debugSelectDevice = (deviceId: string) => {
  console.log('通过调试工具选择设备:', deviceId);
  selectDevice(deviceId);
  return selectedDevice.value;
};

// 将调试方法附加到window对象以便从控制台访问
if (typeof window !== 'undefined') {
  // @ts-ignore
  window._farmMapDebug = {
    getDeviceIds,
    selectDevice: debugSelectDevice,
    getSelectedDevice: () => selectedDevice.value
  };
  console.log('调试工具已加载，可通过控制台使用：window._farmMapDebug');
}

// 单例的useDeviceData函数，所有组件调用这个函数都会获得相同的状态实例
export function useDeviceData() {
  // 组件卸载时的清理工作 - 不再清理全局资源，避免一个组件卸载导致所有状态丢失
  // 资源清理只在应用真正退出时进行
  onUnmounted(() => {
    console.log('组件卸载, 但不清理全局设备数据状态');
    // 不再调用stopDeviceSimulation()，避免一个组件卸载导致设备模拟停止
  });
  
  // 返回共享的状态和方法
  return {
    devices,
    selectedDevice,
    selectDevice,
    onlineDevices,
    standbyDevices,
    offlineDevices,
    droneFlyingHours,
    dogPatrolHours,
    monitorWorkDays,
    dataCollectionSize,
    initDeviceData,
    startDeviceSimulation,
    stopDeviceSimulation,
    updateDeviceStats,
    refreshDeviceData,
    // 添加调试工具方法
    getDeviceIds,
    debugSelectDevice
  };
} 