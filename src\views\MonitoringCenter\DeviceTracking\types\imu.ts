/**
 * IMU数据相关类型定义
 * 用于智慧农场设备追踪系统中的机器狗IMU数据处理
 */

// IMU连接状态枚举
export enum IMUConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  SUBSCRIBING = 'subscribing',
  RECEIVING = 'receiving',
  ERROR = 'error'
}

// 姿态角度数据接口
export interface AttitudeData {
  roll: number;    // 横滚角 (度)
  pitch: number;   // 俯仰角 (度)
  yaw: number;     // 偏航角 (度)
}

// 电池状态数据接口
export interface BatteryState {
  soc: number;           // 电量百分比 (0-100)
  current: number;       // 电流 (A)
  cycle: number;         // 充电循环次数
  bqTemp: number[];      // BQ温度传感器数据 (°C)
  mcuTemp: number[];     // MCU温度传感器数据 (°C)
}

// 足部压力数据接口 (四个足部：前左、前右、后左、后右)
export interface FootForceData {
  frontLeft: number;     // 前左足压力
  frontRight: number;    // 前右足压力
  rearLeft: number;      // 后左足压力
  rearRight: number;     // 后右足压力
}

// 电机状态数据接口
export interface MotorState {
  temperature: number;   // 电机温度 (°C)
  torque?: number;      // 扭矩 (可选)
  speed?: number;       // 转速 (可选)
}

// 传感器数据接口
export interface SensorData {
  temperatureNtc1: number;    // NTC1温度传感器 (°C)
  powerVoltage: number;       // 电源电压 (V)
  motorMaxTemp: number;       // 电机最高温度 (°C)
  motorAvgTemp: number;       // 电机平均温度 (°C)
}

// 完整的IMU数据接口
export interface IMUData {
  timestamp: number;          // 时间戳
  attitude: AttitudeData;     // 姿态角度数据
  battery: BatteryState;      // 电池状态数据
  footForce: FootForceData;   // 足部压力数据
  sensors: SensorData;        // 传感器数据
  motors: MotorState[];       // 电机状态数组
}

// IMU数据历史记录接口
export interface IMUDataRecord {
  id: string;                 // 记录ID
  timestamp: number;          // 记录时间戳
  data: IMUData;             // IMU数据
  quality: number;           // 数据质量分数 (0-100)
  deviceId?: string;         // 设备ID (可选)
}

// IMU数据统计接口
export interface IMUDataStats {
  totalRecords: number;       // 总记录数
  dataRate: number;          // 数据更新频率 (Hz)
  lastUpdateTime: number;    // 最后更新时间
  connectionDuration: number; // 连接持续时间 (秒)
  errorCount: number;        // 错误计数
}

// 设备详情弹窗配置接口
export interface DeviceDetailConfig {
  autoSubscribe: boolean;     // 自动订阅数据
  showCharts: boolean;        // 显示图表
  showHistory: boolean;       // 显示历史数据
  maxHistoryRecords: number;  // 最大历史记录数
  updateInterval: number;     // 更新间隔 (ms)
  chartRefreshRate: number;   // 图表刷新率 (ms)
}

// 设备详情弹窗Props接口
export interface DeviceDetailDialogProps {
  visible: boolean;           // 弹窗可见性
  deviceInfo?: any;          // 设备信息
  rtcInstance?: any;         // WebRTC实例
  config?: Partial<DeviceDetailConfig>; // 弹窗配置
}

// 设备详情弹窗事件接口
export interface DeviceDetailDialogEvents {
  'update:visible': (visible: boolean) => void;
  'data-received': (data: IMUData) => void;
  'connection-status-change': (status: IMUConnectionStatus) => void;
  'error': (error: Error) => void;
}

// 图表数据点接口
export interface ChartDataPoint {
  timestamp: number;
  value: number;
  label?: string;
}

// 图表配置接口
export interface ChartConfig {
  type: 'line' | 'gauge' | 'bar' | 'scatter';
  title: string;
  unit: string;
  min?: number;
  max?: number;
  thresholds?: {
    warning: number;
    danger: number;
  };
}

// 导出数据格式接口
export interface IMUExportData {
  metadata: {
    exportTime: number;
    deviceId: string;
    recordCount: number;
    timeRange: {
      start: number;
      end: number;
    };
  };
  records: IMUDataRecord[];
}

// IMU数据接口 (通过HTTP API获取)
export interface IMUApiResponse {
  success: boolean;
  data: {
    imu_state?: {
      rpy: number[];  // [roll, pitch, yaw] 弧度值
    };
    bms_state?: {
      soc: number;
      current: number;
      cycle: number;
      bq_ntc: number[];
      mcu_ntc: number[];
    };
    foot_force?: number[];
    motor_state?: Array<{
      temperature: number;
    }>;
    temperature_ntc1?: number;
    power_v?: number;
  };
  timestamp: number;
}
