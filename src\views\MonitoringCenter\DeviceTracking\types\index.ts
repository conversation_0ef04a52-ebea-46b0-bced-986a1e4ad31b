/**
 * 设备追踪相关类型定义
 */

import type { RobotLocationData } from '@/utils/websocketService'

// 数据记录项接口
export interface DataRecord {
  id: string
  timestamp: number
  data: RobotLocationData
  deviceName: string
  recordTime: string
}

// 数据记录配置接口
export interface RecordingConfig {
  enabled: boolean
  maxRecords: number
  autoSave: boolean
  saveInterval: number // 分钟
}

// Excel导出配置接口
export interface ExportConfig {
  filename: string
  sheetName: string
  includeHeaders: boolean
  dateFormat: string
}

// 导出数据格式接口
export interface ExportDataRow {
  序号: number
  设备名称: string
  标签ID: number
  X坐标: number
  Y坐标: number
  时间戳: number
  记录时间: string
  备注?: string
}

// 数据统计接口
export interface DataStatistics {
  totalRecords: number
  recordingDuration: string
  averageFrequency: number
  deviceCount: number
  dataSize: string
}
