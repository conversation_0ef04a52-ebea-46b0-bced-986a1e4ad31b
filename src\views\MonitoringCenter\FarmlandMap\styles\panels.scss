/**
 * 农田地图组件面板样式
 */
@use 'variables.scss' as vars;

// 数据面板样式
.data-panel {
  background-color: rgba(0, 21, 65, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 255, 170, 0.15);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  transition: width 0.3s ease;
  
  // 隐藏滚动条但保留滚动功能
  @include vars.hide-scrollbar;
  
  // 左侧面板特殊样式
  &.left-panel {
    .collapse-toggle {
      right: -15px;
    }
    
    &.collapsed .collapse-toggle {
      right: 10px;
    }
  }
  
  // 右侧面板特殊样式
  &.right-panel {
    .collapse-toggle {
      left: -15px;
    }
    
    &.collapsed .collapse-toggle {
      left: 10px;
    }
  }
}

// 数据卡片样式
.data-card {
  background: vars.$background-panel;
  border: 1px solid vars.$border-color;
  border-radius: vars.$border-radius;
  overflow: hidden;
  @include vars.glass-effect;
  box-shadow: 0 4px 15px vars.$shadow-color;
  @include vars.flex-column;
  margin-bottom: 10px;

  .card-title {
    background: linear-gradient(90deg, rgba(0, 32, 82, 0.8), rgba(0, 48, 130, 0.8));
    color: vars.$primary-color;
    padding: 10px 15px;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 1px solid vars.$border-color;
  }

  .card-content {
    padding: vars.$card-padding;
    color: vars.$text-light;
  }
}

// 设备状态样式
.device-status {
  @include vars.flex-column;
  gap: 15px;

  .status-item {
    display: flex;
    align-items: center;
    
    .status-icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 15px;
      
      &.online {
        @include vars.status-dot(vars.$status-online);
      }
      
      &.standby {
        @include vars.status-dot(vars.$status-standby);
      }
      
      &.offline {
        @include vars.status-dot(vars.$status-offline);
        @include vars.blink-animation;
      }
    }
    
    .status-info {
      .status-label {
        font-size: 14px;
        color: vars.$text-secondary;
      }
      
      .status-value {
        font-size: 18px;
        font-weight: bold;
        color: vars.$text-light;
      }
    }
  }
}

// 环境信息样式
.environment-info {
  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    
    .env-item {
      display: flex;
      align-items: center;
      width: 48%;
      
      .el-icon {
        font-size: 24px;
        color: vars.$primary-color;
        margin-right: 10px;
      }
      
      .env-data {
        .env-label {
          font-size: 12px;
          color: vars.$text-secondary;
        }
        
        .env-value {
          font-size: 16px;
          font-weight: bold;
          color: vars.$text-light;
        }
      }
    }
  }
}

// 土壤信息样式
.soil-info {
  display: flex;
  justify-content: space-between;
  
  .soil-item {
    @include vars.flex-column;
    align-items: center;
    
    .soil-label {
      margin-top: 10px;
      font-size: 14px;
      color: vars.$text-secondary;
    }
    
    .soil-data {
      @include vars.flex-column;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: rgba(0, 255, 170, 0.1);
      border: 2px solid vars.$primary-color-light;
      
      .soil-value {
        font-size: 22px;
        font-weight: bold;
        color: vars.$primary-color;
      }
      
      .soil-unit {
        font-size: 12px;
        color: vars.$text-secondary;
      }
    }
  }
}

// 作物状态样式
.crop-status {
  .crop-info {
    margin-bottom: 15px;
    
    .crop-name {
      font-size: 18px;
      font-weight: bold;
      color: vars.$primary-color;
      margin-bottom: 5px;
    }
    
    .crop-stage, .crop-days {
      font-size: 14px;
      color: vars.$text-secondary;
      margin-bottom: 5px;
    }
  }
  
  .crop-progress {
    .progress-title {
      font-size: 14px;
      color: vars.$text-secondary;
      margin-bottom: 10px;
    }
    
    .progress-legend {
      @include vars.flex-between;
      font-size: 12px;
      color: vars.$text-tertiary;
      margin-top: 5px;
    }
  }
}

// 设备统计样式
.device-stats {
  .stats-item {
    @include vars.flex-between;
    padding: 10px 0;
    border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
    
    &:last-child {
      border-bottom: none;
    }
    
    .stats-label {
      color: vars.$text-secondary;
    }
    
    .stats-value {
      font-weight: bold;
      color: vars.$primary-color;
    }
  }
}

// 预警列表样式
.alert-list {
  max-height: 300px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 5px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: vars.$primary-color-light;
    border-radius: 10px;
  }
  
  .alert-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    &:last-child {
      border-bottom: none;
    }
    
    .alert-icon {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 10px;
      margin-top: 5px;
      
      &.info {
        @include vars.status-dot(vars.$status-info);
      }
      
      &.warning {
        @include vars.status-dot(vars.$status-warning);
      }
      
      &.danger {
        @include vars.status-dot(vars.$status-danger);
        @include vars.blink-animation;
      }
    }
    
    .alert-content {
      flex: 1;
      
      .alert-title {
        font-size: 14px;
        font-weight: bold;
        color: vars.$text-light;
        margin-bottom: 5px;
      }
      
      .alert-desc {
        font-size: 12px;
        color: vars.$text-secondary;
        margin-bottom: 5px;
      }
      
      .alert-time {
        font-size: 12px;
        color: vars.$text-tertiary;
      }
    }
  }
}

// 面板头部样式
.panel-header {
  @include vars.flex-between;
  margin-bottom: 10px;
  padding: 15px 15px 10px;
  border-bottom: 1px solid rgba(0, 255, 170, 0.15);
  
  h3 {
    color: vars.$primary-color;
    margin: 0;
    font-size: 16px;
    position: relative;
    padding-left: 15px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 18px;
      background: vars.$primary-color;
      border-radius: 2px;
    }
  }
  
  .close-btn {
    color: vars.$text-secondary;
    padding: 4px;
    
    &:hover {
      color: vars.$text-light;
    }
  }
}

// 收起/展开按钮样式
.collapse-toggle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  background-color: rgba(0, 21, 65, 0.9);
  border: 1px solid rgba(0, 255, 170, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  
  &:hover {
    background-color: rgba(0, 255, 170, 0.2);
  }
  
  .el-icon {
    color: vars.$primary-color;
    font-size: 16px;
    transition: transform 0.3s ease;
    
    &.rotate-icon {
      transform: rotate(180deg);
    }
  }
}

// 面板内容区域
.panel-content {
  flex: 1;
  overflow-y: auto;
  transition: opacity 0.3s ease;
  padding: 0 15px;
  
  // 隐藏滚动条但保留滚动功能
  @include vars.hide-scrollbar;
}

// 响应式调整
@media (max-width: 768px) {
  .data-panel {
    .panel-header {
      padding: 12px;
      
      h3 {
        font-size: 15px;
      }
    }
    
    .panel-content {
      padding: 0 10px;
    }
  }
} 