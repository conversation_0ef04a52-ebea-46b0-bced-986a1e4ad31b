# WebSocket实时数据获取升级

## 升级背景

用户反馈需要更实时的数据获取，HTTP轮询方式存在以下问题：
- 延迟较高，无法满足实时性要求
- 服务器负载大，频繁的HTTP请求消耗资源
- 用户体验不够流畅

## 解决方案

将IMU数据获取从HTTP轮询升级为WebSocket实时推送，提供真正的实时数据体验。

## 技术实现

### 1. WebSocket服务集成

#### 使用现有WebSocket服务
```typescript
import { robotWebSocketService, type RobotControlMessage } from '@/services/robotWebSocketService'
```

#### WebSocket连接管理
- 自动连接和重连机制
- 连接状态监控
- 错误处理和恢复

### 2. 实时数据获取流程

#### 连接初始化
```typescript
const initWebSocketConnection = async (): Promise<void> => {
  // 设置消息回调
  robotWebSocketService.setOnMessage((message: RobotControlMessage) => {
    if (message.type === 'IMU_DATA' && message.data) {
      processWebSocketIMUData(message.data)
    }
  })

  // 设置状态回调
  robotWebSocketService.setOnStatusChange((status) => {
    wsConnected.value = status === 'CONNECTED'
  })

  // 连接WebSocket
  await robotWebSocketService.connect()
}
```

#### 实时数据请求
```typescript
const startRealtimeDataFetch = (): void => {
  // 立即获取一次数据
  requestIMUData()

  // 每500ms请求一次数据（高频实时）
  dataTimer.value = window.setInterval(() => {
    if (wsConnected.value) {
      requestIMUData()
    }
  }, 500) // 500ms间隔，提供更好的实时性
}
```

### 3. 数据处理优化

#### WebSocket数据适配
```typescript
const processWebSocketIMUData = (wsData: any): void => {
  // 适配不同的数据包装格式
  let imuData = wsData
  if (wsData.data) {
    imuData = wsData.data
  }
  
  // 统一处理
  processIMUData(imuData)
  
  // 动态错误恢复
  if (stats.errorCount > 0) {
    stats.errorCount = Math.max(0, stats.errorCount - 1)
  }
}
```

#### 统一数据处理
保持原有的`processIMUData`函数，确保数据格式转换和UI更新逻辑不变。

### 4. 连接状态管理

#### 多层状态监控
- WebSocket连接状态：`wsConnected`
- IMU数据流状态：`connectionStatus`
- 后端机器狗连接状态：通过API检查

#### 自动恢复机制
- WebSocket断线自动重连
- 数据获取失败自动重试
- 连续失败超过阈值时停止并提示

## 性能优化

### 1. 实时性提升
- **HTTP轮询**：1000ms间隔 → **WebSocket**：500ms间隔
- 数据传输延迟从秒级降低到毫秒级
- 更流畅的用户界面更新

### 2. 资源优化
- 减少HTTP请求数量，降低服务器负载
- 持久连接，减少连接建立开销
- 按需数据推送，避免无效轮询

### 3. 错误处理增强
- 智能错误恢复：成功获取数据时自动减少错误计数
- 分级错误处理：区分WebSocket错误和数据处理错误
- 用户友好提示：不同错误类型提供不同的用户反馈

## 配置参数

### 时间间隔配置
```typescript
const CONFIG = {
  DATA_REQUEST_INTERVAL: 500,    // 数据请求间隔（毫秒）
  STATS_UPDATE_INTERVAL: 1000,   // 统计更新间隔（毫秒）
  MAX_ERROR_COUNT: 20,           // 最大错误次数
  CONNECTION_TIMEOUT: 5000       // 连接超时时间
}
```

### WebSocket配置
- 自动重连：启用
- 心跳检测：30秒间隔
- 消息队列：支持离线消息缓存
- 超时处理：5秒响应超时

## 使用效果

### 实时性对比

#### 升级前（HTTP轮询）
- 数据更新频率：1Hz（每秒1次）
- 响应延迟：1-2秒
- 网络请求：每秒1次HTTP请求
- 用户体验：数据更新有明显延迟

#### 升级后（WebSocket实时）
- 数据更新频率：2Hz（每秒2次）
- 响应延迟：50-100毫秒
- 网络连接：1个持久WebSocket连接
- 用户体验：数据更新流畅实时

### 功能特性

#### 实时数据显示
- ✅ 姿态角度实时更新（Roll, Pitch, Yaw）
- ✅ 电池状态实时监控
- ✅ 电机温度实时显示
- ✅ 足部压力实时反馈
- ✅ 传感器数据实时更新

#### 连接状态监控
- ✅ WebSocket连接状态指示
- ✅ 机器狗设备连接状态
- ✅ 数据流状态实时显示
- ✅ 自动连接和恢复

#### 数据统计
- ✅ 实时数据更新频率计算
- ✅ 连接持续时间统计
- ✅ 数据质量评分
- ✅ 错误次数统计

## 测试验证

### 测试场景
1. **正常连接**：WebSocket连接正常，数据流畅更新
2. **网络中断**：模拟网络中断，验证自动重连
3. **设备离线**：机器狗设备离线，验证错误处理
4. **高频数据**：验证高频数据更新的稳定性
5. **长时间运行**：验证长时间运行的稳定性

### 预期结果
- 数据更新频率稳定在2Hz
- 网络中断后能自动重连并恢复数据流
- 设备离线时有明确的错误提示
- 长时间运行无内存泄漏
- 用户界面响应流畅

## 兼容性说明

### 向后兼容
- 保持原有的API接口不变
- 数据格式和处理逻辑兼容
- 组件接口和事件保持一致

### 降级方案
如果WebSocket连接失败，可以回退到HTTP轮询模式：
```typescript
// 在WebSocket连接失败时的降级处理
if (!wsConnected.value && retryCount > MAX_RETRY) {
  console.warn('WebSocket连接失败，降级到HTTP轮询模式')
  // 启动HTTP轮询作为备用方案
}
```

## 后续优化建议

1. **服务端推送**：考虑实现服务端主动推送数据，进一步提高实时性
2. **数据压缩**：对大量数据进行压缩传输，减少带宽占用
3. **智能频率调节**：根据数据变化频率动态调整请求间隔
4. **离线缓存**：实现离线数据缓存和同步机制
5. **性能监控**：添加详细的性能监控和分析工具
