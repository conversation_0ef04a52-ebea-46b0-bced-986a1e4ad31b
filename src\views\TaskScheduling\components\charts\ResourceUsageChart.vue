<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';
import type { TaskProgressInfo } from '@/types/taskScheduling';

const props = defineProps<{
  taskData: TaskProgressInfo;
}>();

const chartContainer = ref<HTMLDivElement | null>(null);
let chart: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;
  
  chart = echarts.init(chartContainer.value);
  updateChart();
  
  window.addEventListener('resize', () => {
    chart?.resize();
  });
};

// 更新图表数据
const updateChart = () => {
  if (!chart || !props.taskData.resources || props.taskData.resources.length === 0) return;

  const resources = props.taskData.resources;
  const resourceNames = resources.map(item => item.name);
  const resourceData = resources.map(item => ({
    value: (item.value / item.total) * 100,
    name: item.name,
    unit: item.unit,
    actual: item.value,
    total: item.total,
    type: item.type
  }));
  
  const colors = {
    pesticide: '#f56c6c',
    battery: '#e6a23c',
    area: '#67c23a',
    water: '#409eff',
    default: '#909399'
  };

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const data = params.data;
        return `${data.name}: ${data.actual}${data.unit} / ${data.total}${data.unit}<br/>使用率: ${data.value.toFixed(1)}%`;
      },
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: resourceNames,
      textStyle: {
        color: '#9ca3af'
      },
      formatter: function(name: string) {
        const resource = resources.find(item => item.name === name);
        if (resource) {
          return `${name}: ${resource.value}${resource.unit}`;
        }
        return name;
      }
    },
    series: [
      {
        name: '资源使用',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#1f2937',
          borderWidth: 2,
          color: function(params: any) {
            const resourceType = resources[params.dataIndex].type;
            return colors[resourceType as keyof typeof colors] || colors.default;
          }
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            color: '#e5e7eb'
          }
        },
        labelLine: {
          show: false
        },
        data: resourceData
      }
    ]
  };

  chart.setOption(option);
};

// 监听数据变化
watch(() => props.taskData, () => {
  updateChart();
}, { deep: true });

// 生命周期钩子
onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', () => {
    chart?.resize();
  });
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style> 