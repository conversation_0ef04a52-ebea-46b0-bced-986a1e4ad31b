<template>
  <div class="decision-tree-management" :style="{'background-color': '#1f2937'}">
    <div class="management-header">
      <h2>决策树管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon> 创建决策树
        </el-button>
        <el-button type="success" @click="showImportDialog = true">
          <el-icon><Upload /></el-icon> 导入决策树
        </el-button>
      </div>
    </div>

    <!-- Decision Trees List -->
    <div class="trees-container">
      <el-card class="tree-filters">
        <div class="filter-header">
          <h3>筛选器</h3>
          <el-button text @click="resetFilters">重置</el-button>
        </div>
        
        <el-form :model="filters" label-position="top">
          <el-form-item label="决策树类型">
            <el-select v-model="filters.type" placeholder="选择类型" clearable>
              <el-option label="病虫害防治" value="pest_control" />
              <el-option label="施肥管理" value="fertilizer" />
              <el-option label="灌溉策略" value="irrigation" />
              <el-option label="农药使用" value="pesticide" />
              <el-option label="产品质量" value="quality" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="复杂度">
            <el-select v-model="filters.complexity" placeholder="选择复杂度" clearable>
              <el-option label="简单" value="simple" />
              <el-option label="中等" value="medium" />
              <el-option label="复杂" value="complex" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="使用频率">
            <el-select v-model="filters.usage" placeholder="选择使用频率" clearable>
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </el-card>
      
      <div class="tree-list">
        <div class="list-header">
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索决策树"
              prefix-icon="Search"
              clearable
            />
          </div>
          
          <el-radio-group v-model="viewType" size="small">
            <el-radio-button label="grid">
              <el-icon><Grid /></el-icon>
            </el-radio-button>
            <el-radio-button label="list">
              <el-icon><List /></el-icon>
            </el-radio-button>
          </el-radio-group>
        </div>
        
        <!-- Grid View -->
        <div v-if="viewType === 'grid'" class="tree-grid">
          <el-card
            v-for="tree in filteredTrees"
            :key="tree.id"
            class="tree-card"
            :body-style="{ padding: '0px' }"
            @click="viewTree(tree)"
          >
            <div class="tree-thumbnail" :style="{ backgroundColor: getTypeColor(tree.type) }">
              <el-icon :size="40" class="tree-icon"><Connection /></el-icon>
              <span class="node-count">{{ tree.nodeCount }} 节点</span>
            </div>
            <div class="tree-info">
              <div class="tree-title">{{ tree.name }}</div>
              <div class="tree-meta">
                <span class="tree-type">
                  <el-tag size="small" :type="getTagType(tree.type)">
                    {{ getTypeName(tree.type) }}
                  </el-tag>
                </span>
                <span class="tree-date">{{ formatDate(tree.createdAt) }}</span>
              </div>
              <div class="tree-stats">
                <div class="stat-item">
                  <el-icon><View /></el-icon>
                  <span>{{ tree.views }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><Star /></el-icon>
                  <span>{{ tree.accuracy }}%</span>
                </div>
                <div class="stat-item">
                  <el-icon><ChatDotRound /></el-icon>
                  <span>{{ tree.feedbacks }}</span>
                </div>
              </div>
            </div>
            <div class="tree-actions">
              <el-button-group>
                <el-button size="small" type="primary" @click.stop="editTree(tree)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button size="small" type="success" @click.stop="duplicateTree(tree)">
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
                <el-button size="small" type="danger" @click.stop="confirmDelete(tree)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-button-group>
            </div>
          </el-card>
        </div>
        
        <!-- List View -->
        <div v-else class="tree-table">
          <el-table :data="filteredTrees" style="width: 100%">
            <el-table-column label="决策树名称" min-width="200">
              <template #default="{ row }">
                <div class="tree-name-cell">
                  <div class="color-block" :style="{ backgroundColor: getTypeColor(row.type) }"></div>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="类型" width="120">
              <template #default="{ row }">
                <el-tag size="small" :type="getTagType(row.type)">
                  {{ getTypeName(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="节点数" prop="nodeCount" width="100" />
            
            <el-table-column label="创建时间" width="150">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
            
            <el-table-column label="使用频率" width="120">
              <template #default="{ row }">
                <el-tag size="small" :type="getUsageTagType(row.usage)">
                  {{ getUsageName(row.usage) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="准确率" width="120">
              <template #default="{ row }">
                <div class="accuracy-indicator">
                  <span>{{ row.accuracy }}%</span>
                  <el-progress
                    :percentage="row.accuracy"
                    :status="getAccuracyStatus(row.accuracy)"
                    :stroke-width="5"
                    :show-text="false"
                  />
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="viewTree(row)">查看</el-button>
                <el-button size="small" type="primary" @click="editTree(row)">编辑</el-button>
                <el-dropdown>
                  <el-button size="small">
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="duplicateTree(row)">复制</el-dropdown-item>
                      <el-dropdown-item @click="exportTree(row)">导出</el-dropdown-item>
                      <el-dropdown-item divided @click="confirmDelete(row)" class="text-danger">
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- Create Decision Tree Dialog -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建决策树"
      width="600px"
    >
      <el-form :model="newTreeForm" label-width="120px">
        <el-form-item label="决策树名称">
          <el-input v-model="newTreeForm.name" placeholder="输入名称" />
        </el-form-item>
        
        <el-form-item label="决策树类型">
          <el-select v-model="newTreeForm.type" placeholder="选择类型" style="width: 100%">
            <el-option label="病虫害防治" value="pest_control" />
            <el-option label="施肥管理" value="fertilizer" />
            <el-option label="灌溉策略" value="irrigation" />
            <el-option label="农药使用" value="pesticide" />
            <el-option label="产品质量" value="quality" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="创建方式">
          <el-radio-group v-model="newTreeForm.createMethod">
            <el-radio label="template">使用模板</el-radio>
            <el-radio label="scratch">从头创建</el-radio>
            <el-radio label="ai">AI辅助创建</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <template v-if="newTreeForm.createMethod === 'template'">
          <el-form-item label="选择模板">
            <el-select v-model="newTreeForm.templateId" placeholder="选择模板" style="width: 100%">
              <el-option v-for="template in templates" :key="template.id" :label="template.name" :value="template.id" />
            </el-select>
          </el-form-item>
        </template>
        
        <template v-if="newTreeForm.createMethod === 'ai'">
          <el-form-item label="AI构建目标">
            <el-input
              v-model="newTreeForm.aiPrompt"
              type="textarea"
              :rows="3"
              placeholder="描述您需要的决策树的目标和关键决策点..."
            />
          </el-form-item>
        </template>
        
        <el-form-item label="描述">
          <el-input
            v-model="newTreeForm.description"
            type="textarea"
            :rows="3"
            placeholder="描述决策树的用途和目标..."
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="createTree">创建</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- Import Dialog -->
    <el-dialog
      v-model="showImportDialog"
      title="导入决策树"
      width="500px"
    >
      <el-upload
        class="tree-uploader"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">拖拽文件到此处或 <em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">
            支持 .json 或 .xml 格式的决策树文件
          </div>
        </template>
      </el-upload>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showImportDialog = false">取消</el-button>
          <el-button type="primary" @click="importTree" :disabled="!importFile">导入</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- Delete Confirmation Dialog -->
    <el-dialog
      v-model="showDeleteDialog"
      title="删除决策树"
      width="400px"
    >
      <div v-if="treeToDelete">
        <p>您确定要删除决策树 <strong>{{ treeToDelete.name }}</strong> 吗？</p>
        <p class="text-danger">此操作不可撤销。</p>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDeleteDialog = false">取消</el-button>
          <el-button type="danger" @click="deleteTree">确认删除</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Plus, Upload, Search, Grid, List, Connection, 
  View, Star, ChatDotRound, Edit, CopyDocument, 
  Delete, ArrowDown, UploadFilled
} from '@element-plus/icons-vue'

// State
const viewType = ref('grid')
const searchQuery = ref('')
const showCreateDialog = ref(false)
const showImportDialog = ref(false)
const showDeleteDialog = ref(false)
const treeToDelete = ref(null)
const importFile = ref(null)

// Filters
const filters = reactive({
  type: '',
  complexity: '',
  usage: '',
  dateRange: []
})

// Form for new tree
const newTreeForm = reactive({
  name: '',
  type: '',
  description: '',
  createMethod: 'template',
  templateId: '',
  aiPrompt: ''
})

// Mock data
const templates = [
  { id: 1, name: '病虫害防治通用模板' },
  { id: 2, name: '施肥决策基础模板' },
  { id: 3, name: '灌溉策略优化模板' },
  { id: 4, name: '农药使用安全模板' },
  { id: 5, name: '品质评估标准模板' }
]

const decisionTrees = ref([
  {
    id: 1,
    name: '水稻稻飞虱防治决策树',
    type: 'pest_control',
    nodeCount: 15,
    complexity: 'medium',
    usage: 'high',
    createdAt: '2023-06-15',
    views: 128,
    accuracy: 92,
    feedbacks: 24
  },
  {
    id: 2,
    name: '小麦施肥优化决策树',
    type: 'fertilizer',
    nodeCount: 12,
    complexity: 'medium',
    usage: 'high',
    createdAt: '2023-05-20',
    views: 86,
    accuracy: 88,
    feedbacks: 15
  },
  {
    id: 3,
    name: '果园灌溉策略决策树',
    type: 'irrigation',
    nodeCount: 8,
    complexity: 'simple',
    usage: 'medium',
    createdAt: '2023-04-10',
    views: 64,
    accuracy: 95,
    feedbacks: 12
  },
  {
    id: 4,
    name: '有机蔬菜病虫害防治决策树',
    type: 'pest_control',
    nodeCount: 22,
    complexity: 'complex',
    usage: 'medium',
    createdAt: '2023-07-05',
    views: 52,
    accuracy: 85,
    feedbacks: 8
  },
  {
    id: 5,
    name: '农药安全使用决策树',
    type: 'pesticide',
    nodeCount: 18,
    complexity: 'complex',
    usage: 'high',
    createdAt: '2023-03-15',
    views: 156,
    accuracy: 96,
    feedbacks: 32
  },
  {
    id: 6,
    name: '水果品质评估决策树',
    type: 'quality',
    nodeCount: 10,
    complexity: 'simple',
    usage: 'low',
    createdAt: '2023-02-22',
    views: 38,
    accuracy: 78,
    feedbacks: 5
  }
])

// Computed
const filteredTrees = computed(() => {
  let result = [...decisionTrees.value]
  
  // Apply text search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(tree => 
      tree.name.toLowerCase().includes(query) || 
      getTypeName(tree.type).toLowerCase().includes(query)
    )
  }
  
  // Apply filters
  if (filters.type) {
    result = result.filter(tree => tree.type === filters.type)
  }
  
  if (filters.complexity) {
    result = result.filter(tree => tree.complexity === filters.complexity)
  }
  
  if (filters.usage) {
    result = result.filter(tree => tree.usage === filters.usage)
  }
  
  if (filters.dateRange && filters.dateRange.length === 2) {
    const startDate = new Date(filters.dateRange[0])
    const endDate = new Date(filters.dateRange[1])
    
    result = result.filter(tree => {
      const treeDate = new Date(tree.createdAt)
      return treeDate >= startDate && treeDate <= endDate
    })
  }
  
  return result
})

// Methods
function resetFilters() {
  Object.keys(filters).forEach(key => {
    filters[key] = key === 'dateRange' ? [] : ''
  })
  searchQuery.value = ''
}

function formatDate(dateStr) {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

function getTypeColor(type) {
  const colorMap = {
    'pest_control': '#3b82f6', // blue
    'fertilizer': '#10b981',   // green
    'irrigation': '#06b6d4',   // cyan
    'pesticide': '#f59e0b',    // amber
    'quality': '#8b5cf6'       // purple
  }
  return colorMap[type] || '#64748b' // gray default
}

function getTagType(type) {
  const tagMap = {
    'pest_control': 'primary',
    'fertilizer': 'success',
    'irrigation': 'info',
    'pesticide': 'warning',
    'quality': ''
  }
  return tagMap[type] || ''
}

function getTypeName(type) {
  const nameMap = {
    'pest_control': '病虫害防治',
    'fertilizer': '施肥管理',
    'irrigation': '灌溉策略',
    'pesticide': '农药使用',
    'quality': '产品质量'
  }
  return nameMap[type] || type
}

function getUsageTagType(usage) {
  const usageTagMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return usageTagMap[usage] || ''
}

function getUsageName(usage) {
  const usageMap = {
    'high': '高频',
    'medium': '中频',
    'low': '低频'
  }
  return usageMap[usage] || usage
}

function getAccuracyStatus(accuracy) {
  if (accuracy >= 90) return 'success'
  if (accuracy >= 70) return 'warning'
  return 'exception'
}

function viewTree(tree) {
  ElMessage.info(`查看决策树: ${tree.name}`)
  // 实际实现中，这里会导航到决策树查看页面
}

function editTree(tree) {
  ElMessage.info(`编辑决策树: ${tree.name}`)
  // 实际实现中，这里会导航到决策树编辑页面
}

function duplicateTree(tree) {
  ElMessage.success(`已复制决策树: ${tree.name}`)
  
  // 创建副本
  const newTree = { ...tree }
  newTree.id = decisionTrees.value.length + 1
  newTree.name = `${tree.name} (副本)`
  newTree.createdAt = new Date().toISOString().split('T')[0]
  newTree.views = 0
  newTree.feedbacks = 0
  
  decisionTrees.value.push(newTree)
}

function exportTree(tree) {
  ElMessage.success(`已导出决策树: ${tree.name}`)
  // 实际实现中，这里会触发下载树结构的JSON或XML文件
}

function confirmDelete(tree) {
  treeToDelete.value = tree
  showDeleteDialog.value = true
}

function deleteTree() {
  if (!treeToDelete.value) return
  
  const index = decisionTrees.value.findIndex(tree => tree.id === treeToDelete.value.id)
  if (index > -1) {
    decisionTrees.value.splice(index, 1)
    ElMessage.success(`已删除决策树: ${treeToDelete.value.name}`)
  }
  
  showDeleteDialog.value = false
  treeToDelete.value = null
}

function handleFileChange(file) {
  importFile.value = file
}

function importTree() {
  if (!importFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  ElMessage.success('决策树导入成功')
  showImportDialog.value = false
  importFile.value = null
}

function createTree() {
  if (!newTreeForm.name || !newTreeForm.type) {
    ElMessage.warning('请填写决策树名称和类型')
    return
  }
  
  const newTree = {
    id: decisionTrees.value.length + 1,
    name: newTreeForm.name,
    type: newTreeForm.type,
    nodeCount: Math.floor(Math.random() * 10) + 5,
    complexity: 'medium',
    usage: 'low',
    createdAt: new Date().toISOString().split('T')[0],
    views: 0,
    accuracy: 85,
    feedbacks: 0
  }
  
  decisionTrees.value.push(newTree)
  
  ElMessage.success(`创建决策树: ${newTree.name}`)
  showCreateDialog.value = false
  
  // 重置表单
  Object.keys(newTreeForm).forEach(key => {
    newTreeForm[key] = key === 'createMethod' ? 'template' : ''
  })
}
</script>

<style scoped>
.decision-tree-management {
  padding: 20px;
  min-height: 100%;
  color: white;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.management-header h2 {
  margin: 0;
  color: white;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.trees-container {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 20px;
  height: calc(100vh - 100px);
}

.tree-filters {
  background-color: #1e3a8a;
  color: white;
  height: 100%;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.filter-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.tree-list {
  background-color: #1f2937;
  border-radius: 8px;
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-box {
  width: 250px;
}

.tree-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.tree-card {
  background-color: #2a3747;
  border: none;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.tree-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
}

.tree-thumbnail {
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
}

.tree-icon {
  margin-bottom: 10px;
}

.node-count {
  font-size: 0.9rem;
  opacity: 0.8;
}

.tree-info {
  padding: 15px;
}

.tree-title {
  font-weight: bold;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tree-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.tree-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: #9ca3af;
}

.tree-actions {
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: flex-end;
}

.tree-name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-block {
  width: 4px;
  height: 20px;
  border-radius: 2px;
}

.accuracy-indicator {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

:deep(.el-table) {
  --el-table-header-bg-color: #1e3a8a;
  --el-table-header-text-color: #ffffff;
  --el-table-row-hover-bg-color: #374151;
  background-color: transparent;
  color: #ffffff;
}

:deep(.el-table tr) {
  background-color: transparent;
}

:deep(.el-table td),
:deep(.el-table th.el-table__cell) {
  border-bottom-color: #374151;
}

.text-danger {
  color: #f56c6c;
}

.tree-uploader {
  display: flex;
  justify-content: center;
}
</style> 