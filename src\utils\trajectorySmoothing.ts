/**
 * 轨迹数据平滑处理模块
 * 提供多种轨迹平滑算法，解决设备定位精度问题
 */

import { mean, standardDeviation } from 'simple-statistics';
import type { Point } from './coordinateTransform';

/**
 * 简化的贝塞尔曲线实现
 */
class SimpleBezier {
  private p0: Point;
  private p1: Point;
  private p2: Point;
  private p3: Point;

  constructor(p0x: number, p0y: number, p1x: number, p1y: number,
              p2x: number, p2y: number, p3x: number, p3y: number) {
    this.p0 = { x: p0x, y: p0y };
    this.p1 = { x: p1x, y: p1y };
    this.p2 = { x: p2x, y: p2y };
    this.p3 = { x: p3x, y: p3y };
  }

  /**
   * 获取贝塞尔曲线上指定参数t的点
   * @param t 参数值，范围[0, 1]
   */
  get(t: number): Point {
    const mt = 1 - t;
    const mt2 = mt * mt;
    const mt3 = mt2 * mt;
    const t2 = t * t;
    const t3 = t2 * t;

    return {
      x: mt3 * this.p0.x + 3 * mt2 * t * this.p1.x + 3 * mt * t2 * this.p2.x + t3 * this.p3.x,
      y: mt3 * this.p0.y + 3 * mt2 * t * this.p1.y + 3 * mt * t2 * this.p2.y + t3 * this.p3.y
    };
  }
}

// 轨迹点扩展接口
export interface TrajectoryPoint extends Point {
  timestamp: number;
  speed?: number;
  isOutlier?: boolean; // 是否为异常点
}

// 平滑算法配置接口
export interface SmoothingConfig {
  // 移动平均窗口大小
  movingAverageWindow: number;
  // 贝塞尔曲线控制点权重
  bezierWeight: number;
  // 异常点检测阈值（标准差倍数）
  outlierThreshold: number;
  // 最小距离阈值（米）
  minDistanceThreshold: number;
  // 自适应窗口启用
  adaptiveWindow: boolean;
  // 平滑强度 (0-1)
  smoothingStrength: number;
}

// 默认平滑配置
export const DEFAULT_SMOOTHING_CONFIG: SmoothingConfig = {
  movingAverageWindow: 5,
  bezierWeight: 0.3,
  outlierThreshold: 2.0,
  minDistanceThreshold: 0.001,
  adaptiveWindow: true,
  smoothingStrength: 0.7
};

/**
 * 轨迹平滑处理器类
 */
export class TrajectorySmoothing {
  private config: SmoothingConfig;
  private rawPoints: TrajectoryPoint[] = [];
  private smoothedPoints: TrajectoryPoint[] = [];

  constructor(config: Partial<SmoothingConfig> = {}) {
    this.config = { ...DEFAULT_SMOOTHING_CONFIG, ...config };
  }

  /**
   * 添加新的轨迹点并进行平滑处理，包含坐标四舍五入
   */
  addPoint(point: TrajectoryPoint): TrajectoryPoint[] {
    const startTime = performance.now();

    // 对输入坐标进行四舍五入处理（保留两位小数）
    const roundedPoint: TrajectoryPoint = {
      ...point,
      x: Math.round(point.x * 100) / 100,
      y: Math.round(point.y * 100) / 100
    };

    this.rawPoints.push(roundedPoint);

    // 限制原始数据点数量，避免内存溢出
    if (this.rawPoints.length > 200) {
      this.rawPoints.shift();
    }

    // 执行平滑处理
    this.smoothedPoints = this.processTrajectory(this.rawPoints);

    const endTime = performance.now();
    const processingTime = endTime - startTime;

    // 性能监控：如果处理时间超过5ms，输出警告
    if (processingTime > 5) {
      console.warn(`轨迹平滑处理耗时较长: ${processingTime.toFixed(2)}ms`);
    }

    return this.smoothedPoints;
  }

  /**
   * 批量处理轨迹数据
   */
  processTrajectory(points: TrajectoryPoint[]): TrajectoryPoint[] {
    if (points.length < 2) {
      return [...points];
    }

    // 第一步：异常点检测和标记
    const pointsWithOutliers = this.detectOutliers(points);

    // 第二步：移动平均平滑
    const movingAveragePoints = this.applyMovingAverage(pointsWithOutliers);

    // 第三步：贝塞尔曲线平滑
    const bezierSmoothedPoints = this.applyBezierSmoothing(movingAveragePoints);

    return bezierSmoothedPoints;
  }

  /**
   * 异常点检测 - 基于距离和速度的统计分析
   */
  private detectOutliers(points: TrajectoryPoint[]): TrajectoryPoint[] {
    if (points.length < 3) {
      return points.map(p => ({ ...p, isOutlier: false }));
    }

    const distances: number[] = [];
    const speeds: number[] = [];

    // 计算相邻点之间的距离和速度
    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];

      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2)
      );
      distances.push(distance);

      const timeDiff = (curr.timestamp - prev.timestamp) / 1000; // 转换为秒
      if (timeDiff > 0) {
        speeds.push(distance / timeDiff);
      }
    }

    // 计算统计指标
    const meanDistance = mean(distances);
    const stdDistance = standardDeviation(distances);
    const meanSpeed = speeds.length > 0 ? mean(speeds) : 0;
    const stdSpeed = speeds.length > 0 ? standardDeviation(speeds) : 0;

    // 标记异常点
    return points.map((point, index) => {
      let isOutlier = false;

      if (index > 0) {
        const prev = points[index - 1];
        const distance = Math.sqrt(
          Math.pow(point.x - prev.x, 2) + Math.pow(point.y - prev.y, 2)
        );

        const timeDiff = (point.timestamp - prev.timestamp) / 1000;
        const speed = timeDiff > 0 ? distance / timeDiff : 0;

        // 基于距离的异常检测
        if (distance > meanDistance + this.config.outlierThreshold * stdDistance) {
          isOutlier = true;
        }

        // 基于速度的异常检测
        if (speeds.length > 0 && speed > meanSpeed + this.config.outlierThreshold * stdSpeed) {
          isOutlier = true;
        }
      }

      return { ...point, isOutlier };
    });
  }

  /**
   * 自适应移动平均平滑
   */
  private applyMovingAverage(points: TrajectoryPoint[]): TrajectoryPoint[] {
    const result: TrajectoryPoint[] = [];

    for (let i = 0; i < points.length; i++) {
      const currentPoint = points[i];

      // 如果是异常点，使用插值处理
      if (currentPoint.isOutlier && i > 0 && i < points.length - 1) {
        const prev = points[i - 1];
        const next = points[i + 1];

        // 线性插值
        const interpolatedPoint: TrajectoryPoint = {
          x: (prev.x + next.x) / 2,
          y: (prev.y + next.y) / 2,
          timestamp: currentPoint.timestamp,
          speed: currentPoint.speed,
          isOutlier: false
        };

        result.push(interpolatedPoint);
        continue;
      }

      // 计算自适应窗口大小
      let windowSize = this.config.movingAverageWindow;
      if (this.config.adaptiveWindow && currentPoint.speed) {
        // 根据速度调整窗口大小：速度越快，窗口越小
        windowSize = Math.max(3, Math.min(10,
          Math.round(this.config.movingAverageWindow * (1 - currentPoint.speed / 5))
        ));
      }

      // 获取窗口范围内的点
      const halfWindow = Math.floor(windowSize / 2);
      const startIndex = Math.max(0, i - halfWindow);
      const endIndex = Math.min(points.length - 1, i + halfWindow);

      const windowPoints = points.slice(startIndex, endIndex + 1)
        .filter(p => !p.isOutlier); // 排除异常点

      if (windowPoints.length === 0) {
        result.push(currentPoint);
        continue;
      }

      // 计算加权平均（距离当前点越近权重越大）
      let totalWeight = 0;
      let weightedX = 0;
      let weightedY = 0;

      windowPoints.forEach((point, idx) => {
        const distance = Math.abs(idx - (i - startIndex));
        const weight = 1 / (1 + distance * 0.5); // 距离权重

        weightedX += point.x * weight;
        weightedY += point.y * weight;
        totalWeight += weight;
      });

      const smoothedPoint: TrajectoryPoint = {
        x: weightedX / totalWeight,
        y: weightedY / totalWeight,
        timestamp: currentPoint.timestamp,
        speed: currentPoint.speed,
        isOutlier: false
      };

      // 应用平滑强度并四舍五入结果
      const finalPoint: TrajectoryPoint = {
        x: Math.round((currentPoint.x * (1 - this.config.smoothingStrength) +
           smoothedPoint.x * this.config.smoothingStrength) * 100) / 100,
        y: Math.round((currentPoint.y * (1 - this.config.smoothingStrength) +
           smoothedPoint.y * this.config.smoothingStrength) * 100) / 100,
        timestamp: currentPoint.timestamp,
        speed: currentPoint.speed,
        isOutlier: false
      };

      result.push(finalPoint);
    }

    return result;
  }

  /**
   * 贝塞尔曲线平滑处理
   */
  private applyBezierSmoothing(points: TrajectoryPoint[]): TrajectoryPoint[] {
    if (points.length < 4) {
      return points;
    }

    const result: TrajectoryPoint[] = [];

    // 保留第一个点
    result.push(points[0]);

    // 对每组4个点应用贝塞尔曲线
    for (let i = 0; i < points.length - 3; i += 3) {
      const p0 = points[i];
      const p1 = points[i + 1];
      const p2 = points[i + 2];
      const p3 = points[i + 3];

      // 创建贝塞尔曲线
      const bezier = new SimpleBezier(
        p0.x, p0.y,
        p1.x, p1.y,
        p2.x, p2.y,
        p3.x, p3.y
      );

      // 在曲线上采样点
      const sampleCount = 10;
      for (let t = 0.1; t <= 1; t += 1 / sampleCount) {
        const point = bezier.get(t);

        // 插值时间戳和速度
        const timeRatio = t;
        const interpolatedTimestamp = p0.timestamp +
          (p3.timestamp - p0.timestamp) * timeRatio;

        const interpolatedSpeed = p0.speed && p3.speed ?
          p0.speed + (p3.speed - p0.speed) * timeRatio : undefined;

        result.push({
          x: Math.round(point.x * 100) / 100,
          y: Math.round(point.y * 100) / 100,
          timestamp: interpolatedTimestamp,
          speed: interpolatedSpeed,
          isOutlier: false
        });
      }
    }

    // 保留最后一个点
    if (points.length > 0) {
      result.push(points[points.length - 1]);
    }

    return result;
  }

  /**
   * 获取原始轨迹点
   */
  getRawPoints(): TrajectoryPoint[] {
    return [...this.rawPoints];
  }

  /**
   * 获取平滑后的轨迹点
   */
  getSmoothedPoints(): TrajectoryPoint[] {
    return [...this.smoothedPoints];
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SmoothingConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // 重新处理现有数据
    if (this.rawPoints.length > 0) {
      this.smoothedPoints = this.processTrajectory(this.rawPoints);
    }
  }

  /**
   * 清除所有数据
   */
  clear(): void {
    this.rawPoints = [];
    this.smoothedPoints = [];
  }

  /**
   * 获取当前配置
   */
  getConfig(): SmoothingConfig {
    return { ...this.config };
  }
}

/**
 * 创建轨迹平滑处理器的工厂函数
 */
export function createTrajectorySmoothing(config?: Partial<SmoothingConfig>): TrajectorySmoothing {
  return new TrajectorySmoothing(config);
}
