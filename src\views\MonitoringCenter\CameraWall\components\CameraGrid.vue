<!--
摄像头网格布局组件
功能：
1. 支持多种网格布局（1x1, 2x2, 3x3, 4x4）
2. WebSocket摄像头流集成
3. 摄像头状态监控
4. 响应式布局设计
-->

<template>
  <div class="camera-grid-container" :class="`layout-${currentLayout}`">
    <!-- 摄像头网格 -->
    <div class="camera-grid" :style="gridStyle">
      <div
        v-for="(camera, index) in displayCameras"
        :key="camera?.id || `empty-${index}`"
        class="camera-slot"
        :class="{
          'has-camera': !!camera,
          'is-selected': selectedCameraId === camera?.id,
          'is-offline': camera?.status === 'offline'
        }"
        @click="selectCamera(camera)"
      >
        <!-- 摄像头视频 -->
        <div v-if="camera" class="camera-content">
          <!-- 视频元素 -->
          <video
            :ref="el => setVideoRef(camera.id, el)"
            class="camera-video"
            autoplay
            playsinline
            muted
            @loadstart="handleVideoLoadStart(camera.id)"
            @canplay="handleVideoCanPlay(camera.id)"
            @error="handleVideoError(camera.id, $event)"
          ></video>

          <!-- 摄像头信息覆盖层 -->
          <div class="camera-overlay">
            <div class="camera-info">
              <div class="camera-name">{{ camera.name }}</div>
              <div class="camera-location">{{ camera.location }}</div>
            </div>
            
            <!-- 状态指示器 -->
            <div class="status-indicators">
              <div class="status-dot" :class="camera.status"></div>
              <div class="stream-status" :class="camera.streamStatus">
                <el-icon v-if="camera.streamStatus === 'normal'"><VideoCamera /></el-icon>
                <el-icon v-else-if="camera.streamStatus === 'stuttering'"><Warning /></el-icon>
                <el-icon v-else><Close /></el-icon>
              </div>
            </div>
          </div>

          <!-- 连接状态 -->
          <div v-if="connectionStates[camera.id]?.connecting" class="connecting-overlay">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <span>连接中...</span>
          </div>

          <!-- 错误状态 -->
          <div v-if="connectionStates[camera.id]?.error" class="error-overlay">
            <el-icon class="error-icon"><WarningFilled /></el-icon>
            <span>连接失败</span>
            <el-button size="small" @click.stop="reconnectCamera(camera.id)">重试</el-button>
          </div>
        </div>

        <!-- 空槽位 -->
        <div v-else class="empty-slot">
          <el-icon class="empty-icon"><Plus /></el-icon>
          <span>空闲位置</span>
        </div>
      </div>
    </div>

    <!-- 全屏模式 -->
    <div v-if="fullscreenCamera" class="fullscreen-overlay" @click="exitFullscreen">
      <video
        :ref="el => setVideoRef(`${fullscreenCamera.id}-fullscreen`, el)"
        class="fullscreen-video"
        autoplay
        playsinline
        muted
      ></video>
      
      <div class="fullscreen-controls">
        <div class="camera-title">{{ fullscreenCamera.name }} - {{ fullscreenCamera.location }}</div>
        <el-button @click.stop="exitFullscreen" icon="Close" circle></el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElButton, ElIcon } from 'element-plus';
import { VideoCamera, Warning, Close, Loading, WarningFilled, Plus } from '@element-plus/icons-vue';
import type { Camera } from '../types';
import { WEBSOCKET_URLS } from '@/config/env';

// 组件属性
const props = defineProps<{
  cameras: Camera[];
  layout: string;
  selectedStatuses: string[];
}>();

// 组件事件
const emit = defineEmits<{
  'camera-selected': [camera: Camera];
  'camera-error': [cameraId: string, error: string];
}>();

// 状态变量
const videoRefs = ref<Record<string, HTMLVideoElement>>({});
const websockets = ref<Record<string, WebSocket>>({});
const connectionStates = ref<Record<string, {
  connecting: boolean;
  connected: boolean;
  error: boolean;
  errorMessage: string;
}>>({});
const selectedCameraId = ref<string | null>(null);
const fullscreenCamera = ref<Camera | null>(null);

// 计算属性
const currentLayout = computed(() => props.layout);

const gridStyle = computed(() => {
  const layouts: Record<string, { cols: number; rows: number }> = {
    '1x1': { cols: 1, rows: 1 },
    '2x2': { cols: 2, rows: 2 },
    '3x3': { cols: 3, rows: 3 },
    '4x4': { cols: 4, rows: 4 }
  };
  
  const layout = layouts[currentLayout.value] || layouts['2x2'];
  
  return {
    gridTemplateColumns: `repeat(${layout.cols}, 1fr)`,
    gridTemplateRows: `repeat(${layout.rows}, 1fr)`
  };
});

const displayCameras = computed(() => {
  const layouts: Record<string, number> = {
    '1x1': 1,
    '2x2': 4,
    '3x3': 9,
    '4x4': 16
  };
  
  const maxSlots = layouts[currentLayout.value] || 4;
  
  // 过滤摄像头
  const filteredCameras = props.cameras.filter(camera => 
    props.selectedStatuses.includes(camera.status)
  );
  
  // 填充到指定槽位数量
  const result: (Camera | null)[] = [];
  for (let i = 0; i < maxSlots; i++) {
    result.push(filteredCameras[i] || null);
  }
  
  return result;
});

// 方法
const setVideoRef = (id: string, el: HTMLVideoElement | null) => {
  if (el) {
    videoRefs.value[id] = el;
  }
};

const selectCamera = (camera: Camera | null) => {
  if (!camera) return;
  
  selectedCameraId.value = camera.id;
  emit('camera-selected', camera);
  
  // 双击进入全屏
  if (selectedCameraId.value === camera.id) {
    setTimeout(() => {
      if (selectedCameraId.value === camera.id) {
        enterFullscreen(camera);
      }
    }, 300);
  }
};

const enterFullscreen = (camera: Camera) => {
  fullscreenCamera.value = camera;
  // 复制视频流到全屏视频元素
  const sourceVideo = videoRefs.value[camera.id];
  const fullscreenVideo = videoRefs.value[`${camera.id}-fullscreen`];
  
  if (sourceVideo && fullscreenVideo && sourceVideo.srcObject) {
    fullscreenVideo.srcObject = sourceVideo.srcObject;
  }
};

const exitFullscreen = () => {
  fullscreenCamera.value = null;
};

// WebSocket连接管理
const connectCamera = (camera: Camera) => {
  if (websockets.value[camera.id]) {
    websockets.value[camera.id].close();
  }
  
  // 初始化连接状态
  connectionStates.value[camera.id] = {
    connecting: true,
    connected: false,
    error: false,
    errorMessage: ''
  };
  
  try {
    const ws = new WebSocket(camera.streamUrl);
    websockets.value[camera.id] = ws;
    
    ws.onopen = () => {
      connectionStates.value[camera.id] = {
        connecting: false,
        connected: true,
        error: false,
        errorMessage: ''
      };
      console.log(`摄像头 ${camera.id} WebSocket连接成功`);
    };
    
    ws.onmessage = (event) => {
      // 处理WebSocket消息（如果是视频流数据）
      handleWebSocketMessage(camera.id, event);
    };
    
    ws.onerror = (error) => {
      connectionStates.value[camera.id] = {
        connecting: false,
        connected: false,
        error: true,
        errorMessage: '连接失败'
      };
      emit('camera-error', camera.id, '连接失败');
      console.error(`摄像头 ${camera.id} WebSocket连接错误:`, error);
    };
    
    ws.onclose = () => {
      if (connectionStates.value[camera.id]?.connected) {
        connectionStates.value[camera.id] = {
          connecting: false,
          connected: false,
          error: true,
          errorMessage: '连接断开'
        };
      }
      console.log(`摄像头 ${camera.id} WebSocket连接关闭`);
    };
    
  } catch (error) {
    connectionStates.value[camera.id] = {
      connecting: false,
      connected: false,
      error: true,
      errorMessage: '连接异常'
    };
    emit('camera-error', camera.id, '连接异常');
    console.error(`摄像头 ${camera.id} 连接异常:`, error);
  }
};

const disconnectCamera = (cameraId: string) => {
  if (websockets.value[cameraId]) {
    websockets.value[cameraId].close();
    delete websockets.value[cameraId];
  }
  
  if (connectionStates.value[cameraId]) {
    delete connectionStates.value[cameraId];
  }
};

const reconnectCamera = (cameraId: string) => {
  const camera = props.cameras.find(c => c.id === cameraId);
  if (camera) {
    disconnectCamera(cameraId);
    setTimeout(() => connectCamera(camera), 1000);
  }
};

const handleWebSocketMessage = (cameraId: string, event: MessageEvent) => {
  // TODO: 处理WebSocket视频流数据
  // 这里需要根据后端WebSocket的具体实现来处理视频数据
  console.log(`收到摄像头 ${cameraId} 的数据:`, event.data);
};

// 视频事件处理
const handleVideoLoadStart = (cameraId: string) => {
  console.log(`摄像头 ${cameraId} 开始加载视频`);
};

const handleVideoCanPlay = (cameraId: string) => {
  console.log(`摄像头 ${cameraId} 视频可以播放`);
};

const handleVideoError = (cameraId: string, event: Event) => {
  console.error(`摄像头 ${cameraId} 视频错误:`, event);
  emit('camera-error', cameraId, '视频播放错误');
};

// 监听摄像头列表变化
watch(() => props.cameras, (newCameras, oldCameras) => {
  // 断开已移除的摄像头连接
  if (oldCameras) {
    const removedCameras = oldCameras.filter(old => 
      !newCameras.find(newCam => newCam.id === old.id)
    );
    removedCameras.forEach(camera => disconnectCamera(camera.id));
  }
  
  // 连接新摄像头
  newCameras.forEach(camera => {
    if (!websockets.value[camera.id] && camera.status !== 'offline') {
      connectCamera(camera);
    }
  });
}, { immediate: true });

// 生命周期
onMounted(() => {
  // 初始化连接所有在线摄像头
  props.cameras.forEach(camera => {
    if (camera.status !== 'offline') {
      connectCamera(camera);
    }
  });
});

onUnmounted(() => {
  // 清理所有WebSocket连接
  Object.keys(websockets.value).forEach(cameraId => {
    disconnectCamera(cameraId);
  });
});
</script>

<style scoped lang="scss">
.camera-grid-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #0a0a0a;
  border-radius: 12px;
  overflow: hidden;
}

.camera-grid {
  display: grid;
  gap: 2px;
  width: 100%;
  height: 100%;
  padding: 2px;
}

.camera-slot {
  position: relative;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 20px rgba(0, 255, 136, 0.3);
  }
  
  &.is-selected {
    border: 2px solid #00ff88;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
  }
  
  &.is-offline {
    opacity: 0.5;
    filter: grayscale(100%);
  }
}

.camera-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;
}

.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.7) 0%,
    transparent 30%,
    transparent 70%,
    rgba(0, 0, 0, 0.7) 100%
  );
  pointer-events: none;
}

.camera-info {
  position: absolute;
  top: 8px;
  left: 8px;
  color: white;
  
  .camera-name {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
  }
  
  .camera-location {
    font-size: 12px;
    opacity: 0.8;
  }
}

.status-indicators {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  &.normal { background: #00ff88; }
  &.lowBattery { background: #ffaa00; }
  &.offline { background: #ff4444; }
}

.stream-status {
  color: white;
  font-size: 14px;
  
  &.normal { color: #00ff88; }
  &.stuttering { color: #ffaa00; }
  &.interrupted { color: #ff4444; }
}

.connecting-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  gap: 8px;
}

.loading-icon {
  font-size: 24px;
  animation: spin 1s linear infinite;
}

.error-icon {
  font-size: 24px;
  color: #ff4444;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  gap: 8px;
  
  .empty-icon {
    font-size: 32px;
  }
}

.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-video {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

.fullscreen-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  color: white;
  
  .camera-title {
    font-size: 18px;
    font-weight: 600;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 响应式布局
@media (max-width: 768px) {
  .camera-grid {
    gap: 1px;
    padding: 1px;
  }
  
  .camera-info {
    .camera-name {
      font-size: 12px;
    }
    
    .camera-location {
      font-size: 10px;
    }
  }
}
</style>
