<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';
import type { TaskProgressInfo } from '@/types/taskScheduling';

const props = defineProps<{
  taskData: TaskProgressInfo;
  progressHistory: {
    time: string;
    progress: number;
  }[];
}>();

const chartContainer = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;
  
  chart = echarts.init(chartContainer.value);
  updateChart();
  
  window.addEventListener('resize', () => {
    chart?.resize();
  });
};

// 更新图表数据
const updateChart = () => {
  if (!chart) return;

  const times = props.progressHistory.map(item => {
    const date = new Date(item.time);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  });
  
  const progress = props.progressHistory.map(item => item.progress);
  
  const option = {
    grid: {
      top: 40,
      bottom: 40,
      left: 60,
      right: 20
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        return `时间: ${params[0].axisValue}<br />进度: ${params[0].value}%`;
      },
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    xAxis: {
      type: 'category',
      data: times,
      axisLabel: {
        color: '#9ca3af',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#4b5563'
        }
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      name: '完成进度(%)',
      nameTextStyle: {
        color: '#9ca3af'
      },
      axisLabel: {
        color: '#9ca3af',
        formatter: '{value}%'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#4b5563'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(75, 85, 99, 0.1)'
        }
      }
    },
    series: [
      {
        name: '任务进度',
        type: 'line',
        data: progress,
        smooth: true,
        symbol: 'emptyCircle',
        symbolSize: 8,
        itemStyle: {
          color: '#10b981',
          borderColor: '#1f2937',
          borderWidth: 2
        },
        lineStyle: {
          width: 3,
          color: '#10b981',
          shadowColor: 'rgba(16, 185, 129, 0.3)',
          shadowBlur: 10
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(16, 185, 129, 0.5)' },
            { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
          ])
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ],
          itemStyle: {
            color: '#10b981'
          },
          label: {
            color: '#fff'
          }
        }
      }
    ]
  };

  chart.setOption(option);
};

// 监听数据变化
watch(() => props.progressHistory, () => {
  updateChart();
}, { deep: true });

// 生命周期钩子
onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', () => {
    chart?.resize();
  });
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style> 