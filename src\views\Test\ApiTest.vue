<template>
  <div class="api-test-container">
    <h1>API测试工具</h1>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>API配置信息</h2>
        </div>
      </template>

      <div class="api-config">
        <p><strong>API基础URL:</strong> {{ apiBaseUrl }}</p>
        <el-button type="primary" @click="testConnection" :loading="loading.connection">测试连接</el-button>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>登录API测试</h2>
        </div>
      </template>

      <el-form :model="loginForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testLogin" :loading="loading.login">测试登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>摄像头API测试</h2>
        </div>
      </template>

      <el-button type="primary" @click="getAllCameras" :loading="loading.cameras">获取所有摄像头</el-button>

      <el-form v-if="cameraList.length > 0" class="mt-4">
        <el-form-item label="摄像头">
          <el-select v-model="selectedCamera" placeholder="请选择摄像头">
            <el-option
              v-for="camera in cameraList"
              :key="camera.id"
              :label="camera.name"
              :value="camera.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getCameraInfo" :loading="loading.cameraInfo" :disabled="!selectedCamera">
            获取摄像头信息
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>设备控制API测试</h2>
        </div>
      </template>

      <div class="device-controls">
        <div class="device-control">
          <h3>超声波设备</h3>
          <el-switch
            v-model="ultrasonicStatus"
            active-text="开启"
            inactive-text="关闭"
            @change="controlUltrasonic"
            :loading="loading.ultrasonic"
          ></el-switch>
        </div>

        <div class="device-control">
          <h3>捕虫灯</h3>
          <el-switch
            v-model="lassoStatus"
            active-text="开启"
            inactive-text="关闭"
            @change="controlLasso"
            :loading="loading.lasso"
          ></el-switch>
        </div>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>API响应</h2>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="请求信息" name="request">
          <pre class="response-pre">{{ requestInfo }}</pre>
        </el-tab-pane>
        <el-tab-pane label="响应数据" name="response">
          <pre class="response-pre">{{ responseData }}</pre>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import envConfig from '@/config/env'

// API基础URL
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || '/api'

// 定义接口响应类型
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 加载状态
const loading = reactive({
  connection: false,
  login: false,
  cameras: false,
  cameraInfo: false,
  ultrasonic: false,
  lasso: false
})

// 设备状态
const ultrasonicStatus = ref(false)
const lassoStatus = ref(false)

// 摄像头数据
const cameraList = ref<any[]>([])
const selectedCamera = ref('')

// 响应数据
const requestInfo = ref('')
const responseData = ref('')
const activeTab = ref('request')

// 创建axios实例
const api = axios.create({
  baseURL: apiBaseUrl,
  timeout: envConfig.apiTimeout
})

// 记录请求和响应
function logRequest(url: string, method: string, data?: any, params?: any) {
  requestInfo.value = JSON.stringify({
    url: `${apiBaseUrl}${url}`,
    method,
    headers: {
      'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : undefined
    },
    data,
    params
  }, null, 2)
}

function logResponse(data: any) {
  responseData.value = JSON.stringify(data, null, 2)
}

// 测试API连接
const testConnection = async () => {
  loading.connection = true
  logRequest('', 'OPTIONS')

  try {
    // 尝试发送一个简单的OPTIONS请求
    await axios({
      method: 'OPTIONS',
      url: apiBaseUrl,
      timeout: 5000
    })

    const response = {
      status: 'success',
      message: '连接成功！可以访问API服务器。'
    }

    logResponse(response)
    ElMessage.success('连接成功！可以访问API服务器。')
  } catch (error: any) {
    const errorResponse = {
      status: 'error',
      message: '连接失败',
      error: error.message
    }

    logResponse(errorResponse)
    ElMessage.error(`连接失败: ${error.message}`)
  } finally {
    loading.connection = false
  }
}

// 测试登录API
const testLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    ElMessage.warning('请输入用户名和密码')
    return
  }

  loading.login = true
  const data = {
    username: loginForm.username,
    password: loginForm.password
  }

  logRequest('/auth/login', 'POST', data)

  try {
    const response = await api.post('/auth/login', data)
    logResponse(response.data)

    const apiResponse = response.data as ApiResponse

    if (apiResponse.code === 200) {
      ElMessage.success('登录成功')
      localStorage.setItem('token', apiResponse.data.token)
    } else {
      ElMessage.error(apiResponse.message || '登录失败')
    }
  } catch (error: any) {
    if (error.response) {
      logResponse(error.response.data)
    } else {
      logResponse({ error: error.message })
    }
    ElMessage.error(error.response?.data?.message || '登录失败')
  } finally {
    loading.login = false
  }
}

// 获取所有摄像头
const getAllCameras = async () => {
  loading.cameras = true
  logRequest('/camera/getAllCameras', 'GET')

  try {
    const response = await api.get('/camera/getAllCameras')
    logResponse(response.data)

    const apiResponse = response.data as ApiResponse

    if (apiResponse.code === 200) {
      cameraList.value = apiResponse.data
      ElMessage.success('获取摄像头列表成功')
    } else {
      ElMessage.error(apiResponse.message || '获取摄像头列表失败')
    }
  } catch (error: any) {
    if (error.response) {
      logResponse(error.response.data)
    } else {
      logResponse({ error: error.message })
    }
    ElMessage.error(error.response?.data?.message || '获取摄像头列表失败')
  } finally {
    loading.cameras = false
  }
}

// 获取摄像头信息
const getCameraInfo = async () => {
  if (!selectedCamera.value) {
    ElMessage.warning('请先选择摄像头')
    return
  }

  loading.cameraInfo = true
  logRequest(`/camera/getCameraInfo/${selectedCamera.value}`, 'GET')

  try {
    const response = await api.get(`/camera/getCameraInfo/${selectedCamera.value}`)
    logResponse(response.data)

    const apiResponse = response.data as ApiResponse

    if (apiResponse.code === 200) {
      ElMessage.success('获取摄像头信息成功')
    } else {
      ElMessage.error(apiResponse.message || '获取摄像头信息失败')
    }
  } catch (error: any) {
    if (error.response) {
      logResponse(error.response.data)
    } else {
      logResponse({ error: error.message })
    }
    ElMessage.error(error.response?.data?.message || '获取摄像头信息失败')
  } finally {
    loading.cameraInfo = false
  }
}

// 控制超声波设备
const controlUltrasonic = async () => {
  loading.ultrasonic = true
  const data = { status: ultrasonicStatus.value ? 1 : 0 }
  logRequest('/ultrasonic/startOrStop', 'POST', data)

  try {
    const response = await api.post('/ultrasonic/startOrStop', data)
    logResponse(response.data)

    ElMessage.success(ultrasonicStatus.value ? '已开启超声波设备' : '已关闭超声波设备')
  } catch (error: any) {
    if (error.response) {
      logResponse(error.response.data)
    } else {
      logResponse({ error: error.message })
    }
    ElMessage.error(error.response?.data?.message || '控制超声波设备失败')
    // 恢复状态
    ultrasonicStatus.value = !ultrasonicStatus.value
  } finally {
    loading.ultrasonic = false
  }
}

// 控制捕虫灯
const controlLasso = async () => {
  loading.lasso = true
  const data = { status: lassoStatus.value ? 1 : 0 }
  logRequest('/lasso-mechanism/startOrStop', 'POST', data)

  try {
    const response = await api.post('/lasso-mechanism/startOrStop', data)
    logResponse(response.data)

    ElMessage.success(lassoStatus.value ? '已开启捕虫灯' : '已关闭捕虫灯')
  } catch (error: any) {
    if (error.response) {
      logResponse(error.response.data)
    } else {
      logResponse({ error: error.message })
    }
    ElMessage.error(error.response?.data?.message || '控制捕虫灯失败')
    // 恢复状态
    lassoStatus.value = !lassoStatus.value
  } finally {
    loading.lasso = false
  }
}

// 页面加载时自动测试连接
onMounted(() => {
  testConnection()
})
</script>

<style scoped lang="scss">
.api-test-container {
  max-width: 1000px;
  margin: 20px auto;
  padding: 0 20px;
}

.test-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }
}

.api-config {
  margin-bottom: 20px;
}

.device-controls {
  display: flex;
  gap: 40px;

  .device-control {
    display: flex;
    flex-direction: column;
    align-items: center;

    h3 {
      margin-bottom: 10px;
    }
  }
}

.response-pre {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  overflow: auto;
  max-height: 300px;
  font-family: monospace;
}

.mt-4 {
  margin-top: 16px;
}
</style>
