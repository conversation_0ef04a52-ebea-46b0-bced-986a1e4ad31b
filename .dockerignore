# Docker 构建忽略文件
# 用于排除不必要的文件和目录，提高构建速度和减小镜像大小

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# 构建输出（CI/CD时会重新构建）
dist/
build/
.output/
.nuxt/
.next/

# 缓存目录
.cache/
.parcel-cache/
.vite/
.turbo/

# 环境变量文件（敏感信息）
.env.local
.env.*.local

# 版本控制
.git/
.gitignore
.gitattributes

# CI/CD 配置文件
.drone.yml
.gitee-ci.yml

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/
*.log

# 测试相关
coverage/
.nyc_output/
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 文档和说明
README.md
CHANGELOG.md
LICENSE
*.md
docs/

# Docker 相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 临时文件
tmp/
temp/
.tmp/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 其他开发工具配置
.eslintcache
.stylelintcache
.prettierignore
.editorconfig
