<!-- 
  ElectronicFenceManagement.vue
  施药区域电子围栏管理模块
  用于创建、编辑和管理农田电子围栏，限制农药喷洒区域
-->
<template>
  <div class="fence-management">
    <!-- 页面标题 -->
    <PageHeader
      title="施药区域电子围栏管理"
      description="创建和管理农田电子围栏，限制农药喷洒区域，保护敏感地带"
      icon="Place"
    >
      <template #actions>
        <el-button type="primary" @click="showFenceForm(null)">
          <el-icon><Plus /></el-icon>
          新建围栏
        </el-button>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ getActiveFencesCount() }}</span>
            <span class="summary-label">已启用围栏</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 左侧围栏列表 -->
      <DataPanel title="围栏列表">
        <template #actions>
          <el-input
            v-model="searchQuery"
            placeholder="搜索围栏名称"
            clearable
            :prefix-icon="Search"
            class="search-input"
          />
        </template>
        
        <div class="list-content">
          <div v-if="loading" class="loading-container">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
          
          <el-empty v-else-if="filteredFences.length === 0" description="暂无围栏数据" />
          
          <div v-else class="fence-items">
            <div
              v-for="fence in filteredFences"
              :key="fence.id"
              :class="['fence-item', { active: selectedFence?.id === fence.id }]"
              @click="selectFence(fence)"
            >
              <div class="fence-item-color" :style="{ backgroundColor: fence.color }"></div>
              <div class="fence-item-info">
                <div class="fence-item-title">
                  <span>{{ fence.name }}</span>
                  <StatusIndicator 
                    :type="fence.isActive ? 'success' : 'normal'" 
                    :label="fence.isActive ? '已启用' : '已禁用'"
                    size="small"
                  />
                </div>
                <div class="fence-item-type">
                  {{ getFenceTypeLabel(fence.fenceType) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </DataPanel>
      
      <!-- 右侧地图与详情 -->
      <div class="map-detail-container">
        <!-- 地图区域 -->
        <DataPanel title="电子围栏地图">
          <template #actions>
            <StatusIndicator type="normal" label="拖动地图可查看更多区域" size="small" />
          </template>
          <div class="map-wrapper">
            <div ref="mapContainer" class="map-container"></div>
            <div v-if="mapLoading" class="map-loading">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <span>地图加载中...</span>
            </div>
            <div v-if="mapError" class="map-error">
              <el-icon><Warning /></el-icon>
              <p>{{ mapError }}</p>
            </div>
          </div>
        </DataPanel>
        
        <!-- 围栏详情 -->
        <DataPanel v-if="selectedFence" title="围栏详情">
          <template #actions>
            <div class="detail-actions">
              <el-button 
                size="small" 
                type="primary"
                plain
                @click="showFenceForm(selectedFence)"
              >
                <el-icon><Edit /></el-icon> 编辑
              </el-button>
              <el-button 
                size="small" 
                type="danger"
                plain
                @click="confirmDelete"
              >
                <el-icon><Delete /></el-icon> 删除
              </el-button>
            </div>
          </template>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="围栏名称">
              {{ selectedFence.name }}
            </el-descriptions-item>
            <el-descriptions-item label="围栏类型">
              {{ getFenceTypeLabel(selectedFence.fenceType) }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-switch
                v-model="selectedFence.isActive"
                @change="updateFenceStatus"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-descriptions-item>
            <el-descriptions-item label="有效期">
              <span v-if="selectedFence.startDate && selectedFence.endDate">
                {{ formatDate(selectedFence.startDate) }} 至 {{ formatDate(selectedFence.endDate) }}
              </span>
              <span v-else>无期限</span>
            </el-descriptions-item>
            <el-descriptions-item label="描述">
              {{ selectedFence.description || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </DataPanel>
      </div>
    </div>
    
    <!-- 状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="禁止喷洒区域" />
        <StatusIndicator type="warning" label="限制喷洒区域" />
        <StatusIndicator type="error" label="禁止进入区域" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="fetchFences">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 围栏表单对话框 -->
    <el-dialog
      v-model="showDialog"
      :title="isEditMode ? '编辑电子围栏' : '创建电子围栏'"
      width="600px"
    >
      <el-form
        ref="fenceFormRef"
        :model="fenceForm"
        label-width="120px"
        :rules="fenceRules"
      >
        <el-form-item label="围栏名称" prop="name">
          <el-input v-model="fenceForm.name" placeholder="请输入围栏名称" />
        </el-form-item>
        
        <el-form-item label="围栏类型" prop="fenceType">
          <el-select v-model="fenceForm.fenceType" placeholder="请选择围栏类型" style="width: 100%">
            <el-option label="禁止进入区域" value="no_entry" />
            <el-option label="禁止喷洒区域" value="no_spray" />
            <el-option label="限制喷洒区域" value="limited_spray" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="围栏描述" prop="description">
          <el-input 
            v-model="fenceForm.description" 
            type="textarea" 
            rows="3" 
            placeholder="请输入围栏描述" 
          />
        </el-form-item>
        
        <el-form-item label="围栏区域" prop="coordinates">
          <div class="coordinate-info">
            <span v-if="fenceForm.coordinates.length > 0">
              已绘制 {{ fenceForm.coordinates.length }} 个坐标点
            </span>
            <span v-else class="text-warning">
              尚未绘制围栏区域
            </span>
          </div>
          <el-button type="primary" @click="handleDrawFence">
            <el-icon><Edit /></el-icon>
            在地图上绘制
          </el-button>
        </el-form-item>
        
        <el-form-item label="围栏颜色" prop="color">
          <el-color-picker v-model="fenceForm.color" show-alpha />
        </el-form-item>
        
        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="fenceForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        
        <el-form-item label="有效时间段">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="saveFence" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="showDeleteDialog"
      title="确认删除"
      width="400px"
    >
      <p>确定要删除围栏 "{{ selectedFence?.name }}" 吗？此操作不可撤销。</p>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDeleteDialog = false">取消</el-button>
          <el-button type="danger" @click="deleteFence" :loading="deleting">删除</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Place, 
  Plus, 
  Loading, 
  Search,
  Edit,
  Delete,
  Refresh,
  Warning
} from '@element-plus/icons-vue'
import type { ElectronicFence } from '@/types/pesticide'
import { formatDate } from '@/utils/date'
import { loadAMapAPI } from '@/utils/mapLoader'

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue'
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue'
import DataPanel from '../DeviceManagement/components/DataPanel.vue'

/**
 * 电子围栏管理页面
 * 
 * 注意：此版本使用本地模拟数据，不依赖后端API
 * 所有围栏数据的增删改查操作都在前端内存中完成
 */

// 加载状态
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const mapLoading = ref(false)
const mapError = ref<string | null>(null)

// 围栏列表
const fences = ref<ElectronicFence[]>([])
const selectedFence = ref<ElectronicFence | null>(null)
const searchQuery = ref('')
const lastUpdateTime = ref(new Date())

// 筛选后的围栏列表
const filteredFences = computed(() => {
  if (!searchQuery.value) return fences.value
  
  const query = searchQuery.value.toLowerCase()
  return fences.value.filter(fence => 
    fence.name.toLowerCase().includes(query) ||
    fence.description.toLowerCase().includes(query)
  )
})

// 表单相关
const showDialog = ref(false)
const isEditMode = ref(false)
const fenceFormRef = ref()
const dateRange = ref<[string, string] | null>(null)

// 删除确认
const showDeleteDialog = ref(false)

// 地图相关
const mapContainer = ref<HTMLElement | null>(null)
const map = ref<any>(null)
const polygons = ref<any[]>([])

// 围栏表单
const fenceForm = reactive({
  id: '',
  name: '',
  description: '',
  fenceType: 'no_spray' as 'no_entry' | 'no_spray' | 'limited_spray',
  color: '#ff0000',
  coordinates: [] as { lat: number; lng: number }[],
  isActive: true,
  startDate: '',
  endDate: '',
  createdBy: 'admin'
})

// 表单验证规则
const fenceRules = {
  name: [
    { required: true, message: '请输入围栏名称', trigger: 'blur' },
    { min: 2, max: 50, message: '围栏名称长度应为2-50个字符', trigger: 'blur' }
  ],
  fenceType: [
    { required: true, message: '请选择围栏类型', trigger: 'change' }
  ],
  color: [
    { required: true, message: '请选择围栏颜色', trigger: 'change' }
  ]
}

// 获取围栏类型标签
const getFenceTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'no_entry': '禁止进入区域',
    'no_spray': '禁止喷洒区域',
    'limited_spray': '限制喷洒区域'
  }
  return typeMap[type] || type
}

// 获取在线围栏数量
const getActiveFencesCount = () => {
  // 确保 fences.value 是数组
  if (!Array.isArray(fences.value)) {
    console.warn('fences.value 不是数组:', fences.value);
    return 0;
  }
  return fences.value.filter(fence => fence.isActive).length;
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 获取围栏列表 - 使用本地模拟数据
const fetchFences = async () => {
  loading.value = true;
  try {
    // 模拟数据 - 在实际项目中，这些数据通常来自后端API
    const mockFences: ElectronicFence[] = [
      {
        id: '1',
        name: '东部水稻田禁喷区',
        description: '东部水稻田周边的生态保护区，禁止喷洒农药',
        coordinates: [
          { lat: 39.90923, lng: 116.397428 },
          { lat: 39.90923, lng: 116.407428 },
          { lat: 39.91923, lng: 116.407428 },
          { lat: 39.91923, lng: 116.397428 }
        ],
        fenceType: 'no_spray',
        color: '#ff5500',
        isActive: true,
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        createdBy: 'admin',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: '西北角限制喷洒区',
        description: '西北角靠近村庄区域，限制高浓度农药喷洒',
        coordinates: [
          { lat: 39.92923, lng: 116.387428 },
          { lat: 39.92923, lng: 116.397428 },
          { lat: 39.93923, lng: 116.397428 },
          { lat: 39.93923, lng: 116.387428 }
        ],
        fenceType: 'limited_spray',
        color: '#ffaa00',
        isActive: true,
        startDate: '',
        endDate: '',
        createdBy: 'admin',
        createdAt: '2023-02-15T00:00:00Z',
        updatedAt: '2023-02-15T00:00:00Z'
      },
      {
        id: '3',
        name: '南部禁入区',
        description: '南部高压设备区域，禁止人员和设备进入',
        coordinates: [
          { lat: 39.89923, lng: 116.397428 },
          { lat: 39.89923, lng: 116.407428 },
          { lat: 39.88923, lng: 116.407428 },
          { lat: 39.88923, lng: 116.397428 }
        ],
        fenceType: 'no_entry',
        color: '#ff0000',
        isActive: false,
        startDate: '2023-03-01',
        endDate: '2023-06-30',
        createdBy: 'admin',
        createdAt: '2023-03-01T00:00:00Z',
        updatedAt: '2023-03-01T00:00:00Z'
      }
    ];
    
    // 设置围栏数据
    fences.value = mockFences;
    console.log('使用模拟围栏数据:', mockFences.length, '个围栏');
    lastUpdateTime.value = new Date();
  } catch (error) {
    console.error('模拟围栏数据加载失败:', error);
    ElMessage.error('获取围栏数据失败');
    fences.value = [];
  } finally {
    loading.value = false;
  }
};

// 选择围栏
const selectFence = (fence: ElectronicFence) => {
  selectedFence.value = fence
  // 在地图上高亮显示
  highlightFenceOnMap(fence)
}

// 高亮显示围栏
const highlightFenceOnMap = (fence: ElectronicFence) => {
  // 此处实现依赖于地图库，例如高德地图、百度地图等
  if (map.value && window.AMap) {
    // 使用类型断言处理AMap的API
    const AMapWithPlugins = window.AMap as any;
    
    // 清除之前的高亮
    polygons.value.forEach(polygon => polygon.setOptions({
      strokeColor: '#000',
      strokeWeight: 1,
      fillColor: '#000',
      fillOpacity: 0
    }))
    
    // 创建新的围栏多边形
    const path = fence.coordinates.map(coord => new AMapWithPlugins.LngLat(coord.lng, coord.lat));
    
    const polygon = new AMapWithPlugins.Polygon({
      path,
      strokeColor: fence.color,
      strokeWeight: 3,
      fillColor: fence.color,
      fillOpacity: 0.3
    })
    
    // 添加到图层
    map.value.add(polygon);
    polygons.value.push(polygon)
    
    // 调整视图以包含整个围栏
    map.value.setFitView([polygon])
  }
}

// 显示围栏表单
const showFenceForm = (fence: ElectronicFence | null) => {
  isEditMode.value = !!fence
  
  if (fence) {
    Object.assign(fenceForm, fence)
    if (fence.startDate && fence.endDate) {
      dateRange.value = [fence.startDate, fence.endDate]
    } else {
      dateRange.value = null
    }
  } else {
    // 重置表单
    Object.assign(fenceForm, {
      id: '',
      name: '',
      description: '',
      fenceType: 'no_spray' as 'no_entry' | 'no_spray' | 'limited_spray',
      color: '#ff0000',
      coordinates: [],
      isActive: true,
      startDate: '',
      endDate: '',
      createdBy: 'admin'
    })
    dateRange.value = null
    
    // 检查地图是否已加载
    if (!map.value || !window.AMap) {
      ElMessage.warning('地图尚未加载完成，请稍后再试');
      showDialog.value = true;
      return;
    }
    
    // 启用绘图模式 - 使用startDrawFence替代
    nextTick(() => {
      // 先打开对话框，让用户填写基本信息
      showDialog.value = true;
    });
  }
  
  showDialog.value = true
}

// 开始绘制围栏
const startDrawFence = () => {
  if (!map.value || !window.AMap) {
    console.warn('地图未初始化，无法开始绘制');
    ElMessage.warning('地图尚未加载完成，请稍后再试');
    return;
  }
  
  try {
    // 使用类型断言处理AMap的API
    const AMapWithPlugins = window.AMap as any;
    
    // 创建绘图工具
    const drawTool = new AMapWithPlugins.MouseTool(map.value);
    
    // 开始绘制多边形
    drawTool.polygon({
      strokeColor: '#3498db',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      fillColor: '#3498db',
      fillOpacity: 0.3
    });
    
    // 直接使用on方法绑定事件，避免使用AMap.event
    drawTool.on('draw', function(e: any) {
      const { obj } = e;
      if (obj) {
        // 获取绘制的多边形路径
        const path = obj.getPath();
        const coordinates = path.map((point: any) => ({
          lng: point.getLng(),
          lat: point.getLat()
        }));
        
        // 设置当前围栏的坐标
        fenceForm.coordinates = coordinates;
        
        // 关闭绘图工具
        drawTool.close();
        
        // 重新打开对话框
        nextTick(() => {
          showDialog.value = true;
        });
      }
    });
    
    console.log('已启动围栏绘制工具，请在地图上绘制围栏');
  } catch (error) {
    console.error('启动围栏绘制工具失败:', error);
    ElMessage.error('绘制工具加载失败，请刷新页面重试');
    
    // 发生错误时，重新打开对话框
    nextTick(() => {
      showDialog.value = true;
    });
  }
};

// 保存围栏 - 本地模拟实现
const saveFence = async () => {
  if (!fenceFormRef.value) return
  
  await fenceFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    
    // 设置日期范围
    if (dateRange.value) {
      fenceForm.startDate = dateRange.value[0]
      fenceForm.endDate = dateRange.value[1]
    } else {
      fenceForm.startDate = ''
      fenceForm.endDate = ''
    }
    
    // 如果是新建围栏，检查是否已绘制区域
    if (!isEditMode.value && fenceForm.coordinates.length === 0) {
      ElMessage.warning('请先在地图上绘制围栏区域')
      return
    }
    
    saving.value = true
    try {
      if (isEditMode.value) {
        // 编辑现有围栏 - 本地模拟更新
        const updateData = {
          name: fenceForm.name,
          description: fenceForm.description,
          fenceType: fenceForm.fenceType,
          color: fenceForm.color,
          coordinates: fenceForm.coordinates,
          isActive: fenceForm.isActive,
          startDate: fenceForm.startDate,
          endDate: fenceForm.endDate
        }
        
        // 在本地数据中查找并更新
        const index = fences.value.findIndex(f => f.id === fenceForm.id);
        if (index !== -1) {
          fences.value[index] = {
            ...fences.value[index],
            ...updateData,
            updatedAt: new Date().toISOString()
          };
        }
        
        ElMessage.success('围栏已更新')
      } else {
        // 创建新围栏 - 本地模拟创建
        const newFence: ElectronicFence = {
          id: 'new_' + Date.now(),
          name: fenceForm.name,
          description: fenceForm.description,
          fenceType: fenceForm.fenceType,
          color: fenceForm.color,
          coordinates: fenceForm.coordinates,
          isActive: fenceForm.isActive,
          startDate: fenceForm.startDate,
          endDate: fenceForm.endDate,
          createdBy: fenceForm.createdBy,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        
        // 添加到本地数据
        fences.value.push(newFence);
        
        ElMessage.success('围栏已创建')
      }
      
      // 关闭对话框
      showDialog.value = false
      
      // 更新地图上的围栏显示
      nextTick(() => {
        drawFencesOnMap();
      });
    } catch (error) {
      console.error('保存围栏失败:', error)
      ElMessage.error('保存围栏失败')
    } finally {
      saving.value = false
    }
  })
}

// 更新围栏状态 - 本地模拟实现
const updateFenceStatus = async () => {
  if (!selectedFence.value) return
  
  try {
    // 模拟更新围栏状态
    const index = fences.value.findIndex(f => f.id === selectedFence.value?.id);
    if (index !== -1) {
      fences.value[index].isActive = selectedFence.value.isActive;
      fences.value[index].updatedAt = new Date().toISOString();
    }
    
    ElMessage.success(`围栏已${selectedFence.value.isActive ? '启用' : '禁用'}`)
    
    // 更新地图上的围栏显示
    nextTick(() => {
      drawFencesOnMap();
    });
  } catch (error) {
    console.error('更新围栏状态失败:', error)
    ElMessage.error('更新围栏状态失败')
    // 恢复状态
    if (selectedFence.value) {
      selectedFence.value.isActive = !selectedFence.value.isActive
    }
  }
}

// 确认删除
const confirmDelete = () => {
  showDeleteDialog.value = true
}

// 删除围栏 - 本地模拟实现
const deleteFence = async () => {
  if (!selectedFence.value) return
  
  deleting.value = true
  try {
    // 模拟删除围栏
    const index = fences.value.findIndex(f => f.id === selectedFence.value?.id);
    if (index !== -1) {
      fences.value.splice(index, 1);
    }
    
    ElMessage.success('围栏已删除')
    selectedFence.value = null
    showDeleteDialog.value = false
    
    // 更新地图上的围栏显示
    nextTick(() => {
      drawFencesOnMap();
    });
  } catch (error) {
    console.error('删除围栏失败:', error)
    ElMessage.error('删除围栏失败')
  } finally {
    deleting.value = false
  }
}

// 初始化地图
const initMap = async () => {
  if (!mapContainer.value) {
    console.warn('地图容器不存在，无法初始化地图');
    mapError.value = '地图容器不存在，请检查DOM元素';
    return;
  }
  
  if (!window.AMap) {
    console.warn('AMap API尚未加载，无法初始化地图');
    mapError.value = '地图API未成功加载，请检查网络连接或API密钥配置';
    return;
  }
  
  try {
    console.log('正在初始化地图...');
    // 创建地图实例
    const container = mapContainer.value as HTMLElement;
    map.value = new window.AMap.Map(container, {
      zoom: 13,
      center: [116.397428, 39.90923],
      resizeEnable: true
    });
    
    // 添加控件 - 使用插件方式添加
    try {
      // 使用类型断言处理AMap的plugin方法
      const AMapWithPlugins = window.AMap as any;
      
      // 检查是否有插件管理器
      if (AMapWithPlugins.plugin) {
        AMapWithPlugins.plugin(['AMap.Scale', 'AMap.ToolBar'], function() {
          // 添加比例尺
          if (AMapWithPlugins.Scale) {
            const scale = new AMapWithPlugins.Scale();
            map.value.addControl(scale);
          }
          
          // 添加工具条
          if (AMapWithPlugins.ToolBar) {
            const toolBar = new AMapWithPlugins.ToolBar();
            map.value.addControl(toolBar);
          }
        });
      } else {
        console.warn('AMap插件管理器不可用，无法加载控件');
      }
    } catch (controlError) {
      console.warn('添加地图控件失败:', controlError);
      // 控件加载失败不影响地图基本功能，继续执行
    }
    
    // 加载围栏数据
    await drawFencesOnMap();
    
    // 初始化完成
    console.log('地图初始化完成');
    // 清除可能存在的错误
    mapError.value = null;
  } catch (error) {
    console.error('初始化地图时出错:', error);
    mapError.value = '地图初始化失败，请刷新页面重试';
  }
};

// 渲染所有围栏到地图
const drawFencesOnMap = async () => {
  if (!map.value || !window.AMap) {
    console.warn('地图或AMap API尚未加载完成，无法绘制围栏');
    return;
  }
  
  // 使用类型断言处理AMap的API
  const AMapWithPlugins = window.AMap as any;
  
  // 清除旧的围栏
  if (polygons.value.length) {
    polygons.value.forEach(polygon => {
      if (polygon && map.value) {
        map.value.remove(polygon);
      }
    });
    polygons.value = [];
  }
  
  if (!Array.isArray(fences.value) || fences.value.length === 0) {
    console.log('没有围栏数据可绘制');
    return;
  }

  // 存储所有围栏的边界点，用于最后调整视图
  const allPoints: any[] = [];
  
  try {
    // 绘制新的围栏
    fences.value.forEach((fence: ElectronicFence) => {
      if (!fence.coordinates || !Array.isArray(fence.coordinates) || fence.coordinates.length < 3) {
        console.warn(`围栏 [ID: ${fence.id}] 坐标数据无效:`, fence.coordinates);
        return;
      }
      
      try {
        // 创建AMap中的坐标点数组
        const path = fence.coordinates.map((coord: {lng: number, lat: number}) => {
          const point = new AMapWithPlugins.LngLat(coord.lng, coord.lat);
          allPoints.push(point);
          return point;
        });
        
        // 创建多边形
        const polygon = new AMapWithPlugins.Polygon({
          path,
          strokeColor: fence.color,
          strokeWeight: 3,
          strokeOpacity: 0.8,
          fillColor: fence.color,
          fillOpacity: 0.3,
          cursor: 'pointer',
          extData: { fenceId: fence.id }
        });
        
        // 添加多边形到地图
        map.value.add(polygon);
        
        // 添加点击事件
        polygon.on('click', () => {
          console.log(`围栏 "${fence.name}" 被点击`);
          selectedFence.value = fence;
          showDialog.value = true;
        });
        
        polygons.value.push(polygon);
      } catch (error) {
        console.error(`绘制围栏 [ID: ${fence.id}] 时出错:`, error);
      }
    });
    
    // 调整地图视图以适应所有围栏
    if (allPoints.length > 0 && map.value) {
      try {
        map.value.setFitView(null, false, [60, 60, 60, 60]);
      } catch (error) {
        console.error('调整地图视图时出错:', error);
      }
    }
  } catch (error) {
    console.error('绘制围栏时出错:', error);
  }
};

// 监听围栏数据变化，更新地图
watch(fences, () => {
  if (map.value && window.AMap) {
    drawFencesOnMap();
  }
}, { deep: true });

// 监听地图初始化完成
watch(() => map.value, (newMap) => {
  if (newMap && window.AMap) {
    drawFencesOnMap();
  }
});

// 组件挂载时
onMounted(async () => {
  await fetchFences();
  
  // 先加载地图API，再初始化地图
  try {
    mapLoading.value = true;
    mapError.value = null; // 清除可能存在的错误
    console.log('正在加载高德地图API...');
    await loadAMapAPI();
    console.log('高德地图API加载完成');
    await initMap();
  } catch (error) {
    console.error('加载地图API失败:', error);
    mapError.value = '加载地图API失败，请检查网络连接或API密钥配置';
  } finally {
    mapLoading.value = false;
  }
});

// 处理在地图上绘制按钮点击
const handleDrawFence = () => {
  // 检查地图是否已加载
  if (!map.value || !window.AMap) {
    ElMessage.warning('地图尚未加载完成，请稍后再试');
    return;
  }
  
  // 关闭对话框
  showDialog.value = false;
  
  // 在下一个渲染周期启动绘制
  nextTick(() => {
    try {
      startDrawFence();
      
      // 提示用户
      ElMessage.info('请在地图上绘制围栏区域，点击确定完成绘制');
      
      // 设置一个延时器，在用户完成绘制后重新打开对话框（作为备用方案）
      setTimeout(() => {
        if (!showDialog.value) {
          console.log('绘制超时，自动重新打开对话框');
          showDialog.value = true;
        }
      }, 30000); // 30秒后自动重新打开对话框
    } catch (error) {
      console.error('启动绘制工具失败:', error);
      ElMessage.error('无法启动绘制工具，请刷新页面重试');
      showDialog.value = true; // 出错时重新打开对话框
    }
  });
};
</script>

<style scoped>
.fence-management {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 主内容区 */
.main-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

/* 地图与详情容器 */
.map-detail-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 地图容器 */
.map-wrapper {
  position: relative;
  width: 100%;
  height: 400px;
}

.map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(31, 41, 55, 0.7);
  color: #e5e7eb;
  z-index: 10;
  border-radius: 8px;
}

.map-loading .loading-icon {
  font-size: 32px;
  margin-bottom: 10px;
  animation: rotate 1.5s linear infinite;
}

.map-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(31, 41, 55, 0.7);
  color: #e5e7eb;
  z-index: 10;
  border-radius: 8px;
}

.map-error .loading-icon {
  font-size: 32px;
  margin-bottom: 10px;
  animation: rotate 1.5s linear infinite;
}

/* 围栏列表样式 */
.list-content {
  height: 100%;
  overflow-y: auto;
}

.search-input {
  width: 180px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;
}

.loading-icon {
  font-size: 32px;
  animation: rotate 1.5s linear infinite;
}

.fence-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.fence-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s;
}

.fence-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.fence-item.active {
  background-color: rgba(59, 130, 246, 0.2);
  border: 1px solid #3b82f6;
}

.fence-item-color {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  flex-shrink: 0;
}

.fence-item-info {
  flex: 1;
}

.fence-item-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  margin-bottom: 4px;
}

.fence-item-type {
  font-size: 12px;
  color: #9ca3af;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
  margin-left: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>

<!--
注意: 此组件需要高德地图SDK支持，请确保在index.html中引入了高德地图API:
<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=您的高德地图密钥&plugin=AMap.MouseTool,AMap.PolygonEditor"></script>

如果没有引入，请先引入高德地图SDK。
--> 