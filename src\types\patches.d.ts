// 临时类型补丁文件，用于修复或扩展项目中的类型定义问题

import * as echarts from 'echarts';
import { VNodeRef } from 'vue';

// Element Plus 图标声明
declare module '@element-plus/icons-vue' {
  export const Humidity: any;
  export const Wind: any;
  export const WaterMeter: any;
  export const WindPower: any;
}

// 为 echarts 提供更完整的类型支持
declare module 'echarts' {
  export function registerMap(
    mapName: string,
    geoJson: any,
    specialAreas?: Record<string, any>
  ): void;
}

// 扩展 PesticideLedger.vue 中使用的类型
declare global {
  interface Pesticide {
    id: string;
    name: string;
    registrationNumber: string;
    activeIngredients: string;
    formulation: string;
    manufacturer: string;
    approvalDate: string;
    validUntil: string;
    toxicityLevel: string;
    // 添加缺失的属性
    purchaseQuantity?: number;
    usageQuantity?: number;
    stockQuantity?: number;
    lowStockWarning?: number;
    [key: string]: any; // 允许任意额外属性
  }
}

// 为 Vue 的 VNodeRef 类型添加兼容性
declare module '@vue/runtime-dom' {
  interface HTMLVideoElementRef extends VNodeRef {
    (el: HTMLVideoElement | null): void;
    value?: HTMLVideoElement | null;
  }
  
  interface VideoHTMLAttributes {
    ref?: HTMLVideoElementRef | string | ((el: HTMLVideoElement | null) => void);
  }
}

// 为 ThreeDMap.vue 中的 Alert 类型
declare global {
  interface Alert {
    level: string;
    title: string;
    description: string;
    time: string;
  }
}

// 将来自组件的类型声明为任意类型，用于临时修复
declare module 'vue' {
  interface ComponentCustomProperties {
    $refs: {
      [key: string]: any;
    };
  }
}

export {}; 