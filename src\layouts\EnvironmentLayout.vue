<template>
  <BaseLayout
    title="环境智能感知系统"
    theme="environment"
    themeColor="#10b981"
    moduleIcon="Odometer"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="微气候监测矩阵"
        icon="DataLine"
        route="/environment/microclimate"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="土壤墒情监测图层"
        icon="MapLocation"
        route="/environment/soil-moisture"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="作物生长状态评估"
        icon="PictureRounded"
        route="/environment/crop-assessment"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="环境-虫害关联分析"
        icon="Connection"
        route="/environment/pest-analysis"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="智能通风/灌溉联动"
        icon="SetUp"
        route="/environment/smart-control"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
    </template>

    <template #header-actions>
      <div class="environmental-indicators">
        <div class="indicator-item">
          <el-icon><Sunrise /></el-icon>
          <span class="indicator-label">光照: </span>
          <span class="indicator-value">78,500 lx</span>
        </div>
        <div class="indicator-item">
          <el-icon><Cloudy /></el-icon>
          <span class="indicator-label">湿度: </span>
          <span class="indicator-value">68%</span>
        </div>
        <div class="indicator-item">
          <el-icon><Sunny /></el-icon>
          <span class="indicator-label">温度: </span>
          <span class="indicator-value">26.4°C</span>
        </div>
        <div class="indicator-item">
          <el-icon><WindPower /></el-icon>
          <span class="indicator-label">风速: </span>
          <span class="indicator-value">3.2m/s</span>
        </div>
      </div>

      <TechActionButton
        type="primary"
        size="small"
        icon="RefreshRight"
        text="更新数据"
        @click="refreshData"
      />
    </template>

    <router-view />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  DataLine,
  MapLocation,
  PictureRounded,
  Connection,
  SetUp,
  HomeFilled,
  Odometer,
  RefreshRight,
  Sunrise,
  Cloudy,
  Sunny,
  WindPower
} from '@element-plus/icons-vue'
import { ElMessage, ElNotification } from 'element-plus'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'
import { getModuleTheme } from './components/layoutConfig'

const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 刷新环境数据
const refreshData = () => {
  ElMessage({
    message: '环境监测数据已更新',
    type: 'success',
    duration: 3000
  })
}

// 组件挂载
onMounted(() => {
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<style scoped>
.environmental-indicators {
  display: flex;
  gap: 20px;
  margin-right: 20px;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(16, 185, 129, 0.1);
  padding: 5px 10px;
  border-radius: 20px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  transition: all 0.3s ease;
}

.indicator-item:hover {
  background: rgba(16, 185, 129, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.2);
}

.indicator-label {
  color: #d1d5db;
  font-size: 14px;
}

.indicator-value {
  color: #ffffff;
  font-weight: 500;
}

/* 响应式样式 */
@media (max-width: 1024px) {
  .environmental-indicators {
    gap: 10px;
  }

  .indicator-item {
    padding: 3px 8px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .environmental-indicators {
    display: none;
  }
}
</style>
