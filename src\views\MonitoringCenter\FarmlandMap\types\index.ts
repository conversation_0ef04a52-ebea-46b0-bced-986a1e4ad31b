/**
 * 农田地图组件类型定义
 */

// 设备状态类型
export type DeviceStatus = 'online' | 'standby' | 'offline';

// 设备信息接口
export interface DeviceInfo {
  id: string;
  name: string;
  type: 'dog' | 'drone';
  position: {
    x: number;
    y: number;
  };
  status: DeviceStatus;
  // 设备详情面板所需的扩展属性
  location?: string;
  installDate?: string;
  battery?: number;
  data?: Record<string, string>;
}

// 农田区域接口
export interface FarmlandArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * 警报信息类型
 */
export interface AlertInfo {
  id: string;
  level: 'high' | 'medium' | 'low';
  title: string;
  message: string;
  deviceId: string;
  timestamp: number;
  resolved: boolean;
}

// 环境数据接口
export interface EnvironmentData {
  temperature: string;
  humidity: string;
  windSpeed: string;
  lightIntensity: string;
}

// 土壤数据接口
export interface SoilData {
  moisture: string;
  nutrition: string;
  pH: string;
}

// 设备统计数据接口
export interface DeviceStats {
  onlineCount: number;
  standbyCount: number;
  offlineCount: number;
  droneFlyingHours: string;
  dogPatrolHours: string;
  monitorWorkDays: string;
  dataCollectionSize: string;
}

// 地图状态接口
export interface MapState {
  scale: number;
  offsetX: number;
  offsetY: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

// 轨迹点接口
export interface TrajectoryPoint {
  x: number;
  y: number;
}

// 面板位置类型
export type PanelPosition = 'left' | 'right';

// 面板属性接口
export interface PanelProps {
  title: string;
  position: PanelPosition;
  show: boolean;
} 