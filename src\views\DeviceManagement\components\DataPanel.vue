<!-- 
  DataPanel.vue
  数据面板组件
  用于展示各类数据的容器面板
-->
<template>
  <div class="data-panel">
    <div class="panel-header">
      <h3 class="panel-title">{{ title }}</h3>
      <div class="panel-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
    <div class="panel-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  /** 面板标题 */
  title: {
    type: String,
    required: true
  }
});
</script>

<style scoped>
.data-panel {
  height: 100%;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: rgba(59, 130, 246, 0.1);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.panel-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #e5e7eb;
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}
</style> 