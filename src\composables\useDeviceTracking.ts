/**
 * 设备追踪数据管理 Composable Hook
 * 处理机器狗设备状态、轨迹记录、WebSocket连接等
 */

import { ref, reactive, computed, onMounted, onUnmounted, readonly, nextTick } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import {
  WebSocketService,
  WebSocketStatus,
  type RobotLocationData,
  type AnchorData
} from '@/utils/websocketService';
import type { Point } from '@/utils/coordinateTransform';
import {
  TrajectorySmoothing,
  createTrajectorySmoothing,
  type TrajectoryPoint,
  type SmoothingConfig
} from '@/utils/trajectorySmoothing';

// 机器狗设备信息接口
export interface RobotDevice {
  id: string;
  name: string;
  tagId: number;
  status: 'active' | 'idle' | 'warning' | 'error' | 'offline';
  battery: number;
  position: Point;
  speed: number;
  runTime: string;
  task?: string;
  lastUpdateTime: Date;
}

// 轨迹点接口
export interface TrackPoint extends Point {
  timestamp: number;
  speed?: number;
}

import { DEVICE_TRACKING_CONFIG } from '@/config/business';

// WebSocket配置接口
export interface WebSocketConfig {
  url: string;
  autoConnect?: boolean;
  maxTrackPoints?: number;
}

/**
 * 设备追踪数据管理Hook
 */
export function useDeviceTracking(config: WebSocketConfig) {
  // WebSocket服务实例
  const wsService = ref<WebSocketService | null>(null);

  // 连接状态
  const connectionStatus = ref<WebSocketStatus>(WebSocketStatus.DISCONNECTED);
  const isConnected = computed(() => connectionStatus.value === WebSocketStatus.CONNECTED);

  // 基站数据
  const anchors = ref<AnchorData[]>([]);

  // 机器狗设备数据
  const robotDevices = reactive<Map<number, RobotDevice>>(new Map());

  // 当前活跃的机器狗
  const activeRobotId = ref<number | null>(null);
  const activeRobot = computed(() => {
    return activeRobotId.value !== null ? robotDevices.get(activeRobotId.value) || null : null;
  });

  // 轨迹数据
  const robotTracks = reactive<Map<number, TrackPoint[]>>(new Map());
  const activeRobotTrack = computed(() => {
    return activeRobotId.value !== null ? robotTracks.get(activeRobotId.value) || [] : [];
  });

  // 轨迹平滑相关状态
  const trajectorySmoothing = reactive<Map<number, TrajectorySmoothing>>(new Map());
  const smoothingEnabled = ref<boolean>(DEVICE_TRACKING_CONFIG.TRAJECTORY_SMOOTHING.ENABLED);
  const smoothingConfig = ref<SmoothingConfig>({
    movingAverageWindow: DEVICE_TRACKING_CONFIG.TRAJECTORY_SMOOTHING.MOVING_AVERAGE_WINDOW,
    bezierWeight: DEVICE_TRACKING_CONFIG.TRAJECTORY_SMOOTHING.BEZIER_WEIGHT,
    outlierThreshold: DEVICE_TRACKING_CONFIG.TRAJECTORY_SMOOTHING.OUTLIER_THRESHOLD,
    minDistanceThreshold: DEVICE_TRACKING_CONFIG.TRAJECTORY_SMOOTHING.MIN_DISTANCE_THRESHOLD,
    adaptiveWindow: DEVICE_TRACKING_CONFIG.TRAJECTORY_SMOOTHING.ADAPTIVE_WINDOW,
    smoothingStrength: DEVICE_TRACKING_CONFIG.TRAJECTORY_SMOOTHING.SMOOTHING_STRENGTH
  });

  // 平滑后的轨迹数据
  const robotSmoothedTracks = reactive<Map<number, TrajectoryPoint[]>>(new Map());
  const activeRobotSmoothedTrack = computed(() => {
    if (!smoothingEnabled.value || activeRobotId.value === null) {
      return activeRobotTrack.value;
    }
    return robotSmoothedTracks.get(activeRobotId.value) || [];
  });

  // 最新位置数据
  const latestLocationData = ref<RobotLocationData | null>(null);

  // 统计数据
  const onlineDevicesCount = computed(() => {
    return Array.from(robotDevices.values()).filter(device => device.status !== 'offline').length;
  });

  const movingDevicesCount = computed(() => {
    return Array.from(robotDevices.values()).filter(device => device.speed > 0).length;
  });

  // 配置参数
  const maxTrackPoints = config.maxTrackPoints || 100;

  /**
   * 初始化WebSocket连接
   */
  const initWebSocket = () => {
    if (wsService.value) {
      wsService.value.disconnect();
    }

    wsService.value = new WebSocketService(config.url);

    // 设置回调函数
    wsService.value.setLocationDataCallback(handleLocationData);
    wsService.value.setStatusChangeCallback(handleStatusChange);
    wsService.value.setErrorCallback(handleError);
  };

  /**
   * 连接WebSocket
   */
  const connect = async (): Promise<void> => {
    if (!wsService.value) {
      initWebSocket();
    }

    try {
      await wsService.value!.connect();
      ElMessage({
        message: 'WebSocket连接已建立',
        type: 'success'
      });
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      ElMessage({
        message: 'WebSocket连接失败，请检查网络连接',
        type: 'error'
      });
      throw error;
    }
  };

  /**
   * 断开WebSocket连接
   */
  const disconnect = () => {
    if (wsService.value) {
      wsService.value.disconnect();
    }
  };

  /**
   * 处理位置数据 - 优化为无延迟实时处理
   */
  const handleLocationData = (data: RobotLocationData) => {
    // 立即更新最新位置数据
    latestLocationData.value = data;

    // 立即更新基站数据（无延迟）
    if (data.anchors && data.anchors.length === 4) {
      anchors.value = [...data.anchors];
    }

    // 立即更新设备信息
    updateRobotDevice(data);

    // 立即添加轨迹点
    addTrackPoint(data);

    // 立即触发响应式更新
    nextTick(() => {
      // 确保所有响应式数据已更新
    });
  };

  /**
   * 更新机器狗设备信息 - 优化为实时无延迟更新，包含坐标四舍五入
   */
  const updateRobotDevice = (data: RobotLocationData) => {
    const existingDevice = robotDevices.get(data.tagId);
    const currentTime = new Date();

    // 确保坐标数据已经四舍五入（WebSocket层已处理，这里作为双重保险）
    const roundedX = Math.round(data.x * 100) / 100;
    const roundedY = Math.round(data.y * 100) / 100;

    // 实时计算速度（如果有历史位置数据）
    let speed = 0;
    if (existingDevice) {
      const timeDiff = (currentTime.getTime() - existingDevice.lastUpdateTime.getTime()) / 1000;
      if (timeDiff > 0.01) { // 降低最小时间差阈值，提高灵敏度
        const distance = Math.sqrt(
          Math.pow(roundedX - existingDevice.position.x, 2) +
          Math.pow(roundedY - existingDevice.position.y, 2)
        );
        speed = distance / timeDiff;
      }
    }

    // 实时确定设备状态
    let status: RobotDevice['status'] = 'active';
    if (speed < 0.05) { // 降低阈值，更敏感的状态检测
      status = 'idle';
    } else if (speed > 1.5) {
      status = 'active';
    }

    // 立即创建设备对象
    const device: RobotDevice = {
      id: `robot-${data.tagId}`,
      name: `农田机器狗-${data.tagId.toString().padStart(3, '0')}`,
      tagId: data.tagId,
      status,
      battery: existingDevice?.battery || 85,
      position: { x: roundedX, y: roundedY },
      speed: Math.round(speed * 1000) / 1000, // 保留三位小数，更精确
      runTime: existingDevice?.runTime || '0小时0分钟',
      task: existingDevice?.task || '实时巡检',
      lastUpdateTime: currentTime
    };

    // 立即更新设备映射
    robotDevices.set(data.tagId, device);

    // 立即设置活跃设备
    if (activeRobotId.value === null || !robotDevices.has(activeRobotId.value)) {
      activeRobotId.value = data.tagId;
    }
  };

  /**
   * 获取或创建轨迹平滑处理器
   */
  const getTrajectorySmoothing = (tagId: number) => {
    if (!trajectorySmoothing.has(tagId)) {
      const smoother = createTrajectorySmoothing(smoothingConfig.value);
      trajectorySmoothing.set(tagId, smoother);
    }
    return trajectorySmoothing.get(tagId)!;
  };

  /**
   * 添加轨迹点 - 优化为实时无延迟添加，集成平滑处理，包含坐标四舍五入
   */
  const addTrackPoint = (data: RobotLocationData) => {
    const trackPoints = robotTracks.get(data.tagId) || [];

    // 确保坐标数据已经四舍五入（WebSocket层已处理，这里作为双重保险）
    const roundedX = Math.round(data.x * 100) / 100;
    const roundedY = Math.round(data.y * 100) / 100;

    const newPoint: TrackPoint = {
      x: roundedX,
      y: roundedY,
      timestamp: data.timestamp,
      speed: robotDevices.get(data.tagId)?.speed
    };

    // 优化距离检查 - 降低阈值，允许更精细的轨迹记录
    if (trackPoints.length > 0) {
      const lastPoint = trackPoints[trackPoints.length - 1];
      const distance = Math.sqrt(
        Math.pow(newPoint.x - lastPoint.x, 2) +
        Math.pow(newPoint.y - lastPoint.y, 2)
      );

      // 使用配置中的最小距离阈值
      const threshold = smoothingEnabled.value ?
        smoothingConfig.value.minDistanceThreshold : 0.001;

      if (distance < threshold) {
        return;
      }
    }

    // 立即添加轨迹点
    trackPoints.push(newPoint);

    // 高效的轨迹点数量限制
    if (trackPoints.length > maxTrackPoints) {
      trackPoints.shift(); // 使用shift()比splice()更高效
    }

    // 立即更新轨迹映射
    robotTracks.set(data.tagId, trackPoints);

    // 如果启用了轨迹平滑，进行平滑处理
    if (smoothingEnabled.value) {
      const smoother = getTrajectorySmoothing(data.tagId);
      const trajectoryPoint: TrajectoryPoint = {
        x: roundedX,
        y: roundedY,
        timestamp: data.timestamp,
        speed: robotDevices.get(data.tagId)?.speed
      };

      const smoothedPoints = smoother.addPoint(trajectoryPoint);
      robotSmoothedTracks.set(data.tagId, smoothedPoints);
    }
  };

  /**
   * 处理连接状态变化
   */
  const handleStatusChange = (status: WebSocketStatus) => {
    connectionStatus.value = status;

    switch (status) {
      case WebSocketStatus.CONNECTED:
        console.log('WebSocket已连接');
        break;
      case WebSocketStatus.DISCONNECTED:
        console.log('WebSocket已断开');
        // 将所有设备标记为离线
        robotDevices.forEach(device => {
          device.status = 'offline';
        });
        break;
      case WebSocketStatus.RECONNECTING:
        ElMessage({
          message: 'WebSocket连接断开，正在尝试重连...',
          type: 'warning'
        });
        break;
      case WebSocketStatus.ERROR:
        console.error('WebSocket连接错误');
        break;
    }
  };

  /**
   * 处理错误
   */
  const handleError = (error: Error) => {
    console.error('WebSocket错误:', error);
    ElMessage({
      message: error.message,
      type: 'error'
    });
  };

  /**
   * 设置活跃机器狗
   */
  const setActiveRobot = (tagId: number) => {
    if (robotDevices.has(tagId)) {
      activeRobotId.value = tagId;
    }
  };

  /**
   * 清除指定机器狗的轨迹
   */
  const clearRobotTrack = (tagId: number) => {
    robotTracks.set(tagId, []);
  };

  /**
   * 清除所有轨迹
   */
  const clearAllTracks = () => {
    robotTracks.clear();
  };

  /**
   * 获取设备列表
   */
  const getDeviceList = () => {
    return Array.from(robotDevices.values());
  };

  /**
   * 获取指定设备的轨迹
   */
  const getRobotTrack = (tagId: number): Point[] => {
    const trackPoints = robotTracks.get(tagId) || [];
    return trackPoints.map(point => ({ x: point.x, y: point.y }));
  };

  /**
   * 获取指定设备的平滑轨迹
   */
  const getRobotSmoothedTrack = (tagId: number): Point[] => {
    if (!smoothingEnabled.value) {
      return getRobotTrack(tagId);
    }
    const smoothedPoints = robotSmoothedTracks.get(tagId) || [];
    return smoothedPoints.map(point => ({ x: point.x, y: point.y }));
  };

  /**
   * 切换轨迹平滑功能
   */
  const toggleTrajectorySmoothing = (enabled: boolean) => {
    smoothingEnabled.value = enabled;

    // 保存设置到本地存储
    localStorage.setItem('trajectory_smoothing_enabled', enabled.toString());

    // 如果禁用平滑，清除平滑数据
    if (!enabled) {
      robotSmoothedTracks.clear();
      trajectorySmoothing.clear();
    } else {
      // 如果启用平滑，重新处理现有轨迹数据
      robotTracks.forEach((trackPoints, tagId) => {
        const smoother = getTrajectorySmoothing(tagId);
        smoother.clear();

        // 重新添加所有轨迹点进行平滑处理
        trackPoints.forEach(point => {
          const trajectoryPoint: TrajectoryPoint = {
            x: point.x,
            y: point.y,
            timestamp: point.timestamp,
            speed: point.speed
          };
          smoother.addPoint(trajectoryPoint);
        });

        robotSmoothedTracks.set(tagId, smoother.getSmoothedPoints());
      });
    }
  };

  /**
   * 更新平滑配置
   */
  const updateSmoothingConfig = (newConfig: Partial<SmoothingConfig>) => {
    smoothingConfig.value = { ...smoothingConfig.value, ...newConfig };

    // 保存配置到本地存储
    localStorage.setItem('trajectory_smoothing_config', JSON.stringify(smoothingConfig.value));

    // 更新所有平滑处理器的配置
    trajectorySmoothing.forEach(smoother => {
      smoother.updateConfig(smoothingConfig.value);
    });

    // 更新平滑轨迹数据
    trajectorySmoothing.forEach((smoother, tagId) => {
      robotSmoothedTracks.set(tagId, smoother.getSmoothedPoints());
    });
  };

  /**
   * 模拟电池电量变化（用于演示）
   */
  const simulateBatteryDrain = () => {
    robotDevices.forEach(device => {
      if (device.status === 'active') {
        device.battery = Math.max(0, device.battery - 0.1);
        if (device.battery < 20) {
          device.status = 'warning';
        }
      }
    });
  };

  // 初始化本地存储的设置
  const initializeSettings = () => {
    // 从本地存储恢复平滑功能设置
    const savedSmoothingEnabled = localStorage.getItem('trajectory_smoothing_enabled');
    if (savedSmoothingEnabled !== null) {
      smoothingEnabled.value = savedSmoothingEnabled === 'true';
    }

    // 从本地存储恢复平滑配置
    const savedSmoothingConfig = localStorage.getItem('trajectory_smoothing_config');
    if (savedSmoothingConfig) {
      try {
        const parsedConfig = JSON.parse(savedSmoothingConfig);
        smoothingConfig.value = { ...smoothingConfig.value, ...parsedConfig };
      } catch (error) {
        console.warn('无法解析保存的平滑配置:', error);
      }
    }
  };

  // 生命周期管理
  onMounted(() => {
    // 初始化设置
    initializeSettings();

    if (config.autoConnect !== false) {
      connect().catch(console.error);
    }

    // 启动电池模拟（每30秒）
    const batteryInterval = setInterval(simulateBatteryDrain, 30000);

    onUnmounted(() => {
      clearInterval(batteryInterval);
      disconnect();
    });
  });

  onUnmounted(() => {
    disconnect();
  });

  return {
    // 连接状态
    connectionStatus: readonly(connectionStatus),
    isConnected,

    // 数据
    anchors: readonly(anchors),
    robotDevices: readonly(robotDevices),
    activeRobotId: readonly(activeRobotId),
    activeRobot,
    robotTracks: readonly(robotTracks),
    activeRobotTrack,
    latestLocationData: readonly(latestLocationData),

    // 轨迹平滑相关
    smoothingEnabled: readonly(smoothingEnabled),
    smoothingConfig: readonly(smoothingConfig),
    robotSmoothedTracks: readonly(robotSmoothedTracks),
    activeRobotSmoothedTrack,

    // 统计
    onlineDevicesCount,
    movingDevicesCount,

    // 方法
    connect,
    disconnect,
    setActiveRobot,
    clearRobotTrack,
    clearAllTracks,
    getDeviceList,
    getRobotTrack,
    getRobotSmoothedTrack,
    toggleTrajectorySmoothing,
    updateSmoothingConfig
  };
}
