<template>
  <div class="chat-sidebar">
    <div class="sidebar-header">
      <h3>会话列表</h3>
      <el-button type="success" size="small" @click="$emit('create-session')">
        <el-icon><Plus /></el-icon>
        新会话
      </el-button>
    </div>
    
    <div class="session-list">
      <div 
        v-for="session in sessions" 
        :key="session.id"
        class="session-item"
        :class="{ 'active': session.id === activeSessionId }"
        @click="$emit('select-session', session.id)"
      >
        <div class="session-info">
          <div class="session-title">
            {{ session.title }}
          </div>
          <div class="session-preview">
            {{ session.preview || '开始新的对话...' }}
          </div>
          <div class="session-date">
            {{ formatDate(session.updatedAt) }}
          </div>
        </div>
        
        <div class="session-actions">
          <el-dropdown trigger="click" @command="(cmd) => handleCommand(cmd, session.id)">
            <el-button type="text" size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="rename">重命名</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除会话</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <div v-if="sessions.length === 0" class="empty-state">
        <el-empty description="没有会话记录" />
        <el-button type="primary" @click="$emit('create-session')">
          开始对话
        </el-button>
      </div>
    </div>
    
    <div class="sidebar-footer">
      <div class="tech-info">
        <span>AI智能问答系统</span>
        <small>v1.0.0</small>
      </div>
    </div>
    
    <!-- 重命名对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="重命名会话"
      width="30%"
      :show-close="false"
      :close-on-click-modal="false"
    >
      <el-input v-model="newTitle" placeholder="请输入会话名称" />
      <template #footer>
        <span>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRename">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Plus, MoreFilled } from '@element-plus/icons-vue';
import type { ChatSession } from '@/types/chat';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const props = defineProps<{
  sessions: ChatSession[];
  activeSessionId: string;
}>();

const emit = defineEmits<{
  (e: 'select-session', sessionId: string): void;
  (e: 'create-session'): void;
  (e: 'delete-session', sessionId: string): void;
  (e: 'rename-session', sessionId: string, newTitle: string): void;
}>();

const dialogVisible = ref(false);
const newTitle = ref('');
const selectedSessionId = ref('');

// 格式化日期
const formatDate = (timestamp: number): string => {
  return formatDistanceToNow(new Date(timestamp), { addSuffix: true, locale: zhCN });
};

// 处理下拉菜单命令
const handleCommand = (command: string, sessionId: string) => {
  if (command === 'delete') {
    emit('delete-session', sessionId);
  } else if (command === 'rename') {
    const session = props.sessions.find(s => s.id === sessionId);
    if (session) {
      newTitle.value = session.title;
      selectedSessionId.value = sessionId;
      dialogVisible.value = true;
    }
  }
};

// 确认重命名
const confirmRename = () => {
  if (newTitle.value.trim() && selectedSessionId.value) {
    emit('rename-session', selectedSessionId.value, newTitle.value);
    dialogVisible.value = false;
  }
};
</script>

<style lang="scss" scoped>
.chat-sidebar {
  width: 280px;
  display: flex;
  flex-direction: column;
  background: rgba(0, 21, 65, 0.8);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(0, 255, 170, 0.15);
  position: relative;
  z-index: 5;
  
  .sidebar-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    h3 {
      margin: 0;
      font-size: 16px;
      color: #00ffaa;
    }
  }
  
  .session-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 255, 170, 0.3);
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-track {
      background-color: rgba(0, 0, 0, 0.1);
    }
    
    .session-item {
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      display: flex;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(0, 255, 170, 0.3);
      }
      
      &.active {
        background: rgba(0, 255, 170, 0.1);
        border-color: rgba(0, 255, 170, 0.5);
        box-shadow: 0 0 10px rgba(0, 255, 170, 0.2);
      }
      
      .session-info {
        flex: 1;
        overflow: hidden;
      }
      
      .session-title {
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .session-preview {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .session-date {
        font-size: 11px;
        color: rgba(255, 255, 255, 0.4);
      }
      
      .session-actions {
        display: flex;
        align-items: center;
      }
    }
    
    .empty-state {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 20px;
    }
  }
  
  .sidebar-footer {
    padding: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: center;
    
    .tech-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      span {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
      }
      
      small {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}
</style> 