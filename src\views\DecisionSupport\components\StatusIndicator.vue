<!-- 
  StatusIndicator.vue
  状态指示器组件
  用于显示各种状态的指示器
-->
<template>
  <div class="status-indicator" :class="[`type-${type}`, size]">
    <span class="indicator-dot"></span>
    <span class="indicator-label">{{ label }}</span>
  </div>
</template>

<script setup lang="ts">
defineProps({
  /** 状态类型 */
  type: {
    type: String,
    default: 'normal',
    validator: (value: string) => {
      return ['success', 'warning', 'error', 'normal', 'offline'].includes(value);
    }
  },
  /** 显示文本 */
  label: {
    type: String,
    required: true
  },
  /** 尺寸 */
  size: {
    type: String,
    default: 'default',
    validator: (value: string) => {
      return ['small', 'default', 'large'].includes(value);
    }
  }
});
</script>

<style scoped>
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 10px;
  border-radius: 16px;
  background-color: rgba(31, 41, 55, 0.7);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.indicator-label {
  font-size: 14px;
  font-weight: 500;
}

/* 尺寸 */
.status-indicator.small {
  padding: 2px 8px;
}

.status-indicator.small .indicator-dot {
  width: 6px;
  height: 6px;
}

.status-indicator.small .indicator-label {
  font-size: 12px;
}

.status-indicator.large {
  padding: 6px 12px;
}

.status-indicator.large .indicator-dot {
  width: 10px;
  height: 10px;
}

.status-indicator.large .indicator-label {
  font-size: 16px;
}

/* 类型 */
.status-indicator.type-success .indicator-dot {
  background-color: #10b981;
  box-shadow: 0 0 6px #10b981;
}

.status-indicator.type-success .indicator-label {
  color: #10b981;
}

.status-indicator.type-warning .indicator-dot {
  background-color: #f59e0b;
  box-shadow: 0 0 6px #f59e0b;
}

.status-indicator.type-warning .indicator-label {
  color: #f59e0b;
}

.status-indicator.type-error .indicator-dot {
  background-color: #ef4444;
  box-shadow: 0 0 6px #ef4444;
}

.status-indicator.type-error .indicator-label {
  color: #ef4444;
}

.status-indicator.type-normal .indicator-dot {
  background-color: #6b7280;
  box-shadow: 0 0 6px #6b7280;
}

.status-indicator.type-normal .indicator-label {
  color: #9ca3af;
}

.status-indicator.type-offline .indicator-dot {
  background-color: #4b5563;
  box-shadow: none;
}

.status-indicator.type-offline .indicator-label {
  color: #6b7280;
}
</style> 