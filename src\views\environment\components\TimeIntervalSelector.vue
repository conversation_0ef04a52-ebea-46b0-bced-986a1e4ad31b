<template>
  <div class="time-interval-selector">
    <div v-if="label" class="selector-label">{{ label }}</div>
    <div class="selector-content">
      <el-select
        v-model="selectedInterval"
        class="interval-select"
        size="small"
        @change="handleIntervalChange"
      >
        <el-option
          v-for="option in intervalOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
      
      <div v-if="showCustomOptions" class="custom-options">
        <el-input-number
          v-model="customValue"
          :min="1"
          :max="999"
          size="small"
          @change="handleCustomValueChange"
        />
        <el-select
          v-model="customUnit"
          size="small"
          @change="handleCustomUnitChange"
        >
          <el-option
            v-for="unit in customUnitOptions"
            :key="unit.value"
            :label="unit.label"
            :value="unit.value"
          />
        </el-select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface IntervalOption {
  label: string
  value: string
}

interface CustomUnitOption {
  label: string
  value: string
}

const props = defineProps({
  modelValue: {
    type: String,
    default: '1h'
  },
  label: {
    type: String,
    default: ''
  },
  predefinedIntervals: {
    type: Array as () => IntervalOption[],
    default: () => [
      { label: '最近1小时', value: '1h' },
      { label: '最近6小时', value: '6h' },
      { label: '最近12小时', value: '12h' },
      { label: '最近24小时', value: '24h' },
      { label: '最近3天', value: '3d' },
      { label: '最近7天', value: '7d' },
      { label: '最近30天', value: '30d' },
      { label: '自定义', value: 'custom' }
    ]
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedInterval = ref(props.modelValue)
const showCustomOptions = ref(false)
const customValue = ref(1)
const customUnit = ref('h')

const customUnitOptions: CustomUnitOption[] = [
  { label: '分钟', value: 'm' },
  { label: '小时', value: 'h' },
  { label: '天', value: 'd' }
]

const intervalOptions = computed(() => {
  return props.predefinedIntervals
})

// 解析初始值
watch(() => props.modelValue, (newValue) => {
  selectedInterval.value = newValue
  
  // 如果是自定义值，解析出数值和单位
  if (newValue !== 'custom' && !props.predefinedIntervals.some(opt => opt.value === newValue)) {
    const match = newValue.match(/(\d+)([mhd])/)
    if (match) {
      customValue.value = parseInt(match[1])
      customUnit.value = match[2]
      selectedInterval.value = 'custom'
      showCustomOptions.value = true
    }
  }
}, { immediate: true })

// 处理间隔变化
const handleIntervalChange = () => {
  if (selectedInterval.value === 'custom') {
    showCustomOptions.value = true
    const customInterval = `${customValue.value}${customUnit.value}`
    emit('update:modelValue', customInterval)
    emit('change', customInterval)
  } else {
    showCustomOptions.value = false
    emit('update:modelValue', selectedInterval.value)
    emit('change', selectedInterval.value)
  }
}

// 处理自定义值变化
const handleCustomValueChange = () => {
  const customInterval = `${customValue.value}${customUnit.value}`
  emit('update:modelValue', customInterval)
  emit('change', customInterval)
}

// 处理自定义单位变化
const handleCustomUnitChange = () => {
  const customInterval = `${customValue.value}${customUnit.value}`
  emit('update:modelValue', customInterval)
  emit('change', customInterval)
}
</script>

<style scoped>
.time-interval-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selector-label {
  font-size: 14px;
  color: #d1d5db;
  white-space: nowrap;
}

.selector-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.interval-select {
  width: 120px;
}

.custom-options {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-input-number) {
  width: 80px;
}

:deep(.el-select) {
  width: 80px;
}
</style> 