<template>
    <div class="home-container">
      <!-- 背景层 -->
      <div class="background-layer">
        <!-- 粒子背景 -->
        <ParticleBackground
          :particleCount="80"
          particleColor="#00ffaa"
          lineColor="#00ffaa"
          :linkDistance="150"
          :moveSpeed="1"
          particleShape="circle"
          :enableTwinkle="true"
        />

        <!-- 网格背景 -->
        <GridBackground
          gridColor="#00ffaa"
          :lineWidth="0.3"
          :gridSize="50"
          :enablePulse="true"
          :enableParallax="true"
          :parallaxStrength="20"
        />

        <!-- 扫描效果 -->
        <ScanEffect
          scanColor="#00ffaa"
          :scanWidth="2"
          :scanSpeed="2"
          direction="vertical"
          :enableGlitch="true"
          :scanOpacity="0.4"
        />
      </div>

      <!-- 顶部导航栏 -->
      <HomeHeader @settings="handleSettings" @logout="handleLogout" />

      <!-- 中间内容区 -->
      <main class="main-content">
        <!-- 状态面板 -->
        <div class="status-panel">
          <div class="status-header">
            <TypingText
              text="系统状态监测"
              :typeSpeed="50"
              :loop="false"
              :showCursor="true"
              class="status-title"
            />
            <div class="status-indicators">
              <div class="status-indicator" :class="{ 'active': systemStatus.online }">
                <span class="indicator-dot"></span>
                <span class="indicator-label">系统在线</span>
              </div>
              <div class="status-indicator" :class="{ 'active': systemStatus.dataTransmission }">
                <span class="indicator-dot"></span>
                <span class="indicator-label">数据传输</span>
              </div>
              <div class="status-indicator" :class="{ 'active': systemStatus.aiActive }">
                <span class="indicator-dot"></span>
                <span class="indicator-label">AI分析</span>
              </div>
            </div>
          </div>

          <!-- 中心植物监测 -->
          <PlantMonitor
            :monitorStatus="monitorStatus"
            plantImageUrl="/src/assets/logo.svg"
            :statusType="statusType"
          />

          <!-- 数据流 -->
          <div class="data-streams">
            <div class="data-stream">
              <div class="stream-header">环境数据流</div>
              <DataFlow
                direction="vertical"
                flowColor="#00ffaa"
                :flowSpeed="1.5"
                :flowDensity="30"
                height="120px"
              />
            </div>
            <div class="data-stream">
              <div class="stream-header">系统日志</div>
              <DataFlow
                direction="vertical"
                flowColor="#1890ff"
                :flowSpeed="2"
                :flowDensity="40"
                height="120px"
              />
            </div>
          </div>
        </div>

        <!-- 功能模块入口 -->
        <div class="modules-container">
          <div class="modules-header">
            <TypingText
              text="控制中心模块"
              :typeSpeed="50"
              :loop="false"
              :showCursor="true"
              class="modules-title"
            />
          </div>
          <div class="module-entries">
            <ModuleCard
              v-for="module in modules"
              :key="module.id"
              :module="module"
              :isActive="activeModuleId === module.id"
              @click="handleModuleClick"
            />
          </div>
        </div>
      </main>

      <!-- 底部信息 -->
      <HomeFooter :showLinks="true" />
    </div>
  </template>

  <script setup lang="ts">
  import { ref, onMounted, onUnmounted, computed, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    Bell, Setting, SwitchButton, Monitor, Cpu,
    DataAnalysis, Aim, Odometer, Lightning, Crop, Compass, Phone, ChatRound
  } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { useAuth } from '@/composables/useAuth'
  import gsap from 'gsap'

  // 导入组件
  import HomeHeader from '@/components/home/<USER>'
  import ModuleCard from '@/components/home/<USER>'
  import PlantMonitor from '@/components/home/<USER>'
  import HomeFooter from '@/components/home/<USER>'
  import ParticleBackground from '@/components/home/<USER>'
  import GridBackground from '@/components/home/<USER>'
  import ScanEffect from '@/components/home/<USER>'
  import TypingText from '@/components/home/<USER>'
  import DataFlow from '@/components/home/<USER>'
  import { TIMER_CONFIG } from '@/config/ui'

  // 定义模块接口
  interface Module {
    id: number
    title: string
    description: string
    icon: any
  }

  // 路由
  const router = useRouter()

  // 用户信息
  const userInfo = ref({
    username: '',
    avatar: '',
    role: ''
  })

  // 系统状态
  const systemStatus = reactive({
    online: true,
    dataTransmission: false,
    aiActive: false
  })

  // 监测状态
  const monitorStatus = ref('健康监测中...')
  const statusType = ref<'normal' | 'warning' | 'error' | 'success'>('normal')

  // 活跃的模块ID
  const activeModuleId = ref<number | null>(null)

  // 功能模块定义
  const modules = ref<Module[]>([
    {
      id: 1,
      title: '实时监控与预警中心',
      description: '实时监测农作物生长状况，提供病虫害预警和生长环境监控',
      icon: Monitor
    },
    {
      id: 2,
      title: '智能设备管理舱',
      description: '集中管理和控制农业智能设备，包括传感器、灌溉设备和自动化系统',
      icon: Setting
    },
    {
      id: 3,
      title: 'AI 任务调度系统',
      description: '智能分配和管理农业任务，优化资源分配和工作流程',
      icon: Cpu
    },
    {
      id: 4,
      title: '虫害大数据分析平台',
      description: '基于大数据技术分析虫害发生规律，提供精准防治策略',
      icon: DataAnalysis
    },
    {
      id: 5,
      title: '精准施药管理系统',
      description: '根据病虫害情况智能计算最佳用药量，减少农药使用并提高防治效果',
      icon: Aim
    },
    {
      id: 6,
      title: '环境智能感知系统',
      description: '全方位监测农业环境参数，提供实时数据支持和环境预警',
      icon: Odometer
    },
    {
      id: 7,
      title: '生物防治拓展模块',
      description: '整合生物防治技术，提供环保型病虫害防治解决方案',
      icon: Lightning
    },
    {
      id: 8,
      title: '决策支持中心',
      description: '综合各类数据为农业生产决策提供科学依据和建议',
      icon: Compass
    },
    {
      id: 9,
      title: 'AI智能问答助手',
      description: '基于大数据和AI技术的智能农业顾问，解答农业技术问题和提供决策支持',
      icon: ChatRound
    },
    {
      id: 10,
      title: '测试板块',
      description: '系统调试',
      icon: ChatRound
    }
  ])

  // 监测状态轮询
  let monitorTimer: number | null = null
  const updateMonitorStatus = () => {
    const statuses = [
      { text: '健康监测中...', type: 'normal' },
      { text: '叶片生长状态良好', type: 'success' },
      { text: '检测到轻微虫害迹象', type: 'warning' },
      { text: '正在分析土壤湿度', type: 'normal' },
      { text: '光照强度适中', type: 'normal' }
    ]
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
    monitorStatus.value = randomStatus.text
    statusType.value = randomStatus.type as 'normal' | 'warning' | 'error' | 'success'
  }

  // 系统状态更新
  let systemStatusTimer: number | null = null
  const updateSystemStatus = () => {
    // 随机更新数据传输状态
    systemStatus.dataTransmission = Math.random() > 0.3

    // 随机更新AI活动状态
    systemStatus.aiActive = Math.random() > 0.5
  }

  onMounted(() => {
    // 加载用户信息
    const userStr = localStorage.getItem('user')
    if (userStr) {
      userInfo.value = JSON.parse(userStr)
    } else {
      router.push('/login')
    }

    // 启动监测状态轮询
    monitorTimer = window.setInterval(updateMonitorStatus, TIMER_CONFIG.STATUS_UPDATE_INTERVAL)
    updateMonitorStatus()

    // 启动系统状态更新
    systemStatusTimer = window.setInterval(updateSystemStatus, TIMER_CONFIG.SYSTEM_STATUS_INTERVAL)
    updateSystemStatus()
  })

  onUnmounted(() => {
    if (monitorTimer) {
      clearInterval(monitorTimer)
    }

    if (systemStatusTimer) {
      clearInterval(systemStatusTimer)
    }
  })

  const handleSettings = () => {
    ElMessage.info('系统设置功能开发中')
  }

  // 使用认证组合式函数
  const { logout } = useAuth()

  const handleLogout = async () => {
    await logout()
  }

  const handleModuleClick = (module: Module) => {
    activeModuleId.value = module.id

    // 添加点击动画效果
    gsap.to('.home-container', {
      opacity: 0.8,
      duration: 0.3,
      onComplete: () => {
        navigateToModule(module)
        gsap.to('.home-container', {
          opacity: 1,
          duration: 0.3
        })
      }
    })
  }

  // 导航到相应模块
  const navigateToModule = (module: Module) => {
    console.log(module.id)
    if (module.id === 1) {
      router.push('/monitoring-center')
    } else if (module.id === 2) {
      router.push('/device-management')
    } else if (module.id === 3) {
      router.push('/task-scheduling')
    } else if (module.id === 4) {
      router.push('/pest-analysis')
    } else if (module.id === 5) {
      router.push('/pesticide-management')
    } else if (module.id === 6) {
      router.push('/environment')
    } else if (module.id === 7) {
      router.push('/biological-control')
    } else if (module.id === 8) {
      router.push('/decision-support')
    } else if (module.id === 9) {
      router.push('/ai-assistant')
    } else if (module.id === 10) {
      router.push('/test')
    } else {
      ElMessage.info(`${module.title}功能开发中`)
    }
  }
  </script>

  <style lang="scss" scoped>
  .home-container {
    position: fixed;
    inset: 0;
    background: linear-gradient(135deg, #0a1628 0%, #0a1e46 100%);
    color: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  // 背景层
  .background-layer {
    position: absolute;
    inset: 0;
    z-index: 1;
  }

  .main-content {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 40px 60px;
    position: relative;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    max-height: calc(100vh - 130px); /* 70px header + 60px footer */
    z-index: 10;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 255, 170, 0.3);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  // 状态面板
  .status-panel {
    width: 100%;
    max-width: 1600px;
    margin: 0 auto 30px;
    background: rgba(10, 22, 70, 0.7);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 170, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 255, 170, 0.2);
    position: relative;

    // 装饰性边角
    &::before, &::after {
      content: '';
      position: absolute;
      width: 100px;
      height: 100px;
      border-style: solid;
      border-color: rgba(0, 255, 170, 0.5);
      z-index: 1;
    }

    &::before {
      top: 0;
      left: 0;
      border-width: 2px 0 0 2px;
      border-radius: 16px 0 0 0;
    }

    &::after {
      bottom: 0;
      right: 0;
      border-width: 0 2px 2px 0;
      border-radius: 0 0 16px 0;
    }

    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(0, 255, 170, 0.3);

      .status-title {
        font-size: 22px;
        font-weight: 600;
        color: #00ffaa;
        text-shadow: 0 0 10px rgba(0, 255, 170, 0.5);
      }

      .status-indicators {
        display: flex;
        gap: 20px;

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          opacity: 0.5;
          transition: all 0.3s ease;

          .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #888;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              inset: -2px;
              border-radius: 50%;
              border: 1px solid #888;
              opacity: 0;
              transform: scale(1.2);
              transition: all 0.3s ease;
            }
          }

          .indicator-label {
            font-size: 14px;
            color: #ccc;
          }

          &.active {
            opacity: 1;

            .indicator-dot {
              background-color: #00ffaa;
              box-shadow: 0 0 10px rgba(0, 255, 170, 0.7);

              &::after {
                border-color: #00ffaa;
                opacity: 1;
                animation: pulse 1.5s infinite;
              }
            }

            .indicator-label {
              color: #fff;
            }
          }
        }
      }
    }

    .data-streams {
      display: flex;
      gap: 20px;
      margin-top: 20px;

      .data-stream {
        flex: 1;
        background: rgba(0, 10, 30, 0.5);
        border-radius: 8px;
        border: 1px solid rgba(0, 255, 170, 0.2);
        overflow: hidden;

        .stream-header {
          padding: 8px 12px;
          font-size: 14px;
          background: rgba(0, 10, 30, 0.8);
          border-bottom: 1px solid rgba(0, 255, 170, 0.2);
          color: #00ffaa;
        }
      }
    }
  }

  // 模块容器
  .modules-container {
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;

    .modules-header {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(0, 255, 170, 0.3);

      .modules-title {
        font-size: 22px;
        font-weight: 600;
        color: #00ffaa;
        text-shadow: 0 0 10px rgba(0, 255, 170, 0.5);
      }
    }
  }

  .module-entries {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
    width: 100%;
    z-index: 2;
  }

  // 脉冲动画
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    70% {
      transform: scale(1.5);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 0;
    }
  }

  // 响应式布局
  @media (max-width: 1600px) {
    .module-entries {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
  }

  @media (max-width: 1200px) {
    .module-entries {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 25px;
    }

    .main-content {
      padding: 20px 30px 60px;
    }

    .status-panel {
      .status-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
      }
    }
  }

  @media (max-width: 768px) {
    .module-entries {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .main-content {
      padding: 20px 15px 60px;
    }

    .status-panel {
      .data-streams {
        flex-direction: column;
      }
    }
  }
  </style>
