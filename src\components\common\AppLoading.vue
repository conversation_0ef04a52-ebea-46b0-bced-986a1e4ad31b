<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  text: {
    type: String,
    default: '加载中...'
  },
  fullscreen: {
    type: Boolean,
    default: true
  }
});

const visible = ref(props.show);

watch(() => props.show, (value) => {
  visible.value = value;
});
</script>

<template>
  <div v-if="visible" :class="['app-loading', { 'fullscreen': fullscreen }]">
    <div class="loader-container">
      <div class="loader"></div>
      <div class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.app-loading {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  
  &.fullscreen {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  
  .loader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
  
  .loader {
    width: 48px;
    height: 48px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
  }
  
  .loading-text {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 