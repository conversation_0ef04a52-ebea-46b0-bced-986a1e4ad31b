<template>
  <div class="ai-chat-page" ref="chatPage">
    <div class="chat-header" :class="{ 'scrolled': headerScrolled }">
      <div class="header-title">
        <h2>
          <span class="title-text">AI智能问答助手</span>
          <span class="tech-badge">智慧农业版</span>
        </h2>
      </div>
      <div class="header-actions">
        <div class="connection-status" :class="{ 'connected': isConnected }">
          <span class="status-dot"></span>
          {{ isConnected ? '在线' : '连接中...' }}
        </div>
        <el-button
          class="test-voice-button"
          type="success"
          size="small"
          plain
          @click="showVoiceTest = !showVoiceTest"
        >
          <el-icon><Microphone /></el-icon>
          {{ showVoiceTest ? '隐藏' : '语音测试' }}
        </el-button>
        <el-button
          class="home-button"
          type="primary"
          size="small"
          plain
          @click="returnToHome"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回首页
        </el-button>
      </div>
    </div>

    <div class="ai-chat-container">
      <ChatSidebar
        :sessions="sessions"
        :activeSessionId="activeSessionId"
        @select-session="handleSelectSession"
        @create-session="handleCreateSession"
        @delete-session="handleDeleteSession"
        @rename-session="renameSession"
        ref="sidebar"
      />
      <ChatContainer
        :messages="activeSessionMessages"
        :loading="isLoading"
        @send-message="sendMessage"
        @create-session="handleCreateSession"
        @clear-session="confirmClearChat"
        ref="chatContainer"
        key="chat-container"
      />
      <TechDecoration />

      <!-- 浮动按钮 - 只在中大屏幕上显示 -->
      <div class="floating-actions d-none d-md-flex">
        <el-tooltip content="创建新会话" placement="left" :show-after="500">
          <el-button
            class="action-button new-chat"
            circle
            type="success"
            @click="handleCreateSession"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </el-tooltip>

        <el-tooltip content="清空当前会话" placement="left" :show-after="500">
          <el-button
            class="action-button clear-chat"
            circle
            type="danger"
            :disabled="!activeSession || activeSessionMessages.length === 0"
            @click="confirmClearChat"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 引导提示 -->
    <Transition name="fade">
      <div class="welcome-guide" v-if="showGuide" @click="hideGuide">
        <div class="guide-content">
          <div class="guide-header">
            <h3>欢迎使用AI智能问答助手</h3>
            <el-icon class="close-icon" @click.stop="hideGuide"><Close /></el-icon>
          </div>
          <div class="guide-body">
            <p>您可以在这里向智慧农业助手咨询问题，获取专业解答。</p>
            <ul class="guide-tips">
              <li><el-icon><Plus /></el-icon> 点击"创建新会话"开始对话</li>
              <li><el-icon><ChatRound /></el-icon> 在侧边栏管理您的会话记录</li>
              <li><el-icon><Connection /></el-icon> 智能AI会分析您的农业问题</li>
            </ul>
          </div>
          <div class="guide-footer">
            <label>
              <el-checkbox v-model="doNotShowAgain">不再显示</el-checkbox>
            </label>
            <el-button type="primary" size="small" @click.stop="hideGuide">开始使用</el-button>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 确认清空对话框 -->
    <el-dialog
      v-model="clearDialogVisible"
      title="确认清空会话"
      width="300px"
      center
      :show-close="false"
      :close-on-click-modal="false"
    >
      <div class="clear-dialog-content">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <p>清空会话后，所有消息将被删除且<strong>无法恢复</strong>。</p>
      </div>
      <template #footer>
        <span>
          <el-button @click="clearDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="clearCurrentChat">确认清空</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useChatSessionManager } from '@/utils/chatSessionManager';
import type { ChatMessage } from '@/types/chat';
import { chatWithSpeech } from '@/api/aiChat';
import { gsap } from 'gsap';
import {
  ArrowLeft,
  Plus,
  Delete,
  Close,
  Warning,
  ChatRound,
  Connection,
  Microphone
} from '@element-plus/icons-vue';
import { useEventListener } from '@vueuse/core';
import { ElMessage, ElNotification, ElMessageBox } from 'element-plus';
import ChatSidebar from './components/ChatSidebar.vue';
import ChatContainer from './components/ChatContainer.vue';
import TechDecoration from './components/TechDecoration.vue';

// 路由
const router = useRouter();

// DOM引用
const chatPage = ref(null);
const sidebar = ref(null);
const chatContainer = ref(null);

// 状态变量
const isConnected = ref(false);
const headerScrolled = ref(false);
const showGuide = ref(false);
const doNotShowAgain = ref(false);
const clearDialogVisible = ref(false);
const showVoiceTest = ref(false);

// 使用会话管理器
const {
  sessions,
  activeSessionId,
  activeSession,
  activeSessionMessages,
  createSession,
  switchSession,
  updateSession,
  deleteSession,
  addMessageToActiveSession,
  renameSession,
  clearActiveSessionMessages,
} = useChatSessionManager();

// 加载状态
const isLoading = ref(false);

// 连接动画
const simulateConnection = () => {
  setTimeout(() => {
    isConnected.value = true;
    ElNotification({
      title: '连接成功',
      message: '已连接到AI智能问答系统',
      type: 'success',
      duration: 2000
    });
  }, 1500);
};

// 监听滚动，改变头部样式
const handleScroll = () => {
  if (chatContainer.value && chatContainer.value.$el) {
    const messagesContainer = chatContainer.value.$el.querySelector('.messages-container');
    if (messagesContainer) {
      headerScrolled.value = messagesContainer.scrollTop > 20;
    }
  }
};

// 创建新会话（添加动画）
const handleCreateSession = async () => {
  const sessionId = createSession('新的问答');

  // 添加进入动画
  await nextTick();
  if (sidebar.value && sidebar.value.$el) {
    const sessionItems = sidebar.value.$el.querySelectorAll('.session-item');
    if (sessionItems.length > 0) {
      const newSession = sessionItems[0];
      gsap.from(newSession, {
        y: -20,
        opacity: 0,
        duration: 0.3,
        ease: 'back.out(1.7)'
      });
    }
  }

  ElMessage.success('已创建新会话');
};

// 选择会话（添加过渡）
const handleSelectSession = (sessionId: string) => {
  if (sessionId === activeSessionId.value) return;

  // 淡入淡出效果
  if (chatContainer.value && chatContainer.value.$el) {
    const messagesContainer = chatContainer.value.$el.querySelector('.messages-container');
    if (messagesContainer) {
      gsap.to(messagesContainer, {
        opacity: 0,
        duration: 0.2,
        onComplete: () => {
          switchSession(sessionId);
          nextTick(() => {
            gsap.to(messagesContainer, {
              opacity: 1,
              duration: 0.3
            });
          });
        }
      });
    } else {
      switchSession(sessionId);
    }
  } else {
    switchSession(sessionId);
  }
};

// 删除会话（添加动画）
const handleDeleteSession = async (sessionId: string) => {
  if (sidebar.value && sidebar.value.$el) {
    const sessionItem = sidebar.value.$el.querySelector(`.session-item[data-id="${sessionId}"]`);
    if (sessionItem) {
      await gsap.to(sessionItem, {
        x: '-100%',
        opacity: 0,
        duration: 0.3,
        onComplete: () => {
          deleteSession(sessionId);
          ElMessage.success('会话已删除');
        }
      });
    } else {
      deleteSession(sessionId);
    }
  } else {
    deleteSession(sessionId);
  }
};

// 确认清空当前会话
const confirmClearChat = () => {
  if (!activeSession.value) return;
  clearDialogVisible.value = true;
};

// 清空当前会话
const clearCurrentChat = () => {
  if (!activeSession.value) return;

  clearActiveSessionMessages();
  clearDialogVisible.value = false;
  ElMessage.success('会话已清空');
};

// 隐藏引导
const hideGuide = () => {
  gsap.to('.guide-content', {
    y: -20,
    opacity: 0,
    duration: 0.3,
    onComplete: () => {
      showGuide.value = false;

      // 保存到本地存储
      if (doNotShowAgain.value) {
        localStorage.setItem('ai-chat-guide-hidden', 'true');
      }
    }
  });
};

// 返回首页
const returnToHome = () => {
  // 页面退出动画
  gsap.to(chatPage.value, {
    opacity: 0,
    y: 20,
    duration: 0.4,
    onComplete: () => {
      router.push('/home');
    }
  });
};

// 发送消息
const sendMessage = async (content: string) => {
  // 创建用户消息
  const userMessageId = Date.now().toString(36) + Math.random().toString(36).substring(2);
  const userMessage: ChatMessage = {
    id: userMessageId,
    role: 'user',
    content,
    timestamp: Date.now(),
    type: 'text',
    status: 'sent'
  };

  // 添加用户消息到当前会话
  addMessageToActiveSession(userMessage);

  // 调用真实的AI API
  isLoading.value = true;

  try {
    // 准备请求数据
    const requestData = {
      message: content,
      session_id: activeSession.value?.backendSessionId,
      new_conversation: !activeSession.value?.backendSessionId,
      generate_audio: true,
      voice_id: '1'
    };

    console.log('🚀 发送请求到后端:', requestData);

    // 调用聊天+语音一体化接口
    const response = await chatWithSpeech(requestData);

    console.log('📥 收到后端响应:', response);
    console.log('📊 响应数据类型:', typeof response);
    console.log('✅ 响应成功状态:', response?.success);
    console.log('📝 响应回答长度:', response?.answer?.length);
    console.log('🎵 音频可用:', response?.audio_available);

    if (!response) {
      throw new Error('后端响应为空');
    }

    if (!response.hasOwnProperty('success')) {
      console.error('❌ 响应缺少success字段:', response);
      throw new Error('后端响应格式异常：缺少success字段');
    }

    if (response.success) {
      console.log('✅ 开始处理成功响应');

      // 检查必要字段
      if (!response.answer) {
        console.error('❌ 响应缺少answer字段:', response);
        throw new Error('后端响应格式异常：缺少answer字段');
      }

      // 更新会话的后端ID
      if (activeSession.value && response.session_id) {
        updateSession(activeSession.value.id, {
          backendSessionId: response.session_id,
          conversationId: response.conversation_id
        });
        console.log('📝 已更新会话ID:', response.session_id);
      }

      // 创建助手消息
      const assistantMessage: ChatMessage = {
        id: Date.now().toString(36) + Math.random().toString(36).substring(2),
        role: 'assistant',
        content: response.answer,
        timestamp: Date.now(),
        type: 'text',
        status: 'sent',
        metadata: {
          hasAudio: response.audio_available,
          audioData: response.audio_data,
          audioFormat: response.audio_format
        }
      };

      console.log('📨 准备添加助手消息:', {
        contentLength: assistantMessage.content.length,
        hasAudio: assistantMessage.metadata?.hasAudio,
        hasAudioData: !!assistantMessage.metadata?.audioData
      });

      // 添加助手消息到当前会话
      addMessageToActiveSession(assistantMessage);
      console.log('✅ 助手消息已添加到会话');

      // 播放语音（如果有）
      if (response.audio_available) {
        console.log('🎵 开始处理音频播放');
        if (response.audio_data) {
          // Base64音频数据
          console.log('🎵 播放Base64音频数据');
          playAudioFromBase64(response.audio_data, response.audio_format || 'wav');
        } else if (response.audio_url) {
          // 音频URL
          console.log('🎵 播放音频URL:', response.audio_url);
          playAudioFromUrl(response.audio_url);
        } else {
          console.log('🔔 播放通知声音（无音频数据）');
          playNotificationSound();
        }
      } else {
        // 播放通知声音
        console.log('🔔 播放通知声音（无音频）');
        playNotificationSound();
      }

      ElMessage.success('AI回复成功');
      console.log('✅ 消息处理完成');
    } else {
      console.error('❌ 后端返回失败状态:', response);
      throw new Error(`AI回复失败: ${response.error || '未知错误'}`);
    }

  } catch (error) {
    console.error('💥 发送消息失败:', error);
    console.error('💥 错误类型:', error.constructor.name);
    console.error('💥 错误消息:', error.message);
    console.error('💥 错误堆栈:', error.stack);

    // 创建错误消息
    const errorMessage: ChatMessage = {
      id: Date.now().toString(36) + Math.random().toString(36).substring(2),
      role: 'assistant',
      content: '抱歉，我暂时无法回复您的消息，请稍后再试。',
      timestamp: Date.now(),
      type: 'text',
      status: 'error'
    };

    addMessageToActiveSession(errorMessage);

    // 根据错误类型显示不同的错误消息
    if (error.message.includes('timeout') || error.message.includes('超时')) {
      ElMessage.error('请求超时，请稍后再试');
    } else if (error.message.includes('网络') || error.message.includes('network')) {
      ElMessage.error('网络连接失败，请检查网络连接');
    } else {
      ElMessage.error(`发送消息失败: ${error.message}`);
    }
  } finally {
    isLoading.value = false;
  }
};

// 播放通知声音
const playNotificationSound = () => {
  const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
  const oscillator = audioContext.createOscillator();
  const gainNode = audioContext.createGain();

  oscillator.type = 'sine';
  oscillator.frequency.value = 840;
  gainNode.gain.value = 0.1;

  oscillator.connect(gainNode);
  gainNode.connect(audioContext.destination);

  oscillator.start(0);

  gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5);
  setTimeout(() => {
    oscillator.stop();
  }, 500);
};

// 播放Base64音频
const playAudioFromBase64 = (audioData: string, format: string = 'wav') => {
  try {
    // 将Base64转换为Blob
    const byteCharacters = atob(audioData);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: `audio/${format}` });

    // 创建音频URL并播放
    const audioUrl = URL.createObjectURL(blob);
    const audio = new Audio(audioUrl);

    audio.onended = () => {
      // 清理URL对象
      URL.revokeObjectURL(audioUrl);
    };

    audio.onerror = () => {
      console.error('音频播放失败');
      URL.revokeObjectURL(audioUrl);
      // 播放通知声音作为备选
      playNotificationSound();
    };

    audio.play().catch(error => {
      console.error('音频播放失败:', error);
      URL.revokeObjectURL(audioUrl);
      // 播放通知声音作为备选
      playNotificationSound();
    });

  } catch (error) {
    console.error('音频数据处理失败:', error);
    // 播放通知声音作为备选
    playNotificationSound();
  }
};

// 播放URL音频
const playAudioFromUrl = (audioUrl: string) => {
  try {
    const audio = new Audio(audioUrl);

    audio.onerror = () => {
      console.error('音频URL播放失败');
      // 播放通知声音作为备选
      playNotificationSound();
    };

    audio.play().catch(error => {
      console.error('音频URL播放失败:', error);
      // 播放通知声音作为备选
      playNotificationSound();
    });

  } catch (error) {
    console.error('音频URL处理失败:', error);
    // 播放通知声音作为备选
    playNotificationSound();
  }
};

// 模拟AI响应
const generateAIResponse = (userMessage: string): string => {
  const lowerCase = userMessage.toLowerCase();

  if (lowerCase.includes('你好') || lowerCase.includes('hi') || lowerCase.includes('hello')) {
    return '你好！我是智慧农业AI助手，很高兴为你服务。你可以向我咨询任何与农业相关的问题，例如作物种植、病虫害防治、智能设备使用等。';
  }
  else if (lowerCase.includes('病虫害') || lowerCase.includes('虫害') || lowerCase.includes('病害')) {
    return '智能病虫害防治是现代农业的重要环节。根据我们的数据分析，目前季节常见的病虫害包括稻飞虱、玉米螟和小麦赤霉病。建议采用综合防治措施，结合生物防治和精准用药，减少农药使用量同时提高防治效果。';
  }
  else if (lowerCase.includes('农药') || lowerCase.includes('化肥') || lowerCase.includes('施肥')) {
    return '科学施肥用药是保障作物健康生长的关键。建议您：\n\n1. 进行土壤检测，了解土壤养分状况\n2. 根据作物需求制定施肥方案\n3. 优先考虑有机肥料\n4. 采用水肥一体化技术提高利用率\n5. 实施精准施药，减少环境影响';
  }
  else if (lowerCase.includes('智能') || lowerCase.includes('设备') || lowerCase.includes('监测')) {
    return '智慧农业设备能显著提升农业生产效率。我们的系统支持多种传感器和智能设备，包括土壤湿度传感器、气象站、无人机和病虫害识别设备等。这些设备可以实时监测农田环境，为精准农业决策提供数据支持。';
  }
  else if (lowerCase.includes('种植') || lowerCase.includes('栽培') || lowerCase.includes('作物')) {
    return '作物种植需要考虑多方面因素，包括气候条件、土壤特性和水资源可用性。根据您所在地区的气候特征，我建议选择适合当地条件的农作物品种，同时采用科学的栽培技术，如合理密植、适时灌溉和病虫害综合防治等措施，以确保作物健康生长和高产。';
  }
  else {
    return '感谢您的问题。作为智慧农业AI助手，我专注于提供农业技术支持和决策建议。您的问题很有价值，我们团队正在不断完善知识库。如果您有更具体的农业问题，例如关于特定作物的种植技术、病虫害防治方法或智能设备应用等，我将能提供更精准的回答。';
  }
};

// 组件挂载时执行
onMounted(async () => {
  // 连接动画
  simulateConnection();

  // 检查是否显示引导
  const guideHidden = localStorage.getItem('ai-chat-guide-hidden');
  if (!guideHidden) {
    setTimeout(() => {
      showGuide.value = true;
    }, 1000);
  }

  // 页面入场动画
  if (chatPage.value) {
    gsap.from(chatPage.value, {
      opacity: 0,
      y: 30,
      duration: 0.8,
      ease: "power2.out"
    });
  }
});

// 监听会话变化，用节流的方式保存到本地存储
watch(() => sessions.value.length, (newVal) => {
  // 使用储存在useChatSessionManager中的storage方法自动处理
}, { deep: true });

// 清理工作
onUnmounted(() => {
  // 清理工作都由useEventListener自动处理
});
</script>

<style lang="scss" scoped>
.ai-chat-page {
  position: fixed;
  inset: 0;
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: linear-gradient(135deg, #121826 0%, #1a1b41 100%);
  color: #fff;
}

.chat-header {
  height: 60px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0, 255, 170, 0.15);
  background: rgba(0, 21, 65, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  z-index: 10;

  &.scrolled {
    height: 50px;
    background: rgba(0, 21, 65, 0.9);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }

  .header-title {
    h2 {
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;

      .title-text {
        color: #00ffaa;
        font-size: 18px;
        background: linear-gradient(90deg, #00ffaa, #1890ff);
        -webkit-background-clip: text;
        color: transparent;
      }

      .tech-badge {
        font-size: 12px;
        background: rgba(0, 255, 170, 0.2);
        border: 1px solid rgba(0, 255, 170, 0.3);
        padding: 2px 6px;
        border-radius: 10px;
        color: #00ffaa;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .connection-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.5);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        inset: -2px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        animation: pulse 2s infinite;
      }
    }

    &.connected {
      color: #52c41a;

      .status-dot {
        background-color: #52c41a;

        &::after {
          background: rgba(82, 196, 26, 0.3);
        }
      }
    }
  }

  .test-voice-button {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .home-button {
    display: flex;
    align-items: center;
    gap: 5px;
  }
}

.ai-chat-container {
  display: flex;
  flex: 1;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: url('/src/assets/logo.svg') repeat;
    opacity: 0.02;
    pointer-events: none;
    z-index: 0;
    animation: slow-move 120s linear infinite;
  }

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 50% 50%,
        rgba(0, 255, 170, 0.05) 0%,
        rgba(24, 144, 255, 0.05) 50%,
        transparent 100%);
    pointer-events: none;
    z-index: 1;
  }
}

// 响应式工具类
.d-none {
  display: none !important;
}

@media (min-width: 768px) {
  .d-md-flex {
    display: flex !important;
  }
}

// 浮动按钮
.floating-actions {
  position: fixed;
  right: 30px;
  bottom: 30px;
  flex-direction: column;
  gap: 15px;
  z-index: 100;

  .action-button {
    width: 50px;
    height: 50px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0.85;
    transform: scale(0.95);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover, &:focus {
      opacity: 1;
      transform: scale(1.08);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), 0 0 10px rgba(0, 255, 170, 0.3);

      &::before {
        opacity: 1;
      }
    }

    .el-icon {
      font-size: 20px;
    }

    &.clear-chat:disabled {
      opacity: 0.4;
      cursor: not-allowed;
      transform: scale(0.9);

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  @media (max-width: 768px) {
    right: 20px;
    bottom: 20px;

    .action-button {
      width: 45px;
      height: 45px;
    }
  }
}

// 引导提示
.welcome-guide {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: opacity 0.3s ease;

  .guide-content {
    width: 90%;
    max-width: 450px;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 170, 0.3);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 255, 170, 0.2);
    padding: 0;
    overflow: hidden;

    .guide-header {
      padding: 15px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: rgba(0, 255, 170, 0.1);

      h3 {
        margin: 0;
        font-size: 16px;
        color: #00ffaa;
      }

      .close-icon {
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.3s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .guide-body {
      padding: 20px;

      p {
        margin-top: 0;
        color: rgba(255, 255, 255, 0.9);
      }

      .guide-tips {
        list-style: none;
        padding: 0;
        margin: 20px 0;

        li {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 12px;
          color: rgba(255, 255, 255, 0.8);

          .el-icon {
            color: #00ffaa;
          }
        }
      }
    }

    .guide-footer {
      padding: 15px 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: rgba(0, 0, 0, 0.2);
    }
  }
}

// 清空对话框
.clear-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
  padding: 10px 0;

  .warning-icon {
    font-size: 24px;
    color: #ff4d4f;
  }

  p {
    margin: 0;
    color: #ff4d4f;
  }
}

// 动画
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  70% {
    transform: scale(2.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes slow-move {
  0% {
    transform: translate(0, 0) rotate(0);
  }

  100% {
    transform: translate(-10%, -10%) rotate(5deg);
  }
}

// 过渡效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 性能优化
:deep(.el-icon) {
  will-change: transform;
}

:deep(.messages-container) {
  scroll-behavior: smooth;
  will-change: transform;

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 255, 170, 0.3);
    backdrop-filter: blur(10px);
    will-change: transform;
  }
}
</style>
