<template>
  <div class="scan-effect" :class="{ 'horizontal': direction === 'horizontal' }">
    <div class="scan-line" :style="scanLineStyles"></div>
    <div class="scan-overlay" :style="overlayStyles"></div>
    <div class="scan-glitch" v-if="enableGlitch"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  /** 扫描线颜色，默认为 #00ffaa */
  scanColor?: string
  /** 扫描线宽度，单位为像素，默认为 2 */
  scanWidth?: number
  /** 扫描线速度，值越大越快，默认为 3 */
  scanSpeed?: number
  /** 扫描线方向，默认为 vertical */
  direction?: 'vertical' | 'horizontal'
  /** 是否启用故障效果，默认为 true */
  enableGlitch?: boolean
  /** 扫描线透明度，默认为 0.5 */
  scanOpacity?: number
  /** 是否启用扫描叠加效果，默认为 true */
  enableOverlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  scanColor: '#00ffaa',
  scanWidth: 2,
  scanSpeed: 3,
  direction: 'vertical',
  enableGlitch: true,
  scanOpacity: 0.5,
  enableOverlay: true
})

// 扫描线位置
const scanPosition = ref(0)
let animationFrameId: number | null = null
let lastTime = 0

// 计算扫描线样式
const scanLineStyles = computed(() => {
  if (props.direction === 'vertical') {
    return {
      height: `${props.scanWidth}px`,
      background: `linear-gradient(to right, transparent 0%, ${props.scanColor} 50%, transparent 100%)`,
      opacity: props.scanOpacity,
      top: `${scanPosition.value}%`,
      left: '0',
      width: '100%'
    }
  } else {
    return {
      width: `${props.scanWidth}px`,
      background: `linear-gradient(to bottom, transparent 0%, ${props.scanColor} 50%, transparent 100%)`,
      opacity: props.scanOpacity,
      left: `${scanPosition.value}%`,
      top: '0',
      height: '100%'
    }
  }
})

// 计算叠加层样式
const overlayStyles = computed(() => {
  if (!props.enableOverlay) return { opacity: 0 }
  
  if (props.direction === 'vertical') {
    return {
      background: `linear-gradient(to bottom, 
        rgba(0, 255, 170, 0.03) 0%, 
        rgba(0, 255, 170, 0.05) ${scanPosition.value - 5}%, 
        rgba(0, 255, 170, 0.1) ${scanPosition.value}%, 
        rgba(0, 255, 170, 0.05) ${scanPosition.value + 5}%, 
        rgba(0, 255, 170, 0.03) 100%)`
    }
  } else {
    return {
      background: `linear-gradient(to right, 
        rgba(0, 255, 170, 0.03) 0%, 
        rgba(0, 255, 170, 0.05) ${scanPosition.value - 5}%, 
        rgba(0, 255, 170, 0.1) ${scanPosition.value}%, 
        rgba(0, 255, 170, 0.05) ${scanPosition.value + 5}%, 
        rgba(0, 255, 170, 0.03) 100%)`
    }
  }
})

// 动画函数
const animate = (time: number) => {
  // 计算时间差
  if (!lastTime) lastTime = time
  const delta = time - lastTime
  lastTime = time
  
  // 更新扫描线位置
  scanPosition.value += props.scanSpeed * delta / 100
  
  // 重置位置
  if (scanPosition.value > 100) {
    scanPosition.value = 0
  }
  
  // 继续动画循环
  animationFrameId = requestAnimationFrame(animate)
}

// 故障效果
let glitchInterval: number | null = null
const triggerGlitch = () => {
  if (!props.enableGlitch) return
  
  const glitchElement = document.querySelector('.scan-glitch')
  if (!glitchElement) return
  
  // 随机决定是否触发故障效果
  if (Math.random() > 0.7) {
    // 添加故障效果类
    glitchElement.classList.add('active')
    
    // 短暂延迟后移除
    setTimeout(() => {
      glitchElement.classList.remove('active')
    }, Math.random() * 200 + 50)
  }
}

onMounted(() => {
  // 开始扫描动画
  animationFrameId = requestAnimationFrame(animate)
  
  // 设置故障效果间隔
  if (props.enableGlitch) {
    glitchInterval = window.setInterval(triggerGlitch, 2000)
  }
})

onUnmounted(() => {
  // 清理资源
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId)
  }
  
  if (glitchInterval !== null) {
    clearInterval(glitchInterval)
  }
})
</script>

<style lang="scss" scoped>
.scan-effect {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 10;
  
  .scan-line {
    position: absolute;
    box-shadow: 0 0 10px rgba(0, 255, 170, 0.7);
    transition: opacity 0.2s ease;
  }
  
  .scan-overlay {
    position: absolute;
    inset: 0;
    opacity: 0.6;
    transition: background 0.3s ease;
  }
  
  .scan-glitch {
    position: absolute;
    inset: 0;
    background: transparent;
    opacity: 0;
    z-index: 2;
    
    &.active {
      opacity: 1;
      background: linear-gradient(
        transparent 0%,
        rgba(0, 255, 170, 0.2) 10%,
        transparent 10.5%,
        transparent 20%,
        rgba(0, 255, 170, 0.2) 20.5%,
        transparent 21%
      );
      background-size: 100% 100%;
      animation: glitch 0.2s steps(2) forwards;
    }
  }
}

// 故障动画
@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-5px, 5px);
  }
  40% {
    transform: translate(-5px, -5px);
  }
  60% {
    transform: translate(5px, 5px);
  }
  80% {
    transform: translate(5px, -5px);
  }
  100% {
    transform: translate(0);
  }
}

// 水平扫描线样式
.scan-effect.horizontal .scan-line {
  transform: translateX(-50%);
}
</style> 