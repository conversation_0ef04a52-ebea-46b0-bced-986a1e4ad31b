<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="dashboard-card" @click="goToModule('pest-database')">
          <template #header>
            <div class="card-header">
              <el-icon><DataLine /></el-icon>
              <span>多维度虫害数据库</span>
            </div>
          </template>
          <div class="card-content">
            <p>整合虫害种类、数量、分布等多维数据，形成全面的虫害数据库，方便查询分析，了解虫害在不同农田、时间的详细情况。</p>
            <div class="card-action">
              <el-button type="primary" plain>
                进入模块
                <el-icon class="el-icon--right"><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="dashboard-card" @click="goToModule('ai-recognition-log')">
          <template #header>
            <div class="card-header">
              <el-icon><PictureFilled /></el-icon>
              <span>AI图像识别日志追溯</span>
            </div>
          </template>
          <div class="card-content">
            <p>记录和管理AI图像识别系统的识别日志，包括识别时间、识别结果、图像路径等信息，方便追溯和查看历史识别记录。</p>
            <div class="card-action">
              <el-button type="primary" plain>
                进入模块
                <el-icon class="el-icon--right"><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="dashboard-card" @click="goToModule('outbreak-prediction')">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>虫害爆发趋势预测</span>
            </div>
          </template>
          <div class="card-content">
            <p>基于历史虫害数据和环境数据，运用机器学习算法构建虫害爆发趋势预测模型，提供预测虫害发展趋势的工具。</p>
            <div class="card-action">
              <el-button type="primary" plain>
                进入模块
                <el-icon class="el-icon--right"><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="dashboard-card" @click="goToModule('control-comparison')">
          <template #header>
            <div class="card-header">
              <el-icon><DataBoard /></el-icon>
              <span>消杀效果对比分析</span>
            </div>
          </template>
          <div class="card-content">
            <p>对比不同消杀方法在不同时间段、不同区域的消杀效果，帮助评估和选择最佳的消杀方案。</p>
            <div class="card-action">
              <el-button type="primary" plain>
                进入模块
                <el-icon class="el-icon--right"><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="dashboard-card" @click="goToModule('smart-recommendation')">
          <template #header>
            <div class="card-header">
              <el-icon><Guide /></el-icon>
              <span>智能用药推荐系统</span>
            </div>
          </template>
          <div class="card-content">
            <p>根据虫害类型、作物种类、环境条件等因素，提供即时、精准的农药使用推荐方案。</p>
            <div class="card-action">
              <el-button type="primary" plain>
                进入模块
                <el-icon class="el-icon--right"><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="dashboard-card" @click="goToModule('report-generation')">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>数据报告自动生成</span>
            </div>
          </template>
          <div class="card-content">
            <p>支持将虫害分析数据、消杀效果数据、环境监测数据等生成PDF或Excel格式的报告文件，方便下载和分享。</p>
            <div class="card-action">
              <el-button type="primary" plain>
                进入模块
                <el-icon class="el-icon--right"><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="statistics-row">
      <el-col :span="24">
        <el-card class="statistics-card">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>平台数据统计</span>
            </div>
          </template>
          <div class="statistics-content">
            <el-row :gutter="40">
              <el-col :span="6">
                <div class="statistic-item">
                  <div class="statistic-value">3,245</div>
                  <div class="statistic-label">虫害记录总数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistic-item">
                  <div class="statistic-value">127</div>
                  <div class="statistic-label">虫害种类数量</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistic-item">
                  <div class="statistic-value">896</div>
                  <div class="statistic-label">AI识别日志数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistic-item">
                  <div class="statistic-value">52</div>
                  <div class="statistic-label">生成报告数量</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  DataLine,
  PictureFilled,
  TrendCharts,
  DataBoard,
  Guide,
  Document,
  DataAnalysis,
  ArrowRight
} from '@element-plus/icons-vue'

const router = useRouter()

const goToModule = (routeName: string) => {
  router.push(`/pest-analysis/${routeName}`)
}
</script>

<style scoped lang="scss">
.dashboard {
  .dashboard-card {
    height: 250px;
    margin-bottom: 20px;
    transition: all 0.3s;
    cursor: pointer;
    border: none;
    background-color: #1e3a8a;
    color: #d1d5db;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
    }

    .card-header {
      display: flex;
      align-items: center;
      font-size: 1.2rem;
      font-weight: bold;
      color: #ffffff;
      background-color: #1f2937;
      
      .el-icon {
        margin-right: 8px;
        font-size: 1.4rem;
        color: #3b82f6;
      }
    }

    .card-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: calc(100% - 20px);
      padding: 10px 0;
      
      p {
        margin-top: 0;
        line-height: 1.6;
      }
      
      .card-action {
        text-align: right;
        margin-top: 10px;
      }
    }
  }
  
  .statistics-row {
    margin-top: 20px;
    
    .statistics-card {
      border: none;
      background-color: #1e3a8a;
      color: #d1d5db;
      
      .card-header {
        display: flex;
        align-items: center;
        font-size: 1.2rem;
        font-weight: bold;
        color: #ffffff;
        background-color: #1f2937;
        
        .el-icon {
          margin-right: 8px;
          font-size: 1.4rem;
          color: #3b82f6;
        }
      }
      
      .statistics-content {
        padding: 20px 0;
        
        .statistic-item {
          text-align: center;
          padding: 15px;
          
          .statistic-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 5px;
          }
          
          .statistic-label {
            font-size: 1rem;
            color: #d1d5db;
          }
        }
      }
    }
  }
}
</style> 