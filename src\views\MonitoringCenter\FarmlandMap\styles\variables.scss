/**
 * 农田地图组件样式变量
 */

// 颜色变量
$primary-color: #2bff96;
$primary-color-light: rgba(43, 255, 150, 0.3);
$primary-color-dark: rgba(0, 48, 130, 0.9);

$background-dark: #001528;
$background-panel: rgba(0, 25, 75, 0.8);
$background-header: linear-gradient(90deg, rgba(10, 22, 70, 0.95), rgba(0, 40, 100, 0.85));

// 添加面板相关变量
$panel-background: rgba(0, 25, 75, 0.8);
$panel-blur: 10px;
$panel-border-color: rgba(43, 255, 150, 0.15);
$panel-shadow: 0 4px 20px rgba(0, 10, 30, 0.5);

$text-light: #ffffff;
$text-secondary: rgba(255, 255, 255, 0.85);
$text-tertiary: rgba(255, 255, 255, 0.65);

// 为了兼容其他组件中使用的变量名称
$text-primary: $text-light;
$bg-dark: $background-dark;
$bg-panel: $background-panel;

$border-color: rgba(43, 255, 150, 0.25);
$shadow-color: rgba(0, 10, 30, 0.6);

// 添加阴影变量，以便与其他组件中使用的变量名称兼容
$shadow-sm: 0 2px 12px rgba(0, 10, 30, 0.4);
$shadow-md: 0 4px 18px rgba(0, 10, 30, 0.5);
$shadow-lg: 0 6px 24px rgba(0, 10, 30, 0.6);

// 更新阴影变量为更易读的名称
$shadow-small: $shadow-sm;
$shadow-medium: $shadow-md;
$shadow-large: $shadow-lg;

// 状态颜色
$status-online: #2bff96;
$status-standby: #ffcc00;
$status-offline: #ff3b5c;
$status-info: #0099ff;
$status-warning: #ffaa00;
$status-danger: #ff3b5c;

// 通用语义颜色
$info-color: #0099ff;
$success-color: #2bff96;
$warning-color: #ffaa00;
$danger-color: #ff3b5c;

// 尺寸变量
$header-height: 70px;
$panel-width: 320px;
$panel-padding: 10px;
$card-padding: 15px;
$border-radius: 12px;

// 添加边框半径变量，以便与其他组件中使用的变量名称兼容
$border-radius-sm: 6px;
$border-radius-md: $border-radius;
$border-radius-lg: 16px;
$border-radius-xl: 24px;

// 添加间距变量，以便与其他组件中使用的变量名称兼容
$spacing-xs: 5px;
$spacing-sm: 10px;
$spacing-md: 15px;
$spacing-lg: 20px;
$spacing-xl: 30px;

// 断点变量
$breakpoint-xl: 1600px;
$breakpoint-lg: 1366px;
$breakpoint-md: 1200px;
$breakpoint-sm: 992px;
$breakpoint-xs: 768px;

// 动画变量
$transition-normal: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
$transition-fast: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin glass-effect {
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

@mixin card-hover {
  transition: $transition-normal;
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 28px rgba(0, 10, 30, 0.7);
  }
}

@mixin status-dot($color) {
  background-color: $color;
  box-shadow: 0 0 10px $color, 0 0 20px rgba($color, 0.4);
}

@mixin blink-animation {
  animation: blink 1.5s infinite;
}

// 添加新的卡片样式混合器
@mixin card-style-new {
  background-color: rgba(0, 21, 65, 0.8);
  border: 1px solid $panel-border-color;
  border-radius: $border-radius;
  box-shadow: $shadow-medium;
  backdrop-filter: blur($panel-blur);
  -webkit-backdrop-filter: blur($panel-blur);
}

// 添加自定义滚动条混合器
@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 5px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(43, 255, 150, 0.3);
    border-radius: 5px;
    
    &:hover {
      background: rgba(43, 255, 150, 0.5);
    }
  }
  
  scrollbar-width: thin;
  scrollbar-color: rgba(43, 255, 150, 0.3) rgba(0, 0, 0, 0.1);
}

// 全局隐藏滚动条混合器
@mixin hide-scrollbar {
  &::-webkit-scrollbar {
    width: 0;
    display: none;
  }
  
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
  
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

// 关键帧动画
@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

// 新增动画
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
} 