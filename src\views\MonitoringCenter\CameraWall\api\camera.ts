import request from '@/utils/request';
import { CameraStatus, StreamStatus } from '../types';
import type { Camera } from '../types';
import envConfig, { WEBSOCKET_URLS } from '@/config/env';

// 获取所有摄像头列表
export const getCameras = async (): Promise<Camera[]> => {
  const response = await request.get<Camera[]>('/api/cameras');
  return response.data;
};

// 获取指定摄像头的视频流地址
export const getCameraStream = (cameraId: string): string => {
  // 返回WebSocket摄像头流地址
  return WEBSOCKET_URLS.CAMERA(cameraId);
};

// 获取指定摄像头的HTTP视频流地址（备用）
export const getCameraHttpStream = (cameraId: string): string => {
  // 使用test.mp4作为临时视频源替代实时摄像头流
  return '/test.mp4';
};

// 获取摄像头状态
export const getCameraStatus = async (cameraId: string): Promise<{
  status: CameraStatus;
  streamStatus: StreamStatus;
}> => {
  const response = await request.get<{
    status: CameraStatus;
    streamStatus: StreamStatus;
  }>(`/api/cameras/${cameraId}/status`);
  return response.data;
};

// 模拟数据（如果后端API尚未实现）
export const getMockCameras = (): Camera[] => {
  return [
    {
      id: 'camera1',
      name: '摄像头1',
      status: CameraStatus.NORMAL,
      streamUrl: getCameraStream('camera1'),
      streamStatus: StreamStatus.NORMAL,
      location: '温室区A',
      lastUpdateTime: new Date()
    },
    {
      id: 'camera2',
      name: '摄像头2',
      status: CameraStatus.NORMAL,
      streamUrl: getCameraStream('camera2'),
      streamStatus: StreamStatus.NORMAL,
      location: '温室区B',
      lastUpdateTime: new Date()
    },
    {
      id: 'camera3',
      name: '摄像头3',
      status: CameraStatus.LOW_BATTERY,
      streamUrl: getCameraStream('camera3'),
      streamStatus: StreamStatus.STUTTERING,
      location: '农田区域',
      lastUpdateTime: new Date()
    },
    {
      id: 'camera4',
      name: '摄像头4',
      status: CameraStatus.OFFLINE,
      streamUrl: getCameraStream('camera4'),
      streamStatus: StreamStatus.INTERRUPTED,
      location: '仓库区域',
      lastUpdateTime: new Date()
    },
    {
      id: 'camera5',
      name: '摄像头5',
      status: CameraStatus.NORMAL,
      streamUrl: getCameraStream('camera5'),
      streamStatus: StreamStatus.NORMAL,
      location: '温室区A',
      lastUpdateTime: new Date()
    },
    {
      id: 'camera6',
      name: '摄像头6',
      status: CameraStatus.NORMAL,
      streamUrl: getCameraStream('camera6'),
      streamStatus: StreamStatus.NORMAL,
      location: '温室区B',
      lastUpdateTime: new Date()
    },
    {
      id: 'camera7',
      name: '摄像头7',
      status: CameraStatus.NORMAL,
      streamUrl: getCameraStream('camera7'),
      streamStatus: StreamStatus.NORMAL,
      location: '农田区域',
      lastUpdateTime: new Date()
    }
  ];
};
