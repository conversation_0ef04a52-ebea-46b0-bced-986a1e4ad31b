# 前端登录功能测试指南

## 重构完成的功能

### 1. API接口更新
- ✅ 登录API: `POST /agriUser/login`
- ✅ 获取用户信息API: `GET /agriUser/profile`
- ✅ 退出登录API: `POST /agriUser/logout`

### 2. 前端功能更新
- ✅ 移除验证码功能
- ✅ 支持用户名或手机号登录
- ✅ 更新用户信息显示
- ✅ 集成新的认证流程

## 测试步骤

### 1. 启动前端项目
```bash
cd smart-agriculture-h53
npm run dev
```

### 2. 测试登录功能
1. 访问 `http://localhost:5174`
2. 应该自动重定向到登录页面 `/login`
3. 输入用户名或手机号和密码
4. 点击登录按钮

### 3. 验证登录成功
- 登录成功后应该跳转到首页 `/home`
- 右上角应该显示用户信息（昵称或用户名）
- 用户角色显示为"农业用户"

### 4. 测试退出登录
1. 点击右上角用户头像下拉菜单
2. 点击"退出登录"
3. 应该返回登录页面
4. 本地存储的token和用户信息应该被清除

### 5. 测试路由守卫
1. 退出登录后，尝试直接访问 `/home`
2. 应该被重定向到 `/login` 并显示"请先登录"提示
3. 登录后再次访问 `/login`
4. 应该被重定向到 `/home`

## 预期行为

### 登录表单
- 用户名输入框支持用户名或手机号
- 密码输入框支持显示/隐藏密码
- 记住我功能正常工作
- 表单验证正确（用户名2-20字符，手机号格式验证，密码6-20字符）

### 认证状态管理
- Token正确存储在localStorage中
- 用户信息正确存储和显示
- 认证状态在页面刷新后保持
- 路由守卫正确拦截未认证访问

### 错误处理
- 登录失败显示友好错误信息
- 网络错误显示相应提示
- Token过期自动跳转登录页

## 注意事项

1. **后端服务**: 确保后端服务已启动并且农业用户登录API可用
2. **CORS配置**: 确保后端CORS配置允许前端域名访问
3. **环境变量**: 检查 `.env` 文件中的API基础URL配置
4. **测试数据**: 确保数据库中有测试用户数据

## 故障排除

### 登录失败
1. 检查浏览器开发者工具的Network标签
2. 确认API请求URL是否正确
3. 检查请求和响应数据格式
4. 确认后端服务状态

### 用户信息不显示
1. 检查localStorage中是否有用户数据
2. 确认用户信息格式是否正确
3. 检查HomeHeader组件的用户信息绑定

### 路由跳转问题
1. 检查路由守卫逻辑
2. 确认token存储和读取
3. 检查路由配置

## 开发者备注

- 所有登录相关的类型定义在 `src/types/user.ts`
- 认证逻辑在 `src/composables/useAuth.ts`
- API调用在 `src/api/auth.ts`
- 表单处理在 `src/composables/useForm.ts`
