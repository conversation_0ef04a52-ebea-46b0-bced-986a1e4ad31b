<template>
  <BaseLayout
    title="测试页面"
    theme="test"
    themeColor="#f97316"
    moduleIcon="Monitor"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="获取摄像头"
        icon="VideoCamera"
        route="/test/camera"
        :collapsed="isAsideCollapsed"
        themeColor="#f97316"
      />
      <TechMenuItem
        title="API测试工具"
        icon="Connection"
        route="/test/api"
        :collapsed="isAsideCollapsed"
        themeColor="#f97316"
      />
    </template>
    
    <router-view />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { VideoCamera, Connection, ArrowLeft, Monitor } from '@element-plus/icons-vue'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import { getModuleTheme } from './components/layoutConfig'

const route = useRoute()
const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 计算当前标题
const currentTitle = computed(() => {
  return route.meta.title || '测试页面'
})

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 组件挂载
onMounted(() => {
  // 监听BaseLayout事件
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<style scoped>
.decision-support-layout {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.el-container {
  height: 100%;
  width: 100%;
}

.sidebar {
  background-color: #1f2937;
  color: #d1d5db;
  height: 100vh;
  padding: 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #374151;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sidebar-header h2 {
  color: #3b82f6;
  margin: 0;
  font-size: 1.5rem;
}

.home-button {
  margin-top: 10px;
}

.menu-container {
  padding: 20px 0;
  flex: 1;
  overflow-y: auto;
}

.main-content {
  background-color: #f3f4f6;
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.page-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 10px;
}

.page-header h1 {
  color: #111827;
  margin: 0;
  font-size: 1.8rem;
}
</style>
