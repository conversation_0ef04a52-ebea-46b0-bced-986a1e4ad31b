/**
 * 地图服务 - 负责Canvas绘图相关的功能
 */
import type { FarmlandArea, DeviceInfo, TrajectoryPoint, MapState } from '../types';

export class MapService {
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private mapState: MapState = {
    scale: 1,
    offsetX: 0,
    offsetY: 0,
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0
  };
  private farmlandImage: HTMLImageElement | null = null;
  private deviceImages: { [key: string]: HTMLImageElement } = {};
  private farmlandArea: FarmlandArea | null = null;
  private devices: DeviceInfo[] = [];

  // 初始化Canvas
  public initCanvas(canvasId: string): Promise<boolean> {
    return new Promise((resolve) => {
      this.canvas = document.getElementById(canvasId) as HTMLCanvasElement;
      if (!this.canvas) {
        console.error('无法找到Canvas元素');
        resolve(false);
        return;
      }
      
      this.ctx = this.canvas.getContext('2d');
      if (!this.ctx) {
        console.error('无法获取Canvas上下文');
        resolve(false);
        return;
      }
      
      // 设置Canvas尺寸
      this.resizeCanvas();
      
      // 加载图片资源
      this.loadImages().then(() => {
        resolve(true);
      });
    });
  }
  
  // 设置农田区域数据
  public setFarmlandArea(area: FarmlandArea): void {
    this.farmlandArea = area;
  }
  
  // 设置设备数据
  public setDevices(devices: DeviceInfo[]): void {
    this.devices = devices;
  }
  
  // 调整Canvas尺寸
  public resizeCanvas(): void {
    if (!this.canvas) return;
    
    const container = this.canvas.parentElement;
    if (!container) return;
    
    this.canvas.width = container.clientWidth;
    this.canvas.height = container.clientHeight;
    
    // 重新绘制
    this.drawMap();
  }
  
  // 加载图片资源
  private async loadImages(): Promise<void> {
    // 加载农田背景图
    this.farmlandImage = new Image();
    this.farmlandImage.src = '@/assets/images/heatmap-example.png';
    
    // 加载设备图标
    const dogImage = new Image();
    dogImage.src = '/images/devices/robot-dog.svg';
    
    const droneImage = new Image();
    droneImage.src = '/images/devices/drone.svg';
    
    // 等待图片加载完成
    await Promise.all([
      this.loadImagePromise(this.farmlandImage),
      this.loadImagePromise(dogImage).then(() => {
        this.deviceImages['dog'] = dogImage;
      }).catch(() => console.error('机器狗图标加载失败')),
      this.loadImagePromise(droneImage).then(() => {
        this.deviceImages['drone'] = droneImage;
      }).catch(() => console.error('无人机图标加载失败'))
    ]).catch(() => console.error('部分图片加载失败'));
  }
  
  // 将图片加载转换为Promise
  private loadImagePromise(img: HTMLImageElement): Promise<void> {
    return new Promise((resolve, reject) => {
      img.onload = () => resolve();
      img.onerror = () => reject();
    });
  }
  
  // 绘制地图
  public drawMap(): void {
    if (!this.ctx || !this.canvas) return;
    
    // 清空画布
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // 绘制背景
    this.drawBackground();
    
    // 如果有农田区域数据，则绘制农田区域
    if (this.farmlandArea) {
      this.drawFarmlandArea();
    }
    
    // 如果有设备数据，则绘制设备
    if (this.devices.length > 0) {
      this.drawDevices();
      this.drawDeviceTrajectories();
    }
  }
  
  // 绘制背景
  private drawBackground(): void {
    if (!this.ctx || !this.canvas) return;
    
    // 如果有背景图，且已成功加载，则绘制背景图
    if (this.farmlandImage && this.farmlandImage.complete && this.farmlandImage.naturalWidth > 0) {
      try {
        const pattern = this.ctx.createPattern(this.farmlandImage, 'repeat');
        if (pattern) {
          this.ctx.save();
          this.ctx.scale(this.mapState.scale, this.mapState.scale);
          this.ctx.translate(this.mapState.offsetX / this.mapState.scale, this.mapState.offsetY / this.mapState.scale);
          this.ctx.fillStyle = pattern;
          this.ctx.fillRect(-this.mapState.offsetX / this.mapState.scale, -this.mapState.offsetY / this.mapState.scale, 
                          this.canvas.width / this.mapState.scale, this.canvas.height / this.mapState.scale);
          this.ctx.restore();
        } else {
          // 如果创建图案失败，使用纯色背景
          this.ctx.fillStyle = '#e8f5e9';
          this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
      } catch (error) {
        console.error('绘制背景图案时出错:', error);
        // 出错时使用纯色背景
        this.ctx.fillStyle = '#e8f5e9';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      }
    } else {
      // 否则绘制简单的背景色
      this.ctx.fillStyle = '#e8f5e9';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
  }
  
  // 绘制农田区域
  private drawFarmlandArea(): void {
    if (!this.ctx || !this.farmlandArea) return;
    
    this.ctx.save();
    this.ctx.scale(this.mapState.scale, this.mapState.scale);
    this.ctx.translate(this.mapState.offsetX / this.mapState.scale, this.mapState.offsetY / this.mapState.scale);
    
    const { x, y, width, height } = this.farmlandArea;
    
    // 绘制农田区域
    this.ctx.fillStyle = 'rgba(50, 150, 80, 0.3)';
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2 / this.mapState.scale;
    this.ctx.beginPath();
    this.ctx.rect(x, y, width, height);
    this.ctx.fill();
    this.ctx.stroke();
    
    // 绘制农田中心标记
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    
    this.ctx.fillStyle = '#ffffff';
    this.ctx.strokeStyle = '#000000';
    this.ctx.lineWidth = 1 / this.mapState.scale;
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, 10, 0, Math.PI * 2);
    this.ctx.fill();
    this.ctx.stroke();
    
    // 绘制农田名称
    this.ctx.font = `${16/this.mapState.scale}px Arial`;
    this.ctx.fillStyle = '#ffffff';
    this.ctx.strokeStyle = '#000000';
    this.ctx.lineWidth = 3 / this.mapState.scale;
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.strokeText('智能农田示范区', centerX, centerY + 15);
    this.ctx.fillText('智能农田示范区', centerX, centerY + 15);
    
    this.ctx.restore();
  }
  
  // 绘制设备
  private drawDevices(): void {
    if (!this.ctx) return;
    
    this.ctx.save();
    this.ctx.scale(this.mapState.scale, this.mapState.scale);
    this.ctx.translate(this.mapState.offsetX / this.mapState.scale, this.mapState.offsetY / this.mapState.scale);
    
    this.devices.forEach(device => {
      // 直接使用设备的位置坐标，确保与点击检测逻辑一致
      const { x, y } = device.position;
      const statusColor = this.getStatusColor(device.status);
      
      // 绘制设备图标或简单形状
      if (this.deviceImages[device.type] && this.deviceImages[device.type].complete) {
        // 使用加载的图片
        const img = this.deviceImages[device.type];
        const size = device.type === 'dog' ? 30 : 40;
        this.ctx!.drawImage(img, x - size/2, y - size/2, size, size);
      } else {
        // 使用简单形状代替
        this.ctx!.fillStyle = statusColor;
        this.ctx!.strokeStyle = '#ffffff';
        this.ctx!.lineWidth = 2 / this.mapState.scale;
        this.ctx!.beginPath();
        
        if (device.type === 'dog') {
          // 绘制方形代表机器狗
          this.ctx!.rect(x - 10, y - 10, 20, 20);
        } else {
          // 绘制圆形代表无人机
          this.ctx!.arc(x, y, 12, 0, Math.PI * 2);
        }
        
        this.ctx!.fill();
        this.ctx!.stroke();
      }
      
      // 绘制设备状态指示圈，确保半径与点击检测一致
      this.ctx!.strokeStyle = statusColor;
      this.ctx!.lineWidth = 2 / this.mapState.scale;
      this.ctx!.beginPath();
      // 与点击检测使用相同的半径
      const clickRadius = device.type === 'drone' ? 25 : 20;
      this.ctx!.arc(x, y, clickRadius, 0, Math.PI * 2);
      this.ctx!.stroke();
      
      // 绘制设备名称
      this.ctx!.font = `${14/this.mapState.scale}px Arial`;
      this.ctx!.fillStyle = '#ffffff';
      this.ctx!.strokeStyle = '#000000';
      this.ctx!.lineWidth = 3 / this.mapState.scale;
      this.ctx!.textAlign = 'center';
      this.ctx!.textBaseline = 'bottom';
      this.ctx!.strokeText(device.name, x, y - 25);
      this.ctx!.fillText(device.name, x, y - 25);
    });
    
    this.ctx.restore();
  }
  
  // 绘制设备轨迹
  private drawDeviceTrajectories(): void {
    if (!this.ctx) return;
    
    this.ctx.save();
    this.ctx.scale(this.mapState.scale, this.mapState.scale);
    this.ctx.translate(this.mapState.offsetX / this.mapState.scale, this.mapState.offsetY / this.mapState.scale);
    
    // 绘制无人机轨迹
    const dronePoints: TrajectoryPoint[] = [
      { x: 400, y: 150 },
      { x: 450, y: 120 },
      { x: 500, y: 200 },
      { x: 450, y: 250 },
      { x: 400, y: 150 }
    ];
    
    this.ctx.strokeStyle = 'rgba(0, 200, 255, 0.7)';
    this.ctx.lineWidth = 2 / this.mapState.scale;
    this.ctx.setLineDash([5 / this.mapState.scale, 3 / this.mapState.scale]);
    this.ctx.beginPath();
    this.ctx.moveTo(dronePoints[0].x, dronePoints[0].y);
    
    for (let i = 1; i < dronePoints.length; i++) {
      this.ctx.lineTo(dronePoints[i].x, dronePoints[i].y);
    }
    
    this.ctx.stroke();
    this.ctx.setLineDash([]);
    
    // 绘制机器狗轨迹
    const dogPoints: TrajectoryPoint[] = [
      { x: 200, y: 300 },
      { x: 180, y: 350 },
      { x: 250, y: 370 },
      { x: 270, y: 320 },
      { x: 200, y: 300 }
    ];
    
    this.ctx.strokeStyle = 'rgba(255, 200, 0, 0.7)';
    this.ctx.lineWidth = 2 / this.mapState.scale;
    this.ctx.setLineDash([2 / this.mapState.scale, 2 / this.mapState.scale]);
    this.ctx.beginPath();
    this.ctx.moveTo(dogPoints[0].x, dogPoints[0].y);
    
    for (let i = 1; i < dogPoints.length; i++) {
      this.ctx.lineTo(dogPoints[i].x, dogPoints[i].y);
    }
    
    this.ctx.stroke();
    this.ctx.setLineDash([]);
    
    this.ctx.restore();
  }
  
  // 获取设备状态对应的颜色
  private getStatusColor(status: string): string {
    switch (status) {
      case 'online':
        return 'rgba(0, 255, 0, 0.8)';
      case 'standby':
        return 'rgba(255, 255, 0, 0.8)';
      case 'offline':
        return 'rgba(255, 0, 0, 0.8)';
      default:
        return 'rgba(255, 255, 255, 0.8)';
    }
  }
  
  // 缩放控制
  public zoomIn(): void {
    if (this.mapState.scale < 5) {
      this.mapState.scale *= 1.2;
      this.drawMap();
    }
  }
  
  public zoomOut(): void {
    if (this.mapState.scale > 0.5) {
      this.mapState.scale /= 1.2;
      this.drawMap();
    }
  }
  
  public resetView(): void {
    this.mapState.scale = 1;
    this.mapState.offsetX = 0;
    this.mapState.offsetY = 0;
    this.drawMap();
  }
  
  // 视图模式控制函数 (根据滑块值调整地图显示效果)
  public changeViewMode(value: number): void {
    // 这里可以根据滑块值实现不同的视图效果
    // 例如调整对比度、亮度等
    this.drawMap();
  }
  
  // 处理鼠标拖动事件
  public handleMouseDown(e: MouseEvent): void {
    // 检查是否点击了设备
    if (this.canvas && this.farmlandArea) {
      const rect = this.canvas.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const clickY = e.clientY - rect.top;
      
      // 转换为地图坐标（考虑缩放和平移）
      const mapX = (clickX / this.mapState.scale) - (this.mapState.offsetX / this.mapState.scale);
      const mapY = (clickY / this.mapState.scale) - (this.mapState.offsetY / this.mapState.scale);
      
      console.log('点击坐标:', { clickX, clickY, mapX, mapY });
      
      // 检查是否有设备在点击范围内
      let clickedDevice: DeviceInfo | null = null;
      
      for (const device of this.devices) {
        // 修正: 直接使用设备的x和y坐标，不再乘以farmlandArea的尺寸
        const deviceX = device.position.x;
        const deviceY = device.position.y;
        
        // 根据设备类型设置不同的点击检测半径
        const clickRadius = device.type === 'drone' ? 25 : 20;
        
        const distance = Math.sqrt(
          Math.pow(mapX - deviceX, 2) + 
          Math.pow(mapY - deviceY, 2)
        );
        
        console.log('设备检测:', { 
          deviceId: device.id, 
          deviceType: device.type,
          deviceX, 
          deviceY, 
          distance, 
          clickRadius,
          inRange: distance <= clickRadius
        });
        
        if (distance <= clickRadius) {
          clickedDevice = device;
          break;
        }
      }
      
      // 如果点击了设备，触发自定义事件
      if (clickedDevice) {
        console.log('设备被点击:', clickedDevice.id, clickedDevice.name);
        
        // 触发设备选择事件
        const event = new CustomEvent('device-selected', { 
          detail: { deviceId: clickedDevice.id } 
        });
        this.canvas.dispatchEvent(event);
        
        // 高亮显示被选中的设备
        this.highlightDevice(clickedDevice);
        
        // 阻止进一步的拖动操作
        return;
      }
    }
    
    // 原有的拖动逻辑
    this.mapState.isDragging = true;
    this.mapState.lastMouseX = e.offsetX;
    this.mapState.lastMouseY = e.offsetY;
    
    if (this.canvas) {
      this.canvas.style.cursor = 'grabbing';
    }
  }
  
  public handleMouseMove(e: MouseEvent): void {
    if (!this.mapState.isDragging) return;
    
    const deltaX = e.offsetX - this.mapState.lastMouseX;
    const deltaY = e.offsetY - this.mapState.lastMouseY;
    
    this.mapState.offsetX += deltaX;
    this.mapState.offsetY += deltaY;
    
    this.mapState.lastMouseX = e.offsetX;
    this.mapState.lastMouseY = e.offsetY;
    
    this.drawMap();
  }
  
  public handleMouseUp(): void {
    this.mapState.isDragging = false;
    
    if (this.canvas) {
      this.canvas.style.cursor = 'grab';
    }
  }
  
  public handleMouseLeave(): void {
    this.mapState.isDragging = false;
    
    if (this.canvas) {
      this.canvas.style.cursor = 'grab';
    }
  }
  
  // 处理鼠标滚轮缩放
  public handleWheel(e: WheelEvent): void {
    e.preventDefault();
    
    if (!this.canvas) return;
    
    const zoomIntensity = 0.1;
    const wheel = e.deltaY < 0 ? 1 : -1;
    const zoom = Math.exp(wheel * zoomIntensity);
    
    // 计算以鼠标位置为中心的缩放
    const rect = this.canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    const newScale = this.mapState.scale * zoom;
    if (newScale < 0.5 || newScale > 5) return; // 限制缩放范围
    
    this.mapState.offsetX = mouseX - (mouseX - this.mapState.offsetX) * zoom;
    this.mapState.offsetY = mouseY - (mouseY - this.mapState.offsetY) * zoom;
    this.mapState.scale *= zoom;
    
    this.drawMap();
  }

  /**
   * 聚焦到指定设备
   * @param deviceId 设备ID
   */
  public focusOnDevice(deviceId: string): void {
    const device = this.devices.find(d => d.id === deviceId);
    
    if (!device || !this.canvas || !this.ctx) return;
    
    // 获取设备位置 - 直接使用设备的位置坐标，与点击检测逻辑保持一致
    const x = device.position.x;
    const y = device.position.y;
    
    console.log('聚焦到设备:', device.id, device.name, '位置:', x, y);
    
    // 重置缩放和偏移
    this.mapState.scale = 1.5;
    
    // 计算居中偏移量
    this.mapState.offsetX = this.canvas.width / 2 - x * this.mapState.scale;
    this.mapState.offsetY = this.canvas.height / 2 - y * this.mapState.scale;
    
    // 绘制地图
    this.drawMap();
    
    // 高亮动画效果
    this.highlightDevice(device);
  }
  
  /**
   * 高亮显示设备
   * @param device 设备信息
   */
  private highlightDevice(device: DeviceInfo): void {
    if (!this.ctx || !this.canvas) return;
    
    // 设备位置 - 直接使用设备的位置坐标，与点击检测逻辑保持一致
    const x = device.position.x;
    const y = device.position.y;
    
    // 记录高亮设备操作
    console.log('高亮显示设备:', device.id, device.name, '位置:', x, y);
    
    // 创建高亮动画
    let radius = 10;
    let opacity = 0.8;
    let frame = 0;
    
    const animate = () => {
      if (!this.ctx || frame >= 30) return;
      
      // 在原始绘制上叠加高亮效果
      this.drawMap();
      
      // 绘制高亮圆圈
      this.ctx.save();
      this.ctx.translate(this.mapState.offsetX, this.mapState.offsetY);
      this.ctx.scale(this.mapState.scale, this.mapState.scale);
      
      this.ctx.beginPath();
      this.ctx.arc(x, y, radius, 0, Math.PI * 2);
      this.ctx.fillStyle = `rgba(33, 150, 243, ${opacity})`;
      this.ctx.fill();
      this.ctx.restore();
      
      // 更新动画参数
      radius += 1;
      opacity -= 0.02;
      frame++;
      
      // 继续动画
      requestAnimationFrame(animate);
    };
    
    // 开始动画
    animate();
  }
}

// 导出单例实例
export const mapService = new MapService(); 