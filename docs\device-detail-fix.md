# 设备详情页数据获取异常修复

## 问题描述

用户反馈设备详情页的各数据获取异常，无法正常显示机器狗的IMU数据、连接状态等信息。

## 问题分析

通过代码分析发现以下问题：

### 1. 缺少机器狗API接口文件
- **问题**: 前端项目中没有机器狗相关的API接口定义
- **影响**: 无法调用后端API获取机器狗数据
- **位置**: `src/api/` 目录下缺少机器狗API文件

### 2. useIMUData中API调用未实现
- **问题**: `useIMUData.ts` 中的数据轮询逻辑只有注释，没有实际的API调用
- **代码位置**: `src/views/MonitoringCenter/DeviceTracking/composables/useIMUData.ts:202-205`
- **问题代码**:
  ```typescript
  // 这里应该调用后端API获取IMU数据
  // const response = await dogApi.getIMUData()
  // processIMUData(response.data)
  console.log('轮询IMU数据 - 需要实现后端API调用')
  ```

### 3. 数据处理函数不匹配
- **问题**: 现有的数据处理函数是为WebRTC数据格式设计的，与API返回的数据格式不匹配
- **影响**: 即使API调用成功，数据也无法正确解析和显示

### 4. 连接状态检查缺失
- **问题**: 设备详情页打开时没有检查机器狗的连接状态
- **影响**: 可能在机器狗未连接时尝试获取数据，导致失败

## 修复方案

### 1. 创建机器狗API接口文件

创建 `src/api/robotDog.ts` 文件，包含以下接口：

```typescript
export const robotDogApi = {
  // 连接管理
  connect(): Promise<ConnectResponse>
  disconnect(): Promise<DisconnectResponse>
  reconnect(): Promise<ConnectResponse>
  getConnectionStatus(): Promise<ConnectionStatus>
  getSystemInfo(): Promise<SystemInfo>
  
  // 运动控制
  movement(params: MoveParams): Promise<MoveResponse>
  stopMovement(): Promise<CommandResponse>
  emergencyStop(): Promise<CommandResponse>
  
  // 数据获取
  getIMUData(): Promise<IMUData>
  getIMURPY(): Promise<Record<string, any>>
  getMotorStatus(): Promise<Record<string, any>>
  getBatteryStatus(): Promise<Record<string, any>>
}
```

### 2. 修复useIMUData数据获取逻辑

#### 添加API导入
```typescript
import { robotDogApi } from '@/api/robotDog'
```

#### 实现数据轮询
```typescript
pollingTimer.value = window.setInterval(async () => {
  try {
    // 调用后端API获取IMU数据
    const imuData = await robotDogApi.getIMUData()
    
    // 处理接收到的IMU数据
    if (imuData) {
      processIMUData(imuData)
      console.log('✅ IMU数据获取成功:', imuData)
    }
  } catch (error) {
    console.error('获取IMU数据失败:', error)
    stats.errorCount++
    
    // 连续失败处理
    if (stats.errorCount > 10) {
      await stopPolling()
      ElMessage.error('IMU数据获取失败次数过多，已停止轮询')
    }
  }
}, 1000) // 1秒轮询间隔
```

#### 新增API数据处理函数
```typescript
const processIMUData = (apiData: any): void => {
  // 解析API返回的数据格式
  // 转换为前端需要的IMUData格式
  // 更新currentData和dataHistory
  // 更新统计信息
}
```

### 3. 增强设备详情页连接检查

#### 添加连接状态检查
```typescript
const checkConnectionStatus = async () => {
  try {
    const status = await robotDogApi.getConnectionStatus()
    console.log('机器狗连接状态:', status)
    
    if (!status.connected) {
      ElMessage.warning('机器狗未连接，正在尝试连接...')
      await robotDogApi.connect()
    }
  } catch (error) {
    console.error('检查连接状态失败:', error)
    ElMessage.error('无法获取机器狗连接状态')
  }
}
```

#### 修改启动逻辑
```typescript
const startIMUSubscription = async () => {
  try {
    // 首先检查机器狗连接状态
    await checkConnectionStatus()
    
    // 开始IMU数据轮询
    const success = await startPolling()
    if (success) {
      emit('connection-status-change', connectionStatus.value)
    }
  } catch (error) {
    console.error('启动IMU订阅失败:', error)
    ElMessage.error('启动数据获取失败')
  }
}
```

## 修复效果

### 修复前
- ❌ 设备详情页无法获取IMU数据
- ❌ 连接状态显示异常
- ❌ 数据统计信息为空
- ❌ 控制台显示"需要实现后端API调用"

### 修复后
- ✅ 正常获取和显示IMU数据
- ✅ 正确显示连接状态
- ✅ 实时更新数据统计信息
- ✅ 自动检查和建立连接
- ✅ 完整的错误处理和用户提示

## 测试验证

### 测试步骤
1. 启动后端服务，确保机器狗API正常工作
2. 打开前端应用，进入设备跟踪页面
3. 点击机器狗设备，打开设备详情弹窗
4. 观察以下内容：
   - 连接状态指示器应显示正确状态
   - IMU数据应开始实时更新
   - 数据统计信息应正常显示
   - 姿态角度、电池状态等数据应正确显示

### 预期结果
- 设备详情页能正常显示所有数据
- 数据更新频率约为1Hz（每秒1次）
- 连接异常时有相应的错误提示
- 数据质量评分正常计算和显示

## 相关文件

### 新增文件
- `src/api/robotDog.ts` - 机器狗API接口定义

### 修改文件
- `src/views/MonitoringCenter/DeviceTracking/composables/useIMUData.ts` - 数据获取逻辑
- `src/views/MonitoringCenter/DeviceTracking/components/DeviceDetailDialog.vue` - 设备详情页

### 后端API依赖
- `GET /dog/imu` - 获取IMU数据
- `GET /dog/status` - 获取连接状态
- `POST /dog/connect` - 连接机器狗

## 注意事项

1. **轮询频率**: 设置为1秒间隔，避免过于频繁的API调用
2. **错误处理**: 连续失败10次后自动停止轮询，防止无效请求
3. **连接检查**: 每次打开详情页都会检查连接状态
4. **数据格式**: API返回的数据格式与WebRTC格式不同，需要专门的处理函数

## 后续优化建议

1. **WebSocket替代**: 考虑使用WebSocket替代HTTP轮询，提高实时性
2. **缓存机制**: 添加数据缓存，减少重复请求
3. **离线处理**: 添加离线状态检测和处理
4. **性能监控**: 添加API调用性能监控和统计
