<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <style>
    .main-body { fill: #3b82f6; }
    .head { fill: #60a5fa; }
    .eye { fill: #10b981; }
    .leg { fill: #1e3a8a; }
    .sensor { fill: #f59e0b; }
    .highlight { fill: #ffffff; opacity: 0.3; }
  </style>
  
  <!-- Body -->
  <rect class="main-body" x="50" y="70" width="100" height="50" rx="10" />
  
  <!-- Head -->
  <rect class="head" x="130" y="60" width="40" height="30" rx="8" />
  
  <!-- Eye -->
  <circle class="eye" cx="155" cy="75" r="5" />
  <circle class="highlight" cx="153" cy="73" r="2" />
  
  <!-- Legs -->
  <rect class="leg" x="60" y="120" width="10" height="30" rx="3" />
  <rect class="leg" x="90" y="120" width="10" height="30" rx="3" />
  <rect class="leg" x="120" y="120" width="10" height="30" rx="3" />
  <rect class="leg" x="150" y="120" width="10" height="30" rx="3" />
  
  <!-- Sensors -->
  <circle class="sensor" cx="70" cy="85" r="8" />
  <circle class="highlight" cx="68" cy="83" r="3" />
  
  <!-- Antenna -->
  <line x1="140" y1="60" x2="140" y2="40" stroke="#60a5fa" stroke-width="3" />
  <circle class="sensor" cx="140" cy="35" r="5" />
</svg> 