<template>
  <div class="plant-monitor-container">
    <div class="plant-inner">
      <!-- 扫描框架 -->
      <div class="scan-frame">
        <div class="scan-corner top-left"></div>
        <div class="scan-corner top-right"></div>
        <div class="scan-corner bottom-left"></div>
        <div class="scan-corner bottom-right"></div>
        <div class="scan-border top"></div>
        <div class="scan-border right"></div>
        <div class="scan-border bottom"></div>
        <div class="scan-border left"></div>
      </div>
      
      <!-- 扫描线和扫描效果 -->
      <div class="scan-effect">
        <div class="scan-line horizontal"></div>
        <div class="scan-line vertical"></div>
        <div class="scan-overlay"></div>
      </div>
      
      <!-- 数据连接线和指示点 -->
      <div class="data-connections">
        <div class="data-point" v-for="(point, index) in dataPoints" :key="index"
          :style="{ 
            left: `${point.x}%`, 
            top: `${point.y}%`,
            animationDelay: `${index * 0.5}s`
          }">
          <div class="point-pulse"></div>
          <div class="data-tooltip">
            <div class="tooltip-header">{{ point.label }}</div>
            <div class="tooltip-value">{{ point.value }}</div>
          </div>
        </div>
        
        <!-- 数据连接线 -->
        <svg class="connection-lines" viewBox="0 0 300 300" preserveAspectRatio="xMidYMid meet">
          <line 
            v-for="(line, index) in connectionLines" 
            :key="`line-${index}`"
            :x1="line.x1" 
            :y1="line.y1" 
            :x2="line.x2" 
            :y2="line.y2" 
            stroke="rgba(0, 255, 170, 0.6)" 
            stroke-width="1"
            stroke-dasharray="5,3"
            :class="`animate-line-${index + 1}`"
          />
        </svg>
      </div>
      
      <!-- 中心植物图标 -->
      <div class="plant-image-wrapper" ref="plantRef">
        <div class="tech-circle"></div>
        <img :src="plantImageUrl" alt="Plant" class="plant-image" />
        
        <!-- 科技装饰环 -->
        <div class="tech-rings">
          <div class="tech-ring ring-1"></div>
          <div class="tech-ring ring-2"></div>
          <div class="tech-ring ring-3"></div>
        </div>
        
        <!-- 扫描线 -->
        <div class="plant-scan-line"></div>
      </div>
      
      <!-- 状态指示器 -->
      <div class="monitor-status">
        <div class="status-icon" :class="statusIcon"></div>
        <span class="status-text">{{ monitorStatus }}</span>
      </div>
      
      <!-- 数据读数 -->
      <div class="data-readings">
        <div class="reading-item" v-for="(reading, index) in readings" :key="index">
          <div class="reading-label">{{ reading.label }}</div>
          <div class="reading-value">{{ reading.value }}</div>
          <div class="reading-bar-container">
            <div class="reading-bar" :style="{ width: `${reading.percentage}%`, backgroundColor: reading.color }"></div>
          </div>
        </div>
      </div>
      
      <!-- 数字化装饰 -->
      <div class="digital-decorations">
        <div class="binary-stream">{{ binaryStream }}</div>
        <div class="hex-codes">{{ hexCodes }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, reactive } from 'vue'
import gsap from 'gsap'

interface DataPoint {
  x: number
  y: number
  label: string
  value: string
}

interface ConnectionLine {
  x1: number
  y1: number
  x2: number
  y2: number
}

interface Reading {
  label: string
  value: string
  percentage: number
  color: string
}

interface Props {
  monitorStatus?: string
  plantImageUrl?: string
  statusType?: 'normal' | 'warning' | 'error' | 'success'
}

const props = withDefaults(defineProps<Props>(), {
  monitorStatus: '健康监测中...',
  plantImageUrl: '/src/assets/logo.svg',
  statusType: 'normal'
})

// 数据点位置
const dataPoints = ref<DataPoint[]>([
  { x: 20, y: 30, label: '叶片健康度', value: '95%' },
  { x: 80, y: 35, label: '光照水平', value: '适中' },
  { x: 25, y: 75, label: '土壤湿度', value: '68%' },
  { x: 75, y: 70, label: '虫害风险', value: '低' },
])

// 连接线
const connectionLines = computed<ConnectionLine[]>(() => {
  return dataPoints.value.map(point => {
    return {
      x1: 150, // 中心点x
      y1: 150, // 中心点y
      x2: point.x * 3, // 转换百分比为坐标
      y2: point.y * 3
    }
  })
})

// 状态图标类
const statusIcon = computed(() => {
  return {
    'status-normal': props.statusType === 'normal',
    'status-warning': props.statusType === 'warning',
    'status-error': props.statusType === 'error',
    'status-success': props.statusType === 'success'
  }
})

// 数据读数
const readings = ref<Reading[]>([
  { label: '生长指数', value: '78/100', percentage: 78, color: '#00ffaa' },
  { label: '水分含量', value: '65%', percentage: 65, color: '#1890ff' },
  { label: '营养水平', value: '82%', percentage: 82, color: '#00ffaa' },
  { label: '抗病性', value: '90%', percentage: 90, color: '#00ffaa' }
])

// 二进制流和十六进制代码（装饰用）
const binaryStream = ref('')
const hexCodes = ref('')

// 生成随机二进制流
const generateBinaryStream = () => {
  let result = ''
  for (let i = 0; i < 32; i++) {
    result += Math.random() > 0.5 ? '1' : '0'
  }
  binaryStream.value = result
}

// 生成随机十六进制代码
const generateHexCodes = () => {
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += Math.floor(Math.random() * 16).toString(16).toUpperCase()
  }
  hexCodes.value = result
}

// 引用植物容器元素
const plantRef = ref<HTMLElement | null>(null)

// 植物动画
let plantAnimation: gsap.core.Timeline | null = null
let decorationTimer: number | null = null

// 设置植物动画
const setupPlantAnimation = () => {
  if (!plantRef.value) return
  
  // 创建动画时间线
  plantAnimation = gsap.timeline({
    repeat: -1,
    yoyo: true
  })
  
  // 添加浮动动画
  plantAnimation
    .to(plantRef.value, {
      y: -15,
      duration: 3,
      ease: 'power1.inOut'
    })
    .to(plantRef.value, {
      y: 0,
      duration: 3,
      ease: 'power1.inOut'
    })
    
  // 添加旋转环动画
  const rings = plantRef.value.querySelectorAll('.tech-ring')
  gsap.to(rings[0], {
    rotation: 360,
    transformOrigin: 'center center',
    duration: 30,
    repeat: -1,
    ease: 'none'
  })
  
  gsap.to(rings[1], {
    rotation: -360,
    transformOrigin: 'center center',
    duration: 50,
    repeat: -1,
    ease: 'none'
  })
  
  gsap.to(rings[2], {
    rotation: 360,
    transformOrigin: 'center center',
    duration: 70,
    repeat: -1,
    ease: 'none'
  })
  
  // 添加科技圆环呼吸效果
  gsap.to(plantRef.value.querySelector('.tech-circle'), {
    scale: 1.1,
    opacity: 0.8,
    duration: 2,
    repeat: -1,
    yoyo: true,
    ease: 'sine.inOut'
  })
  
  // 扫描线动画
  gsap.to('.plant-scan-line', {
    top: '100%',
    duration: 2,
    repeat: -1,
    ease: 'power1.inOut',
    yoyo: true
  })
  
  // 水平扫描线动画
  gsap.to('.scan-line.horizontal', {
    top: '100%',
    duration: 3,
    repeat: -1,
    ease: 'power1.inOut',
    yoyo: true
  })
  
  // 垂直扫描线动画
  gsap.to('.scan-line.vertical', {
    left: '100%',
    duration: 4,
    repeat: -1,
    ease: 'power1.inOut',
    yoyo: true
  })
}

// 监听状态变化
watch(() => props.monitorStatus, (newValue) => {
  if (plantRef.value) {
    // 状态变化时添加一个脉冲效果
    gsap.to(plantRef.value, {
      scale: 1.05,
      duration: 0.3,
      yoyo: true,
      repeat: 1,
      ease: 'power1.inOut'
    })
  }
})

// 更新装饰数据
const updateDecorations = () => {
  generateBinaryStream()
  generateHexCodes()
}

// 组件挂载时
onMounted(() => {
  setupPlantAnimation()
  updateDecorations()
  
  // 定期更新装饰数据
  decorationTimer = window.setInterval(updateDecorations, 2000)
})

// 组件卸载前清理
onBeforeUnmount(() => {
  if (plantAnimation) {
    plantAnimation.kill()
    plantAnimation = null
  }
  
  if (decorationTimer) {
    clearInterval(decorationTimer)
    decorationTimer = null
  }
})
</script>

<style lang="scss" scoped>
.plant-monitor-container {
  width: 100%;
  height: 300px;
  position: relative;
  margin: 0 auto 30px;
  
  .plant-inner {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 10, 30, 0.3);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(0, 255, 170, 0.2);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2) inset;
  }
  
  // 扫描框架
  .scan-frame {
    position: absolute;
    inset: 0;
    pointer-events: none;
    z-index: 5;
    
    .scan-corner {
      position: absolute;
      width: 20px;
      height: 20px;
      border-style: solid;
      border-color: rgba(0, 255, 170, 0.8);
      
      &.top-left {
        top: 10px;
        left: 10px;
        border-width: 2px 0 0 2px;
      }
      
      &.top-right {
        top: 10px;
        right: 10px;
        border-width: 2px 2px 0 0;
      }
      
      &.bottom-left {
        bottom: 10px;
        left: 10px;
        border-width: 0 0 2px 2px;
      }
      
      &.bottom-right {
        bottom: 10px;
        right: 10px;
        border-width: 0 2px 2px 0;
      }
    }
    
    .scan-border {
      position: absolute;
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(0, 255, 170, 0.5) 50%, 
        transparent 100%
      );
      
      &.top, &.bottom {
        height: 1px;
        width: calc(100% - 60px);
        left: 30px;
      }
      
      &.left, &.right {
        width: 1px;
        height: calc(100% - 60px);
        top: 30px;
      }
      
      &.top {
        top: 10px;
      }
      
      &.bottom {
        bottom: 10px;
      }
      
      &.left {
        left: 10px;
      }
      
      &.right {
        right: 10px;
      }
    }
  }
  
  // 扫描效果
  .scan-effect {
    position: absolute;
    inset: 0;
    pointer-events: none;
    z-index: 2;
    
    .scan-line {
      position: absolute;
      background: rgba(0, 255, 170, 0.3);
      box-shadow: 0 0 10px rgba(0, 255, 170, 0.5);
      
      &.horizontal {
        height: 1px;
        width: 100%;
        top: 0;
      }
      
      &.vertical {
        width: 1px;
        height: 100%;
        left: 0;
      }
    }
    
    .scan-overlay {
      position: absolute;
      inset: 0;
      background: radial-gradient(
        circle at center,
        transparent 30%,
        rgba(0, 10, 30, 0.2) 70%
      );
    }
  }
  
  // 数据连接点
  .data-connections {
    position: absolute;
    inset: 0;
    z-index: 10;
    
    .data-point {
      position: absolute;
      width: 10px;
      height: 10px;
      background-color: #00ffaa;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
      box-shadow: 0 0 10px rgba(0, 255, 170, 0.7);
      z-index: 2;
      animation: dataPulse 2s infinite;
      
      .point-pulse {
        position: absolute;
        inset: -5px;
        border-radius: 50%;
        background: rgba(0, 255, 170, 0.3);
        animation: pointPulse 2s infinite;
      }
      
      .data-tooltip {
        position: absolute;
        background: rgba(0, 25, 75, 0.9);
        color: #fff;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
        pointer-events: none;
        border: 1px solid rgba(0, 255, 170, 0.4);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        z-index: 100;
        min-width: 120px;
        
        &::after {
          content: '';
          position: absolute;
          width: 8px;
          height: 8px;
          background: rgba(0, 25, 75, 0.9);
          transform: rotate(45deg);
          z-index: -1;
          border: 1px solid rgba(0, 255, 170, 0.4);
          border-top: none;
          border-left: none;
        }
        
        .tooltip-header {
          color: rgba(255, 255, 255, 0.7);
          font-size: 11px;
          margin-bottom: 4px;
        }
        
        .tooltip-value {
          color: #00ffaa;
          font-size: 14px;
          font-weight: 500;
        }
      }
      
      &:nth-child(1) .data-tooltip {
        top: -60px;
        left: 50%;
        transform: translateX(-50%) translateY(-5px);
        
        &::after {
          top: 100%;
          left: 50%;
          margin-left: -4px;
          margin-top: -4px;
        }
      }
      
      &:nth-child(2) .data-tooltip {
        top: 50%;
        right: -5px;
        transform: translateY(-50%) translateX(-5px);
        
        &::after {
          top: 50%;
          left: -4px;
          margin-top: -4px;
        }
      }
      
      &:nth-child(3) .data-tooltip {
        bottom: -60px;
        left: 50%;
        transform: translateX(-50%) translateY(5px);
        
        &::after {
          bottom: 100%;
          left: 50%;
          margin-left: -4px;
          margin-bottom: -4px;
        }
      }
      
      &:nth-child(4) .data-tooltip {
        top: 50%;
        left: -5px;
        transform: translateY(-50%) translateX(5px);
        
        &::after {
          top: 50%;
          right: -4px;
          margin-top: -4px;
        }
      }
      
      &:hover {
        z-index: 3;
        
        .data-tooltip {
          opacity: 1;
          transform: translateY(0);
        }
      }
    }
    
    .connection-lines {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
      
      line {
        stroke-dashoffset: 100;
        animation: dashOffset 3s linear infinite;
        
        &.animate-line-1 {
          animation-delay: 0s;
        }
        
        &.animate-line-2 {
          animation-delay: 0.5s;
        }
        
        &.animate-line-3 {
          animation-delay: 1s;
        }
        
        &.animate-line-4 {
          animation-delay: 1.5s;
        }
      }
    }
  }
  
  // 植物图像
  .plant-image-wrapper {
    position: relative;
    width: 160px;
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
    
    .tech-circle {
      position: absolute;
      width: 140px;
      height: 140px;
      border-radius: 50%;
      background: radial-gradient(
        circle at center,
        rgba(0, 255, 170, 0.2) 0%,
        rgba(24, 144, 255, 0.1) 60%,
        transparent 80%
      );
    }
    
    .plant-image {
      width: 120px;
      height: 120px;
      object-fit: contain;
      z-index: 2;
      filter: drop-shadow(0 0 20px rgba(0, 255, 170, 0.5));
    }
    
    .tech-rings {
      position: absolute;
      inset: 0;
      z-index: 1;
      
      .tech-ring {
        position: absolute;
        border-radius: 50%;
        border-style: solid;
        border-color: rgba(0, 255, 170, 0.3);
        box-shadow: 0 0 20px rgba(0, 255, 170, 0.1);
        
        &.ring-1 {
          inset: -10px;
          border-width: 1px;
          border-style: dashed;
        }
        
        &.ring-2 {
          inset: -25px;
          border-width: 2px;
          border-style: dotted;
        }
        
        &.ring-3 {
          inset: -40px;
          border-width: 1px;
          border-top-color: rgba(24, 144, 255, 0.3);
          border-right-color: rgba(0, 255, 170, 0.3);
          border-bottom-color: rgba(24, 144, 255, 0.3);
          border-left-color: rgba(0, 255, 170, 0.3);
        }
      }
    }
    
    .plant-scan-line {
      position: absolute;
      width: 100%;
      height: 2px;
      background: linear-gradient(to right, 
        transparent 0%, 
        rgba(0, 255, 170, 0.5) 20%, 
        rgba(0, 255, 170, 0.8) 50%,
        rgba(0, 255, 170, 0.5) 80%,
        transparent 100%
      );
      box-shadow: 0 0 10px rgba(0, 255, 170, 0.5);
      top: 0;
      left: 0;
    }
  }
  
  // 监测状态指示
  .monitor-status {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 21, 65, 0.8);
    padding: 8px 16px 8px 35px;
    border-radius: 30px;
    border: 1px solid rgba(0, 255, 170, 0.3);
    display: flex;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    z-index: 10;
    
    .status-icon {
      position: absolute;
      left: 12px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      
      &.status-normal {
        background-color: #1890ff;
        box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
        animation: statusPulse 1.5s infinite;
      }
      
      &.status-warning {
        background-color: #faad14;
        box-shadow: 0 0 10px rgba(250, 173, 20, 0.5);
        animation: statusPulse 0.8s infinite;
      }
      
      &.status-error {
        background-color: #ff4d4f;
        box-shadow: 0 0 10px rgba(255, 77, 79, 0.5);
        animation: statusPulse 0.5s infinite;
      }
      
      &.status-success {
        background-color: #52c41a;
        box-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
        animation: statusPulse 2s infinite;
      }
    }
    
    .status-text {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
    }
  }
  
  // 数据读数
  .data-readings {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 200px;
    z-index: 5;
    
    .reading-item {
      margin-bottom: 10px;
      background: rgba(0, 21, 65, 0.6);
      padding: 8px 10px;
      border-radius: 4px;
      border: 1px solid rgba(0, 255, 170, 0.2);
      
      .reading-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 4px;
      }
      
      .reading-value {
        font-size: 14px;
        color: #00ffaa;
        font-weight: 500;
        margin-bottom: 6px;
      }
      
      .reading-bar-container {
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
        
        .reading-bar {
          height: 100%;
          border-radius: 2px;
          transition: width 0.5s ease;
        }
      }
    }
  }
  
  // 数字化装饰
  .digital-decorations {
    position: absolute;
    bottom: 15px;
    left: 15px;
    z-index: 5;
    font-family: monospace;
    
    .binary-stream {
      font-size: 12px;
      color: rgba(0, 255, 170, 0.7);
      margin-bottom: 5px;
      letter-spacing: 2px;
    }
    
    .hex-codes {
      font-size: 12px;
      color: rgba(24, 144, 255, 0.7);
      letter-spacing: 2px;
    }
  }
}

// 数据点脉冲动画
@keyframes dataPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 170, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(0, 255, 170, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 170, 0);
  }
}

// 点脉冲动画
@keyframes pointPulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// 状态指示器脉冲
@keyframes statusPulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// 虚线偏移动画
@keyframes dashOffset {
  from {
    stroke-dashoffset: 40;
  }
  to {
    stroke-dashoffset: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .plant-monitor-container {
    height: 400px;
    
    .plant-inner {
      flex-direction: column;
    }
    
    .plant-image-wrapper {
      width: 140px;
      height: 140px;
      margin-bottom: 20px;
      
      .tech-circle {
        width: 120px;
        height: 120px;
      }
      
      .plant-image {
        width: 100px;
        height: 100px;
      }
      
      .tech-rings {
        .tech-ring {
          &.ring-1 {
            inset: -8px;
          }
          
          &.ring-2 {
            inset: -20px;
          }
          
          &.ring-3 {
            inset: -30px;
          }
        }
      }
    }
    
    .data-connections {
      .data-point {
        width: 8px;
        height: 8px;
        
        .data-tooltip {
          font-size: 10px;
          padding: 4px 8px;
        }
      }
    }
    
    .data-readings {
      position: relative;
      top: auto;
      right: auto;
      width: 90%;
      margin: 0 auto;
    }
    
    .digital-decorations {
      display: none;
    }
  }
}
</style>