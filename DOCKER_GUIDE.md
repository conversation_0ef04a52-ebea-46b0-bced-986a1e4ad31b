# 智慧农业前端 Docker 开发环境使用指南

## 📋 概述

本指南将帮助你使用 Docker 快速搭建智慧农业前端项目的开发环境。配置包含了 Vue 3 + TypeScript + Vite 的完整开发环境，支持热重载和实时代码更新。

## 🛠️ 前置要求

确保你的系统已安装以下软件：

- **Docker Desktop** (Windows/Mac) 或 **Docker Engine** (Linux)
- **Docker Compose** (通常随 Docker Desktop 一起安装)

### 验证安装
```bash
# 检查 Docker 版本
docker --version

# 检查 Docker Compose 版本
docker-compose --version
```

## 🚀 快速启动

### 1. 启动开发环境
在项目根目录执行：

```bash
# 构建并启动容器（首次运行）
docker-compose up --build

# 后续启动（无需重新构建）
docker-compose up

# 后台运行
docker-compose up -d
```

### 2. 访问应用
启动成功后，在浏览器中访问：
- **开发服务器**: http://localhost:5174
- **网络访问**: http://你的IP地址:5174

### 3. 停止服务
```bash
# 停止容器
docker-compose down

# 停止并删除卷（清理所有数据）
docker-compose down -v
```

## 📁 配置文件说明

### Dockerfile
- **基础镜像**: Node.js 22 Alpine（轻量级）
- **系统依赖**: 包含编译某些 npm 包所需的工具
- **安全配置**: 使用非 root 用户运行
- **端口**: 暴露 5174 端口

### docker-compose.yml
- **服务名**: smart-agriculture-frontend
- **端口映射**: 5174:5174
- **卷挂载**: 支持代码热重载
- **环境变量**: 自动加载 .env.development
- **网络**: 独立的开发网络

### .dockerignore
- 排除 node_modules、构建文件等
- 优化构建速度和镜像大小
- 保护敏感配置文件

## 🔧 常用命令

### 容器管理
```bash
# 查看运行中的容器
docker-compose ps

# 查看容器日志
docker-compose logs

# 实时查看日志
docker-compose logs -f

# 进入容器内部
docker-compose exec smart-agriculture-frontend sh

# 重启服务
docker-compose restart
```

### 依赖管理
```bash
# 安装新依赖（在容器内）
docker-compose exec smart-agriculture-frontend npm install 包名

# 重新安装所有依赖
docker-compose exec smart-agriculture-frontend npm install

# 清理并重新安装
docker-compose down
docker-compose up --build
```

## 🐛 常见问题解决

### 问题1: 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :5174

# 修改 docker-compose.yml 中的端口映射
ports:
  - "5175:5174"  # 改为其他端口
```

### 问题2: 热重载不工作
确保 docker-compose.yml 中包含以下环境变量：
```yaml
environment:
  - CHOKIDAR_USEPOLLING=true
  - WATCHPACK_POLLING=true
```

### 问题3: 依赖安装失败
```bash
# 清理并重新构建
docker-compose down
docker system prune -f
docker-compose up --build --no-cache
```

### 问题4: 权限问题（Linux/Mac）
```bash
# 修复文件权限
sudo chown -R $USER:$USER .
```

## 🔄 开发工作流

### 日常开发
1. 启动容器：`docker-compose up`
2. 编辑代码：在宿主机上正常编辑
3. 查看效果：浏览器自动刷新
4. 停止开发：`Ctrl+C` 或 `docker-compose down`

### 添加新依赖
```bash
# 方法1: 在容器内安装
docker-compose exec smart-agriculture-frontend npm install 新包名

# 方法2: 修改 package.json 后重新构建
docker-compose up --build
```

## 📊 性能优化建议

1. **使用 .dockerignore**: 已配置，减少构建上下文
2. **卷缓存**: node_modules 使用独立卷，提高性能
3. **多阶段构建**: 当前为开发环境，生产环境可考虑多阶段构建
4. **镜像缓存**: 合理安排 Dockerfile 层级，利用缓存

## 🔗 相关链接

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [Vite 官方文档](https://vitejs.dev/)
- [Vue 3 官方文档](https://vuejs.org/)

## 📞 技术支持

如遇到问题，请检查：
1. Docker 和 Docker Compose 版本
2. 端口是否被占用
3. 防火墙设置
4. 系统资源（内存、磁盘空间）

---

**祝你开发愉快！** 🎉
