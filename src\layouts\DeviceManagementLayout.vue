<template>
  <BaseLayout
    title="智能设备管理舱"
    theme="deviceManagement"
    themeColor="#10b981"
    moduleIcon="Monitor"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="设备状态看板"
        icon="Monitor"
        route="/device-management/device-status"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="机器狗多传感器数据流"
        icon="Connection"
        route="/device-management/robot-dog-sensors"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="无人机药箱余量监测"
        icon="SetUp"
        route="/device-management/drone-medicine"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="超声波强度实时调节面板"
        icon="DataLine"
        route="/device-management/ultrasonic-control"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="捕虫灯工作模式切换"
        icon="Lightning"
        route="/device-management/insect-trap-mode"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="固件远程升级系统"
        icon="Service"
        route="/device-management/firmware-upgrade"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
    </template>

    <template #header-actions>
      <div class="device-status">
        <div class="status-badge online">
          <span class="dot online"></span>
          <span>在线: 34</span>
        </div>
        <div class="status-badge offline">
          <span class="dot offline"></span>
          <span>离线: 3</span>
        </div>
        <div class="status-badge warning">
          <span class="dot warning"></span>
          <span>告警: 2</span>
        </div>
      </div>

      <TechActionButton
        type="warning"
        size="small"
        icon="Bell"
        text="告警中心"
        @click="openAlertCenter"
      />
    </template>

    <router-view />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Monitor,
  Connection,
  SetUp,
  DataLine,
  Lightning,
  Service,
  HomeFilled,
  Bell
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'
import { getModuleTheme } from './components/layoutConfig'

const route = useRoute()
const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 打开告警中心
const openAlertCenter = () => {
  ElMessage({
    message: '告警中心：正在加载设备告警数据...',
    type: 'warning',
    duration: 4000
  })

  // 这里可以添加实际的告警中心逻辑
  setTimeout(() => {
    router.push('/device-management/alert-center')
  }, 500)
}

// 组件挂载
onMounted(() => {
  // 监听BaseLayout事件
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<style scoped>
.device-status {
  display: flex;
  gap: 15px;
  margin-right: 15px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(16, 185, 129, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.2);
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.dot.online {
  background-color: #10b981;
  box-shadow: 0 0 5px #10b981;
}

.dot.offline {
  background-color: #6b7280;
  box-shadow: 0 0 5px #6b7280;
}

.dot.warning {
  background-color: #f59e0b;
  box-shadow: 0 0 5px #f59e0b;
}

@media (max-width: 1024px) {
  .device-status {
    gap: 10px;
  }

  .status-badge {
    padding: 4px 8px;
  }
}

@media (max-width: 768px) {
  .device-status {
    display: none;
  }
}
</style>
