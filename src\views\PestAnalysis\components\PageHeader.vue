<!-- 
  PageHeader.vue
  通用页面标题组件，用于各个模块的顶部标题区域
  支持标题、副标题、图标和操作按钮
-->
<template>
  <div class="page-header">
    <div class="title-container">
      <div class="icon-container" v-if="icon">
        <el-icon class="page-icon"><component :is="icon" /></el-icon>
      </div>
      <div class="text-container">
        <h2 class="page-title">{{ title }}</h2>
        <p v-if="description" class="description">{{ description }}</p>
      </div>
    </div>
    
    <div class="actions-container" v-if="$slots.actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #3b4863;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.icon-container {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(145deg, #1f2937, #374151);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.page-icon {
  font-size: 24px;
  color: #3b82f6;
}

.text-container {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #e5e7eb;
  line-height: 1.2;
}

.description {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #9ca3af;
}

.actions-container {
  display: flex;
  gap: 12px;
  align-items: center;
}
</style> 