/**
 * 地图交互的可复用逻辑
 */
import { ref, onMounted, onUnmounted } from 'vue';
import { mapService } from '../services/mapService';
import type { FarmlandArea, DeviceInfo } from '../types';

// 添加onDeviceSelected参数，用于处理设备选择
export function useMapInteraction(canvasId: string, onDeviceSelected?: (deviceId: string) => void) {
  // 视图模式控制
  const viewMode = ref(50);
  
  // 农田区域数据
  const farmlandArea: FarmlandArea = {
    x: 150,
    y: 100,
    width: 500,
    height: 400
  };
  
  // 初始化地图
  const initMap = async (devices: DeviceInfo[]) => {
    try {
      // 初始化Canvas
      await mapService.initCanvas(canvasId);
      
      // 设置农田区域数据
      mapService.setFarmlandArea(farmlandArea);
      
      // 设置设备数据
      mapService.setDevices(devices);
      
      // 添加事件监听
      setupEventListeners();
      
      // 初始绘制
      mapService.drawMap();
    } catch (error) {
      console.error('地图初始化失败:', error);
    }
  };
  
  // 设备选择事件处理函数
  const handleDeviceSelected = ((e: CustomEvent) => {
    const { deviceId } = e.detail;
    console.log('设备选择事件被触发:', deviceId);
    if (onDeviceSelected) {
      onDeviceSelected(deviceId);
    }
  }) as EventListener;
  
  // 设置事件监听
  const setupEventListeners = () => {
    const canvas = document.getElementById(canvasId) as HTMLCanvasElement;
    if (!canvas) return;
    
    // 鼠标拖动事件
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseLeave);
    
    // 鼠标滚轮缩放
    canvas.addEventListener('wheel', handleWheel);
    
    // 添加设备选择事件监听
    canvas.addEventListener('device-selected', handleDeviceSelected);
    
    // 窗口大小变化时调整Canvas尺寸
    window.addEventListener('resize', handleResize);
  };
  
  // 移除事件监听
  const removeEventListeners = () => {
    const canvas = document.getElementById(canvasId) as HTMLCanvasElement;
    if (!canvas) return;
    
    canvas.removeEventListener('mousedown', handleMouseDown);
    canvas.removeEventListener('mousemove', handleMouseMove);
    canvas.removeEventListener('mouseup', handleMouseUp);
    canvas.removeEventListener('mouseleave', handleMouseLeave);
    canvas.removeEventListener('wheel', handleWheel);
    
    // 移除设备选择事件监听
    canvas.removeEventListener('device-selected', handleDeviceSelected);
    
    window.removeEventListener('resize', handleResize);
  };
  
  // 处理鼠标按下事件
  const handleMouseDown = (e: MouseEvent) => {
    mapService.handleMouseDown(e);
  };
  
  // 处理鼠标移动事件
  const handleMouseMove = (e: MouseEvent) => {
    mapService.handleMouseMove(e);
  };
  
  // 处理鼠标抬起事件
  const handleMouseUp = () => {
    mapService.handleMouseUp();
  };
  
  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    mapService.handleMouseLeave();
  };
  
  // 处理鼠标滚轮事件
  const handleWheel = (e: WheelEvent) => {
    mapService.handleWheel(e);
  };
  
  // 处理窗口大小变化
  const handleResize = () => {
    mapService.resizeCanvas();
  };
  
  // 视图模式控制函数
  const changeViewMode = (value: number) => {
    viewMode.value = value;
    mapService.changeViewMode(value);
  };
  
  // 缩放控制
  const zoomIn = () => {
    mapService.zoomIn();
  };
  
  const zoomOut = () => {
    mapService.zoomOut();
  };
  
  const resetView = () => {
    mapService.resetView();
  };
  
  // 聚焦到指定设备
  const focusOnDevice = (deviceId: string) => {
    mapService.focusOnDevice(deviceId);
  };
  
  // 组件卸载时清理资源
  onUnmounted(() => {
    removeEventListeners();
  });
  
  return {
    viewMode,
    initMap,
    changeViewMode,
    zoomIn,
    zoomOut,
    resetView,
    farmlandArea,
    focusOnDevice
  };
} 