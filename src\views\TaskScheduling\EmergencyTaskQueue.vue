<!-- 
  EmergencyTaskQueue.vue
  应急消杀任务插队机制模块
  在紧急情况下，能够将应急消杀任务插入到任务队列中，优先执行，以快速响应病虫害危机
-->
<template>
  <div class="emergency-task-container">
    <!-- 页面标题 -->
    <PageHeader
      title="应急消杀任务插队机制"
      description="在紧急情况下，能够将应急消杀任务插入到任务队列中，优先执行，以快速响应病虫害危机"
      icon="AlarmClock"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ getActiveTasksCount() }}</span>
            <span class="summary-label">执行中</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getPendingTasksCount() }}</span>
            <span class="summary-label">待处理</span>
          </div>
          <el-button type="danger" @click="createEmergencyTask">
            <el-icon><AlarmClock /></el-icon>
            创建应急任务
          </el-button>
        </div>
      </template>
    </PageHeader>

    <el-row :gutter="24">
      <!-- 左侧任务列表 -->
      <el-col :span="16">
        <DataPanel title="应急任务队列" dark>
          <template #actions>
            <el-tag type="danger" effect="dark">优先级高于常规任务</el-tag>
          </template>
          
          <el-table
            :data="emergencyTasks"
            style="width: 100%"
            :row-class-name="tableRowClassName"
            border
            class="dark-table"
          >
            <el-table-column type="expand">
              <template #default="props">
                <div class="task-detail-expansion">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <h4>影响区域</h4>
                      <div class="area-preview">
                        <!-- 这里应该放置区域地图预览 -->
                        <div class="map-placeholder">区域地图预览</div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <h4>资源消耗预估</h4>
                      <div class="resource-usage">
                        <el-progress 
                          :percentage="props.row.estimatedResourceUsage.pesticide * 10" 
                          :format="() => `农药用量: ${props.row.estimatedResourceUsage.pesticide}升`"
                          status="warning"
                        />
                        <el-progress 
                          :percentage="props.row.estimatedResourceUsage.battery" 
                          :format="() => `电池消耗: ${props.row.estimatedResourceUsage.battery}%`"
                          status="exception"
                        />
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <h4>执行设备</h4>
                      <div class="device-tags">
                        <el-tag 
                          v-for="deviceId in props.row.deviceIds" 
                          :key="deviceId"
                          class="device-tag"
                        >
                          {{ getDeviceName(deviceId) }}
                        </el-tag>
                      </div>
                    </el-col>
                  </el-row>
                  <el-divider />
                  <el-row>
                    <el-col :span="24">
                      <h4>任务描述</h4>
                      <p class="task-description">{{ props.row.description || '无描述' }}</p>
                    </el-col>
                  </el-row>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="紧急标识" width="80" align="center">
              <template #default="scope">
                <el-tag 
                  v-if="scope.row.severity === 'high'" 
                  type="danger" 
                  effect="dark"
                >紧急</el-tag>
                <el-tag 
                  v-else-if="scope.row.severity === 'medium'" 
                  type="warning"
                >中度</el-tag>
                <el-tag 
                  v-else 
                  type="info"
                >一般</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="任务名称" width="180" />
            <el-table-column prop="pestType" label="病虫害类型" width="140" />
            <el-table-column prop="affectedArea" label="影响面积(㎡)" width="120" />
            <el-table-column label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusTagType(scope.row.status)">
                  {{ getStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="240">
              <template #default="scope">
                <el-button-group>
                  <el-button 
                    size="small" 
                    type="primary"
                    :disabled="scope.row.status !== 'pending'"
                    @click="startTask(scope.row.id)"
                  >
                    执行
                  </el-button>
                  <el-button 
                    size="small" 
                    type="warning"
                    :disabled="scope.row.status !== 'running'"
                    @click="pauseTask(scope.row.id)"
                  >
                    暂停
                  </el-button>
                  <el-button 
                    size="small" 
                    type="danger"
                    @click="deleteTask(scope.row.id)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="empty-placeholder" v-if="emergencyTasks.length === 0">
            <el-empty description="暂无应急任务" />
          </div>
        </DataPanel>
      </el-col>
      
      <!-- 右侧监控面板 -->
      <el-col :span="8">
        <DataPanel title="实时监控" dark>
          <template #actions>
            <el-badge :value="activeTasksCount" class="monitoring-badge" type="danger" />
          </template>
          
          <div class="active-tasks-container">
            <div 
              v-for="task in activeTasks" 
              :key="task.id"
              class="active-task-card"
            >
              <div class="task-card-header">
                <div class="task-title">{{ task.name }}</div>
                <el-tag size="small" type="danger" effect="dark">进行中</el-tag>
              </div>
              
              <div class="task-progress">
                <el-progress 
                  :percentage="task.progressPercentage" 
                  :stroke-width="15"
                  status="success"
                />
                <div class="progress-info">
                  <span>预计剩余时间: {{ task.estimatedRemainingTime }}分钟</span>
                  <span>已消杀面积: {{ task.completedArea }}㎡</span>
                </div>
              </div>
              
              <div class="device-status">
                <div class="device-avatar">
                  <el-avatar :size="32" :src="getDeviceAvatar(task.deviceIds[0])" />
                </div>
                <div class="device-info">
                  <span class="device-name">{{ getDeviceName(task.deviceIds[0]) }}</span>
                  <span class="device-battery">
                    <el-icon><Connection /></el-icon>
                    {{ getDeviceBattery(task.deviceIds[0]) }}%
                  </span>
                </div>
              </div>
            </div>
            
            <div class="empty-tasks" v-if="activeTasks.length === 0">
              <el-empty description="暂无正在执行的任务" />
            </div>
          </div>
        </DataPanel>
      </el-col>
    </el-row>

    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="任务执行中" />
        <StatusIndicator type="warning" label="任务待处理" />
        <StatusIndicator type="error" label="任务异常" />
        <StatusIndicator type="normal" label="农田健康" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 应急任务创建对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建应急消杀任务"
      width="70%"
      destroy-on-close
      class="dark-dialog"
    >
      <el-form 
        ref="taskForm" 
        :model="taskFormData" 
        :rules="taskRules" 
        label-width="100px"
        class="emergency-task-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="taskFormData.name" placeholder="请输入任务名称" />
            </el-form-item>
            
            <el-form-item label="病虫害类型" prop="pestType">
              <el-select v-model="taskFormData.pestType" placeholder="请选择病虫害类型" style="width: 100%">
                <el-option label="蚜虫" value="aphid" />
                <el-option label="菜粉蝶" value="cabbage-butterfly" />
                <el-option label="稻飞虱" value="rice-planthopper" />
                <el-option label="玉米螟" value="corn-borer" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="影响面积" prop="affectedArea">
              <el-input-number 
                v-model="taskFormData.affectedArea" 
                :min="1" 
                :max="10000" 
                style="width: 100%"
                controls-position="right"
              />
            </el-form-item>
            
            <el-form-item label="紧急程度" prop="severity">
              <el-rate
                v-model="severityValue"
                :colors="severityColors"
                :texts="severityTexts"
                show-text
                @change="updateSeverity"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="消杀区域" prop="location.fieldId">
              <el-select 
                v-model="taskFormData.location!.fieldId" 
                placeholder="请选择消杀区域"
                style="width: 100%"
              >
                <el-option label="北区农田" value="field-001" />
                <el-option label="南区农田" value="field-002" />
                <el-option label="东区农田" value="field-003" />
                <el-option label="西区农田" value="field-004" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="执行设备" prop="deviceIds">
              <el-select 
                v-model="taskFormData.deviceIds" 
                multiple 
                placeholder="请选择执行设备"
                style="width: 100%"
              >
                <el-option label="机器狗-01" value="device-001" />
                <el-option label="机器狗-02" value="device-002" />
                <el-option label="无人机-01" value="device-003" />
                <el-option label="无人机-02" value="device-004" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="任务描述" prop="description">
              <el-input 
                v-model="taskFormData.description" 
                type="textarea" 
                :rows="4" 
                placeholder="请输入任务描述"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div class="form-footer">
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitTaskForm" :loading="submitting">立即创建</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  AlarmClock, 
  Refresh, 
  Monitor,
  View, 
  Plus, 
  Delete,
  Connection
} from '@element-plus/icons-vue'
import { emergencyTaskApi } from '@/api/taskScheduling'
import type { EmergencyTask, TaskStatus } from '@/types/taskScheduling'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'

// 任务列表数据
const emergencyTasks = ref<EmergencyTask[]>([
  {
    id: '1',
    name: '北区蚜虫应急消杀',
    description: '北区农田发现大量蚜虫，需紧急处理',
    pestType: '蚜虫',
    severity: 'high',
    affectedArea: 500,
    location: {
      fieldId: 'field-001',
      coordinates: [{ x: 120, y: 150 }]
    },
    deviceIds: ['device-001', 'device-003'],
    status: 'running',
    estimatedResourceUsage: {
      pesticide: 5,
      battery: 60
    },
    createdAt: '2023-08-10T08:30:00Z',
    startedAt: '2023-08-10T08:35:00Z'
  },
  {
    id: '2',
    name: '南区菜粉蝶防治',
    description: '南区蔬菜地发现菜粉蝶，需要进行防治',
    pestType: '菜粉蝶',
    severity: 'medium',
    affectedArea: 300,
    location: {
      fieldId: 'field-002',
      coordinates: [{ x: 220, y: 180 }]
    },
    deviceIds: ['device-002'],
    status: 'pending',
    estimatedResourceUsage: {
      pesticide: 3,
      battery: 40
    },
    createdAt: '2023-08-10T09:15:00Z'
  }
])

// 活动任务数据（正在执行的任务）
const activeTasks = ref([
  {
    id: '1',
    name: '北区蚜虫应急消杀',
    deviceIds: ['device-001'],
    progressPercentage: 65,
    estimatedRemainingTime: 12,
    completedArea: 325
  }
])

// 计算活动任务数量
const activeTasksCount = computed(() => activeTasks.value.length)

// 对话框和表单状态
const showCreateDialog = ref(false)
const taskFormData = reactive<Partial<EmergencyTask>>({
  name: '',
  description: '',
  pestType: '',
  severity: 'medium',
  affectedArea: 100,
  location: {
    fieldId: '',
    coordinates: []
  },
  deviceIds: [],
  estimatedResourceUsage: {
    pesticide: 0,
    battery: 0
  }
})

const taskRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  pestType: [
    { required: true, message: '请选择病虫害类型', trigger: 'change' }
  ],
  affectedArea: [
    { required: true, message: '请输入影响面积', trigger: 'blur' }
  ],
  'location.fieldId': [
    { required: true, message: '请选择消杀区域', trigger: 'change' }
  ],
  deviceIds: [
    { required: true, message: '请选择至少一个执行设备', trigger: 'change' },
    { type: 'array', min: 1, message: '请选择至少一个执行设备', trigger: 'change' }
  ]
}

// 紧急程度相关
const severityValue = ref(2) // 默认中等紧急程度
const severityTexts = ['极低', '低', '中等', '高', '极高']
const severityColors = ['#909399', '#E6A23C', '#F56C6C', '#F56C6C', '#F56C6C']

// 表单提交状态
const submitting = ref(false)
const taskForm = ref<any>(null)

// 根据评分更新紧急程度
const updateSeverity = (value: number) => {
  const severityMap: Record<number, EmergencyTask['severity']> = {
    1: 'low',
    2: 'medium',
    3: 'high',
    4: 'high',
    5: 'high'
  }
  taskFormData.severity = severityMap[value] || 'medium'
}

// 获取设备名称
const getDeviceName = (deviceId: string) => {
  const deviceMap: Record<string, string> = {
    'device-001': '机器狗-01',
    'device-002': '机器狗-02',
    'device-003': '无人机-01',
    'device-004': '无人机-02'
  }
  return deviceMap[deviceId] || '未知设备'
}

// 获取设备头像
const getDeviceAvatar = (deviceId: string) => {
  // 这里应该返回设备的头像URL
  return ''
}

// 获取设备电量
const getDeviceBattery = (deviceId: string) => {
  // 模拟数据
  const batteryMap: Record<string, number> = {
    'device-001': 85,
    'device-002': 72,
    'device-003': 64,
    'device-004': 91
  }
  return batteryMap[deviceId] || 0
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 获取状态标签类型
const getStatusTagType = (status: TaskStatus) => {
  const statusMap: Record<TaskStatus, string> = {
    'pending': 'info',
    'running': 'success',
    'paused': 'warning',
    'completed': '',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: TaskStatus) => {
  const statusMap: Record<TaskStatus, string> = {
    'pending': '等待中',
    'running': '执行中',
    'paused': '已暂停',
    'completed': '已完成',
    'failed': '已失败'
  }
  return statusMap[status] || '未知状态'
}

// 表格行样式
const tableRowClassName = ({ row }: { row: EmergencyTask }) => {
  if (row.severity === 'high') {
    return 'high-severity-row'
  }
  return ''
}

// 创建应急任务
const createEmergencyTask = () => {
  showCreateDialog.value = true
}

// 提交任务表单
const submitTaskForm = async () => {
  if (!taskForm.value) return
  
  // 表单验证
  await taskForm.value.validate(async (valid: boolean) => {
    if (!valid) {
      ElMessage.error('请完善表单信息')
      return
    }
    
    submitting.value = true
    
    try {
      // 计算资源消耗（模拟）
      const pesticide = Math.round(taskFormData.affectedArea || 100 / 100)
      const battery = Math.min(90, Math.round((taskFormData.affectedArea || 100) / 10))
      
      const task: EmergencyTask = {
        id: Date.now().toString(),
        name: taskFormData.name || '',
        description: taskFormData.description || '',
        pestType: taskFormData.pestType || '',
        severity: taskFormData.severity || 'medium',
        affectedArea: taskFormData.affectedArea || 100,
        location: taskFormData.location || { fieldId: '', coordinates: [] },
        deviceIds: taskFormData.deviceIds || [],
        status: 'pending',
        estimatedResourceUsage: {
          pesticide,
          battery
        },
        createdAt: new Date().toISOString()
      }
      
      // 在实际项目中调用API
      // await emergencyTaskApi.createEmergencyTask(task)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 添加到任务列表
      emergencyTasks.value.unshift(task)
      
      showCreateDialog.value = false
      ElMessage.success('应急任务创建成功')
      
      // 清空表单
      taskForm.value.resetFields()
    } catch (error) {
      console.error('创建应急任务失败:', error)
      ElMessage.error('创建应急任务失败，请重试')
    } finally {
      submitting.value = false
    }
  })
}

// 开始任务
const startTask = async (taskId: string) => {
  try {
    // 在实际项目中调用API
    // await emergencyTaskApi.startTask(taskId)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新任务状态
    const task = emergencyTasks.value.find(t => t.id === taskId)
    if (task) {
      task.status = 'running'
      task.startedAt = new Date().toISOString()
      
      // 添加到活动任务
      activeTasks.value.push({
        id: task.id,
        name: task.name,
        deviceIds: task.deviceIds,
        progressPercentage: 0,
        estimatedRemainingTime: Math.round(task.affectedArea / 25),
        completedArea: 0
      })
    }
    
    ElMessage.success('任务已开始执行')
  } catch (error) {
    console.error('开始任务失败:', error)
    ElMessage.error('开始任务失败，请重试')
  }
}

// 暂停任务
const pauseTask = async (taskId: string) => {
  try {
    // 在实际项目中调用API
    // await emergencyTaskApi.pauseTask(taskId)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新任务状态
    const task = emergencyTasks.value.find(t => t.id === taskId)
    if (task) {
      task.status = 'paused'
      
      // 从活动任务中移除
      activeTasks.value = activeTasks.value.filter(t => t.id !== taskId)
    }
    
    ElMessage.success('任务已暂停')
  } catch (error) {
    console.error('暂停任务失败:', error)
    ElMessage.error('暂停任务失败，请重试')
  }
}

// 删除任务
const deleteTask = (taskId: string) => {
  ElMessageBox.confirm(
    '确认删除此应急任务？该操作不可恢复',
    '删除任务',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 在实际项目中调用API
      // await emergencyTaskApi.deleteTask(taskId)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 更新任务列表
      emergencyTasks.value = emergencyTasks.value.filter(t => t.id !== taskId)
      
      // 从活动任务中移除
      activeTasks.value = activeTasks.value.filter(t => t.id !== taskId)
      
      ElMessage.success('任务已删除')
    } catch (error) {
      console.error('删除任务失败:', error)
      ElMessage.error('删除任务失败，请重试')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 最后更新时间
const lastUpdateTime = ref(new Date())

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 添加数据自动更新相关代码
// 模拟数据自动更新
let dataUpdateInterval: number | null = null;

// 刷新数据
const refreshData = () => {
  // 更新任务进度
  activeTasks.value.forEach(task => {
    // 随机增加进度
    const progressIncrease = Math.random() * 5;
    task.progressPercentage = Math.min(100, task.progressPercentage + progressIncrease);
    
    // 根据进度计算剩余时间和完成面积
    if (task.progressPercentage < 100) {
      const remainingPercentage = 100 - task.progressPercentage;
      task.estimatedRemainingTime = Math.max(1, Math.round(remainingPercentage / 5));
      
      // 查找对应的任务获取总面积
      const originalTask = emergencyTasks.value.find(t => t.id === task.id);
      if (originalTask) {
        task.completedArea = Math.round((task.progressPercentage / 100) * originalTask.affectedArea);
      }
    } else {
      task.estimatedRemainingTime = 0;
      
      // 标记任务为已完成
      const originalTask = emergencyTasks.value.find(t => t.id === task.id);
      if (originalTask) {
        originalTask.status = 'completed';
        task.completedArea = originalTask.affectedArea;
      }
      
      // 从活动任务中移除已完成的任务
      setTimeout(() => {
        activeTasks.value = activeTasks.value.filter(t => t.id !== task.id);
        ElMessage.success(`任务"${task.name}"已完成`);
      }, 3000);
    }
  });
  
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

onMounted(() => {
  // 启动数据自动更新
  dataUpdateInterval = window.setInterval(() => {
    // 小幅度更新任务进度
    activeTasks.value.forEach(task => {
      // 随机增加进度
      const progressIncrease = Math.random() * 2;
      task.progressPercentage = Math.min(100, task.progressPercentage + progressIncrease);
      
      // 根据进度计算剩余时间和完成面积
      if (task.progressPercentage < 100) {
        const remainingPercentage = 100 - task.progressPercentage;
        task.estimatedRemainingTime = Math.max(1, Math.round(remainingPercentage / 5));
        
        // 查找对应的任务获取总面积
        const originalTask = emergencyTasks.value.find(t => t.id === task.id);
        if (originalTask) {
          task.completedArea = Math.round((task.progressPercentage / 100) * originalTask.affectedArea);
        }
      }
    });
    
    lastUpdateTime.value = new Date();
  }, 15000); // 每15秒更新一次
});

onUnmounted(() => {
  // 清除定时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
});

// 获取活动任务数量
const getActiveTasksCount = () => {
  return emergencyTasks.value.filter(task => task.status === 'running').length;
};

// 获取待处理任务数量
const getPendingTasksCount = () => {
  return emergencyTasks.value.filter(task => task.status === 'pending').length;
};
</script>

<style scoped lang="scss">
.emergency-task-container {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #e5e7eb;

  .task-header {
    margin-bottom: 24px;
    
    .page-title {
      font-size: 24px;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 8px;
    }
    
    .page-description {
      font-size: 14px;
      color: #6b7280;
      margin-bottom: 16px;
    }
    
    .header-actions {
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .task-list-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 24px;
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        font-size: 18px;
        font-weight: bold;
        color: #1f2937;
        margin: 0;
      }
    }
    
    .empty-placeholder {
      padding: 40px 0;
    }
  }
  
  .monitoring-panel {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 20px;
    height: 100%;
    
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        font-size: 18px;
        font-weight: bold;
        color: #1f2937;
        margin: 0;
      }
      
      .monitoring-badge {
        margin-top: 4px;
      }
    }
    
    .active-tasks-container {
      .active-task-card {
        background-color: rgba(31, 41, 55, 0.5);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        border-left: 4px solid #ef4444;
        
        .task-card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .task-title {
            font-weight: bold;
            color: #e5e7eb;
          }
        }
        
        .task-progress {
          margin-bottom: 16px;
          
          .progress-info {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 12px;
            color: #9ca3af;
          }
        }
        
        .device-status {
          display: flex;
          align-items: center;
          
          .device-avatar {
            margin-right: 12px;
            background-color: #1f2937;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          
          .device-info {
            display: flex;
            flex-direction: column;
            
            .device-name {
              font-size: 14px;
              color: #e5e7eb;
              margin-bottom: 4px;
            }
            
            .device-battery {
              font-size: 12px;
              color: #9ca3af;
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }
        }
      }
      
      .empty-tasks {
        padding: 40px 0;
      }
    }
  }
  
  .task-detail-expansion {
    padding: 16px;
    color: #ffffff;
    
    h4 {
      font-size: 14px;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 12px;
    }
    
    .area-preview {
      .map-placeholder {
        height: 120px;
        background-color: rgba(31, 41, 55, 0.5);
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #9ca3af;
      }
    }
    
    .resource-usage {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .device-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
    
    .task-description {
      font-size: 14px;
      color: #ffffff;
      line-height: 1.5;
    }
  }
  
  .emergency-task-form {
    .form-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 24px;
      gap: 12px;
    }
  }
}

/* 表格样式 */
:deep(.dark-table) {
  background-color: transparent;
  color: #ffffff;
  border-color: #3b4863;
}

:deep(.dark-table th) {
  background-color: rgba(31, 41, 55, 0.8);
  color: #ffffff;
  border-bottom: 1px solid #3b4863;
  font-weight: 600;
}

:deep(.dark-table td) {
  background-color: rgba(31, 41, 55, 0.6);
  color: #ffffff;
  border-bottom: 1px solid #3b4863;
}

:deep(.dark-table .el-table__row:hover > td) {
  background-color: rgba(59, 130, 246, 0.2) !important;
}

/* 增加表格文字亮度 */
:deep(.dark-table .cell) {
  color: #ffffff;
  font-weight: 500;
}

/* 增强表格中标签的可见度 */
:deep(.dark-table .el-tag) {
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 调整表格区域的背景色，增加对比度 */
:deep(.dark-table .el-table__expanded-cell) {
  background-color: rgba(31, 41, 55, 0.8) !important;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 深色对话框 */
:deep(.dark-dialog) {
  background-color: #1f2937;
}

:deep(.dark-dialog .el-dialog__header) {
  background-color: #1a2234;
  color: #e5e7eb;
  border-bottom: 1px solid #3b4863;
}

:deep(.dark-dialog .el-dialog__title) {
  color: #e5e7eb;
}

:deep(.dark-dialog .el-dialog__body) {
  background-color: #1f2937;
  color: #e5e7eb;
}

:deep(.dark-dialog .el-form-item__label) {
  color: #d1d5db;
}

:deep(.dark-dialog .el-input__inner) {
  background-color: rgba(31, 41, 55, 0.7);
  border-color: #3b4863;
  color: #e5e7eb;
}

:deep(.dark-dialog .el-textarea__inner) {
  background-color: rgba(31, 41, 55, 0.7);
  border-color: #3b4863;
  color: #e5e7eb;
}

:deep(.dark-dialog .el-dialog__footer) {
  background-color: #1a2234;
  border-top: 1px solid #3b4863;
}

/* 表格行样式 */
:deep(.high-severity-row td) {
  background-color: rgba(239, 68, 68, 0.25) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  border-bottom: 1px solid rgba(239, 68, 68, 0.5) !important;
}

:deep(.high-severity-row:hover > td) {
  background-color: rgba(239, 68, 68, 0.35) !important;
}

/* 为紧急任务添加左侧边框标识 */
:deep(.high-severity-row td:first-child) {
  border-left: 4px solid #ef4444 !important;
}

/* 增强紧急标签的可见度 */
:deep(.el-tag--danger) {
  background-color: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 添加状态摘要样式 */
.status-summary {
  display: flex;
  gap: 20px;
  align-items: center;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}
</style> 