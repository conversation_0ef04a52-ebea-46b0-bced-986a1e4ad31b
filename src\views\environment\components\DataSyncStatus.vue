<template>
  <div class="data-sync-status">
    <div class="sync-status-indicator" @click="showDetails = true">
      <div :class="['indicator-light', status]"></div>
    </div>

    <el-dialog
      v-model="showDetails"
      :title="dialogTitle"
      width="400px"
      class="sync-details-dialog"
    >
      <div class="sync-details">
        <div class="sync-progress">
          <div class="progress-label">同步进度</div>
          <el-progress :percentage="progress" :status="progressStatus"></el-progress>
        </div>
        <div v-if="reason" class="delay-reason">
          <div class="reason-label">延迟原因</div>
          <div class="reason-content">{{ reason }}</div>
        </div>
        <div v-if="status === 'error'" class="troubleshooting">
          <div class="guide-label">故障排查指南</div>
          <ul class="guide-list">
            <li v-for="(item, index) in troubleshootingGuide" :key="index">{{ item }}</li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps<{
  status: 'normal' | 'warning' | 'error'
  progress: number
  reason?: string
  troubleshootingGuide?: string[]
}>()

const showDetails = ref(false)

const progressStatus = computed(() => {
  switch (props.status) {
    case 'normal':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'exception'
    default:
      return 'success'
  }
})

const dialogTitle = computed(() => {
  switch (props.status) {
    case 'normal':
      return '数据同步状态 - 正常'
    case 'warning':
      return '数据同步状态 - 警告'
    case 'error':
      return '数据同步状态 - 错误'
    default:
      return '数据同步状态'
  }
})
</script>

<style scoped>
.sync-status-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(31, 41, 55, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.indicator-light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.indicator-light.normal {
  background-color: #3b82f6;
  box-shadow: 0 0 8px #3b82f6;
}

.indicator-light.warning {
  background-color: #f59e0b;
  box-shadow: 0 0 8px #f59e0b;
  animation: blink 1s infinite;
}

.indicator-light.error {
  background-color: #ef4444;
  box-shadow: 0 0 8px #ef4444;
  animation: blink 0.5s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.sync-details {
  color: #d1d5db;
}

.progress-label,
.reason-label,
.guide-label {
  font-weight: 500;
  margin-bottom: 8px;
}

.sync-progress {
  margin-bottom: 16px;
}

.delay-reason {
  margin-bottom: 16px;
}

.reason-content {
  padding: 8px 12px;
  background-color: rgba(245, 158, 11, 0.1);
  border-left: 3px solid #f59e0b;
  border-radius: 4px;
}

.guide-list {
  padding-left: 20px;
  margin-top: 8px;
}

.guide-list li {
  margin-bottom: 4px;
}
</style> 