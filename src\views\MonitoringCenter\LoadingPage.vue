<template>
  <div class="loading-page">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">数字农田系统加载中...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 加载页面无需额外逻辑
</script>

<style scoped lang="scss">
.loading-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000820;
  color: white;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(0, 255, 170, 0.3);
  border-radius: 50%;
  border-top-color: #00ffaa;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 18px;
  color: #00ffaa;
  text-shadow: 0 0 10px rgba(0, 255, 170, 0.5);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style> 