/**
 * 机器狗WebSocket控制服务
 * 提供低延迟的机器狗实时控制功能
 * 
 * 功能特性：
 * 1. WebSocket连接管理和自动重连
 * 2. 实时移动控制命令发送
 * 3. 心跳检测和连接状态监控
 * 4. 消息队列和错误处理
 * 5. 键盘控制支持
 */

import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { WEBSOCKET_URLS } from '@/config/env'
import envConfig from '@/config/env'

// 消息类型枚举
export enum MessageType {
  CONNECT = 'CONNECT',
  DISCONNECT = 'DISCONNECT',
  HEARTBEAT = 'HEARTBEAT',
  MOVE = 'MOVE',
  STOP = 'STOP',
  EMERGENCY_STOP = 'EMERGENCY_STOP',
  STATUS = 'STATUS',
  IMU_DATA = 'IMU_DATA'
}

// 连接状态枚举
export enum ConnectionStatus {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR'
}

// 移动参数接口
export interface MoveParams {
  x: number  // 前后移动：正值前进，负值后退
  y: number  // 左右移动：正值左移，负值右移
  z: number  // 旋转：正值左转，负值右转
}

// WebSocket消息接口
export interface RobotControlMessage {
  type: MessageType
  messageId: string
  timestamp: number
  moveParams?: MoveParams
  success?: boolean
  message?: string
  data?: any
}

// 连接状态接口
export interface RobotConnectionState {
  status: ConnectionStatus
  connected: boolean
  lastHeartbeat: number
  reconnectAttempts: number
  error: string | null
  robotIP: string
}

/**
 * 机器狗WebSocket控制服务类
 */
class RobotWebSocketService {
  private ws: WebSocket | null = null
  private heartbeatTimer: number | null = null
  private reconnectTimer: number | null = null
  private messageQueue: RobotControlMessage[] = []
  private pendingMessages = new Map<string, { resolve: Function, reject: Function, timeout: number }>()

  // 响应式状态
  public connectionState = reactive<RobotConnectionState>({
    status: ConnectionStatus.DISCONNECTED,
    connected: false,
    lastHeartbeat: 0,
    reconnectAttempts: 0,
    error: null,
    robotIP: envConfig.robotIP
  })

  // 事件回调
  private onMessageCallback: ((message: RobotControlMessage) => void) | null = null
  private onStatusChangeCallback: ((status: ConnectionStatus) => void) | null = null
  private onErrorCallback: ((error: Error) => void) | null = null

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket已连接，无需重复连接')
      return
    }

    return new Promise((resolve, reject) => {
      try {
        this.updateConnectionStatus(ConnectionStatus.CONNECTING)
        
        this.ws = new WebSocket(WEBSOCKET_URLS.ROBOT_CONTROL)

        this.ws.onopen = () => {
          console.log('机器狗控制WebSocket连接成功')
          this.updateConnectionStatus(ConnectionStatus.CONNECTED)
          this.connectionState.reconnectAttempts = 0
          this.startHeartbeat()
          this.processMessageQueue()
          resolve()
        }

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data)
        }

        this.ws.onclose = (event) => {
          console.log('机器狗控制WebSocket连接关闭:', event.code, event.reason)
          this.updateConnectionStatus(ConnectionStatus.DISCONNECTED)
          this.stopHeartbeat()
          
          // 自动重连
          if (this.connectionState.reconnectAttempts < envConfig.websocketReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('机器狗控制WebSocket连接错误:', error)
          this.updateConnectionStatus(ConnectionStatus.ERROR, '连接错误')
          this.onErrorCallback?.(new Error('WebSocket连接错误'))
          reject(new Error('WebSocket连接失败'))
        }

        // 连接超时处理
        setTimeout(() => {
          if (this.connectionState.status === ConnectionStatus.CONNECTING) {
            this.ws?.close()
            reject(new Error('连接超时'))
          }
        }, 10000)

      } catch (error) {
        this.updateConnectionStatus(ConnectionStatus.ERROR, '连接失败')
        reject(error)
      }
    })
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    this.stopHeartbeat()
    this.clearReconnectTimer()
    
    if (this.ws) {
      this.ws.close(1000, '主动断开连接')
      this.ws = null
    }
    
    this.updateConnectionStatus(ConnectionStatus.DISCONNECTED)
    console.log('机器狗控制WebSocket已断开')
  }

  /**
   * 发送移动控制命令
   */
  async sendMoveCommand(x: number, y: number, z: number): Promise<RobotControlMessage> {
    const message: RobotControlMessage = {
      type: MessageType.MOVE,
      messageId: this.generateMessageId(),
      timestamp: Date.now(),
      moveParams: { x, y, z }
    }

    return this.sendMessage(message)
  }

  /**
   * 发送停止命令
   */
  async sendStopCommand(): Promise<RobotControlMessage> {
    const message: RobotControlMessage = {
      type: MessageType.STOP,
      messageId: this.generateMessageId(),
      timestamp: Date.now()
    }

    return this.sendMessage(message)
  }

  /**
   * 发送紧急停止命令
   */
  async sendEmergencyStopCommand(): Promise<RobotControlMessage> {
    const message: RobotControlMessage = {
      type: MessageType.EMERGENCY_STOP,
      messageId: this.generateMessageId(),
      timestamp: Date.now()
    }

    return this.sendMessage(message)
  }

  /**
   * 发送连接命令
   */
  async sendConnectCommand(): Promise<RobotControlMessage> {
    const message: RobotControlMessage = {
      type: MessageType.CONNECT,
      messageId: this.generateMessageId(),
      timestamp: Date.now()
    }

    return this.sendMessage(message)
  }

  /**
   * 获取连接状态
   */
  async getStatus(): Promise<RobotControlMessage> {
    const message: RobotControlMessage = {
      type: MessageType.STATUS,
      messageId: this.generateMessageId(),
      timestamp: Date.now()
    }

    return this.sendMessage(message)
  }

  /**
   * 获取IMU数据
   */
  async getIMUData(): Promise<RobotControlMessage> {
    const message: RobotControlMessage = {
      type: MessageType.IMU_DATA,
      messageId: this.generateMessageId(),
      timestamp: Date.now()
    }

    return this.sendMessage(message)
  }

  /**
   * 发送消息
   */
  private async sendMessage(message: RobotControlMessage): Promise<RobotControlMessage> {
    return new Promise((resolve, reject) => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        // 如果未连接，将消息加入队列
        this.messageQueue.push(message)
        reject(new Error('WebSocket未连接，消息已加入队列'))
        return
      }

      try {
        // 设置响应超时
        const timeout = setTimeout(() => {
          this.pendingMessages.delete(message.messageId)
          reject(new Error('消息响应超时'))
        }, 5000)

        // 存储待响应消息
        this.pendingMessages.set(message.messageId, { resolve, reject, timeout })

        // 发送消息
        this.ws.send(JSON.stringify(message))
        console.log('发送机器狗控制消息:', message.type, message.moveParams)

      } catch (error) {
        this.pendingMessages.delete(message.messageId)
        reject(error)
      }
    })
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: string): void {
    try {
      const message: RobotControlMessage = JSON.parse(data)
      
      // 更新心跳时间
      if (message.type === MessageType.HEARTBEAT) {
        this.connectionState.lastHeartbeat = Date.now()
      }

      // 处理响应消息
      if (message.messageId && this.pendingMessages.has(message.messageId)) {
        const pending = this.pendingMessages.get(message.messageId)!
        clearTimeout(pending.timeout)
        this.pendingMessages.delete(message.messageId)
        
        if (message.success) {
          pending.resolve(message)
        } else {
          pending.reject(new Error(message.message || '操作失败'))
        }
      }

      // 触发消息回调
      this.onMessageCallback?.(message)

    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
      this.onErrorCallback?.(new Error('消息解析失败'))
    }
  }

  /**
   * 更新连接状态
   */
  private updateConnectionStatus(status: ConnectionStatus, error?: string): void {
    this.connectionState.status = status
    this.connectionState.connected = status === ConnectionStatus.CONNECTED
    this.connectionState.error = error || null
    
    this.onStatusChangeCallback?.(status)
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = window.setInterval(async () => {
      try {
        const heartbeatMessage: RobotControlMessage = {
          type: MessageType.HEARTBEAT,
          messageId: this.generateMessageId(),
          timestamp: Date.now()
        }
        
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          this.ws.send(JSON.stringify(heartbeatMessage))
        }
      } catch (error) {
        console.error('发送心跳失败:', error)
      }
    }, envConfig.robotControlHeartbeatInterval)
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.clearReconnectTimer()
    this.connectionState.reconnectAttempts++
    
    const delay = Math.min(1000 * Math.pow(2, this.connectionState.reconnectAttempts - 1), 30000)
    
    console.log(`${delay}ms后尝试第${this.connectionState.reconnectAttempts}次重连`)
    this.updateConnectionStatus(ConnectionStatus.RECONNECTING)
    
    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败:', error)
      })
    }, delay)
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!
      this.sendMessage(message).catch(error => {
        console.error('处理队列消息失败:', error)
      })
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 设置事件回调
   */
  setMessageCallback(callback: (message: RobotControlMessage) => void): void {
    this.onMessageCallback = callback
  }

  setStatusChangeCallback(callback: (status: ConnectionStatus) => void): void {
    this.onStatusChangeCallback = callback
  }

  setErrorCallback(callback: (error: Error) => void): void {
    this.onErrorCallback = callback
  }
}

// 创建单例实例
export const robotWebSocketService = new RobotWebSocketService()

// 导出服务类供其他地方使用
export default RobotWebSocketService
