<template>
  <div class="voice-test-container">
    <div class="test-header">
      <h3>语音输入功能测试</h3>
      <p>测试麦克风权限和语音识别功能</p>
    </div>
    
    <div class="test-content">
      <!-- 服务状态检查 -->
      <div class="status-section">
        <h4>🔧 服务状态</h4>
        <div class="status-grid">
          <div class="status-item" :class="{ 'success': serviceStatus?.success }">
            <span class="status-label">API连接:</span>
            <span class="status-value">{{ serviceStatus ? '✅ 正常' : '❌ 异常' }}</span>
          </div>
          <div class="status-item" :class="{ 'success': serviceStatus?.asr_ready }">
            <span class="status-label">语音识别:</span>
            <span class="status-value">{{ serviceStatus?.asr_ready ? '✅ 就绪' : '❌ 不可用' }}</span>
          </div>
          <div class="status-item" :class="{ 'success': microphoneAvailable }">
            <span class="status-label">麦克风权限:</span>
            <span class="status-value">{{ microphoneAvailable ? '✅ 已授权' : '❌ 未授权' }}</span>
          </div>
        </div>
        <el-button @click="checkServiceStatus" size="small" type="primary">刷新状态</el-button>
      </div>
      
      <!-- 语音录制测试 -->
      <div class="recording-section">
        <h4>🎤 语音录制测试</h4>
        <div class="recording-controls">
          <el-button
            :type="isRecording ? 'danger' : 'success'"
            :class="{ 'recording': isRecording }"
            size="large"
            circle
            @mousedown="startRecording"
            @mouseup="stopRecording"
            @mouseleave="cancelRecording"
            :disabled="!microphoneAvailable || isProcessing"
            :loading="isProcessing"
          >
            <el-icon>
              <component :is="recordingIcon" />
            </el-icon>
          </el-button>
          
          <div class="recording-info" v-if="isRecording">
            <div class="recording-time">{{ recordingTime }}s</div>
            <div class="recording-hint">按住录音，松开识别</div>
          </div>
          
          <div class="processing-info" v-if="isProcessing">
            <div class="processing-text">正在识别语音...</div>
          </div>
        </div>
      </div>
      
      <!-- 识别结果显示 -->
      <div class="result-section" v-if="recognitionResults.length > 0">
        <h4>📝 识别结果</h4>
        <div class="results-list">
          <div 
            v-for="(result, index) in recognitionResults" 
            :key="index"
            class="result-item"
            :class="{ 'success': result.success, 'error': !result.success }"
          >
            <div class="result-header">
              <span class="result-index">#{{ index + 1 }}</span>
              <span class="result-time">{{ result.timestamp }}</span>
              <span class="result-status">{{ result.success ? '✅ 成功' : '❌ 失败' }}</span>
            </div>
            <div class="result-content">
              <div class="result-text">{{ result.text || result.error }}</div>
              <div class="result-details" v-if="result.details">
                <small>
                  文件大小: {{ result.details.fileSize }} | 
                  识别耗时: {{ result.details.processingTime }}s
                </small>
              </div>
            </div>
          </div>
        </div>
        <el-button @click="clearResults" size="small" type="danger" plain>清除结果</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Microphone, Loading, VideoPlay } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getServiceStatus, speechToText } from '@/api/aiChat';

interface RecognitionResult {
  timestamp: string;
  success: boolean;
  text?: string;
  error?: string;
  details?: {
    fileSize: string;
    processingTime: number;
  };
}

// 状态变量
const serviceStatus = ref<any>(null);
const microphoneAvailable = ref(false);
const isRecording = ref(false);
const isProcessing = ref(false);
const recordingTime = ref(0);
const recognitionResults = ref<RecognitionResult[]>([]);

// 录音相关
const mediaRecorder = ref<MediaRecorder | null>(null);
const audioChunks = ref<Blob[]>([]);
const recordingTimer = ref<number | null>(null);

// 计算属性
const recordingIcon = computed(() => {
  if (isProcessing.value) return Loading;
  if (isRecording.value) return VideoPlay;
  return Microphone;
});

// 检查服务状态
const checkServiceStatus = async () => {
  try {
    const status = await getServiceStatus();
    serviceStatus.value = status;
    console.log('服务状态:', status);
  } catch (error) {
    console.error('获取服务状态失败:', error);
    ElMessage.error('无法连接到后端服务');
  }
};

// 检查麦克风权限
const checkMicrophonePermission = async () => {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('浏览器不支持语音录制');
    }
    
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    microphoneAvailable.value = true;
    
    // 立即停止流
    stream.getTracks().forEach(track => track.stop());
    
    ElMessage.success('麦克风权限已获取');
  } catch (error) {
    console.error('麦克风权限检查失败:', error);
    microphoneAvailable.value = false;
    ElMessage.error('无法获取麦克风权限，请检查浏览器设置');
  }
};

// 开始录音
const startRecording = async () => {
  if (!microphoneAvailable.value || isRecording.value || isProcessing.value) return;
  
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 16000
      }
    });
    
    audioChunks.value = [];
    recordingTime.value = 0;
    
    mediaRecorder.value = new MediaRecorder(stream, {
      mimeType: MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/wav'
    });
    
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.value.push(event.data);
      }
    };
    
    mediaRecorder.value.onstop = () => {
      stream.getTracks().forEach(track => track.stop());
      processRecording();
    };
    
    mediaRecorder.value.start();
    isRecording.value = true;
    
    // 开始计时
    recordingTimer.value = window.setInterval(() => {
      recordingTime.value++;
      if (recordingTime.value >= 30) {
        stopRecording();
      }
    }, 1000);
    
  } catch (error) {
    console.error('开始录音失败:', error);
    ElMessage.error('无法开始录音');
  }
};

// 停止录音
const stopRecording = () => {
  if (!isRecording.value || !mediaRecorder.value) return;
  
  isRecording.value = false;
  
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value);
    recordingTimer.value = null;
  }
  
  if (mediaRecorder.value.state === 'recording') {
    mediaRecorder.value.stop();
  }
};

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  
  isRecording.value = false;
  
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value);
    recordingTimer.value = null;
  }
  
  if (mediaRecorder.value) {
    if (mediaRecorder.value.state === 'recording') {
      mediaRecorder.value.stop();
    }
    mediaRecorder.value = null;
  }
  
  audioChunks.value = [];
  ElMessage.info('录音已取消');
};

// 处理录音
const processRecording = async () => {
  if (audioChunks.value.length === 0) {
    ElMessage.warning('录音数据为空');
    return;
  }
  
  if (recordingTime.value < 1) {
    ElMessage.warning('录音时间太短');
    return;
  }
  
  isProcessing.value = true;
  const startTime = Date.now();
  
  try {
    const audioBlob = new Blob(audioChunks.value, { 
      type: audioChunks.value[0].type || 'audio/wav'
    });
    
    const fileSizeKB = (audioBlob.size / 1024).toFixed(1);
    
    console.log('开始语音识别测试:', {
      fileSize: `${fileSizeKB}KB`,
      fileType: audioBlob.type,
      duration: `${recordingTime.value}s`
    });
    
    const recognizedText = await speechToText(audioBlob);
    const processingTime = (Date.now() - startTime) / 1000;
    
    const result: RecognitionResult = {
      timestamp: new Date().toLocaleTimeString(),
      success: true,
      text: recognizedText,
      details: {
        fileSize: `${fileSizeKB}KB`,
        processingTime: processingTime
      }
    };
    
    recognitionResults.value.unshift(result);
    ElMessage.success(`识别成功: ${recognizedText}`);
    
  } catch (error) {
    const processingTime = (Date.now() - startTime) / 1000;
    
    const result: RecognitionResult = {
      timestamp: new Date().toLocaleTimeString(),
      success: false,
      error: error instanceof Error ? error.message : '识别失败',
      details: {
        fileSize: `${(audioChunks.value.reduce((sum, chunk) => sum + chunk.size, 0) / 1024).toFixed(1)}KB`,
        processingTime: processingTime
      }
    };
    
    recognitionResults.value.unshift(result);
    ElMessage.error('语音识别失败');
    console.error('语音识别失败:', error);
  } finally {
    isProcessing.value = false;
    audioChunks.value = [];
  }
};

// 清除结果
const clearResults = () => {
  recognitionResults.value = [];
};

// 组件挂载时初始化
onMounted(async () => {
  await checkServiceStatus();
  await checkMicrophonePermission();
});
</script>

<style lang="scss" scoped>
.voice-test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: rgba(0, 21, 65, 0.6);
  border-radius: 12px;
  color: white;
  
  .test-header {
    text-align: center;
    margin-bottom: 30px;
    
    h3 {
      color: #00ffaa;
      margin-bottom: 8px;
    }
    
    p {
      color: rgba(255, 255, 255, 0.7);
      margin: 0;
    }
  }
  
  .test-content {
    space-y: 30px;
    
    > div:not(:last-child) {
      margin-bottom: 30px;
    }
  }
  
  .status-section, .recording-section, .result-section {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 170, 0.2);
    
    h4 {
      color: #00ffaa;
      margin-bottom: 15px;
      font-size: 16px;
    }
  }
  
  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 15px;
    
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      border-left: 3px solid #666;
      
      &.success {
        border-left-color: #00ffaa;
        background: rgba(0, 255, 170, 0.1);
      }
      
      .status-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
      }
      
      .status-value {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
  
  .recording-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    
    .el-button {
      width: 80px;
      height: 80px;
      font-size: 24px;
      transition: all 0.3s ease;
      
      &.recording {
        animation: pulse 1.5s infinite;
        box-shadow: 0 0 20px rgba(255, 77, 79, 0.5);
      }
      
      &:hover {
        transform: scale(1.1);
      }
    }
    
    .recording-info, .processing-info {
      text-align: center;
      
      .recording-time {
        font-size: 24px;
        font-weight: bold;
        color: #ff4d4f;
        margin-bottom: 5px;
      }
      
      .recording-hint, .processing-text {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
  
  .results-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 15px;
    
    .result-item {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 10px;
      
      &.success {
        border-color: rgba(0, 255, 170, 0.3);
        background: rgba(0, 255, 170, 0.05);
      }
      
      &.error {
        border-color: rgba(255, 77, 79, 0.3);
        background: rgba(255, 77, 79, 0.05);
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        
        .result-index {
          font-weight: bold;
          color: #00ffaa;
        }
        
        .result-time {
          color: rgba(255, 255, 255, 0.6);
        }
        
        .result-status {
          font-weight: 500;
        }
      }
      
      .result-content {
        .result-text {
          font-size: 14px;
          line-height: 1.4;
          margin-bottom: 5px;
        }
        
        .result-details {
          color: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .voice-test-container {
    padding: 15px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .recording-controls .el-button {
    width: 60px;
    height: 60px;
    font-size: 20px;
  }
}
</style> 