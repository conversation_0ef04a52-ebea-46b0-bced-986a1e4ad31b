import type { AxiosResponse } from 'axios'
import request from '@/utils/request'
import { SIMULATION_CONFIG } from '@/config/business'

// 微气候数据类型定义
export interface MicroclimateData {
  temperature: number[]
  humidity: number[]
  illuminance: number[]
  rainfall: number[]
  timestamps: string[]
}

export interface KeyIndicator {
  name: string
  value: string
  unit: string
}

export interface HistoryRecord {
  timestamp: string
  temperature: number
  humidity: number
  illuminance: number
  rainfall: number
}

export interface HeatmapData {
  day: string
  hour: string
  value: number
}

// 创建模拟的 AxiosResponse
function createMockResponse<T>(data: T): AxiosResponse<T> {
  return {
    data,
    status: 200,
    statusText: 'OK',
    headers: {},
    config: {} as any
  }
}

// 获取实时微气候数据
export function fetchRealtimeMicroclimateData(timeInterval: string) {
  // 实际项目中应该调用后端API
  // return request.get('/microclimate/realtime', { params: { timeInterval } })

  // 目前使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      let dataPoints = 0;

      // 解析时间间隔
      const match = timeInterval.match(/(\d+)([mhd])/)
      if (match) {
        const value = parseInt(match[1])
        const unit = match[2]

        if (unit === 'm') {
          // 分钟，每分钟一个数据点
          dataPoints = value
        } else if (unit === 'h') {
          // 小时，每5分钟一个数据点
          dataPoints = value * 12
        } else if (unit === 'd') {
          // 天，每小时一个数据点
          dataPoints = value * 24
        }
      } else {
        // 兼容旧格式
        switch (timeInterval) {
          case 'hour':
            dataPoints = 60 // 每分钟一个数据点
            break
          case 'day':
            dataPoints = 24 // 每小时一个数据点
            break
          case 'week':
            dataPoints = 7 // 每天一个数据点
            break
          case 'month':
            dataPoints = 30 // 每天一个数据点
            break
          default:
            dataPoints = 24
        }
      }

      // 生成时间戳
      const timestamps = Array(dataPoints).fill(0).map((_, i) => {
        // 根据时间间隔生成适当的时间戳格式
        if (timeInterval.endsWith('m') || timeInterval === 'hour') {
          // 分钟级别，显示 HH:MM
          const now = new Date()
          const time = new Date(now.getTime() - (dataPoints - i) * 60 * 1000)
          return `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
        } else if (timeInterval.endsWith('h') || timeInterval === 'day') {
          // 小时级别，显示 MM-DD HH:00
          const now = new Date()
          let timeOffset = dataPoints - i
          if (timeInterval.endsWith('h')) {
            // 对于小时间隔，每个数据点代表5分钟
            timeOffset = timeOffset * 5
          }
          const time = new Date(now.getTime() - timeOffset * 60 * 1000)
          return `${(time.getMonth() + 1).toString().padStart(2, '0')}-${time.getDate().toString().padStart(2, '0')} ${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
        } else if (timeInterval.endsWith('d') || timeInterval === 'week' || timeInterval === 'month') {
          // 天级别，显示 MM-DD
          const now = new Date()
          const time = new Date(now.getTime() - (dataPoints - i) * 24 * 60 * 60 * 1000)
          return `${(time.getMonth() + 1).toString().padStart(2, '0')}-${time.getDate().toString().padStart(2, '0')}`
        } else {
          return `数据点 ${i + 1}`
        }
      })

      const data: MicroclimateData = {
        temperature: Array(dataPoints).fill(0).map((_, i) => 20 + Math.sin(i / (dataPoints / 8)) * 5 + Math.random() * 2),
        humidity: Array(dataPoints).fill(0).map((_, i) => 60 + Math.sin(i / (dataPoints / 4)) * 15 + Math.random() * 5),
        illuminance: Array(dataPoints).fill(0).map((_, i) => {
          // 根据时间生成合理的光照数据
          const hourOfDay = (i % 24)
          if (hourOfDay >= 6 && hourOfDay <= 18) {
            return 5000 + Math.sin((hourOfDay - 6) / 4 * Math.PI) * 10000 + Math.random() * 1000
          }
          return Math.random() * 500
        }),
        rainfall: Array(dataPoints).fill(0).map((_, i) => {
          // 随机生成雨量数据
          if (Math.random() > 0.8) {
            return Math.random() * 1.5
          }
          return 0
        }),
        timestamps
      }

      resolve(createMockResponse(data))
    }, SIMULATION_CONFIG.API_DELAY / 2) // 使用较短的延迟
  })
}

// 获取当前关键指标
export function fetchKeyIndicators() {
  // 实际项目中应该调用后端API
  // return request.get('/microclimate/indicators')

  // 目前使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      const data: KeyIndicator[] = [
        { name: '当前温度', value: (20 + Math.random() * 10).toFixed(1), unit: '°C' },
        { name: '当前湿度', value: Math.round(50 + Math.random() * 30).toString(), unit: '%' },
        { name: '光照强度', value: Math.round(10000 + Math.random() * 5000).toString(), unit: 'lux' },
        { name: '今日雨量', value: (Math.random() * 2).toFixed(1), unit: 'mm' }
      ]
      resolve(createMockResponse(data))
    }, SIMULATION_CONFIG.API_DELAY / 3) // 使用较短的延迟
  })
}

// 获取历史数据
export function fetchHistoryData(date: string, page = 1, pageSize = 10) {
  // 实际项目中应该调用后端API
  // return request.get('/microclimate/history', { params: { date, page, pageSize } })

  // 目前使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      const total = 24
      const records: HistoryRecord[] = []

      const startHour = (page - 1) * pageSize
      const endHour = Math.min(startHour + pageSize, total)

      for (let hour = startHour; hour < endHour; hour++) {
        records.push({
          timestamp: `${date} ${hour.toString().padStart(2, '0')}:00:00`,
          temperature: 20 + Math.sin(hour / 3) * 5 + Math.random() * 2,
          humidity: 60 + Math.sin(hour / 6) * 15 + Math.random() * 5,
          illuminance: hour >= 6 && hour <= 18
            ? 5000 + Math.sin((hour - 6) / 4 * Math.PI) * 10000 + Math.random() * 1000
            : Math.random() * 500,
          rainfall: hour >= 14 && hour <= 18 && Math.random() > 0.7
            ? Math.random() * 1.5
            : 0
        })
      }

      resolve(createMockResponse({ total, records }))
    }, SIMULATION_CONFIG.API_DELAY / 2) // 使用较短的延迟
  })
}

// 获取多日对比数据
export function fetchComparisonData(dates: string[]) {
  // 实际项目中应该调用后端API
  // return request.get('/microclimate/comparison', { params: { dates } })

  // 目前使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      const result: { [date: string]: MicroclimateData } = {}

      dates.forEach(date => {
        const hourlyData: MicroclimateData = {
          temperature: Array(24).fill(0).map((_, i) => 20 + Math.sin(i / 3) * 5 + Math.random() * 2),
          humidity: Array(24).fill(0).map((_, i) => 60 + Math.sin(i / 6) * 15 + Math.random() * 5),
          illuminance: Array(24).fill(0).map((_, i) => {
            if (i >= 6 && i <= 18) {
              return 5000 + Math.sin((i - 6) / 4 * Math.PI) * 10000 + Math.random() * 1000
            }
            return Math.random() * 500
          }),
          rainfall: Array(24).fill(0).map((_, i) => {
            if (i >= 14 && i <= 18 && Math.random() > 0.7) {
              return Math.random() * 1.5
            }
            return 0
          }),
          timestamps: Array(24).fill(0).map((_, i) => `${i}:00`)
        }

        result[date] = hourlyData
      })

      resolve(createMockResponse(result))
    }, SIMULATION_CONFIG.API_DELAY) // 使用标准延迟
  })
}

// 获取热力图数据
export function fetchHeatmapData(dataType: string = 'temperature') {
  // 实际项目中应该调用后端API
  // return request.get('/microclimate/heatmap', { params: { dataType } })

  // 目前使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      const hours = Array(24).fill(0).map((_, i) => i.toString())
      const data: HeatmapData[] = []

      for (let i = 0; i < 7; i++) {
        for (let j = 0; j < 24; j++) {
          let value: number

          if (dataType === 'temperature') {
            // 温度: 15-35°C
            value = Math.round((Math.random() * 20 + 15) * 10) / 10
          } else if (dataType === 'humidity') {
            // 湿度: 30-90%
            value = Math.round(Math.random() * 60 + 30)
          } else if (dataType === 'illuminance') {
            // 光照: 0-20000 lux
            value = j >= 6 && j <= 18
              ? Math.round(Math.random() * 15000 + 5000)
              : Math.round(Math.random() * 500)
          } else {
            // 雨量: 0-5mm
            value = Math.random() > 0.8 ? Math.round(Math.random() * 50) / 10 : 0
          }

          data.push({
            day: days[i],
            hour: hours[j],
            value
          })
        }
      }

      resolve(createMockResponse(data))
    }, SIMULATION_CONFIG.API_DELAY) // 使用标准延迟
  })
}

// 获取数据同步状态
export function fetchSyncStatus() {
  // 实际项目中应该调用后端API
  // return request.get('/microclimate/sync-status')

  // 目前使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      const random = Math.random()
      let status: 'normal' | 'warning' | 'error' = 'normal'
      let progress = 100
      let reason = ''

      if (random > 0.95) {
        status = 'error'
        progress = 30
        reason = '数据传输网络错误'
      } else if (random > 0.85) {
        status = 'warning'
        progress = 70
        reason = '传感器数据延迟'
      }

      resolve(createMockResponse({ status, progress, reason }))
    }, SIMULATION_CONFIG.API_DELAY / 3) // 使用较短的延迟
  })
}
