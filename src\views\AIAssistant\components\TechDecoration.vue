<template>
  <div class="tech-decoration">
    <!-- 叶子装饰 -->
    <div class="leaf leaf-1"></div>
    <div class="leaf leaf-2"></div>
    <div class="leaf leaf-3"></div>
    
    <!-- 数据线 -->
    <div class="data-line line-1"></div>
    <div class="data-line line-2"></div>
    <div class="data-line line-3"></div>
    
    <!-- 科技节点 -->
    <div class="tech-node node-1"></div>
    <div class="tech-node node-2"></div>
    <div class="tech-node node-3"></div>
    <div class="tech-node node-4"></div>
    
    <!-- 背景网格 -->
    <div class="grid-overlay"></div>
  </div>
</template>

<script setup lang="ts">
// 纯装饰组件，无需脚本
</script>

<style lang="scss" scoped>
.tech-decoration {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 1;
  
  // 叶子装饰
  .leaf {
    position: absolute;
    width: 30px;
    height: 60px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M12 2c5.523 0 10 4.477 10 10 0 .727-.077 1.435-.225 2.118l-1.782-1.783a8 8 0 1 0-4.116 6.66l1.782 1.783A10 10 0 1 1 12 2z' fill='rgba(0,255,170,0.2)'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.4;
    filter: blur(1px);
    
    &.leaf-1 {
      top: 10%;
      left: 5%;
      transform: rotate(-15deg) scale(1.2);
      animation: float 15s infinite ease-in-out;
    }
    
    &.leaf-2 {
      top: 30%;
      right: 8%;
      transform: rotate(45deg) scale(0.8);
      animation: float 18s infinite ease-in-out reverse;
    }
    
    &.leaf-3 {
      bottom: 15%;
      left: 15%;
      transform: rotate(120deg);
      animation: float 20s infinite ease-in-out;
    }
  }
  
  // 数据线
  .data-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(0, 255, 170, 0.3) 20%, 
      rgba(0, 255, 170, 0.5) 50%, 
      rgba(0, 255, 170, 0.3) 80%, 
      transparent 100%);
    
    &.line-1 {
      top: 15%;
      left: 0;
      right: 0;
      animation: data-flow 10s infinite linear;
    }
    
    &.line-2 {
      top: 45%;
      left: 0;
      right: 0;
      animation: data-flow 15s infinite linear reverse;
    }
    
    &.line-3 {
      bottom: 25%;
      left: 0;
      right: 0;
      animation: data-flow 12s infinite linear;
    }
  }
  
  // 科技节点
  .tech-node {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(0, 255, 170, 0.6);
    box-shadow: 0 0 10px rgba(0, 255, 170, 0.8);
    
    &::after {
      content: '';
      position: absolute;
      inset: -4px;
      border-radius: 50%;
      border: 1px solid rgba(0, 255, 170, 0.3);
      animation: pulse 3s infinite;
    }
    
    &.node-1 {
      top: 15%;
      left: 20%;
      animation: node-glow 4s infinite alternate;
    }
    
    &.node-2 {
      top: 25%;
      right: 15%;
      animation: node-glow 5s infinite alternate-reverse;
    }
    
    &.node-3 {
      bottom: 30%;
      left: 30%;
      animation: node-glow 6s infinite alternate;
    }
    
    &.node-4 {
      bottom: 20%;
      right: 25%;
      animation: node-glow 7s infinite alternate-reverse;
    }
  }
  
  // 背景网格
  .grid-overlay {
    position: absolute;
    inset: 0;
    background-image: linear-gradient(rgba(0, 255, 170, 0.05) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(0, 255, 170, 0.05) 1px, transparent 1px);
    background-size: 40px 40px;
    opacity: 0.2;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0);
  }
  50% {
    transform: translateY(-15px) rotate(5deg);
  }
}

@keyframes data-flow {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes node-glow {
  0% {
    opacity: 0.4;
    box-shadow: 0 0 5px rgba(0, 255, 170, 0.6);
  }
  100% {
    opacity: 0.8;
    box-shadow: 0 0 15px rgba(0, 255, 170, 0.9), 0 0 5px rgba(24, 144, 255, 0.7);
  }
}
</style> 