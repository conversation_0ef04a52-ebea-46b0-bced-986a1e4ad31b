<!-- 
  ControlComparison.vue
  消杀效果对比分析模块
  对比不同防治方法的效果、成本和环境影响，为农田害虫防治提供决策支持
-->
<template>
  <div class="control-comparison">
    <!-- 页面标题 -->
    <PageHeader
      title="消杀效果对比分析"
      description="对比不同防治方法的效果、成本和环境影响，为农田害虫防治提供决策支持"
      icon="DataAnalysis"
    >
      <template #actions>
        <div class="analysis-summary">
          <div class="summary-item">
            <span class="summary-value">{{ selectedMethods.length }}</span>
            <span class="summary-label">已选方法</span>
          </div>
          <div class="summary-item" v-if="showResults">
            <span class="summary-value">{{ summaryData.effectivenessScore }}</span>
            <span class="summary-label">综合评分</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <div class="main-content">
      <!-- 筛选条件面板 -->
      <DataPanel title="筛选条件" class="filter-panel">
        <div class="filter-controls">
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="filter-item">
                <label class="filter-label">选择虫害类型</label>
                <el-select v-model="selectedPestType" placeholder="请选择虫害类型" class="filter-input">
                  <el-option v-for="pest in pestTypes" :key="pest.value" :value="pest.value" :label="pest.label">
                  </el-option>
                </el-select>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="filter-item">
                <label class="filter-label">选择防治方法</label>
                <el-select 
                  v-model="selectedMethods" 
                  multiple 
                  placeholder="可选择多种防治方法对比"
                  class="filter-input"
                >
                  <el-option v-for="method in controlMethods" :key="method.value" :value="method.value" :label="method.label"></el-option>
                </el-select>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="filter-item">
                <label class="filter-label">时间范围</label>
                <el-date-picker 
                  v-model="dateRange" 
                  type="daterange" 
                  range-separator="至" 
                  start-placeholder="开始日期" 
                  end-placeholder="结束日期" 
                  class="filter-input" 
                />
              </div>
            </el-col>
          </el-row>
          <div class="filter-actions">
            <el-button type="primary" @click="generateComparison" :loading="loading">
              <el-icon><DataAnalysis /></el-icon>
              生成对比分析
            </el-button>
            <el-button @click="resetFilters">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </DataPanel>
      
      <!-- 无数据占位 -->
      <div v-if="!showResults" class="no-data-wrapper">
        <el-empty description="请选择参数并生成对比分析" :image-size="200">
          <template #image>
            <el-icon class="no-data-icon"><DataAnalysis /></el-icon>
          </template>
        </el-empty>
      </div>
      
      <!-- 分析结果区域 -->
      <div v-if="showResults" class="results-section">
        <!-- 分析结果展示 -->
        <div class="analysis-results">
          <div class="results-header">
            <h2>分析结果</h2>
            <div class="header-actions">
              <span class="update-time">更新时间: {{ formatTime(new Date()) }}</span>
              <div class="export-buttons">
                <el-dropdown @command="exportAnalysisResults">
                  <el-button type="primary" size="small">
                    导出分析 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="image">导出为图片</el-dropdown-item>
                      <el-dropdown-item command="pdf">导出为PDF</el-dropdown-item>
                      <el-dropdown-item command="excel">导出为Excel</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 分析结果概览 -->
          <DataPanel title="概述" class="summary-panel">
            <div class="summary-content">
              <p v-html="analysisResults.summary"></p>
              <p v-html="analysisResults.conclusion"></p>
            </div>
          </DataPanel>

          <!-- 图表展示 -->
          <div :class="['charts-container', {'mobile-charts': isMobile}]">
            <DataPanel title="防治效果对比" class="chart-panel">
              <div class="chart-wrapper">
                <div ref="effectivenessChart" class="chart-container"></div>
              </div>
            </DataPanel>

            <DataPanel title="成本效益分析" class="chart-panel">
              <div class="chart-wrapper">
                <div ref="costBenefitChart" class="chart-container"></div>
              </div>
            </DataPanel>

            <DataPanel title="环境影响评估" class="chart-panel">
              <div class="chart-wrapper">
                <div ref="environmentalImpactChart" class="chart-container"></div>
              </div>
            </DataPanel>
          </div>
        </div>

        <!-- 分析摘要 -->
        <div class="status-indicators">
          <div class="indicator-group">
            <StatusIndicator type="success" :label="`${bestMethod}推荐`" />
            <StatusIndicator type="normal" label="数据分析完成" />
            <StatusIndicator type="warning" label="AI预测中" />
          </div>
          <div class="refresh-info">
            <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
            <el-button type="primary" size="small" plain @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import type { EChartsOption, RadarSeriesOption } from 'echarts';
import {
  DataAnalysis, 
  Refresh, 
  View, 
  Setting, 
  Warning,
  ArrowDown
} from '@element-plus/icons-vue';
import PageHeader from './components/PageHeader.vue';
import DataPanel from './components/DataPanel.vue';
import StatusIndicator from './components/StatusIndicator.vue';
import html2canvas from 'html2canvas';
import { saveAs } from 'file-saver';

// 自定义类型
interface PestType {
  value: string;
  label: string;
}

interface ControlMethod {
  value: string;
  label: string;
  description: string;
}

interface SummaryData {
  effectivenessScore: number;
  costScore: number;
  environmentalScore: number;
  conclusion: string;
}

// 基础数据
const pestTypes = ref<PestType[]>([
  { value: 'aphid', label: '蚜虫' },
  { value: 'beetle', label: '甲虫' },
  { value: 'moth', label: '蛾类' },
  { value: 'mite', label: '螨虫' },
  { value: 'nematode', label: '线虫' },
]);

const controlMethods = ref<ControlMethod[]>([
  { value: 'chemical', label: '化学农药', description: '传统化学农药防治' },
  { value: 'biological', label: '生物防治', description: '天敌或微生物制剂' },
  { value: 'physical', label: '物理防治', description: '陷阱、粘板等物理措施' },
  { value: 'integrated', label: '综合防治', description: '多种方法协同使用' },
  { value: 'organic', label: '有机农药', description: '植物源和微生物源农药' }
]);

// 响应式数据
const selectedPestType = ref<string>('');
const selectedMethods = ref<string[]>([]);
const dateRange = ref<[Date, Date] | null>(null);
const showResults = ref<boolean>(false);
const bestMethod = ref<string>('');
const lastUpdateTime = ref(new Date());

// 图表引用
const effectivenessChart = ref<HTMLDivElement | null>(null);
const costBenefitChart = ref<HTMLDivElement | null>(null);
const environmentalImpactChart = ref<HTMLDivElement | null>(null);

// 图表实例
let effectivenessChartInstance: echarts.ECharts | null = null;
let costBenefitChartInstance: echarts.ECharts | null = null;
let environmentalImpactChartInstance: echarts.ECharts | null = null;

// 摘要数据
const summaryData = reactive<SummaryData>({
  effectivenessScore: 0,
  costScore: 0,
  environmentalScore: 0,
  conclusion: ''
});

// 分析结果
const analysisResults = ref({
  summary: '',
  conclusion: ''
});

// 获取害虫类型选项
const pestTypeOptions = ref([
  { value: 'insect', label: '昆虫' },
  { value: 'fungus', label: '真菌' },
  { value: 'weed', label: '杂草' },
  { value: 'virus', label: '病毒' },
  { value: 'nematode', label: '线虫' }
]);

// 选择的时间范围
const selectedTimeRange = ref('recent');
const timeRangeOptions = [
  { value: 'recent', label: '近期(1-6个月)' },
  { value: 'medium', label: '中期(6-12个月)' },
  { value: 'long', label: '长期(1年以上)' }
];

// 响应式处理
const isMobile = ref(window.innerWidth < 768);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 格式化时间
const formatTime = (date: Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

// 加载状态
const loading = ref(false);

// 生成对比分析
const generateComparison = async () => {
  try {
    // 验证输入
    if (!selectedPestType.value) {
      ElMessage.warning('请选择害虫类型');
      return;
    }

    if (selectedMethods.value.length < 2) {
      ElMessage.warning('请至少选择两种防治方法进行对比');
      return;
    }

    if (!dateRange.value || !dateRange.value[0] || !dateRange.value[1]) {
      ElMessage.warning('请选择时间范围');
      return;
    }

    // 显示加载中
    loading.value = true;
    
    // 模拟延迟，显示数据加载过程
    await new Promise(resolve => setTimeout(resolve, 800));

    // 生成模拟数据
    generateMockData();
    showResults.value = true;
    lastUpdateTime.value = new Date();
  
  // 等待DOM更新后渲染图表
    await nextTick();
    
    // 设置定时器确保DOM已准备好
    setTimeout(() => {
      renderCharts();
      // 滚动到结果区域
      const resultsElement = document.querySelector('.analysis-results');
      if (resultsElement) {
        resultsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 300);
    
    ElMessage.success('对比分析数据生成成功');
  } catch (error) {
    console.error('生成对比分析时出错:', error);
    ElMessage.error('生成分析数据失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 重置筛选条件
const resetFilters = () => {
  selectedPestType.value = '';
  selectedMethods.value = [];
  dateRange.value = null;
  showResults.value = false;
  
  // 清除图表
  disposeCharts();
  
  ElMessage.info('已重置所有筛选条件');
};

// 刷新数据
const refreshData = async () => {
  if (showResults.value) {
    try {
      loading.value = true;
      ElMessage.info('正在更新数据...');
      
      // 模拟网络请求延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 重新生成模拟数据
      generateMockData();
      
      // 重新渲染图表
      await nextTick();
      renderCharts();
      
      lastUpdateTime.value = new Date();
      ElMessage.success('数据已更新');
    } catch (error) {
      console.error('刷新数据时出错:', error);
      ElMessage.error('数据更新失败，请重试');
    } finally {
      loading.value = false;
    }
  }
};

// 处理方法选择变化
const handleMethodsChange = () => {
  if (showResults.value && selectedMethods.value.length > 0) {
    refreshData();
  }
};

// 处理时间范围变化
const handleTimeRangeChange = () => {
  if (showResults.value) {
    refreshData();
  }
};

// 添加监听
watch(selectedMethods, (newVal, oldVal) => {
  // 如果是初始化，不触发更新
  if (oldVal.length > 0 && newVal.length > 0 && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    handleMethodsChange();
  }
}, { deep: true });

watch(selectedTimeRange, () => {
  handleTimeRangeChange();
});

// 生成模拟数据
const generateMockData = () => {
  // 生成模拟数据
  const methods = selectedMethods.value;
  
  // 分析哪种方法最好
  const scores: Record<string, number> = {};
  
  methods.forEach(method => {
    // 为每种方法生成随机评分
    const effectScore = Math.floor(Math.random() * 50) + 50; // 50-100之间
    const costScore = Math.floor(Math.random() * 50) + 50;
    const envScore = Math.floor(Math.random() * 50) + 50;
    
    // 综合得分
    scores[method] = effectScore * 0.5 + costScore * 0.3 + envScore * 0.2;
  });
  
  // 找出最高分的方法
  let highestScore = 0;
  let bestMethodValue = '';
  
  Object.entries(scores).forEach(([method, score]) => {
    if (score > highestScore) {
      highestScore = score;
      bestMethodValue = method;
    }
  });
  
  // 设置最佳方法
  const bestMethodObj = controlMethods.value.find(m => m.value === bestMethodValue);
  bestMethod.value = bestMethodObj ? bestMethodObj.label : '';
  
  // 设置摘要数据
  summaryData.effectivenessScore = Math.floor(Math.random() * 50) + 50;
  summaryData.costScore = Math.floor(Math.random() * 50) + 50;
  summaryData.environmentalScore = Math.floor(Math.random() * 50) + 50;
  
  // 生成更智能的结论
  generateIntelligentConclusion(bestMethodValue, scores);
};

// 生成智能化结论
const generateIntelligentConclusion = (bestMethodValue: string, scores: Record<string, number>) => {
  // 获取最佳方法的名称
  const bestMethodObj = controlMethods.value.find(m => m.value === bestMethodValue);
  const bestMethodName = bestMethodObj ? bestMethodObj.label : bestMethodValue;
  
  // 获取所选害虫类型的名称
  const pestTypeName = pestTypes.value.find(p => p.value === selectedPestType.value)?.label || '害虫';
  
  // 获取时间范围
  const timeRange = dateRange.value ? 
    `${formatTime(dateRange.value[0], 'YYYY/MM/DD')}至${formatTime(dateRange.value[1], 'YYYY/MM/DD')}` : 
    '所选时间范围';
  
  // 计算各方法之间的分数差异
  const methodDiffs: Record<string, { method: string, diff: number }[]> = {};
  
  selectedMethods.value.forEach(method1 => {
    methodDiffs[method1] = [];
    selectedMethods.value.forEach(method2 => {
      if (method1 !== method2) {
        methodDiffs[method1].push({
          method: method2,
          diff: scores[method1] - scores[method2]
        });
      }
    });
    // 按差异排序，从大到小
    methodDiffs[method1].sort((a, b) => b.diff - a.diff);
  });
  
  // 生成最佳方法与其他方法的优势描述
  const bestMethodDiffs = methodDiffs[bestMethodValue] || [];
  let advantageText = '';
  
  if (bestMethodDiffs.length > 0) {
    // 找出劣势最大的方法
    const worstComparison = bestMethodDiffs[bestMethodDiffs.length - 1];
    const worstMethodName = controlMethods.value.find(m => m.value === worstComparison.method)?.label || worstComparison.method;
    
    if (worstComparison.diff > 15) {
      advantageText = `<strong>明显优于</strong>其他所有方法，特别是比${worstMethodName}高出<strong>${Math.abs(worstComparison.diff).toFixed(1)}分</strong>`;
    } else if (worstComparison.diff > 5) {
      advantageText = `<strong>总体表现较好</strong>，相比${worstMethodName}有<strong>${Math.abs(worstComparison.diff).toFixed(1)}分</strong>的优势`;
    } else {
      advantageText = `<strong>略优于</strong>其他方法，但与${worstMethodName}的差距较小，仅<strong>${Math.abs(worstComparison.diff).toFixed(1)}分</strong>`;
    }
  }
  
  // 生成适用场景建议
  let applicationScenario = '';
  switch (bestMethodValue) {
    case 'chemical':
      applicationScenario = '适用于<strong>发生面积大、危害严重</strong>的情况，但需注意对环境的影响';
      break;
    case 'biological':
      applicationScenario = '适用于<strong>长期防治和生态敏感区域</strong>，效果持续性好';
      break;
    case 'physical':
      applicationScenario = '适用于<strong>小规模精准防治</strong>，对环境友好';
      break;
    case 'integrated':
      applicationScenario = '适合<strong>综合考量成本和效果的大面积应用</strong>，平衡多方面因素';
      break;
    case 'organic':
      applicationScenario = '适用于<strong>有机农业和对环境要求高</strong>的区域，残留少';
      break;
    default:
      applicationScenario = '根据具体情况选择适用场景';
  }
  
  // 生成完整结论
  summaryData.conclusion = `在${timeRange}内针对<strong>${pestTypeName}</strong>的防治中，<strong>${bestMethodName}</strong>${advantageText}。
  该方法${applicationScenario}。建议在实际应用中结合当地气候条件和害虫发生规律，合理安排防治时机，以获得最佳效果。`;
  
  // 更新分析结果
  analysisResults.value.summary = `基于<strong>${selectedMethods.value.length}</strong>种防治方法的对比分析，我们可以得出以下结论：在<strong>${pestTypeName}</strong>的防治过程中，`;
  analysisResults.value.conclusion = `<strong>${bestMethodName}</strong>在综合考量成本效益、环境影响和防治效果后表现最佳。${advantageText}。${applicationScenario}。建议根据防治区域和目标制定合理的防治计划。`;
};

// 清除图表实例
const disposeCharts = () => {
  if (effectivenessChartInstance) {
    effectivenessChartInstance.dispose();
    effectivenessChartInstance = null;
  }
  
  if (costBenefitChartInstance) {
    costBenefitChartInstance.dispose();
    costBenefitChartInstance = null;
  }
  
  if (environmentalImpactChartInstance) {
    environmentalImpactChartInstance.dispose();
    environmentalImpactChartInstance = null;
  }
};

// 更新分析结果摘要
const updateResultsSummary = () => {
  // 生成分析结果摘要
  analysisResults.value.summary = `基于${selectedMethods.value.length}种防治方法的对比分析，${
    isMobile.value ? '可以' : '我们可以'
  }得出以下结论：在${pestTypeOptions.value.find((item: {value: string, label: string}) => item.value === selectedPestType.value)?.label || ''}的防治过程中，`;
  
  // 随机选择一个方法作为最佳方法
  const bestMethodIndex = Math.floor(Math.random() * selectedMethods.value.length);
  const bestMethod = controlMethods.value.find(
    m => m.value === selectedMethods.value[bestMethodIndex]
  )?.label || selectedMethods.value[bestMethodIndex];
  
  // 生成详细结论
  analysisResults.value.conclusion = `${bestMethod}在综合考量成本效益、环境影响和防治效果后表现最佳。该方法在${
    Math.random() > 0.5 ? '初期效果' : '持续性'
  }方面具有明显优势，同时其环境友好性评分也较高。建议在${
    selectedTimeRange.value === 'recent' ? '近期' : '长期'
  }防治计划中优先考虑该方法，并结合其他方法构建完整的防治体系。`;
  
  // 更新图表视觉效果（如果需要）
  if (effectivenessChartInstance && costBenefitChartInstance && environmentalImpactChartInstance) {
    // 可以在这里添加高亮显示最佳方法的逻辑
    setTimeout(() => {
      try {
        // 高亮最佳方法
        effectivenessChartInstance?.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: bestMethodIndex
        });
        
        costBenefitChartInstance?.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: bestMethodIndex
        });
        
        environmentalImpactChartInstance?.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: bestMethodIndex
        });
      } catch (error) {
        console.error('高亮最佳方法时出错:', error);
      }
    }, 500);
  }
};

// 渲染所有图表
const renderCharts = () => {
  // 确保DOM已经渲染完成
  setTimeout(() => {
    try {
      // 清除之前的图表实例
      disposeCharts();
      
      // 准备方法名称数据
      const methodLabels = selectedMethods.value.map(value => {
        const method = controlMethods.value.find(m => m.value === value);
        return method ? method.label : value;
      });
      
      // 有效性雷达图
      if (effectivenessChart.value) {
        effectivenessChartInstance = echarts.init(effectivenessChart.value as HTMLDivElement);
        
        // 使用any类型绕过类型检查
        const option: any = {
          tooltip: {
            trigger: 'axis'
    },
    legend: {
            data: methodLabels,
            textStyle: {
              color: '#9ca3af'
            },
            orient: isMobile.value ? 'horizontal' : 'vertical',
            left: isMobile.value ? 'center' : 'right',
            top: isMobile.value ? 'bottom' : 'middle',
            icon: 'roundRect'
          },
          radar: {
            indicator: [
              { name: '初期效果', max: 100 },
              { name: '持续性', max: 100 },
              { name: '总体效果', max: 100 },
              { name: '二次防效果', max: 100 }
            ],
            radius: isMobile.value ? '60%' : '70%',
            splitNumber: isMobile.value ? 3 : 5,
            axisName: {
              color: '#9ca3af'
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(75, 85, 99, 0.1)'
              }
            },
            splitArea: {
              show: true,
              areaStyle: {
                color: ['rgba(75, 85, 99, 0.02)', 'rgba(75, 85, 99, 0.05)']
              }
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(75, 85, 99, 0.3)'
              }
            }
          },
          series: [{
            type: 'radar',
            data: methodLabels.map((methodName, index) => {
              const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
              return {
                name: methodName,
                value: [
                  Math.floor(Math.random() * 100),
                  Math.floor(Math.random() * 100),
                  Math.floor(Math.random() * 100),
                  Math.floor(Math.random() * 100)
                ],
                lineStyle: {
                  width: 2,
                  color: colors[index % colors.length]
                },
                areaStyle: {
                  color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                    {
                      offset: 0,
                      color: colors[index % colors.length] + '80'
                    },
                    {
                      offset: 1,
                      color: colors[index % colors.length] + '10'
                    }
                  ])
                }
              };
            })
          }]
        };
        
        effectivenessChartInstance.setOption(option);
      }
      
      // 成本效益柱状图
      if (costBenefitChart.value) {
        costBenefitChartInstance = echarts.init(costBenefitChart.value as HTMLDivElement);
        const option: any = {
    tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(31, 41, 55, 0.8)',
            borderColor: '#3b82f6',
            textStyle: {
              color: '#e5e7eb'
            }
    },
    legend: {
            data: ['成本', '收益', '投资回报率'],
            textStyle: {
              color: '#9ca3af'
            },
            orient: isMobile.value ? 'horizontal' : 'vertical',
            left: isMobile.value ? 'center' : 'right',
            top: isMobile.value ? 'bottom' : 'middle',
            icon: 'roundRect'
    },
    grid: {
            left: isMobile.value ? '10%' : '3%',
            right: isMobile.value ? '10%' : '4%',
            bottom: isMobile.value ? '15%' : '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
            data: methodLabels,
            axisLabel: {
              color: '#9ca3af',
              rotate: isMobile.value ? 45 : 0,
              margin: isMobile.value ? 15 : 8,
            },
            axisLine: {
              lineStyle: {
                color: '#4b5563'
              }
            }
    },
    yAxis: [
      {
        type: 'value',
        name: '成本/收益(元)',
        min: 0,
              max: 10000,
              nameTextStyle: {
                color: '#9ca3af'
              },
              axisLabel: {
                color: '#9ca3af'
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(75, 85, 99, 0.1)'
                }
              }
      },
      {
        type: 'value',
        name: '投资回报率(%)',
        min: 0,
              max: 500,
              nameTextStyle: {
                color: '#9ca3af'
              },
              axisLabel: {
                color: '#9ca3af'
              },
              splitLine: {
                show: false
              }
      }
    ],
    series: [
      {
        name: '成本',
        type: 'bar',
              data: selectedMethods.value.map(() => Math.floor(Math.random() * 5000) + 1000),
              itemStyle: {
                color: '#ef4444'
              }
      },
      {
        name: '收益',
        type: 'bar',
              data: selectedMethods.value.map(() => Math.floor(Math.random() * 7000) + 3000),
              itemStyle: {
                color: '#10b981'
              }
      },
      {
        name: '投资回报率',
        type: 'line',
        yAxisIndex: 1,
              data: selectedMethods.value.map(() => Math.floor(Math.random() * 300) + 100),
              lineStyle: {
                width: 3,
                color: '#3b82f6',
                shadowColor: 'rgba(59, 130, 246, 0.3)',
                shadowBlur: 10
              },
              symbol: 'emptyCircle',
              symbolSize: 8,
              itemStyle: {
                color: '#3b82f6',
                borderColor: '#1f2937',
                borderWidth: 2
              }
            }
          ]
        };
        costBenefitChartInstance.setOption(option);
      }
      
      // 环境影响折线图
      if (environmentalImpactChart.value) {
        environmentalImpactChartInstance = echarts.init(environmentalImpactChart.value as HTMLDivElement);
        const option: any = {
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(31, 41, 55, 0.8)',
            borderColor: '#3b82f6',
            textStyle: {
              color: '#e5e7eb'
            }
          },
          legend: {
            data: methodLabels,
            textStyle: {
              color: '#9ca3af'
            },
            orient: isMobile.value ? 'horizontal' : 'vertical',
            left: isMobile.value ? 'center' : 'right',
            top: isMobile.value ? 'bottom' : 'center',
            icon: 'roundRect'
    },
    radar: {
      indicator: [
        { name: '农药残留', max: 100 },
        { name: '水体污染', max: 100 },
        { name: '土壤影响', max: 100 },
        { name: '生物多样性', max: 100 },
        { name: '空气质量', max: 100 }
            ],
            shape: 'circle',
            splitNumber: isMobile.value ? 3 : 5,
            axisName: {
              color: '#9ca3af'
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(75, 85, 99, 0.3)'
              }
            },
            splitArea: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(75, 85, 99, 0.3)'
              }
            },
            radius: isMobile.value ? '60%' : '70%',
          },
          series: [{
        type: 'radar',
            data: methodLabels.map((method, index) => {
              const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
              // 将十六进制颜色转换为rgba
              const hexToRgb = (hex: string) => {
                const rgb = hex.replace('#', '').match(/.{2}/g);
                if (!rgb) return '255,255,255';
                return rgb.map(x => parseInt(x, 16)).join(',');
              };
              
              return {
                name: method,
          value: [
            Math.floor(Math.random() * 100),
            Math.floor(Math.random() * 100),
            Math.floor(Math.random() * 100),
            Math.floor(Math.random() * 100),
            Math.floor(Math.random() * 100)
          ],
                areaStyle: {
                  color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                    {
                      offset: 0,
                      color: `rgba(${hexToRgb(colors[index % colors.length])},0.6)`
                    },
                    {
                      offset: 1,
                      color: `rgba(${hexToRgb(colors[index % colors.length])},0.2)`
                    }
                  ])
                },
                lineStyle: {
                  width: 2,
                  color: colors[index % colors.length]
                }
              };
            })
          }]
        };
        environmentalImpactChartInstance.setOption(option);
      }
      
      // 更新分析结果的视觉效果
      updateResultsSummary();
    } catch (error) {
      console.error('渲染图表时出错:', error);
      ElMessage.error('渲染图表时出错，请刷新页面重试');
    }
  }, 300); // 确保DOM已经渲染完成
};

// 窗口大小变化时自动调整图表大小
const handleResize = () => {
  checkMobile(); // 检查是否为移动设备
  
  if (effectivenessChartInstance) {
    effectivenessChartInstance.resize();
  }
  if (costBenefitChartInstance) {
    costBenefitChartInstance.resize();
  }
  if (environmentalImpactChartInstance) {
    environmentalImpactChartInstance.resize();
  }
};

// 数据更新计时器
let dataUpdateInterval: number | null = null;
let resizeObserver: ResizeObserver | null = null;

onMounted(() => {
  window.addEventListener('resize', handleResize);
  
  // 为图表容器添加ResizeObserver，当容器大小变化时重新渲染图表
  if (typeof ResizeObserver !== 'undefined') {
    resizeObserver = new ResizeObserver(() => {
      handleResize(); // 简化处理逻辑，避免类型检查问题
    });
    
    if (effectivenessChart.value) {
      resizeObserver.observe(effectivenessChart.value as HTMLDivElement);
    }
    if (costBenefitChart.value) {
      resizeObserver.observe(costBenefitChart.value as HTMLDivElement);
    }
    if (environmentalImpactChart.value) {
      resizeObserver.observe(environmentalImpactChart.value as HTMLDivElement);
    }
  }
  
  // 如果已经有选择的方法和数据，初始化生成图表
  if (selectedMethods.value.length > 0 && showResults.value) {
    // 等待DOM完全渲染后再初始化图表
    setTimeout(() => {
      renderCharts();
    }, 300);
  }
  
  // 可选：设置定时自动更新数据
  if (import.meta.env.PROD) { // 只在生产环境中启用，避免开发环境中的干扰
    dataUpdateInterval = window.setInterval(() => {
      if (showResults.value) {
        refreshData();
      }
    }, 60000); // 每60秒更新一次
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  
  // 清除ResizeObserver
  if (resizeObserver) {
    if (effectivenessChart.value) {
      resizeObserver.unobserve(effectivenessChart.value as HTMLDivElement);
    }
    if (costBenefitChart.value) {
      resizeObserver.unobserve(costBenefitChart.value as HTMLDivElement);
    }
    if (environmentalImpactChart.value) {
      resizeObserver.unobserve(environmentalImpactChart.value as HTMLDivElement);
    }
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  
  // 清除计时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
  
  // 清除图表实例
  disposeCharts();
});

// 导出分析结果
const exportAnalysisResults = async (type: 'pdf' | 'image' | 'excel') => {
  try {
    loading.value = true;
    
    if (type === 'image') {
      // 导出为图片
      const resultsElement = document.querySelector('.analysis-results') as HTMLElement;
      if (!resultsElement) {
        ElMessage.error('未找到分析结果元素');
        return;
      }
      
      // 使用类型断言来规避类型检查问题
      const html2canvasFunc = html2canvas as any;
      const canvas = await html2canvasFunc(resultsElement, {
        scale: 2, // 提高图片质量
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff'
      });
      
      const imgData = canvas.toDataURL('image/png');
      const blob = dataURLtoBlob(imgData);
      
      // 使用类型断言来规避类型检查问题
      const saveAsFunc = saveAs as any;
      saveAsFunc(blob, `害虫防治对比分析_${formatTime(new Date(), 'YYYYMMDD_HHmmss')}.png`);
      
      ElMessage.success('分析结果已导出为图片');
    } else if (type === 'pdf') {
      // 导出为PDF
      ElMessage.info('PDF导出功能即将推出，敬请期待');
    } else if (type === 'excel') {
      // 导出为Excel
      ElMessage.info('Excel导出功能即将推出，敬请期待');
    }
  } catch (error) {
    console.error('导出分析结果时出错:', error);
    ElMessage.error('导出失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 将dataURL转换为Blob
const dataURLtoBlob = (dataurl: string) => {
  const arr = dataurl.split(',');
  if (arr.length < 2) return new Blob();
  
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new Blob([u8arr], { type: mime });
};
</script>

<style scoped>
.control-comparison {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 添加垂直滚动 */
}

.main-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 20px; /* 各个部分之间的间距 */
}

/* 筛选面板样式 */
.filter-panel {
  margin-bottom: 0;
}

/* 筛选条件样式 */
.filter-controls {
  padding: 5px;
}

.filter-item {
  margin-bottom: 16px;
}

.filter-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #9ca3af;
}

.filter-input {
  width: 100%;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 16px;
}

/* 结果区域样式 */
.results-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 分析结果面板 */
.analysis-results {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.results-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #e2e8f0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.update-time {
  color: #cbd5e1;
  font-size: 0.9rem;
}

.export-buttons {
  display: flex;
  gap: 8px;
}

/* 分析摘要 */
.analysis-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

.summary-panel {
  border-left: 4px solid #3b82f6;
}

.summary-content {
  line-height: 1.6;
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.01em;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.summary-content p {
  margin-bottom: 12px;
}

.summary-content strong {
  color: #60a5fa;
  font-weight: 600;
}

/* 图表展示 */
.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

.chart-panel {
  height: 100%;
}

.chart-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 300px;
  overflow: hidden;
  touch-action: pan-y;
}

.chart-container {
  height: 280px;
  width: 100%;
  margin: 10px 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(31, 41, 55, 0.3);
  flex: 1;
  min-height: 280px;
  touch-action: pan-x pan-y;
}

/* 添加清晰的视觉分隔 */
.DataPanel {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 无数据占位 */
.no-data-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  margin-top: 20px; /* 增加顶部间距 */
}

.no-data-icon {
  font-size: 80px;
  color: #3b4863;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.6);
  border-radius: 8px;
  margin-bottom: 10px; /* 确保底部有间距 */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #cbd5e1;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-controls {
    padding: 0;
  }
  
  .main-content {
    gap: 15px; /* 小屏幕上减少间距 */
  }
  
  .results-section {
    gap: 15px; /* 小屏幕上减少间距 */
  }
  
  .analysis-results {
    flex-direction: column;
    gap: 15px; /* 小屏幕上减少间距 */
  }
  
  .results-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    margin-top: 12px;
    width: 100%;
    justify-content: space-between;
  }
  
  .charts-container {
    grid-template-columns: 1fr;
    gap: 15px; /* 小屏幕上减少间距 */
  }
  
  .chart-container {
    height: 250px;
    min-height: 250px;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
    padding: 10px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }
  
  /* 加载中动画 */
  .el-loading-spinner {
    margin-top: -20px;
  }
  
  /* 改善触摸反馈 */
  .el-button {
    padding: 10px 16px;
  }
  
  .summary-content {
    font-size: 14px;
    line-height: 1.4;
  }
}
</style> 

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save
--> 