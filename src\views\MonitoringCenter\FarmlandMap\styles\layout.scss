/**
 * 新农田地图布局样式
 */
@use 'variables.scss' as vars;

// 主容器样式
.farmland-map-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: vars.$background-dark;
  color: vars.$text-light;
  overflow: hidden;
}

// 内容包装器
.map-content-wrapper {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

// 左侧固定面板
.left-panel {
  width: 320px;
  min-width: 320px;
  height: 100%;
  background-color: vars.$panel-background;
  border-right: 1px solid vars.$panel-border-color;
  position: relative;
  overflow: hidden;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  
  // 折叠状态
  &.collapsed {
    width: 50px;
    min-width: 50px;
  }
  
  // 折叠切换按钮
  .panel-toggle {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
    cursor: pointer;
  }
  
  // 面板内容
  .panel-content {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}

// 抽屉式侧边栏（保留右侧抽屉）
.side-drawer {
  position: relative;
  height: 100%;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible;
  z-index: 10;
  
  // 右侧抽屉
  &.right-drawer {
    width: 0;
    border-left: 1px solid rgba(0, 255, 170, 0.15);
    
    &.expanded {
      width: 380px;
    }
    
    .drawer-toggle {
      left: -15px;
    }
  }
  
  // 抽屉内容
  .drawer-content {
    width: 380px;
    height: 100%;
    overflow: hidden;
  }
  
  // 抽屉面板
  .drawer-panel {
    height: 100%;
    width: 100%;
  }
  
  // 抽屉切换按钮
  .drawer-toggle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background-color: rgba(0, 21, 65, 0.9);
    border: 1px solid rgba(0, 255, 170, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 100;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    
    &:hover {
      background-color: rgba(0, 255, 170, 0.2);
    }
    
    .el-icon {
      color: vars.$primary-color;
      font-size: 16px;
      transition: transform 0.3s ease;
      
      &.rotate-icon {
        transform: rotate(180deg);
      }
    }
  }
}

// 主内容区域
.main-content {
  flex: 1;
  position: relative;
  transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.right-expanded {
    margin-right: 0;
  }
}

// 地图画布容器
.map-canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

// 浮动控制组件
.floating-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
  
  .control-group {
    position: absolute;
    display: flex;
    flex-direction: column;
    gap: 15px;
    pointer-events: auto;
    
    &.top-right {
      top: 15px;
      right: 15px;
      max-height: calc(60vh - 30px);
      overflow: visible;
    }
    
    &.bottom-right {
      bottom: 15px;
      right: 15px;
      max-height: calc(30vh - 15px);
    }
  }
  
  .control-card {
    background: rgba(0, 21, 65, 0.8);
    border: 1px solid rgba(0, 255, 170, 0.2);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
      border-color: rgba(0, 255, 170, 0.4);
    }
    
    &.minimized {
      width: 40px;
      height: 40px;
      padding: 0;
      overflow: hidden;
    }
  }
}

// 状态指示器
.status-indicators {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  
  .indicator-group {
    display: flex;
    gap: 12px;
  }
  
  .refresh-info {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
    color: vars.$text-secondary;
  }
}

// 响应式布局
@media (max-width: vars.$breakpoint-md) {
  .left-panel {
    width: 280px;
    
    &.collapsed {
      width: 50px;
    }
  }
}

@media (max-width: vars.$breakpoint-sm) {
  .map-content-wrapper {
    flex-direction: column;
  }
  
  .left-panel {
    width: 100%;
    height: auto;
    max-height: 50vh;
    border-right: none;
    border-bottom: 1px solid vars.$panel-border-color;
    
    &.collapsed {
      height: 50px;
      min-height: 50px;
      width: 100%;
    }
    
    .panel-toggle {
      right: 15px;
      top: 10px;
    }
  }

  .side-drawer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100% !important;
    height: 40vh;
    transform: translateY(100%);
    z-index: 100;
    
    &.expanded {
      transform: translateY(0);
    }
    
    &.right-drawer {
      border: none;
      border-top: 1px solid rgba(0, 255, 170, 0.15);
      border-radius: 15px 15px 0 0;
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
      
      .drawer-toggle {
        top: -15px;
        left: 50%;
        right: auto;
        transform: translateX(-50%);
      }
      
      .drawer-content {
        width: 100%;
      }
    }
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 10px;
    
    .indicator-group,
    .refresh-info {
      width: 100%;
      justify-content: center;
    }
  }
  
  .floating-controls {
    .control-group {
      &.top-right,
      &.bottom-right {
        right: 10px;
      }
    }
  }
} 