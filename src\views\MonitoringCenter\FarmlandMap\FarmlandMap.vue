<template>
  <div class="farmland-map-container">
    <!-- 页面标题 -->
    <PageHeader
      title="智慧农场地图监控"
      description="实时监控农场设备位置、土壤状况及环境数据"
      icon="Map"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ deviceCount }}</span>
            <span class="summary-label">设备总数</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ alertCount }}</span>
            <span class="summary-label">警报数量</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <div class="map-content-wrapper">
      <!-- 左侧固定面板 -->
      <div class="left-panel" :class="{ 'collapsed': leftPanelCollapsed }">
        <div class="panel-toggle" @click="toggleLeftPanel">
          <el-button type="text" circle>
            <el-icon :class="{ 'rotate-icon': !leftPanelCollapsed }">
              <ArrowLeft v-if="!leftPanelCollapsed" />
              <ArrowRight v-else />
            </el-icon>
          </el-button>
        </div>
        <div class="panel-content">
          <!-- 设备详情面板 - 传递选中的设备数据 -->
          <device-panel :device-data="selectedDevice" />
        </div>
      </div>
      
      <!-- 中间主地图区域 -->
      <div class="main-content" :class="{ 'right-expanded': rightDrawerExpanded }">
        <!-- 地图画布容器 -->
        <div class="map-canvas-container">
          <map-canvas />
          
          <!-- 浮动控制组件容器 -->
          <div class="floating-controls">
            <div class="control-group top-right">
              <three-view-controls class="control-card" />
              <perspective-control class="control-card" />
            </div>
            <div class="control-group bottom-right">
              <device-legend class="control-card" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧抽屉式面板 -->
      <div class="side-drawer right-drawer" :class="{ 'expanded': rightDrawerExpanded }">
        <div class="drawer-toggle" @click="toggleRightDrawer">
          <el-icon :class="{ 'rotate-icon': !rightDrawerExpanded }">
            <ArrowLeft />
          </el-icon>
        </div>
        <div class="drawer-content">
          <!-- 警报列表面板 -->
          <alert-list class="drawer-panel" />
        </div>
      </div>
    </div>
    
    <!-- 状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="正常" />
        <StatusIndicator type="warning" label="警告" />
        <StatusIndicator type="error" label="危险" />
        <StatusIndicator type="offline" label="离线" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 移动设备上的抽屉控制按钮 -->
    <div class="mobile-drawer-controls">
      <div class="drawer-button" :class="{ 'active': !leftPanelCollapsed }" @click="toggleLeftPanel">
        <el-icon><Monitor /></el-icon>
        设备详情
      </div>
      <div class="drawer-button" :class="{ 'active': rightDrawerExpanded }" @click="toggleRightDrawer">
        <el-icon><Bell /></el-icon>
        实时警报
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue';
import { Refresh, ArrowLeft, ArrowRight, Monitor, Bell } from '@element-plus/icons-vue';
import MapCanvas from './components/MapCanvas.vue';
import MapControls from './components/MapControls.vue';
import PerspectiveControl from './components/PerspectiveControl.vue';
import DeviceLegend from './components/DeviceLegend.vue';
import DevicePanel from './components/DevicePanel.vue';
import AlertList from './components/AlertList.vue';
import PageHeader from '../components/PageHeader.vue';
import StatusIndicator from '../components/StatusIndicator.vue';
import ThreeViewControls from './components/ThreeViewControls.vue';
import { useDeviceData } from './composables/useDeviceData';
import { useWeatherData } from './composables/useWeatherData';
import { useAlertData } from './composables/useAlertData';

// 获取设备数据管理 
const { 
  selectedDevice, 
  refreshDeviceData, 
  devices,
  onlineDevices,
  standbyDevices,
  offlineDevices
} = useDeviceData();

// 获取警报数据管理
const { alerts } = useAlertData();

// 面板状态管理
const leftPanelCollapsed = ref(false); // 默认展开
const rightDrawerExpanded = ref(false);

// 切换左侧面板折叠状态
const toggleLeftPanel = () => {
  leftPanelCollapsed.value = !leftPanelCollapsed.value;
  // 在移动设备上，如果展开左侧面板，关闭右侧抽屉
  if (!leftPanelCollapsed.value && window.innerWidth <= 992) {
    rightDrawerExpanded.value = false;
  }
};

// 切换右侧抽屉
const toggleRightDrawer = () => {
  rightDrawerExpanded.value = !rightDrawerExpanded.value;
  // 在移动设备上，如果打开右侧抽屉，折叠左侧面板
  if (rightDrawerExpanded.value && window.innerWidth <= 992) {
    leftPanelCollapsed.value = true;
  }
};

// 数据状态
const lastUpdateTime = ref(new Date());

// 计算设备总数
const deviceCount = computed(() => {
  return devices.value ? devices.value.length : 0;
});

// 计算警报总数
const alertCount = computed(() => {
  return alerts ? alerts.value.length : 0;
});

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 刷新数据
const refreshData = () => {
  try {
    // 刷新设备数据
    refreshDeviceData();
    
    // 刷新天气数据
    const weatherDataModule = useWeatherData();
    // 使用类型断言来避免 TypeScript 错误
    const weatherData = weatherDataModule as any;
    if (weatherData.refreshWeatherData) {
      weatherData.refreshWeatherData();
    } else {
      console.log('天气数据刷新方法不存在，可能需要实现');
    }
    
    // 刷新警报数据
    const alertDataModule = useAlertData();
    // 使用类型断言来避免 TypeScript 错误
    const alertData = alertDataModule as any;
    if (alertData.refreshAlertData) {
      alertData.refreshAlertData();
    } else {
      console.log('警报数据刷新方法不存在，可能需要实现');
    }
    
    // 更新时间
    lastUpdateTime.value = new Date();
  } catch (error) {
    console.error('刷新数据时发生错误:', error);
  }
};

// 初始化数据
onMounted(async () => {
  console.log('FarmlandMap组件已挂载，开始初始化数据');
  
  // 初始化设备数据
  const { initDeviceData } = useDeviceData();
  initDeviceData();
  console.log('设备数据已初始化');
  
  // 初始化天气数据
  const { initWeatherData } = useWeatherData();
  initWeatherData();
  console.log('天气数据已初始化');
  
  // 初始化警报数据
  const { initAlertData } = useAlertData();
  initAlertData();
  console.log('警报数据已初始化');
  
  // 设置定时更新
  const updateInterval = setInterval(() => {
    lastUpdateTime.value = new Date();
  }, 30000); // 每30秒更新一次
  
  // 等待DOM更新完成后检查设备面板元素
  await nextTick();
  const devicePanelElement = document.querySelector('.device-detail-panel');
  console.log('设备详情面板元素:', devicePanelElement);
  if (devicePanelElement) {
    console.log('设备详情面板类名:', devicePanelElement.className);
  }
  
  // 组件卸载时清理资源
  onUnmounted(() => {
    console.log('FarmlandMap组件将卸载，开始清理资源');
    
    // 清除定时器
    clearInterval(updateInterval);
    
    try {
      // 停止警报数据模拟
      // 使用类型断言来避免 TypeScript 错误
      const alertData = useAlertData() as any;
      if (alertData.stopAlertSimulation) {
        alertData.stopAlertSimulation();
      }
      console.log('警报数据模拟已停止');
    } catch (error) {
      console.error('停止警报数据模拟时发生错误:', error);
    }
  });
});

// 添加全局的资源清理逻辑，确保在页面卸载时释放资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    console.log('页面即将卸载，清理所有资源');
    try {
      // 使用类型断言来避免 TypeScript 错误
      const deviceData = useDeviceData() as any;
      if (deviceData.stopDeviceSimulation) {
        deviceData.stopDeviceSimulation();
      }
    } catch (error) {
      console.error('停止设备模拟时发生错误:', error);
    }
  });
}
</script>

<style scoped lang="scss">
@use './styles/variables.scss' as vars;
@use './styles/responsive.scss' as responsive;
@use './styles/global.scss' as global;
@use './styles/layout.scss';
@use './styles/cards.scss';
@use './styles/mixins.scss' as mixins;
@use "sass:color";

.farmland-map-container {
  padding: vars.$card-padding;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: vars.$background-dark;
  color: vars.$text-light;
  position: relative;
  overflow: hidden;
  animation: fadeIn 0.5s ease-out; // 添加淡入动画
  
  // 优化背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 10% 10%, rgba(43, 255, 150, 0.08) 0%, transparent 60%),
      radial-gradient(circle at 90% 90%, rgba(0, 153, 255, 0.06) 0%, transparent 70%);
    z-index: -1;
  }
  
  // 优化网格背景
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      linear-gradient(rgba(43, 255, 150, 0.04) 1px, transparent 1px),
      linear-gradient(90deg, rgba(43, 255, 150, 0.04) 1px, transparent 1px);
    background-size: 25px 25px; // 调整网格大小
    z-index: -1;
    opacity: 0.7; // 调整透明度
  }
}

// 主内容包装器
.map-content-wrapper {
  display: flex;
  gap: 20px;
  flex: 1;
  margin-bottom: 20px;
  position: relative;
  animation: slideUp 0.4s ease-out 0.2s both; // 添加向上滑动动画，延迟0.2秒
  min-height: 0; // 确保flex布局正确计算高度
  
  // 设备面板打开时调整地图容器
  .device-panel.is-open ~ & {
    .map-canvas-wrapper {
      transition: padding-right 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      
      @media (min-width: 1600px) {
        padding-right: 380px; // 大屏幕上为设备面板预留空间
      }
    }
  }
}

// 左侧区域
.left-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative; // 确保子元素能正确绝对定位
}

// 右侧区域
.right-section {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.map-canvas-wrapper {
  flex: 1;
  border-radius: vars.$border-radius;
  overflow: hidden;
  box-shadow: 0 4px 20px vars.$shadow-color;
  position: relative;
  transition: vars.$transition-normal;
  min-height: 500px; // 添加最小高度约束，防止拉伸
  
  &:hover {
    box-shadow: 0 10px 30px rgba(0, 10, 30, 0.25);
    transform: translateY(-2px); // 添加悬浮效果
  }
  
  // 优化边框发光效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: vars.$border-radius;
    box-shadow: 
      inset 0 0 0 1px rgba(43, 255, 150, 0.25),
      inset 0 0 20px rgba(0, 10, 30, 0.2); // 添加内阴影
    pointer-events: none;
    transition: vars.$transition-normal;
    z-index: 1;
  }
  
  &:hover::after {
    box-shadow: 
      inset 0 0 0 1px rgba(43, 255, 150, 0.4),
      inset 0 0 30px rgba(0, 10, 30, 0.15); // 悬浮时增强内阴影
  }
}

.map-controls-container {
  position: absolute; // 绝对定位
  top: 15px; // 距离顶部距离
  right: 15px; // 距离右侧距离
  display: flex;
  flex-direction: column; // 垂直排列
  gap: 10px; // 控制组件间距
  z-index: 10; // 确保在地图上方
  animation: slideUp 0.4s ease-out 0.3s both; // 添加向上滑动动画，延迟0.3秒
  width: 280px; // 设置固定宽度
  
  // 当设备面板打开时调整位置，避免重叠
  .device-panel.is-open + & {
    right: 400px; // 设备面板打开时右侧偏移
    transition: right 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  }
  
  // 响应式调整
  @media (max-width: 1200px) {
    width: 250px; // 略微减小宽度
    
    .device-panel.is-open + & {
      right: 350px; // 小屏幕上减小偏移
    }
  }
  
  @media (max-width: 768px) {
    width: 220px; // 移动设备上进一步减小宽度
    
    .device-panel.is-open + & {
      top: 70px; // 在移动设备上改为垂直错开
      right: 15px;
    }
  }
}

.control-card {
  @include mixins.card;
  transition: vars.$transition-normal;
  border-left: 3px solid vars.$primary-color;
  position: relative;
  overflow: hidden;
  background-color: rgba(0, 21, 65, 0.85); // 加深背景色以提高可见度
  backdrop-filter: blur(10px); // 增加模糊效果
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); // 增强阴影
  
  // 添加背景光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(43, 255, 150, 0.05) 0%, transparent 50%);
    z-index: 0;
    pointer-events: none;
  }
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 28px vars.$shadow-color;
    border-left: 3px solid vars.$primary-color;
    
    &::before {
      background: linear-gradient(135deg, rgba(43, 255, 150, 0.08) 0%, transparent 60%);
    }
  }
  
  // 添加点击效果
  &:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px vars.$shadow-color;
    transition: vars.$transition-fast;
  }
  
  // 添加内容容器
  > * {
    position: relative;
    z-index: 1;
  }
}

/* 右侧面板项样式 */
.right-panel-item {
  background-color: rgba(0, 21, 65, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 255, 170, 0.15);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
  animation: fadeIn 0.6s ease-out;
  position: relative;
  transition: all 0.3s ease;
  
  // 添加微妙的边框发光效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 12px;
    box-shadow: inset 0 0 0 1px rgba(43, 255, 150, 0.2);
    pointer-events: none;
    z-index: 1;
  }
  
  // 确保第一个面板在上方
  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  
  // 确保第二个面板在下方，且动画稍晚
  &:nth-child(2) {
    animation-delay: 0.3s;
  }
}

/* 状态指示器区域 - 修复显示问题 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px; // 增加水平内边距
  background: vars.$background-panel;
  border-radius: vars.$border-radius;
  margin-top: 20px; // 确保与上方内容有间距
  box-shadow: 0 4px 15px vars.$shadow-color;
  backdrop-filter: blur(10px);
  transition: vars.$transition-normal;
  border-top: 1px solid rgba(255, 255, 255, 0.08); // 更微妙的边框
  animation: slideUp 0.4s ease-out 0.4s both; // 添加向上滑动动画，延迟0.4秒
  position: relative; // 确保相对定位
  z-index: 5; // 确保在其他元素上方
  
  // 设备面板打开时调整
  .device-panel.is-open ~ & {
    transition: padding-right 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    
    @media (min-width: 1600px) {
      padding-right: 400px; // 大屏幕上为设备面板预留空间
    }
  }
  
  &:hover {
    box-shadow: 0 6px 20px vars.$shadow-color;
  }
}

.indicator-group {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: vars.$text-secondary;
  font-size: 14px;
  
  span {
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: vars.$primary-color;
      margin-right: 8px;
      animation: pulse 2s infinite;
    }
  }
  
  .el-button {
    transition: vars.$transition-normal;
    border-color: rgba(43, 255, 150, 0.3);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(43, 255, 150, 0.2);
      background-color: rgba(43, 255, 150, 0.1);
    }
    
    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(43, 255, 150, 0.1);
    }
    
    .el-icon {
      transition: transform 0.3s ease;
    }
    
    &:hover .el-icon {
      transform: rotate(180deg);
    }
  }
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 25px; // 增加间距
  animation: fadeIn 0.5s ease-out;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease, filter 0.3s ease;
  position: relative;
  padding: 0 10px; // 添加水平内边距
  
  // 添加分隔线
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -12px;
    top: 20%;
    height: 60%;
    width: 1px;
    background: linear-gradient(to bottom, transparent, rgba(43, 255, 150, 0.3), transparent);
  }
  
  &:hover {
    transform: scale(1.08);
    filter: brightness(1.1);
  }
}

.summary-value {
  font-size: 26px; // 增大字体
  font-weight: 600;
  color: vars.$primary-color;
  text-shadow: 0 0 15px rgba(43, 255, 150, 0.4);
  margin-bottom: 4px; // 添加下边距
}

.summary-label {
  font-size: 14px;
  color: vars.$text-secondary;
  position: relative;
  
  // 添加下划线效果
  &::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 100%;
    height: 1px;
    background-color: vars.$primary-color;
    transition: transform 0.3s ease;
  }
  
  .summary-item:hover &::after {
    transform: translateX(-50%) scaleX(0.7);
  }
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .right-section {
    width: 300px;
  }
}

@media (max-width: 1200px) {
  .right-section {
    width: 280px; // 进一步减小右侧面板宽度
  }
  
  .summary-value {
    font-size: 24px; // 减小字体大小
  }
}

@media (max-width: 992px) {
  .right-section {
    width: 260px; // 再次减小右侧面板宽度
  }
  
  .status-summary {
    gap: 15px; // 减小间距
  }
  
  .summary-item:not(:last-child)::after {
    right: -8px; // 调整分隔线位置
  }
  
  .map-controls-container {
    gap: 10px; // 减小控制组件间距
  }
}

@media (max-width: 768px) {
  .farmland-map-container {
    padding: 10px; // 减小内边距
  }
  
  .map-content-wrapper {
    flex-direction: column;
    gap: 15px; // 减小间距
  }
  
  .right-section {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .right-panel-item {
    width: calc(50% - 8px);
    height: 500px;
  }
  
  .map-controls-container {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    
    > * {
      flex: 1 1 calc(33.33% - 10px);
      min-width: 200px;
    }
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
    padding: 15px; // 减小内边距
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
  }
  
  .refresh-info {
    width: 100%;
    justify-content: space-between;
  }
  
  .status-summary {
    width: 100%;
    justify-content: space-around;
    gap: 10px;
  }
  
  .summary-item {
    padding: 0 5px; // 减小内边距
    
    &:not(:last-child)::after {
      display: none; // 移除分隔线
    }
  }
}

@media (max-width: 576px) {
  .right-section {
    flex-direction: column;
  }
  
  .right-panel-item {
    width: 100%;
    height: auto;
    min-height: 400px;
  }
  
  .map-controls-container > * {
    flex: 1 1 100%; // 在超小屏幕上每行显示一个控制卡片
  }
  
  .summary-value {
    font-size: 22px; // 进一步减小字体大小
  }
  
  .summary-label {
    font-size: 12px; // 减小标签字体大小
  }
  
  .refresh-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    
    .el-button {
      width: 100%; // 按钮占满宽度
    }
  }
}

/* 添加动画效果 */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(43, 255, 150, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(43, 255, 150, 0); }
  100% { box-shadow: 0 0 0 0 rgba(43, 255, 150, 0); }
}

.el-button {
  &:active {
    animation: pulse 0.5s;
  }
}

// 控制器组件内的按钮样式优化
.control-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  
  .control-button {
    flex: 1;
    min-width: 90px;
    padding: 12px 15px;
    background-color: rgba(0, 21, 65, 0.7);
    color: vars.$text-light;
    border: 1px solid rgba(43, 255, 150, 0.2);
    border-radius: 8px;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    
    // 光效背景
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(43, 255, 150, 0.05) 0%, transparent 70%);
      z-index: 0;
      transition: all 0.3s ease;
    }
    
    // 图标样式
    .button-icon {
      font-size: 24px;
      margin-bottom: 8px;
      color: vars.$primary-color;
      position: relative;
      z-index: 1;
    }
    
    // 文本样式
    .button-text {
      font-weight: 500;
      position: relative;
      z-index: 1;
    }
    
    // 悬停效果
    &:hover {
      transform: translateY(-2px);
      border-color: rgba(43, 255, 150, 0.4);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
      
      &::before {
        background: linear-gradient(135deg, rgba(43, 255, 150, 0.1) 0%, transparent 80%);
      }
      
      .button-icon {
        color: color.adjust(vars.$primary-color, $lightness: 10%);
      }
    }
    
    // 点击效果
    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: all 0.1s ease;
    }
    
    // 激活状态
    &.active {
      background-color: rgba(43, 255, 150, 0.15);
      border-color: rgba(43, 255, 150, 0.5);
      
      .button-icon,
      .button-text {
        color: vars.$primary-color;
      }
      
      &::before {
        background: linear-gradient(135deg, rgba(43, 255, 150, 0.15) 0%, transparent 80%);
      }
    }
    
    // 禁用状态
    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      
      &:hover {
        transform: none;
        box-shadow: none;
        border-color: rgba(43, 255, 150, 0.2);
      }
    }
  }
}

// 在层次控制器中的选择器样式
.layer-selector {
  margin-top: 10px;
  
  .layer-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    background-color: rgba(0, 21, 65, 0.5);
    border: 1px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    
    .layer-icon {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: vars.$text-secondary;
    }
    
    .layer-name {
      flex: 1;
      font-size: 14px;
    }
    
    .layer-visibility {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: vars.$text-secondary;
      
      &.visible {
        color: vars.$primary-color;
      }
    }
    
    // 悬停效果
    &:hover {
      background-color: rgba(43, 255, 150, 0.08);
      border-color: rgba(43, 255, 150, 0.2);
    }
    
    // 选中效果
    &.selected {
      background-color: rgba(43, 255, 150, 0.1);
      border-color: rgba(43, 255, 150, 0.3);
      
      .layer-name {
        color: vars.$primary-color;
      }
    }
  }
}

// 移动设备上的控制按钮
.mobile-drawer-controls {
  display: none; // 默认隐藏
}

// 响应式调整
@media (max-width: vars.$breakpoint-sm) {
  .mobile-drawer-controls {
    display: flex;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 50;
    gap: 15px;
    
    .drawer-button {
      padding: 8px 15px;
      background: rgba(0, 21, 65, 0.85);
      border: 1px solid rgba(0, 255, 170, 0.2);
      border-radius: 20px;
      display: flex;
      align-items: center;
      gap: 5px;
      color: vars.$text-light;
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      
      &.active {
        background: rgba(0, 255, 170, 0.15);
        border-color: rgba(0, 255, 170, 0.4);
      }
      
      .el-icon {
        color: vars.$primary-color;
      }
    }
  }
  
  .status-indicators {
    display: none; // 在移动设备上隐藏底部状态指示器
  }
}
</style> 