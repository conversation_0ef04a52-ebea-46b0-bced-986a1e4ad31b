<!-- 
  PheromoneControlRecord.vue
  信息素防控记录模块
  记录和分析农业害虫信息素防控措施的实施效果和历史数据
-->
<template>
  <div class="pheromone-control-record">
    <!-- 页面标题 -->
    <PageHeader
      title="信息素防控记录"
      description="记录和分析农业害虫信息素防控措施的实施效果和历史数据"
      icon="DataAnalysis"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ pheromoneRecords.length }}</span>
            <span class="summary-label">记录总数</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getSuccessCount() }}</span>
            <span class="summary-label">成功防控</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 筛选与搜索区域 -->
    <div class="record-filtering-area">
      <el-form :model="filterForm" inline size="small">
        <el-form-item label="信息素类型">
          <el-select 
            v-model="filterForm.pheromoneTypes" 
            multiple 
            placeholder="选择信息素类型"
            collapse-tags
          >
            <el-option 
              v-for="item in pheromoneTypeOptions" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="使用时间范围">
          <el-date-picker
            v-model="filterForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="效果评估">
          <el-radio-group v-model="filterForm.effectiveness">
            <el-radio 
              v-for="item in effectivenessOptions" 
              :key="item.value" 
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-input 
            v-model="filterForm.searchKeyword" 
            placeholder="搜索关键词..." 
            prefix-icon="Search"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="resetFilters">重置筛选</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 时间轴与列表结合布局 -->
    <div class="record-layout">
      <!-- 左侧：时间轴 -->
      <div class="timeline-container">
        <div class="timeline-title">信息素防控时间轴</div>
        <div class="timeline">
          <div 
            v-for="record in sortedRecords" 
            :key="record.id"
            class="timeline-node" 
            :class="getStatusClass(record.status)"
          >
            <el-tooltip 
              effect="dark" 
              placement="right"
              :content="`${record.pheromoneType} - ${record.targetPest} - ${getStatusText(record.status)}`"
            >
              <div class="node-circle"></div>
            </el-tooltip>
            <div class="node-label">
              <div class="label-time">{{ new Date(record.applicationTime).toLocaleDateString() }}</div>
              <div class="label-type">{{ record.pheromoneType }}</div>
            </div>
            <div class="node-line"></div>
          </div>
        </div>
      </div>
      
      <!-- 右侧：详细记录列表与效果评估区 -->
      <div class="records-container">
        <!-- 记录卡片列表 -->
        <div class="record-cards">
          <div 
            v-for="record in sortedRecords" 
            :key="record.id"
            class="record-card"
            :class="getStatusClass(record.status)"
          >
            <!-- 效果指示器 -->
            <div class="effect-indicator">
              <el-icon v-if="record.status === 'success'"><Check /></el-icon>
              <el-icon v-else-if="record.status === 'partial'"><InfoFilled /></el-icon>
              <el-icon v-else><Close /></el-icon>
              <span>{{ getStatusText(record.status) }}</span>
            </div>
            
            <!-- 基本信息 -->
            <div class="card-info">
              <div class="info-item">
                <div class="item-label">信息素类型:</div>
                <div class="item-value">{{ record.pheromoneType }}</div>
              </div>
              <div class="info-item">
                <div class="item-label">目标害虫:</div>
                <div class="item-value">{{ record.targetPest }}</div>
              </div>
              <div class="info-item">
                <div class="item-label">使用时间:</div>
                <div class="item-value">{{ record.applicationTime }}</div>
              </div>
              <div class="info-item">
                <div class="item-label">悬挂位置:</div>
                <div class="item-value">{{ record.suspensionLocation }}</div>
              </div>
              <div class="info-item">
                <div class="item-label">使用数量:</div>
                <div class="item-value">{{ record.quantity }} {{ record.unit }}</div>
              </div>
            </div>
            
            <!-- 效果评估详情 -->
            <div class="effect-details">
              <div class="effect-title">效果评估详情</div>
              <div class="effect-metrics">
                <div class="metric-item">
                  <div class="metric-label">诱捕数量</div>
                  <div class="metric-value">{{ record.trapCatch }}</div>
                  <!-- 迷你图表 -->
                  <div class="mini-chart">
                    <div class="chart-bar" :style="{ width: `${Math.min(record.trapCatch / 2, 100)}%` }"></div>
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">害虫减少率</div>
                  <div class="metric-value">{{ record.pestReduction }}%</div>
                  <!-- 迷你图表 -->
                  <div class="mini-chart">
                    <div class="chart-bar" :style="{ width: `${record.pestReduction}%` }"></div>
                  </div>
                </div>
              </div>
              <div class="effect-notes">
                <div class="notes-label">备注:</div>
                <div class="notes-content">{{ record.notes }}</div>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="operation-bar">
              <el-button 
                size="small" 
                type="primary" 
                @click="editRecord(record)"
              >
                <el-icon><Edit /></el-icon>
                编辑记录
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="deleteRecord(record)"
              >
                <el-icon><Delete /></el-icon>
                删除记录
              </el-button>
              <el-button 
                size="small" 
                type="info" 
                @click="viewHistoricalData(record)"
              >
                <el-icon><Timer /></el-icon>
                查看历史数据
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="成功防控" />
        <StatusIndicator type="warning" label="部分成功" />
        <StatusIndicator type="error" label="防控失败" />
      </div>
      <div class="refresh-info">
        <span>最后更新时间: {{ lastUpdateTime }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 防控效果对比区 -->
    <div class="control-comparison-area">
      <div class="comparison-title">防控效果对比</div>
      <div class="comparison-chart">
        <div class="chart-container">
          <!-- 这里可以添加完整的图表组件，这里仅做简单展示 -->
          <div 
            v-for="(data, type) in effectivenessComparison" 
            :key="type"
            class="chart-bar-group"
          >
            <div class="bar-label">{{ type }}</div>
            <div class="bars">
              <div class="bar-item">
                <div class="bar-value">{{ data.trapSuccess }}</div>
                <div class="bar-container">
                  <div 
                    class="bar trap-bar" 
                    :style="{ height: `${Math.min(data.trapSuccess / 2, 100)}px` }"
                  ></div>
                </div>
                <div class="bar-title">诱捕成功率</div>
              </div>
              <div class="bar-item">
                <div class="bar-value">{{ data.pestReduction }}%</div>
                <div class="bar-container">
                  <div 
                    class="bar reduction-bar" 
                    :style="{ height: `${data.pestReduction}px` }"
                  ></div>
                </div>
                <div class="bar-title">害虫减少率</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Check, 
  Close, 
  Edit, 
  Delete, 
  Timer, 
  Refresh, 
  InfoFilled, 
  DataAnalysis,
  Search
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'

// 记录状态类型
type RecordStatus = 'success' | 'partial' | 'failed'

// 记录数据类型
interface PheromoneRecord {
  id: number
  pheromoneType: string
  targetPest: string
  applicationTime: string
  suspensionLocation: string
  quantity: number
  unit: string
  status: RecordStatus
  trapCatch: number
  pestReduction: number
  notes: string
}

// 效果对比数据类型
interface EffectivenessData {
  trapSuccess: number
  pestReduction: number
  count: number
}

// 效果对比数据集合类型
interface EffectivenessComparison {
  [key: string]: EffectivenessData
}

// 信息素记录数据
const pheromoneRecords = ref<PheromoneRecord[]>([
  {
    id: 1,
    pheromoneType: '性信息素',
    targetPest: '棉铃虫',
    applicationTime: '2023-05-15 08:30:00',
    suspensionLocation: '西北果园区',
    quantity: 50,
    unit: '个',
    status: 'success',
    trapCatch: 120,
    pestReduction: 75,
    notes: '悬挂在果树上，天气晴朗，效果显著'
  },
  {
    id: 2,
    pheromoneType: '聚集信息素',
    targetPest: '稻飞虱',
    applicationTime: '2023-05-05 14:20:00',
    suspensionLocation: '东南水稻区',
    quantity: 30,
    unit: '个',
    status: 'partial',
    trapCatch: 45,
    pestReduction: 40,
    notes: '部分区域效果明显，部分区域效果不佳'
  },
  {
    id: 3,
    pheromoneType: '报警信息素',
    targetPest: '蚜虫',
    applicationTime: '2023-04-20 10:45:00',
    suspensionLocation: '中心蔬菜区',
    quantity: 40,
    unit: '个',
    status: 'failed',
    trapCatch: 10,
    pestReduction: 15,
    notes: '释放后遇到降雨，效果不佳'
  },
  {
    id: 4,
    pheromoneType: '性信息素',
    targetPest: '小菜蛾',
    applicationTime: '2023-04-10 16:30:00',
    suspensionLocation: '西南蔬菜区',
    quantity: 60,
    unit: '个',
    status: 'success',
    trapCatch: 150,
    pestReduction: 80,
    notes: '针对小菜蛾释放，效果显著'
  },
  {
    id: 5,
    pheromoneType: '聚集信息素',
    targetPest: '小麦蚜虫',
    applicationTime: '2023-04-01 09:15:00',
    suspensionLocation: '北部麦田区',
    quantity: 45,
    unit: '个',
    status: 'success',
    trapCatch: 85,
    pestReduction: 70,
    notes: '春季小麦区域使用，效果良好'
  }
])

// 筛选条件
const filterForm = reactive({
  timeRange: [] as string[],
  pheromoneTypes: [] as string[],
  effectiveness: '',
  searchKeyword: ''
})

// 信息素类型选项
const pheromoneTypeOptions = [
  { label: '性信息素', value: '性信息素' },
  { label: '聚集信息素', value: '聚集信息素' },
  { label: '报警信息素', value: '报警信息素' }
]

// 效果评估选项
const effectivenessOptions = [
  { label: '全部', value: '' },
  { label: '成功', value: 'success' },
  { label: '部分成功', value: 'partial' },
  { label: '失败', value: 'failed' }
]

// 最后更新时间
const lastUpdateTime = ref(new Date().toLocaleString('zh-CN'))

// 获取成功防控数量
const getSuccessCount = (): number => {
  return pheromoneRecords.value.filter(record => record.status === 'success').length
}

// 获取状态类名
const getStatusClass = (status: RecordStatus): string => {
  switch (status) {
    case 'success':
      return 'status-success'
    case 'partial':
      return 'status-partial'
    case 'failed':
      return 'status-failed'
    default:
      return ''
  }
}

// 获取状态文本
const getStatusText = (status: RecordStatus): string => {
  switch (status) {
    case 'success':
      return '成功'
    case 'partial':
      return '部分成功'
    case 'failed':
      return '失败'
    default:
      return '未知'
  }
}

// 筛选数据
const filteredPheromoneRecords = computed(() => {
  return pheromoneRecords.value.filter(item => {
    // 时间范围筛选
    if (filterForm.timeRange && filterForm.timeRange.length === 2) {
      const recordDate = new Date(item.applicationTime)
      const startDate = new Date(filterForm.timeRange[0])
      const endDate = new Date(filterForm.timeRange[1])
      if (recordDate < startDate || recordDate > endDate) {
        return false
      }
    }
    
    // 信息素类型筛选
    if (filterForm.pheromoneTypes && filterForm.pheromoneTypes.length > 0) {
      if (!filterForm.pheromoneTypes.includes(item.pheromoneType)) {
        return false
      }
    }
    
    // 效果评估筛选
    if (filterForm.effectiveness && item.status !== filterForm.effectiveness) {
      return false
    }
    
    // 关键词搜索
    if (filterForm.searchKeyword) {
      const keyword = filterForm.searchKeyword.toLowerCase()
      return item.pheromoneType.toLowerCase().includes(keyword) ||
        item.targetPest.toLowerCase().includes(keyword) ||
        item.suspensionLocation.toLowerCase().includes(keyword) ||
        item.notes.toLowerCase().includes(keyword)
    }
    
    return true
  })
})

// 按时间排序的记录
const sortedRecords = computed(() => {
  return [...filteredPheromoneRecords.value].sort((a, b) => {
    return new Date(b.applicationTime).getTime() - new Date(a.applicationTime).getTime()
  })
})

// 根据信息素类型对比效果
const effectivenessComparison = computed<EffectivenessComparison>(() => {
  const data: EffectivenessComparison = {}
  
  pheromoneRecords.value.forEach(record => {
    if (!data[record.pheromoneType]) {
      data[record.pheromoneType] = {
        trapSuccess: 0,
        pestReduction: 0,
        count: 0
      }
    }
    
    data[record.pheromoneType].trapSuccess += record.trapCatch
    data[record.pheromoneType].pestReduction += record.pestReduction
    data[record.pheromoneType].count++
  })
  
  // 计算平均值
  Object.keys(data).forEach(key => {
    data[key].trapSuccess = Math.round(data[key].trapSuccess / data[key].count)
    data[key].pestReduction = Math.round(data[key].pestReduction / data[key].count)
  })
  
  return data
})

// 重置筛选条件
const resetFilters = () => {
  filterForm.timeRange = []
  filterForm.pheromoneTypes = []
  filterForm.effectiveness = ''
  filterForm.searchKeyword = ''
}

// 编辑记录
const editRecord = (record: PheromoneRecord) => {
  ElMessage({
    type: 'info',
    message: `正在编辑记录: ${record.pheromoneType} - ${record.applicationTime}`
  })
}

// 删除记录
const deleteRecord = (record: PheromoneRecord) => {
  ElMessageBox.confirm(`确定要删除 ${record.pheromoneType} (${record.applicationTime}) 的记录吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟删除操作
    const index = pheromoneRecords.value.findIndex(item => item.id === record.id)
    if (index !== -1) {
      pheromoneRecords.value.splice(index, 1)
      ElMessage({
        type: 'success',
        message: '记录已删除'
      })
    }
  }).catch(() => {})
}

// 查看历史数据
const viewHistoricalData = (record: PheromoneRecord) => {
  ElMessage({
    type: 'info',
    message: `查看 ${record.pheromoneType} 的历史数据`
  })
}

// 刷新数据
const refreshData = () => {
  // 更新最后更新时间
  lastUpdateTime.value = new Date().toLocaleString('zh-CN')
  ElMessage.success('数据已刷新')
}

onMounted(() => {
  // 初始化逻辑
  refreshData()
})
</script>

<style scoped>
.pheromone-control-record {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 筛选区域样式 */
.record-filtering-area {
  background-color: rgba(31, 41, 55, 0.5);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 时间轴与列表布局 */
.record-layout {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 20px;
  flex: 1;
  min-height: 0;
  overflow: hidden;
  margin-bottom: 20px;
}

/* 时间轴样式 */
.timeline-container {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.timeline-title {
  font-weight: 600;
  margin-bottom: 15px;
  color: #f9fafb;
  border-bottom: 1px solid #4b5563;
  padding-bottom: 8px;
}

.timeline {
  position: relative;
  margin-left: 10px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 11px;
  top: 0;
  height: 100%;
  width: 2px;
  background-color: #4b5563;
}

.timeline-node {
  position: relative;
  margin-bottom: 25px;
  padding-left: 30px;
}

.node-circle {
  position: absolute;
  left: 0;
  top: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #4b5563;
  z-index: 1;
}

.timeline-node.status-success .node-circle {
  background-color: #10b981;
  box-shadow: 0 0 10px #10b981;
}

.timeline-node.status-partial .node-circle {
  background-color: #f59e0b;
  box-shadow: 0 0 10px #f59e0b;
}

.timeline-node.status-failed .node-circle {
  background-color: #ef4444;
  box-shadow: 0 0 10px #ef4444;
}

.node-label {
  font-size: 0.9rem;
  color: #d1d5db;
}

.label-time {
  font-weight: 600;
  color: #f9fafb;
}

.node-line {
  position: absolute;
  left: 12px;
  top: 24px;
  height: 100%;
  width: 2px;
  background-color: transparent;
}

/* 记录列表样式 */
.records-container {
  overflow-y: auto;
}

.record-cards {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.record-card {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.record-card.status-success {
  border-left: 4px solid #10b981;
}

.record-card.status-partial {
  border-left: 4px solid #f59e0b;
}

.record-card.status-failed {
  border-left: 4px solid #ef4444;
}

.effect-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
}

.status-success .effect-indicator {
  color: #10b981;
}

.status-partial .effect-indicator {
  color: #f59e0b;
}

.status-failed .effect-indicator {
  color: #ef4444;
}

.card-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  gap: 5px;
}

.item-label {
  color: #9ca3af;
  font-size: 0.9rem;
}

.item-value {
  color: #f9fafb;
  font-weight: 500;
}

.effect-details {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 15px;
}

.effect-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #f9fafb;
}

.effect-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 10px;
}

.metric-item {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  padding: 8px;
}

.metric-label {
  font-size: 0.8rem;
  color: #9ca3af;
  margin-bottom: 3px;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #f9fafb;
  margin-bottom: 5px;
}

.mini-chart {
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.chart-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 3px;
  transition: width 0.5s ease-in-out;
}

.effect-notes {
  font-size: 0.9rem;
}

.notes-label {
  color: #9ca3af;
  margin-bottom: 3px;
}

.notes-content {
  color: #d1d5db;
  line-height: 1.4;
}

.operation-bar {
  display: flex;
  gap: 10px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-bottom: 20px;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 防控效果对比区样式 */
.control-comparison-area {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.comparison-title {
  font-weight: 600;
  margin-bottom: 15px;
  color: #f9fafb;
  border-bottom: 1px solid #4b5563;
  padding-bottom: 8px;
}

.comparison-chart {
  height: 200px;
  overflow-x: auto;
}

.chart-container {
  display: flex;
  gap: 40px;
  padding: 10px;
  height: 100%;
  justify-content: center;
}

.chart-bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.bar-label {
  font-weight: 600;
  color: #f9fafb;
  margin-bottom: 10px;
  text-align: center;
}

.bars {
  display: flex;
  gap: 15px;
  height: 100%;
  align-items: flex-end;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40px;
}

.bar-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #f9fafb;
  margin-bottom: 5px;
}

.bar-container {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: flex-end;
  margin-bottom: 8px;
}

.bar {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: height 0.5s ease-in-out;
}

.trap-bar {
  background: linear-gradient(180deg, #3b82f6, #1e40af);
}

.reduction-bar {
  background: linear-gradient(180deg, #10b981, #065f46);
}

.bar-title {
  font-size: 0.8rem;
  color: #9ca3af;
  text-align: center;
  width: 80px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .record-layout {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .card-info {
    grid-template-columns: 1fr;
  }
  
  .effect-metrics {
    grid-template-columns: 1fr;
  }
}
</style> 