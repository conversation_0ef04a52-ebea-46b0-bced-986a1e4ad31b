<template>
  <div class="video-display-container" :class="{ 'is-loading': !videoStream, 'is-fullscreen': isFullscreen, 'control-panel-open': isControlPanelOpen }">
    <!-- 视频容器 -->
    <div class="video-wrapper">
      <!-- 视频元素 -->
      <video
        ref="videoElement"
        class="video-element"
        autoplay
        playsinline
        muted
      ></video>

      <!-- AI检测框Canvas -->
      <canvas
        v-if="isAIDetectionEnabled"
        ref="detectCanvas"
        class="detect-canvas"
      ></canvas>

      <!-- 科技感边框和扫描线效果 -->
      <div class="tech-frame-effect">
        <div class="corner top-left"></div>
        <div class="corner top-right"></div>
        <div class="corner bottom-left"></div>
        <div class="corner bottom-right"></div>
        <div class="scan-line"></div>
        <div class="data-grid"></div>
      </div>

      <!-- 状态HUD覆盖层 -->
      <div class="status-hud" v-if="videoStream">
        <div class="hud-top">
          <div class="connection-status">
            <div class="status-dot" :class="{ 'connected': connected, 'connecting': connecting, 'error': hasError }"></div>
            <span>{{ getStatusText() }}</span>
          </div>
          <div class="connection-info">
            <span>{{ robotIP || '-' }}</span>
            <span>{{ connectionTime || '-' }}</span>
          </div>
        </div>

        <!-- 最新日志条目 -->
        <div class="log-ticker" v-if="latestLogs && latestLogs.length">
          <div class="log-entry" v-for="(log, index) in latestLogs" :key="index">
            <span class="log-time">[{{ log.time }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="loading-overlay" v-show="!videoStream">
        <div class="loading-animation">
          <svg class="loading-circle" viewBox="0 0 50 50">
            <circle cx="25" cy="25" r="20" fill="none" stroke-width="4" stroke="#3b82f6" />
          </svg>
          <el-icon class="loading-icon"><Loading /></el-icon>
        </div>
        <div class="loading-text">连接中，请稍候...</div>
        <div class="loading-subtext">正在建立视频流连接</div>
      </div>

      <!-- 错误状态 -->
      <div class="error-overlay" v-show="hasError">
        <div class="error-icon-container">
          <el-icon class="error-icon"><WarningFilled /></el-icon>
        </div>
        <div class="error-text">{{ errorMessage }}</div>
        <div class="error-subtext">请检查网络连接或机器狗状态</div>
        <el-button type="primary" size="small" @click="$emit('retry')" class="retry-button">
          <el-icon><RefreshRight /></el-icon>
          重试连接
        </el-button>
      </div>
    </div>

    <!-- 视频控制面板 -->
    <div class="video-controls" v-show="videoStream && !isFullscreen">
      <div class="video-status">
        <span class="status-dot" :class="{ 'connected': connected }"></span>
        <span class="status-text">实时视频流</span>
      </div>
      <el-button-group>
        <el-button
          type="primary"
          size="small"
          @click="toggleFullscreen"
          class="control-button"
        >
          <el-icon><FullScreen /></el-icon>
          <span>{{ isFullscreen ? '退出全屏' : '全屏' }}</span>
        </el-button>

        <el-button
          type="primary"
          size="small"
          @click="captureSnapshot"
          class="control-button"
        >
          <el-icon><Picture /></el-icon>
          <span>截图</span>
        </el-button>

        <el-button
          type="primary"
          size="small"
          @click="toggleControlPanel"
          class="control-button"
        >
          <el-icon><Operation /></el-icon>
          <span>控制面板</span>
        </el-button>

        <el-button
          type="primary"
          size="small"
          @click="toggleLogConsole"
          class="control-button"
        >
          <el-icon><Document /></el-icon>
          <span>日志</span>
        </el-button>
      </el-button-group>
    </div>

    <!-- 侧边控制面板 -->
    <div class="control-panel" :class="{ 'open': isControlPanelOpen }">
      <div class="panel-header">
        <h3>机器狗控制面板</h3>
        <el-button
          type="text"
          @click="toggleControlPanel"
          class="close-panel-button"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>

      <div class="panel-content">
        <!-- 基础动作控制 -->
        <div class="control-section">
          <h4>基础动作</h4>
          <div class="action-buttons">
            <el-button
              type="primary"
              @click="sendDogCommand(1004)"
              :disabled="!connected"
              class="action-button"
            >
              站立
            </el-button>
            <el-button
              type="primary"
              @click="sendDogCommand(1009)"
              :disabled="!connected"
              class="action-button"
            >
              坐下
            </el-button>
            <el-button
              type="primary"
              @click="sendDogCommand(1005)"
              :disabled="!connected"
              class="action-button"
            >
              趴下
            </el-button>
            <el-button
              type="primary"
              @click="sendDogCommand(1016)"
              :disabled="!connected"
              class="action-button"
            >
              打招呼
            </el-button>
          </div>
        </div>

        <!-- 移动控制 -->
        <div class="control-section">
          <h4>方向控制</h4>
          <div class="direction-control">
            <div class="direction-row">
              <div></div>
              <el-button
                type="primary"
                @click="sendMoveCommand('forward')"
                :disabled="!connected"
                class="direction-button"
              >
                <el-icon><ArrowUp /></el-icon>
              </el-button>
              <div></div>
            </div>
            <div class="direction-row">
              <el-button
                type="primary"
                @click="sendMoveCommand('left')"
                :disabled="!connected"
                class="direction-button"
              >
                <el-icon><ArrowLeft /></el-icon>
              </el-button>
              <el-button
                type="primary"
                @click="sendDogCommand(1003)"
                :disabled="!connected"
                class="direction-button stop"
              >
                停止
              </el-button>
              <el-button
                type="primary"
                @click="sendMoveCommand('right')"
                :disabled="!connected"
                class="direction-button"
              >
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
            <div class="direction-row">
              <div></div>
              <el-button
                type="primary"
                @click="sendMoveCommand('backward')"
                :disabled="!connected"
                class="direction-button"
              >
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <div></div>
            </div>
          </div>
        </div>

        <!-- 特殊动作 -->
        <div class="control-section">
          <h4>特殊动作</h4>
          <div class="action-buttons">
            <el-button
              type="success"
              @click="sendDogCommand(1022)"
              :disabled="!connected"
              class="action-button"
            >
              舞蹈1
            </el-button>
            <el-button
              type="success"
              @click="sendDogCommand(1023)"
              :disabled="!connected"
              class="action-button"
            >
              舞蹈2
            </el-button>
            <el-button
              type="success"
              @click="sendDogCommand(1030)"
              :disabled="!connected"
              class="action-button"
            >
              前翻
            </el-button>
            <el-button
              type="success"
              @click="sendDogCommand(1031)"
              :disabled="!connected"
              class="action-button"
            >
              跳跃
            </el-button>
          </div>
        </div>

        <!-- AI识别模式 -->
        <div class="control-section">
          <h4>AI识别模式</h4>
          <div class="ai-mode-control">
            <div class="ai-mode-status">
              <span class="status-dot" :class="{
                'connected': isAIDetectionEnabled && !isConnectingAI && !aiConnectionFailed,
                'connecting': isConnectingAI,
                'error': aiConnectionFailed
              }"></span>
              <span>{{ getAIStatusText() }}</span>
            </div>
            <el-switch
              v-model="isAIDetectionEnabled"
              :loading="isConnectingAI"
              :disabled="!connected || isConnectingAI"
              @change="handleAIDetectionToggle"
              active-text="开启"
              inactive-text="关闭"
              class="ai-mode-switch"
            />
          </div>
          <div class="ai-mode-description">
            开启后，AI将自动识别视频中的物体并在画面中标注
          </div>

          <!-- AI连接错误提示 -->
          <div v-if="aiConnectionFailed" class="ai-error-message">
            <el-alert
              title="AI识别服务连接失败"
              :description="aiErrorMessage || '请检查网络连接或服务器状态'"
              type="error"
              show-icon
              :closable="false"
            />
            <el-button
              type="primary"
              size="small"
              @click="retryAIConnection"
              class="retry-ai-button"
              :loading="isConnectingAI"
            >
              重试连接
            </el-button>
          </div>

          <!-- 检测结果显示区域 -->
          <div v-if="isAIDetectionEnabled && detectionResults.length > 0" class="detection-results">
            <h5>检测到的物品：</h5>
            <ul class="detection-list">
              <li v-for="(item, index) in detectionResults" :key="index" class="detection-item">
                <div class="detection-name">{{ item.name }}</div>
                <div class="detection-confidence">置信度: {{ (item.confidence * 100).toFixed(1) }}%</div>
              </li>
            </ul>
          </div>
        </div>

        <!-- 键盘控制说明 -->
        <div class="control-section">
          <h4>键盘控制说明</h4>
          <div class="keyboard-guide">
            <div class="guide-item"><span class="key">W</span> / <span class="key">↑</span> - 前进</div>
            <div class="guide-item"><span class="key">S</span> / <span class="key">↓</span> - 后退</div>
            <div class="guide-item"><span class="key">A</span> - 左平移</div>
            <div class="guide-item"><span class="key">D</span> - 右平移</div>
            <div class="guide-item"><span class="key">Q</span> / <span class="key">←</span> - 左旋转</div>
            <div class="guide-item"><span class="key">E</span> / <span class="key">→</span> - 右旋转</div>
            <div class="guide-item"><span class="key">Z</span> - 减速</div>
            <div class="guide-item"><span class="key">X</span> - 加速</div>
            <div class="guide-item"><span class="key">C</span> - 减小旋转速度 (步长0.2)</div>
            <div class="guide-item"><span class="key">V</span> - 增加旋转速度 (步长0.2，最高2.0)</div>
            <div class="guide-item"><span class="key">1-9</span> - 设置速度等级</div>
            <div class="guide-item"><span class="key">空格</span> - 紧急停止</div>
          </div>
          <div class="speed-info">
            <div>当前移动速度: {{ moveSpeed.toFixed(1) }}</div>
            <div>当前旋转速度: {{ rotateSpeed.toFixed(1) }} <span class="speed-highlight" v-if="rotateSpeed > 1.0">(高速)</span></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志控制台 -->
    <div class="log-console" :class="{ 'open': isLogConsoleOpen }">
      <div class="console-header">
        <h3>连接日志</h3>
        <div class="console-actions">
          <el-button
            type="text"
            size="small"
            @click="$emit('clear-log')"
            :disabled="!logContent"
          >
            清空日志
          </el-button>
          <el-button
            type="text"
            @click="toggleLogConsole"
            class="close-console-button"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
      <div class="console-content" ref="logContentRef">
        <pre>{{ logContent || '无日志记录' }}</pre>
      </div>
    </div>

    <!-- 全屏模式下的浮动控制按钮 -->
    <div class="floating-controls" v-if="isFullscreen && videoStream">
      <el-button
        type="primary"
        circle
        @click="toggleFullscreen"
        class="floating-button"
      >
        <el-icon><FullScreen /></el-icon>
      </el-button>

      <el-button
        type="primary"
        circle
        @click="captureSnapshot"
        class="floating-button"
      >
        <el-icon><Picture /></el-icon>
      </el-button>

      <el-button
        type="primary"
        circle
        @click="toggleControlPanel"
        class="floating-button"
      >
        <el-icon><Operation /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { Loading, WarningFilled, FullScreen, Picture, RefreshRight, Document, Operation, Close, ArrowUp, ArrowDown, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
// WebRTC相关常量已移除，使用本地定义
const SPORT_CMD = {
  StandUp: 1001,
  StandDown: 1002,
  Damping: 1003,
  RecoveryStand: 1004,
  Euler: 1005,
  Move: 1008,
  Sit: 1009,
  RiseSit: 1010,
  SwitchGait: 1011,
  Trigger: 1012,
  BodyHeight: 1013,
  FootRaiseHeight: 1014,
  SpeedLevel: 1015,
  Hello: 1016,
  Stretch: 1017,
  TrajectoryFollow: 1018,
  ContinuousGait: 1019,
  Content: 1020,
  Wallow: 1021,
  Dance1: 1022,
  Dance2: 1023,
  GetBodyHeight: 1024,
  GetFootRaiseHeight: 1025,
  GetSpeedLevel: 1026,
  SwitchJoystick: 1027,
  Pose: 1028,
  Scrape: 1029,
  FrontFlip: 1030,
  FrontJump: 1031,
  FrontPounce: 1032,
  WiggleHips: 1033,
  GetState: 1034,
  EconomicGait: 1035
};

const MOVE_CMD = {
  Move: 1008
};
import envConfig from '@/config/env';

// 定义日志条目类型
interface LogEntry {
  time: string;
  message: string;
}

// 组件属性
const props = defineProps({
  videoStream: {
    type: MediaStream,
    default: null
  },
  hasError: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: '视频流加载失败'
  },
  // 新增属性
  connected: {
    type: Boolean,
    default: false
  },
  connecting: {
    type: Boolean,
    default: false
  },
  robotIP: {
    type: String,
    default: '-'
  },
  connectionTime: {
    type: String,
    default: '-'
  },
  logContent: {
    type: String,
    default: ''
  },
  rtcInstance: {
    type: Object,
    default: null
  }
});

// 组件事件
const emit = defineEmits(['retry', 'snapshot', 'clear-log', 'send-command']);

// 视频元素引用
const videoElement = ref<HTMLVideoElement | null>(null);
const isFullscreen = ref(false);
const isControlPanelOpen = ref(false);
const isLogConsoleOpen = ref(false);
const logContentRef = ref<HTMLElement | null>(null);
const speed = ref(0.5); // 添加机器狗移动速度变量，默认为0.5

// 键盘控制状态
const pressedKeys = ref<Set<string>>(new Set()); // 记录当前按下的所有键，明确指定类型
const moveSpeed = ref(0.5); // 移动速度，默认0.5
const rotateSpeed = ref(0.5); // 旋转速度，默认0.5
const isMoving = ref(false); // 是否正在移动
const commandInterval = ref<number | null>(null); // 命令发送定时器
const COMMAND_FREQUENCY = 100; // 命令发送频率(毫秒)，可以根据需要调整

// AI识别模式相关变量
const isAIDetectionEnabled = ref(false); // AI识别模式开关
const detectionWebsocket = ref<WebSocket | null>(null); // AI检测WebSocket连接
const detectCanvas = ref<HTMLCanvasElement | null>(null); // 检测框绘制Canvas
const detectionResults = ref<Array<{name: string, confidence: number}>>([]);  // 检测结果
const isConnectingAI = ref(false); // 正在连接AI服务
const aiConnectionFailed = ref(false); // AI连接失败标志
const aiErrorMessage = ref(''); // AI连接错误消息
// 视频原始尺寸
const videoNaturalWidth = ref(640);
const videoNaturalHeight = ref(480);
// 视频显示区域信息
const videoDisplayInfo = ref({
  offsetX: 0,
  offsetY: 0,
  displayWidth: 0,
  displayHeight: 0,
  scaleX: 1,
  scaleY: 1
});
// 保存最后一次接收到的完整检测数据，用于在尺寸变化时重绘
const lastDetectionData = ref<any>(null);

// 提取最新的几条日志
const latestLogs = computed<LogEntry[]>(() => {
  if (!props.logContent) return [];

  const lines = props.logContent.split('\n').filter(line => line.trim());
  const lastLines = lines.slice(-3); // 获取最后3条日志

  return lastLines.map(line => {
    // 从日志行中提取时间和消息
    const timeMatch = line.match(/\[(.*?)\]/);
    const time = timeMatch ? timeMatch[1].split(' ')[1] : ''; // 只保留时间部分
    const message = line.replace(/\[.*?\]/, '').trim();
    return { time, message };
  });
});

// 获取状态文本
const getStatusText = () => {
  if (props.connected) return '已连接';
  if (props.connecting) return '连接中';
  if (props.hasError) return '连接错误';
  return '未连接';
};

// 监听视频流变化
watch(() => props.videoStream, (newStream) => {
  if (newStream && videoElement.value) {
    videoElement.value.srcObject = newStream;

    // 添加视频元数据加载事件监听
    videoElement.value.addEventListener('loadedmetadata', handleVideoMetadataLoaded);
  }
});

// 切换全屏模式
const toggleFullscreen = () => {
  const container = document.querySelector('.video-display-container');

  if (!container) return;

  if (!document.fullscreenElement) {
    container.requestFullscreen().catch(err => {
      ElMessage.error(`全屏模式错误: ${err.message}`);
    });
    isFullscreen.value = true;
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
};

// 截取视频快照
const captureSnapshot = () => {
  if (!videoElement.value || !props.videoStream) {
    ElMessage.warning('视频尚未加载，无法截图');
    return;
  }

  const canvas = document.createElement('canvas');
  const video = videoElement.value;

  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    ElMessage.error('无法创建截图上下文');
    return;
  }

  // 绘制当前视频帧到Canvas
  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

  try {
    // 转换为图片URL
    const dataUrl = canvas.toDataURL('image/png');

    // 创建下载链接
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = `机器狗截图_${new Date().toISOString().replace(/:/g, '-').slice(0, 19)}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success('截图已保存');

    // 发出截图事件
    emit('snapshot', dataUrl);
  } catch (error) {
    ElMessage.error(`截图失败: ${error.message}`);
  }
};

// 切换控制面板
const toggleControlPanel = () => {
  isControlPanelOpen.value = !isControlPanelOpen.value;

  // 如果打开控制面板，则关闭日志控制台
  if (isControlPanelOpen.value && isLogConsoleOpen.value) {
    isLogConsoleOpen.value = false;
  }
};

// 切换日志控制台
const toggleLogConsole = () => {
  isLogConsoleOpen.value = !isLogConsoleOpen.value;

  // 如果打开日志控制台，则关闭控制面板
  if (isLogConsoleOpen.value && isControlPanelOpen.value) {
    isControlPanelOpen.value = false;
  }

  // 如果打开日志控制台，滚动到底部
  if (isLogConsoleOpen.value) {
    setTimeout(scrollToBottom, 50);
  }
};

// 发送机器狗命令
const sendDogCommand = (cmdId: number) => {
  if (!props.connected || !props.rtcInstance) {
    ElMessage.warning('未连接到机器狗，无法发送命令');
    return;
  }

  try {
    // 发送动作命令
    props.rtcInstance.publishApi("rt/api/sport/request", cmdId, JSON.stringify({}));
    ElMessage.success(`已发送命令: ${SPORT_CMD[cmdId]}`);
    emit('send-command', { id: cmdId, name: SPORT_CMD[cmdId] });

    // 对于非停止命令，等待一小段时间后自动发送停止命令
    if (cmdId !== 1003) { // 如果不是StopMove命令
      setTimeout(() => {
        sendResetCommand();
      }, 1500); // 等待1.5秒，让动作执行后自动重置
    }
  } catch (error) {
    ElMessage.error(`发送命令失败: ${error.message}`);
  }
};

// 发送移动命令
const sendMoveCommand = (cmd: string) => {
  if (!props.connected || !props.rtcInstance) {
    ElMessage.warning('未连接到机器狗，无法发送命令');
    return;
  }

  try {
    // 根据方向调用复杂移动命令
    switch(cmd) {
      case 'forward':
        sendComplexMove(moveSpeed.value, 0, 0);
        break;
      case 'backward':
        sendComplexMove(-moveSpeed.value, 0, 0);
        break;
      case 'left':
        sendComplexMove(0, 0, rotateSpeed.value);
        break;
      case 'right':
        sendComplexMove(0, 0, -rotateSpeed.value);
        break;
    }

    ElMessage.success(`移动命令: ${cmd}`);
    emit('send-command', { id: 1008, name: '移动', direction: cmd });
  } catch (error) {
    ElMessage.error(`发送移动命令失败: ${error.message}`);
  }
};

// 发送复杂移动命令（支持同时控制前后、左右平移、旋转）
const sendComplexMove = (x: number, y: number, z: number) => {
  if (!props.connected || !props.rtcInstance) {
    ElMessage.warning('未连接到机器狗，无法发送命令');
    return;
  }

  try {
    const params = { x, y, z };
    props.rtcInstance.publishApi("rt/api/sport/request", 1008, JSON.stringify(params));

    // 生成移动描述
    const directions = [];
    if (x > 0) directions.push('前进');
    if (x < 0) directions.push('后退');
    if (y > 0) directions.push('左移');
    if (y < 0) directions.push('右移');
    if (z > 0) directions.push('左转');
    if (z < 0) directions.push('右转');

    const directionText = directions.length > 0 ? directions.join('、') : '停止';

    // 只在控制台记录，避免频繁弹出消息
    console.log(`移动命令: ${directionText} (${JSON.stringify(params)})`);

    // 发出事件
    emit('send-command', { id: 1008, name: '移动', direction: directionText, params });
  } catch (error) {
    ElMessage.error(`发送移动命令失败: ${error.message}`);
  }
};

// 发送重置命令以释放机器狗状态
const sendResetCommand = () => {
  if (!props.connected || !props.rtcInstance) return;

  try {
    // 发送StopMove命令重置状态，但不显示任何消息提示
    props.rtcInstance.publishApi("rt/api/sport/request", 1003, JSON.stringify({}));
    console.log('已自动重置机器狗状态，现在可以发送移动命令');
  } catch (error) {
    console.error('发送重置命令失败:', error);
  }
};

// 启动持续命令发送
const startContinuousCommand = () => {
  if (commandInterval.value) {
    clearInterval(commandInterval.value);
  }

  // 先立即发送一次命令
  updateMovement();

  // 设置定期发送命令的定时器
  commandInterval.value = window.setInterval(() => {
    if (pressedKeys.value.size > 0) {
      updateMovement();
    } else {
      stopContinuousCommand();
    }
  }, COMMAND_FREQUENCY);
};

// 停止持续命令发送
const stopContinuousCommand = () => {
  if (commandInterval.value) {
    clearInterval(commandInterval.value);
    commandInterval.value = null;
  }

  if (isMoving.value) {
    sendDogCommand(1003); // 发送停止命令
    isMoving.value = false;
  }
};

// 日志滚动到底部
const scrollToBottom = () => {
  if (logContentRef.value) {
    logContentRef.value.scrollTop = logContentRef.value.scrollHeight;
  }
};

// 全屏变化监听器
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 组件挂载时处理
onMounted(() => {
  // 添加全屏变更监听
  document.addEventListener('fullscreenchange', handleFullscreenChange);

  // 设置视频流（如果已有）
  if (props.videoStream && videoElement.value) {
    videoElement.value.srcObject = props.videoStream;

    // 添加视频元数据加载事件监听
    videoElement.value.addEventListener('loadedmetadata', handleVideoMetadataLoaded);
  }

  // 监听键盘事件以控制机器狗
  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('keyup', handleKeyUp);

  // 设置尺寸变化监听
  setupResizeObserver();
});

// 键盘控制
const handleKeyDown = (event: KeyboardEvent) => {
  if (!props.connected) return;

  // 防止同一个键被重复记录
  if (pressedKeys.value.has(event.key)) return;

  // 添加键到已按下集合
  pressedKeys.value.add(event.key);

  // 处理特殊单键操作
  switch (event.key) {
    case ' ': // 空格键立即停止
      sendDogCommand(1003); // StopMove
      pressedKeys.value.clear(); // 清空所有按键状态
      stopContinuousCommand(); // 确保停止定时器
      return;
    case 'z': // Z键减速
      moveSpeed.value = Math.max(0.1, moveSpeed.value - 0.1);
      ElMessage.info(`移动速度: ${moveSpeed.value.toFixed(1)}`);
      return;
    case 'x': // X键加速
      moveSpeed.value = Math.min(1.0, moveSpeed.value + 0.1);
      ElMessage.info(`移动速度: ${moveSpeed.value.toFixed(1)}`);
      return;
    case 'c': // C键减小旋转速度
      rotateSpeed.value = Math.max(0.1, rotateSpeed.value - 0.2);
      ElMessage.info(`旋转速度: ${rotateSpeed.value.toFixed(1)}`);
      return;
    case 'v': // V键增加旋转速度
      rotateSpeed.value = Math.min(2.0, rotateSpeed.value + 0.2);
      ElMessage.info(`旋转速度: ${rotateSpeed.value.toFixed(1)}`);
      return;
  }

  // 处理数字键选择速度
  if (/^[1-9]$/.test(event.key)) {
    const speed = parseInt(event.key) / 10;
    moveSpeed.value = speed;
    ElMessage.info(`移动速度设置为: ${speed.toFixed(1)}`);
    return;
  }

  // 检查是否有方向键被按下
  const isDirectionKey = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'w', 's', 'a', 'd', 'q', 'e'].includes(event.key);

  // 如果按下的是方向键，启动持续命令发送
  if (isDirectionKey) {
    startContinuousCommand();
  }
};

// 键盘按键释放处理
const handleKeyUp = (event: KeyboardEvent) => {
  // 从集合中移除释放的键
  pressedKeys.value.delete(event.key);

  if (pressedKeys.value.size === 0) {
    // 所有键都释放了，停止移动和定时器
    stopContinuousCommand();
  } else {
    // 检查是否还有方向键被按下
    const directionKeys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'w', 's', 'a', 'd', 'q', 'e'];
    const hasDirectionKeys = Array.from(pressedKeys.value).some(key => directionKeys.includes(key));

    if (!hasDirectionKeys) {
      // 没有方向键，停止移动和定时器
      stopContinuousCommand();
    }
  }
};

// 根据当前按下的键更新移动状态
const updateMovement = () => {
  const keys = Array.from(pressedKeys.value);

  // 检查是否有方向或旋转键
  const hasDirectionKeys = keys.some(k =>
    k === 'ArrowUp' || k === 'ArrowDown' || k === 'ArrowLeft' || k === 'ArrowRight' ||
    k === 'w' || k === 's' || k === 'a' || k === 'd' || k === 'q' || k === 'e'
  );

  if (!hasDirectionKeys) return;

  // 移动参数
  let x = 0, y = 0, z = 0;

  // 前后移动 (优先使用WASD)
  if (keys.includes('w') || keys.includes('ArrowUp')) {
    x = moveSpeed.value;
  } else if (keys.includes('s') || keys.includes('ArrowDown')) {
    x = -moveSpeed.value;
  }

  // 左右旋转 (优先使用QE)
  if (keys.includes('q') || keys.includes('ArrowLeft')) {
    z = rotateSpeed.value;
  } else if (keys.includes('e') || keys.includes('ArrowRight')) {
    z = -rotateSpeed.value;
  }

  // 侧向移动 (如果支持)
  if (keys.includes('a')) {
    y = moveSpeed.value; // 左平移
  } else if (keys.includes('d')) {
    y = -moveSpeed.value; // 右平移
  }

  // 发送移动命令
  sendComplexMove(x, y, z);
  isMoving.value = true;
};

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('keyup', handleKeyUp);

  // 移除视频元数据加载事件监听
  if (videoElement.value) {
    videoElement.value.removeEventListener('loadedmetadata', handleVideoMetadataLoaded);
  }

  // 清理ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }

  // 清理定时器
  if (commandInterval.value) {
    clearInterval(commandInterval.value);
    commandInterval.value = null;
  }

  // 确保停止所有移动
  if (props.connected && props.rtcInstance) {
    props.rtcInstance.publishApi("rt/api/sport/request", 1003, JSON.stringify({}));
  }

  // 清理AI检测相关资源
  cleanupDetection();

  // 清理视频元素
  if (videoElement.value) {
    videoElement.value.srcObject = null;
  }
});

// 监听日志内容变化，自动滚动到底部
watch(() => props.logContent, () => {
  if (isLogConsoleOpen.value) {
    setTimeout(scrollToBottom, 50);
  }
});

// 获取AI状态文本
const getAIStatusText = () => {
  if (isAIDetectionEnabled.value) {
    if (isConnectingAI.value) return '连接中';
    if (aiConnectionFailed.value) return '连接错误';
    return '已连接';
  }
  return '未连接';
};

// 处理AI连接重试
const retryAIConnection = () => {
  if (!props.connected || !props.rtcInstance) {
    ElMessage.warning('未连接到机器狗，无法重试AI连接');
    return;
  }

  try {
    // 重置AI连接状态
    isAIDetectionEnabled.value = false;
    aiConnectionFailed.value = false;
    aiErrorMessage.value = '';

    // 重新连接AI
    sendDogCommand(1004); // 发送站立命令，尝试重新连接AI
    ElMessage.success('已重试AI连接');
  } catch (error) {
    ElMessage.error(`重试AI连接失败: ${error.message}`);
  }
};

// 处理AI检测模式切换
const handleAIDetectionToggle = (value: boolean) => {
  if (!props.connected || isConnectingAI.value) return;

  try {
    if (value) {
      // 开启AI检测
      connectAIService();
    } else {
      // 关闭AI检测
      cleanupDetection();
      ElMessage.info('已关闭AI识别模式');
    }
  } catch (error) {
    ElMessage.error(`切换AI检测模式失败: ${error.message}`);
    isAIDetectionEnabled.value = false;
  }
};

// 连接AI服务
const connectAIService = () => {
  if (!props.connected || !props.rtcInstance) {
    ElMessage.warning('未连接到机器狗，无法连接AI服务');
    return;
  }

  try {
    // 连接AI服务
    isConnectingAI.value = true;
    aiConnectionFailed.value = false;
    aiErrorMessage.value = '';

    // 关闭已有的WebSocket连接
    if (detectionWebsocket.value) {
      detectionWebsocket.value.close();
      detectionWebsocket.value = null;
    }

    // 使用WebSocket连接到AI检测服务（使用环境配置）
    detectionWebsocket.value = new WebSocket(envConfig.aiDetectionWsUrl);

    detectionWebsocket.value.onopen = () => {
      isConnectingAI.value = false;
      ElMessage.success('已成功连接AI识别服务');

      // 更新视频显示信息并准备Canvas
      updateVideoDisplayInfo();
      prepareDetectCanvas();
    };

    detectionWebsocket.value.onerror = (event) => {
      isConnectingAI.value = false;
      ElMessage.error('AI识别服务连接失败');
      aiConnectionFailed.value = true;
      aiErrorMessage.value = 'WebSocket连接失败';
      isAIDetectionEnabled.value = false;
      console.error('AI WebSocket连接错误:', event);
    };

    detectionWebsocket.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        // 处理检测结果数据
        if (data.boxes) {
          // 更新检测结果列表
          detectionResults.value = data.boxes
            .map((box: any) => ({
              name: box.name || '未知物体',
              confidence: box.confidence || 0
            }))
            .filter((item: any) => item.confidence > 0.3) // 过滤低置信度结果
            .sort((a: any, b: any) => b.confidence - a.confidence); // 按置信度排序

          // 绘制检测框
          drawDetectionBoxes(data);
        }
      } catch (error) {
        console.error('解析AI检测数据失败:', error);
      }
    };

    detectionWebsocket.value.onclose = () => {
      isConnectingAI.value = false;
      if (isAIDetectionEnabled.value) {
        ElMessage.info('AI识别服务连接已断开');
        isAIDetectionEnabled.value = false;
      }
      cleanupDetection();
    };

    // 设置连接超时
    setTimeout(() => {
      if (isConnectingAI.value) {
        isConnectingAI.value = false;
        aiConnectionFailed.value = true;
        aiErrorMessage.value = '连接超时';
        isAIDetectionEnabled.value = false;
        ElMessage.error('AI识别服务连接超时');

        // 关闭连接
        if (detectionWebsocket.value) {
          detectionWebsocket.value.close();
          detectionWebsocket.value = null;
        }
      }
    }, 5000);

  } catch (error) {
    isConnectingAI.value = false;
    aiConnectionFailed.value = true;
    aiErrorMessage.value = error.message || '未知错误';
    isAIDetectionEnabled.value = false;
    ElMessage.error(`连接AI识别服务失败: ${error.message || '未知错误'}`);
  }
};

// 准备检测Canvas
const prepareDetectCanvas = () => {
  // 确保Canvas已经加载
  setTimeout(() => {
    if (!detectCanvas.value) return;

    const canvas = detectCanvas.value;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置Canvas尺寸与视频容器一致
    if (videoElement.value) {
      const videoContainer = videoElement.value.parentElement;
      if (videoContainer) {
        canvas.width = videoContainer.clientWidth;
        canvas.height = videoContainer.clientHeight;

        // 计算视频在容器中的实际显示区域
        updateVideoDisplayInfo();
      }
    }

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制等待提示
    ctx.font = '16px Arial';
    ctx.fillStyle = 'rgba(59, 130, 246, 0.8)';
    ctx.textAlign = 'center';
    ctx.fillText('等待AI识别数据...', canvas.width / 2, 30);
  }, 300);
};

// 计算视频在容器中的实际显示区域
const updateVideoDisplayInfo = () => {
  if (!videoElement.value) return;

  const video = videoElement.value;
  const container = video.parentElement;
  if (!container) return;

  // 获取容器和视频的尺寸
  const containerWidth = container.clientWidth;
  const containerHeight = container.clientHeight;

  // 更新视频原始尺寸
  videoNaturalWidth.value = video.videoWidth || 640;
  videoNaturalHeight.value = video.videoHeight || 480;

  // 计算视频的显示尺寸和位置（考虑object-fit属性）
  const videoRatio = videoNaturalWidth.value / videoNaturalHeight.value;
  const containerRatio = containerWidth / containerHeight;

  let displayWidth, displayHeight, offsetX, offsetY;

  // 根据视频的object-fit属性计算实际显示区域
  const objectFit = getComputedStyle(video).objectFit;

  if (objectFit === 'contain') {
    // 保持纵横比，确保视频完全可见
    if (containerRatio > videoRatio) {
      // 容器更宽，视频高度将填满容器
      displayHeight = containerHeight;
      displayWidth = displayHeight * videoRatio;
      offsetX = (containerWidth - displayWidth) / 2;
      offsetY = 0;
    } else {
      // 容器更高，视频宽度将填满容器
      displayWidth = containerWidth;
      displayHeight = displayWidth / videoRatio;
      offsetX = 0;
      offsetY = (containerHeight - displayHeight) / 2;
    }
  } else {
    // 默认为'cover'，填满容器，可能裁剪视频
    if (containerRatio > videoRatio) {
      // 容器更宽，视频宽度将填满容器
      displayWidth = containerWidth;
      displayHeight = displayWidth / videoRatio;
      offsetX = 0;
      offsetY = (containerHeight - displayHeight) / 2;
    } else {
      // 容器更高，视频高度将填满容器
      displayHeight = containerHeight;
      displayWidth = displayHeight * videoRatio;
      offsetX = (containerWidth - displayWidth) / 2;
      offsetY = 0;
    }
  }

  // 更新视频显示信息
  videoDisplayInfo.value = {
    offsetX,
    offsetY,
    displayWidth,
    displayHeight,
    scaleX: displayWidth / videoNaturalWidth.value,
    scaleY: displayHeight / videoNaturalHeight.value
  };

  console.log('视频显示信息更新:', videoDisplayInfo.value);
};

// 绘制检测框
const drawDetectionBoxes = (detectionData: any) => {
  if (!detectCanvas.value || !isAIDetectionEnabled.value) return;

  // 保存最后一次接收到的完整检测数据，用于在尺寸变化时重绘
  if (detectionData && detectionData.boxes) {
    lastDetectionData.value = JSON.parse(JSON.stringify(detectionData));
  }

  const canvas = detectCanvas.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  // 清除之前的绘制
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 确保有检测数据
  if (!detectionData.boxes || !Array.isArray(detectionData.boxes) || detectionData.boxes.length === 0) {
    // 如果没有检测到物品，显示提示
    ctx.font = '16px Arial';
    ctx.fillStyle = 'rgba(59, 130, 246, 0.8)';
    ctx.textAlign = 'center';
    ctx.fillText('未检测到物品', canvas.width / 2, 30);
    return;
  }

  // 获取视频显示信息
  const { offsetX, offsetY, scaleX, scaleY } = videoDisplayInfo.value;

  // 获取检测数据的原始尺寸
  const detectionWidth = detectionData.width || videoNaturalWidth.value;
  const detectionHeight = detectionData.height || videoNaturalHeight.value;

  // 计算检测坐标到Canvas坐标的转换比例
  const xRatio = scaleX;
  const yRatio = scaleY;

  // 遍历所有检测框
  detectionData.boxes.forEach((box: any) => {
    // 检查是否有边界框数据
    if (!box.bbox || box.bbox.length !== 4) return;

    // 获取边界框坐标
    const [x1, y1, x2, y2] = box.bbox;

    // 将检测坐标转换为Canvas坐标
    const canvasX1 = x1 * xRatio + offsetX;
    const canvasY1 = y1 * yRatio + offsetY;
    const canvasX2 = x2 * xRatio + offsetX;
    const canvasY2 = y2 * yRatio + offsetY;

    const boxWidth = canvasX2 - canvasX1;
    const boxHeight = canvasY2 - canvasY1;

    // 根据置信度设置颜色
    const confidence = box.confidence || 0;
    if (confidence < 0.3) return; // 忽略低置信度结果

    const hue = (1 - confidence) * 60; // 60度是黄色，0度是红色
    ctx.strokeStyle = `hsl(${hue}, 100%, 50%)`;
    ctx.lineWidth = 2;

    // 绘制边界框
    ctx.beginPath();
    ctx.rect(canvasX1, canvasY1, boxWidth, boxHeight);
    ctx.stroke();

    // 绘制标签
    if (box.name) {
      ctx.font = '14px Arial';
      const label = `${box.name} ${(confidence * 100).toFixed(1)}%`;
      const textWidth = ctx.measureText(label).width;

      // 绘制标签背景
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(canvasX1, canvasY1 - 20, textWidth + 10, 20);

      // 绘制文本
      ctx.fillStyle = 'white';
      ctx.textAlign = 'left';
      ctx.fillText(label, canvasX1 + 5, canvasY1 - 5);
    }
  });
};

// 清理AI检测相关资源
const cleanupDetection = () => {
  // 清理检测结果
  detectionResults.value = [];

  // 清理最后一次的检测数据
  lastDetectionData.value = null;

  // 清理Canvas
  if (detectCanvas.value) {
    const ctx = detectCanvas.value.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, detectCanvas.value.width, detectCanvas.value.height);
    }
  }

  // 关闭WebSocket连接
  if (detectionWebsocket.value) {
    detectionWebsocket.value.onopen = null;
    detectionWebsocket.value.onmessage = null;
    detectionWebsocket.value.onerror = null;
    detectionWebsocket.value.onclose = null;

    if (detectionWebsocket.value.readyState === WebSocket.OPEN) {
      detectionWebsocket.value.close();
    }
    detectionWebsocket.value = null;
  }

  // 重置状态
  isAIDetectionEnabled.value = false;
  isConnectingAI.value = false;
  aiConnectionFailed.value = false;
  aiErrorMessage.value = '';
};

// 处理视频元数据加载完成事件
const handleVideoMetadataLoaded = () => {
  if (!videoElement.value) return;

  // 更新视频原始尺寸
  videoNaturalWidth.value = videoElement.value.videoWidth || 640;
  videoNaturalHeight.value = videoElement.value.videoHeight || 480;

  console.log('视频元数据加载完成，原始尺寸:', videoNaturalWidth.value, 'x', videoNaturalHeight.value);

  // 更新视频显示信息
  updateVideoDisplayInfo();

  // 如果AI检测已启用，重新准备Canvas
  if (isAIDetectionEnabled.value) {
    prepareDetectCanvas();
  }
};

// 创建ResizeObserver监听视频容器尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 设置尺寸变化监听
const setupResizeObserver = () => {
  if (!videoElement.value) return;

  const videoContainer = videoElement.value.parentElement;
  if (!videoContainer) return;

  // 如果已有ResizeObserver，先清理
  if (resizeObserver) {
    resizeObserver.disconnect();
  }

  // 创建新的ResizeObserver
  resizeObserver = new ResizeObserver((entries) => {
    // 容器尺寸变化时更新视频显示信息
    updateVideoDisplayInfo();

    // 如果AI检测已启用，重新准备Canvas
    if (isAIDetectionEnabled.value && detectCanvas.value) {
      const canvas = detectCanvas.value;
      const container = videoElement.value?.parentElement;

      if (container) {
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;

        // 如果有最后一次的检测数据，使用它重新绘制
        if (lastDetectionData.value) {
          drawDetectionBoxes(lastDetectionData.value);
        } else if (detectionResults.value.length > 0) {
          // 如果没有保存完整数据但有检测结果，创建临时数据结构
          const tempData = {
            boxes: detectionResults.value.map(item => ({
              name: item.name,
              confidence: item.confidence,
              bbox: [0, 0, videoNaturalWidth.value, videoNaturalHeight.value] // 临时边界框
            }))
          };
          drawDetectionBoxes(tempData);
        }
      }
    }
  });

  // 开始监听容器尺寸变化
  resizeObserver.observe(videoContainer);
};
</script>

<style scoped>
/* 基础容器样式 */
.video-display-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #0a1019 0%, #0f172a 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.video-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  margin: 0 auto;
  width: 100%;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
  z-index: 1;
}

/* 控制面板打开时的样式 */
.control-panel-open .video-wrapper {
  width: calc(100% - 300px);
  margin-right: 300px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); /* 添加阴影增强视觉效果 */
}

.control-panel-open .video-element {
  object-fit: contain; /* 确保视频内容完全可见 */
}

/* 加载和错误状态下的样式 */
.is-loading.control-panel-open .loading-overlay,
.control-panel-open .error-overlay {
  width: 100%;
  left: 0;
  border-radius: 12px;
}

/* 科技感边框效果 */
.tech-frame-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 2;
}

/* 边角装饰 */
.corner {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(59, 130, 246, 0.6);
  z-index: 2;
}

.top-left {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
  border-top-left-radius: 4px;
}

.top-right {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
  border-top-right-radius: 4px;
}

.bottom-left {
  bottom: 10px;
  left: 10px;
  border-right: none;
  border-top: none;
  border-bottom-left-radius: 4px;
}

.bottom-right {
  bottom: 10px;
  right: 10px;
  border-left: none;
  border-top: none;
  border-bottom-right-radius: 4px;
}

/* 扫描线效果 */
.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
  animation: scanline 4s linear infinite;
  z-index: 2;
}

@keyframes scanline {
  0% { transform: translateY(-100%); opacity: 0; }
  10% { opacity: 0.8; }
  80% { opacity: 0.8; }
  100% { transform: translateY(2000%); opacity: 0; }
}

/* 数据网格背景 */
.data-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 30px 30px, 20px 20px, 20px 20px;
  opacity: 0.2;
  z-index: 1;
}

/* 状态HUD覆盖层 */
.status-hud {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 3;
}

.hud-top {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(10, 16, 25, 0.7);
  backdrop-filter: blur(4px);
  padding: 8px 15px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-status .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #64748b;
}

.connection-status .status-dot.connected {
  background-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  animation: pulse-green 2s infinite;
}

.connection-status .status-dot.connecting {
  background-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  animation: pulse-yellow 2s infinite;
}

.connection-status .status-dot.error {
  background-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  animation: pulse-red 2s infinite;
}

.connection-status span {
  color: #f1f5f9;
  font-size: 14px;
  font-weight: 500;
}

.connection-info {
  display: flex;
  gap: 15px;
}

.connection-info span {
  color: #94a3b8;
  font-size: 12px;
  font-family: monospace;
}

/* 最新日志显示区 */
.log-ticker {
  position: absolute;
  left: 15px;
  bottom: 15px;
  width: 60%;
  max-width: 500px;
  background: rgba(10, 16, 25, 0.7);
  backdrop-filter: blur(4px);
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(59, 130, 246, 0.3);
  pointer-events: all;
}

.log-entry {
  font-family: monospace;
  font-size: 12px;
  padding: 4px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #60a5fa;
  margin-right: 5px;
}

.log-message {
  color: #e2e8f0;
}

/* 控制面板样式 */
.control-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: rgba(10, 16, 25, 0.85);
  backdrop-filter: blur(10px);
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 10;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.5);
  border-left: 1px solid rgba(59, 130, 246, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.control-panel.open {
  transform: translateX(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
  margin: 0;
  color: #f1f5f9;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
}

.close-panel-button {
  color: #94a3b8;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.control-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.control-section:last-child {
  border-bottom: none;
}

.control-section h4 {
  color: #60a5fa;
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 12px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.action-button {
  border-radius: 8px;
  text-align: center;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.8));
  border: 1px solid rgba(96, 165, 250, 0.5);
  color: #e2e8f0;
  font-size: 13px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
  background: linear-gradient(to bottom, rgba(59, 130, 246, 1), rgba(37, 99, 235, 1));
}

.direction-control {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.direction-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 5px;
  margin-bottom: 5px;
  width: 100%;
}

.direction-button {
  width: 100%;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.8));
  border: 1px solid rgba(96, 165, 250, 0.5);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.direction-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
  background: linear-gradient(to bottom, rgba(59, 130, 246, 1), rgba(37, 99, 235, 1));
}

.direction-button.stop {
  background: linear-gradient(to bottom, rgba(239, 68, 68, 0.8), rgba(185, 28, 28, 0.8));
  border: 1px solid rgba(252, 165, 165, 0.5);
}

.direction-button.stop:hover {
  background: linear-gradient(to bottom, rgba(239, 68, 68, 1), rgba(185, 28, 28, 1));
}

/* 日志控制台样式 */
.log-console {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: rgba(10, 16, 25, 0.85);
  backdrop-filter: blur(10px);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 10;
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.5);
  border-top: 1px solid rgba(59, 130, 246, 0.3);
  display: flex;
  flex-direction: column;
}

.log-console.open {
  transform: translateY(0);
}

.console-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.console-header h3 {
  margin: 0;
  color: #f1f5f9;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
}

.console-actions {
  display: flex;
  gap: 10px;
}

.close-console-button {
  color: #94a3b8;
}

.console-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background: rgba(0, 0, 0, 0.2);
  font-family: monospace;
  font-size: 12px;
  color: #94a3b8;
}

.console-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 全屏模式样式 */
.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

.is-fullscreen .video-element {
  object-fit: contain;
}

/* 浮动控制按钮样式 */
.floating-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 12px;
  opacity: 0.6;
  transition: opacity 0.3s ease;
  z-index: 5;
}

.floating-controls:hover {
  opacity: 1;
}

.floating-button {
  width: 48px;
  height: 48px;
  background: rgba(59, 130, 246, 0.9);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.floating-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(10, 16, 25, 0.85);
  z-index: 5;
}

.loading-animation {
  position: relative;
  margin-bottom: 24px;
  width: 60px;
  height: 60px;
}

.loading-circle {
  width: 60px;
  height: 60px;
  animation: rotate 2s linear infinite;
  transform-origin: center center;
}

.loading-circle circle {
  stroke-dasharray: 150, 200;
  stroke-dashoffset: -10;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

.loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 30px;
  color: #3b82f6;
}

@keyframes rotate {
  100% { transform: rotate(360deg); }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -125;
  }
}

.loading-text {
  color: #e2e8f0;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.loading-subtext {
  color: #94a3b8;
  font-size: 14px;
}

/* 错误状态样式 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(10, 16, 25, 0.95);
  z-index: 5;
}

/* 键盘控制指南样式 */
.keyboard-guide {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 12px;
  font-size: 12px;
}

.guide-item {
  display: flex;
  align-items: center;
  color: #e2e8f0;
}

.key {
  display: inline-block;
  min-width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.5);
  border-radius: 4px;
  margin-right: 6px;
  padding: 0 4px;
  color: #60a5fa;
  font-family: monospace;
  font-weight: 600;
}

.speed-info {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  font-size: 13px;
  color: #60a5fa;
}

.speed-highlight {
  color: #ef4444;
  font-weight: bold;
  animation: pulse-attention 1.5s infinite;
  margin-left: 4px;
}

@keyframes pulse-attention {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.error-icon-container {
  width: 80px;
  height: 80px;
  background: rgba(239, 68, 68, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  border: 2px solid rgba(239, 68, 68, 0.3);
  box-shadow: 0 0 0 6px rgba(239, 68, 68, 0.1);
  animation: pulse-red 2s infinite;
}

@keyframes pulse-green {
  0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.6); }
  70% { box-shadow: 0 0 0 6px rgba(16, 185, 129, 0); }
  100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

@keyframes pulse-yellow {
  0% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.6); }
  70% { box-shadow: 0 0 0 6px rgba(245, 158, 11, 0); }
  100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0); }
}

@keyframes pulse-red {
  0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
  100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}

.error-icon {
  font-size: 40px;
  color: #ef4444;
}

.error-text {
  color: #f1f5f9;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  text-align: center;
  max-width: 80%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.error-subtext {
  color: #94a3b8;
  font-size: 14px;
  margin-bottom: 24px;
  text-align: center;
}

.retry-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

/* 控制面板样式 */
.video-controls {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to bottom, rgba(15, 23, 42, 0.8), rgba(10, 16, 25, 0.9));
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.video-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #64748b;
}

.status-dot.connected {
  background-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  animation: pulse-green 2s infinite;
}

.status-text {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
}

.control-button {
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  background: linear-gradient(to bottom, #3b82f6, #2563eb);
  border: none;
}

.control-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.control-button:active {
  transform: translateY(0);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .loading-animation {
    width: 50px;
    height: 50px;
  }

  .loading-circle {
    width: 50px;
    height: 50px;
  }

  .loading-icon {
    font-size: 24px;
  }

  .loading-text {
    font-size: 16px;
  }

  .loading-subtext {
    font-size: 12px;
  }

  .error-icon-container {
    width: 60px;
    height: 60px;
  }

  .error-icon {
    font-size: 30px;
  }

  .error-text {
    font-size: 18px;
  }

  .video-controls {
    padding: 10px;
  }

  .control-button span {
    display: none;
  }

  .control-panel {
    width: 250px;
  }

  .hud-top {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .log-ticker {
    width: 80%;
  }

  /* 控制面板打开时的移动端样式 */
  .control-panel-open .video-wrapper {
    width: 100%;
    margin-right: 0;
    transform: scale(0.9); /* 在移动设备上使用缩放代替宽度调整 */
    margin-bottom: 0; /* 移除底部边距 */
  }

  .control-panel {
    width: 100%;
    height: 50%; /* 让控制面板占据屏幕一半高度 */
    max-height: 400px; /* 限制最大高度 */
    top: auto;
    bottom: 0;
    transform: translateY(100%);
  }

  .control-panel.open {
    transform: translateY(0);
  }

  /* 移动设备上AI检测相关样式 */
  .detection-results {
    padding: 8px;
  }

  .detection-list {
    gap: 5px;
    max-height: 120px;
    overflow-y: auto;
  }

  .detection-item {
    padding: 4px 8px;
    min-width: 70px;
  }

  .detection-name {
    font-size: 11px;
  }

  .detection-confidence {
    font-size: 10px;
  }

  .ai-mode-control {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .ai-mode-switch {
    width: 100%;
  }
}

/* AI检测相关样式 */
.detect-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none; /* 允许点击穿透 */
}

.ai-mode-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ai-mode-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-mode-description {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 10px;
}

.ai-error-message {
  margin-top: 10px;
  margin-bottom: 10px;
}

.retry-ai-button {
  margin-top: 10px;
  width: 100%;
}

.detection-results {
  margin-top: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 10px;
}

.detection-results h5 {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #60a5fa;
}

.detection-list {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.detection-item {
  padding: 5px 10px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  min-width: 80px;
}

.detection-name {
  font-weight: bold;
  color: #60a5fa;
  font-size: 12px;
}

.detection-confidence {
  color: #94a3b8;
  font-size: 11px;
}
</style>
