<!-- 
  TaskCreationWizard.vue
  任务创建向导组件，用于引导用户创建新的周期性巡航任务
-->
<template>
  <div class="task-creation-wizard">
    <div class="wizard-header">
      <h3>创建新的周期性巡航任务</h3>
      <div class="header-actions">
        <el-button @click="$emit('cancel')">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" :disabled="activeStep < 3">
          <el-icon><Check /></el-icon>
          创建任务
        </el-button>
      </div>
    </div>
    
    <div class="wizard-content">
      <el-steps :active="activeStep" finish-status="success" class="wizard-steps">
        <el-step title="基本信息" description="设置任务名称和类型" />
        <el-step title="执行计划" description="设置任务周期和时间" />
        <el-step title="设备选择" description="选择执行任务的设备" />
        <el-step title="确认信息" description="确认并提交任务" />
      </el-steps>
      
      <div class="step-content">
        <!-- 步骤1：基本信息 -->
        <div v-show="activeStep === 0" class="step-form">
          <el-form 
            ref="basicFormRef" 
            :model="formData" 
            :rules="basicRules" 
            label-position="top"
            status-icon
          >
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入任务名称" />
            </el-form-item>
            
            <el-form-item label="任务类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择任务类型" class="full-width">
                <el-option label="巡逻任务" value="patrol" />
                <el-option label="喷洒任务" value="spray" />
                <el-option label="检查任务" value="inspection" />
                <el-option label="其他任务" value="other" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="formData.priority" placeholder="请选择优先级" class="full-width">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="emergency" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="任务描述" prop="description">
              <el-input 
                v-model="formData.description" 
                type="textarea" 
                :rows="4" 
                placeholder="请输入任务描述"
              />
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 步骤2：执行计划 -->
        <div v-show="activeStep === 1" class="step-form">
          <el-form 
            ref="scheduleFormRef" 
            :model="formData" 
            :rules="scheduleRules" 
            label-position="top"
            status-icon
          >
            <el-form-item label="执行周期类型" prop="cycleType">
              <el-select v-model="formData.cycleType" placeholder="请选择周期类型" class="full-width">
                <el-option label="单次执行" value="once" />
                <el-option label="每天执行" value="daily" />
                <el-option label="每周执行" value="weekly" />
                <el-option label="每月执行" value="monthly" />
              </el-select>
            </el-form-item>
            
            <el-form-item 
              label="周期值" 
              prop="cycleValue"
              v-if="formData.cycleType !== 'once'"
            >
              <el-input-number 
                v-model="formData.cycleValue" 
                :min="1" 
                :max="30" 
                class="full-width"
              />
            </el-form-item>
            
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                class="full-width"
              />
            </el-form-item>
            
            <el-form-item label="启用状态" prop="enabled">
              <el-switch
                v-model="formData.enabled"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 步骤3：设备选择 -->
        <div v-show="activeStep === 2" class="step-form">
          <el-form 
            ref="deviceFormRef" 
            :model="formData" 
            :rules="deviceRules" 
            label-position="top"
            status-icon
          >
            <el-form-item label="选择执行设备" prop="deviceIds">
              <div class="devices-selector">
                <el-input
                  v-model="deviceSearchQuery"
                  placeholder="搜索设备..."
                  clearable
                  @clear="deviceSearchQuery = ''"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                
                <div class="device-list">
                  <el-checkbox-group v-model="formData.deviceIds">
                    <el-checkbox 
                      v-for="device in filteredDevices" 
                      :key="device.id" 
                      :label="device.id"
                    >
                      <div class="device-item">
                        <span class="device-name">{{ device.name }}</span>
                        <span class="device-id">{{ device.id }}</span>
                      </div>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
                
                <div class="selected-count">
                  已选择 <span class="count">{{ formData.deviceIds.length }}</span> 个设备
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 步骤4：确认信息 -->
        <div v-show="activeStep === 3" class="step-form">
          <div class="confirmation-panel">
            <h4 class="confirmation-title">任务信息确认</h4>
            
            <div class="info-section">
              <h5>基本信息</h5>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">任务名称</div>
                  <div class="info-value">{{ formData.name }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">任务类型</div>
                  <div class="info-value">{{ getTaskTypeLabel(formData.type) }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">优先级</div>
                  <div class="info-value">{{ getPriorityLabel(formData.priority) }}</div>
                </div>
              </div>
              
              <div class="description-box">
                <div class="info-label">任务描述</div>
                <div class="description-content">{{ formData.description || '暂无描述' }}</div>
              </div>
            </div>
            
            <div class="info-section">
              <h5>执行计划</h5>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">执行周期</div>
                  <div class="info-value">{{ getCycleLabel(formData.cycleType, formData.cycleValue) }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">开始时间</div>
                  <div class="info-value">{{ formatDateTime(formData.startTime) }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">启用状态</div>
                  <div class="info-value">
                    <el-tag :type="formData.enabled ? 'success' : 'info'">
                      {{ formData.enabled ? '已启用' : '已禁用' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="info-section">
              <h5>执行设备</h5>
              <div class="devices-list">
                <el-tag
                  v-for="deviceId in formData.deviceIds"
                  :key="deviceId"
                  class="device-tag"
                  effect="dark"
                  type="info"
                >
                  {{ getDeviceName(deviceId) }}
                </el-tag>
                <div v-if="!formData.deviceIds || formData.deviceIds.length === 0" class="no-devices">
                  暂无关联设备
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="step-actions">
        <el-button 
          v-if="activeStep > 0" 
          @click="prevStep"
        >
          上一步
        </el-button>
        <el-button 
          v-if="activeStep < 3" 
          type="primary" 
          @click="nextStep"
        >
          下一步
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Close, 
  Check, 
  Search 
} from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import type { PeriodicTask } from '@/types/taskScheduling';

const emit = defineEmits<{
  (e: 'save', task: PeriodicTask): void
  (e: 'cancel'): void
}>();

// 当前步骤
const activeStep = ref(0);

// 表单引用
const basicFormRef = ref<FormInstance>();
const scheduleFormRef = ref<FormInstance>();
const deviceFormRef = ref<FormInstance>();

// 加载状态
const loading = ref(false);

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  type: 'patrol',
  deviceIds: [] as string[],
  cycleType: 'daily',
  cycleValue: 1,
  startTime: new Date(),
  priority: 'medium',
  status: 'pending',
  enabled: true
});

// 表单验证规则
const basicRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
});

const scheduleRules = reactive<FormRules>({
  cycleType: [
    { required: true, message: '请选择执行周期类型', trigger: 'change' }
  ],
  cycleValue: [
    { required: true, message: '请输入周期值', trigger: 'blur' },
    { type: 'number', min: 1, message: '周期值必须大于0', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ]
});

const deviceRules = reactive<FormRules>({
  deviceIds: [
    { type: 'array', required: true, message: '请至少选择一个设备', trigger: 'change' }
  ]
});

// 设备搜索相关
const deviceSearchQuery = ref('');

// 模拟设备列表数据
const devices = [
  { id: 'device-001', name: '机器狗 Alpha-X' },
  { id: 'device-002', name: '机器狗 Beta-Z' },
  { id: 'device-003', name: '无人机 Sky-7' },
  { id: 'device-004', name: '无人机 Aero-9' },
  { id: 'device-005', name: '捕虫灯 LT-200' },
  { id: 'device-006', name: '超声波装置 US-50' }
];

// 过滤后的设备列表
const filteredDevices = computed(() => {
  if (!deviceSearchQuery.value) return devices;
  
  const query = deviceSearchQuery.value.toLowerCase();
  return devices.filter(device => 
    device.id.toLowerCase().includes(query) || 
    device.name.toLowerCase().includes(query)
  );
});

// 获取设备名称
const getDeviceName = (deviceId: string) => {
  const device = devices.find(d => d.id === deviceId);
  return device ? device.name : deviceId;
};

// 获取任务类型标签
const getTaskTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    'patrol': '巡逻任务',
    'spray': '喷洒任务',
    'inspection': '检查任务',
    'other': '其他任务'
  };
  return map[type] || '未知类型';
};

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const map: Record<string, string> = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'emergency': '紧急'
  };
  return map[priority] || '未知优先级';
};

// 获取周期标签
const getCycleLabel = (cycleType: string, cycleValue: number) => {
  const map: Record<string, string> = {
    'once': '单次',
    'daily': `每${cycleValue > 1 ? cycleValue : ''}天`,
    'weekly': `每${cycleValue > 1 ? cycleValue : ''}周`,
    'monthly': `每${cycleValue > 1 ? cycleValue : ''}月`
  };
  return map[cycleType] || '未知周期';
};

// 格式化日期时间
const formatDateTime = (dateTimeStr: Date) => {
  return dateTimeStr.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 上一步
const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--;
  }
};

// 下一步
const nextStep = async () => {
  // 根据当前步骤验证表单
  if (activeStep.value === 0) {
    if (!basicFormRef.value) return;
    
    await basicFormRef.value.validate(valid => {
      if (valid) {
        activeStep.value++;
      } else {
        ElMessage.warning('请完善基本信息');
      }
    });
  } else if (activeStep.value === 1) {
    if (!scheduleFormRef.value) return;
    
    await scheduleFormRef.value.validate(valid => {
      if (valid) {
        activeStep.value++;
      } else {
        ElMessage.warning('请完善执行计划');
      }
    });
  } else if (activeStep.value === 2) {
    if (!deviceFormRef.value) return;
    
    await deviceFormRef.value.validate(valid => {
      if (valid) {
        activeStep.value++;
      } else {
        ElMessage.warning('请至少选择一个执行设备');
      }
    });
  }
};

// 提交表单
const handleSubmit = async () => {
  loading.value = true;
  try {
    // 处理日期格式
    const taskData = {
      ...formData,
      id: 'task-' + Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      startTime: formData.startTime.toISOString(),
    } as unknown as PeriodicTask;
    
    // 计算下次执行时间（实际项目中应该由后端计算）
    const nextDate = new Date(formData.startTime);
    nextDate.setDate(nextDate.getDate() + (formData.cycleType === 'daily' ? formData.cycleValue : 0));
    taskData.nextExecutionTime = nextDate.toISOString();
    
    emit('save', taskData);
  } catch (error) {
    console.error('提交表单失败:', error);
    ElMessage.error('提交失败，请重试');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.task-creation-wizard {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  overflow: hidden;
}

.wizard-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3b4863;
}

.wizard-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #e5e7eb;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.wizard-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.wizard-steps {
  margin-bottom: 30px;
}

:deep(.el-step__title) {
  color: #9ca3af !important;
}

:deep(.el-step__title.is-process) {
  color: #3b82f6 !important;
  font-weight: bold;
}

:deep(.el-step__title.is-success) {
  color: #10b981 !important;
}

:deep(.el-step__head.is-process) {
  color: #3b82f6;
  border-color: #3b82f6;
}

:deep(.el-step__head.is-success) {
  color: #10b981;
  border-color: #10b981;
}

:deep(.el-step__description) {
  color: #6b7280 !important;
}

.step-content {
  flex: 1;
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.step-form {
  max-width: 800px;
  margin: 0 auto;
}

:deep(.el-form-item__label) {
  color: #9ca3af;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  background-color: rgba(31, 41, 55, 0.3);
  border-color: #4b5563;
  color: #e5e7eb;
}

:deep(.el-input__inner:focus),
:deep(.el-textarea__inner:focus) {
  border-color: #3b82f6;
}

:deep(.el-select-dropdown__item) {
  color: #e5e7eb;
}

:deep(.el-select-dropdown__item.selected) {
  color: #3b82f6;
}

.full-width {
  width: 100%;
}

.devices-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.device-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 15px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  border: 1px solid #4b5563;
}

:deep(.el-checkbox) {
  margin-right: 0;
  margin-bottom: 15px;
  display: block;
  color: #e5e7eb;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #3b82f6;
}

.device-item {
  display: flex;
  flex-direction: column;
}

.device-name {
  font-size: 14px;
}

.device-id {
  font-size: 12px;
  color: #9ca3af;
}

.selected-count {
  text-align: right;
  color: #9ca3af;
  font-size: 14px;
}

.selected-count .count {
  color: #3b82f6;
  font-weight: 600;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 确认信息样式 */
.confirmation-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.confirmation-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: #e5e7eb;
  text-align: center;
}

.info-section {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.info-section h5 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #60a5fa;
  border-bottom: 1px solid #3b4863;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-label {
  font-size: 14px;
  color: #9ca3af;
}

.info-value {
  font-size: 16px;
  color: #e5e7eb;
}

.description-box {
  margin-top: 10px;
}

.description-content {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 6px;
  padding: 10px;
  color: #e5e7eb;
  min-height: 60px;
  white-space: pre-wrap;
}

.devices-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.device-tag {
  margin-right: 0;
}

.no-devices {
  color: #9ca3af;
  font-style: italic;
}
</style> 