<template>
  <BaseLayout
    title="实时监控与预警中心"
    theme="monitoringCenter"
    themeColor="#10b981"
    moduleIcon="Monitor"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        v-for="tab in tabs"
        :key="tab.name"
        :title="tab.label"
        :icon="getTabIcon(tab.name)"
        :route="tab.route"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
    </template>

    <template #actions>
      <TechActionButton
        type="primary"
        size="medium"
        icon="FullScreen"
        :text="isFullscreen ? '退出全屏' : '全屏显示'"
        @click="toggleFullscreen"
        class="action-btn"
      />

      <TechActionButton
        type="danger"
        size="medium"
        icon="Warning"
        text="紧急消杀模式"
        @click="showEmergencyConfirm = true"
        :pulsing="true"
        class="action-btn"
      />
    </template>

    <router-view />

    <!-- 紧急消杀模式确认对话框 -->
    <div v-if="showEmergencyConfirm" class="emergency-overlay">
      <div class="emergency-dialog">
        <div class="warning-header">
          <div class="warning-sign">
            <el-icon class="warning-icon"><Warning /></el-icon>
          </div>
          <div class="countdown-timer">{{ countdownTime }}</div>
        </div>

        <div class="dialog-content">
          <div class="warning-info">
            <h2>紧急消杀模式确认</h2>
            <p>即将启动紧急消杀模式，该操作将激活所有捕虫灯和超声波装置进行集中消杀作业。请确认您要继续此操作。</p>
          </div>

          <div class="device-preview">
            <h3>设备状态预览</h3>
            <div class="device-list">
              <div class="device-item">
                <el-icon><Monitor /></el-icon>
                <span>捕虫灯: 12台 (就绪状态)</span>
              </div>
              <div class="device-item">
                <el-icon><Bell /></el-icon>
                <span>超声波装置: 8台 (就绪状态)</span>
              </div>
              <div class="device-item">
                <el-icon><MapLocation /></el-icon>
                <span>覆盖区域: 约3.5公顷</span>
              </div>
            </div>
          </div>
        </div>

        <div class="dialog-actions">
          <TechActionButton
            type="success"
            size="medium"
            text="确认启动"
            @click="activateEmergencyMode"
          />
          <TechActionButton
            type="info"
            size="medium"
            text="取消"
            @click="cancelEmergencyMode"
          />
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Warning, MapLocation, Monitor, Bell, FullScreen,
  Close, HomeFilled, VideoCamera, DataAnalysis,
  Document, Cpu, Crop
} from '@element-plus/icons-vue'
import { ElMessage, ElNotification } from 'element-plus'
import { getModuleTheme } from './components/layoutConfig'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'

const router = useRouter()
const activeTab = ref('map')
const showEmergencyConfirm = ref(false)
const countdownTime = ref('10s')
const isFullscreen = ref(false)
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)
let countdownTimer: number | null = null

// Tab数据
const tabs = [
  { name: 'map', label: '农田地图', route: '/monitoring-center/3d-map' },
  { name: 'devices', label: '设备实时追踪', route: '/monitoring-center/device-tracking' },
  { name: 'cameras', label: '摄像头直播墙', route: '/monitoring-center/camera-wall' },
]

// 根据tab名称返回对应的图标
const getTabIcon = (tabName: string): string => {
  switch (tabName) {
    case 'map':
      return 'MapLocation'
    case 'devices':
      return 'Monitor'
    case 'cameras':
      return 'VideoCamera'
    case 'heatmap':
      return 'DataAnalysis'
    case 'cropgrowth':
      return 'Crop'
    case 'robotdog':
      return 'Cpu'
    default:
      return 'Document'
  }
}

// 切换全屏模式
const toggleFullscreen = () => {
  if (baseLayoutRef.value) {
    baseLayoutRef.value.toggleFullscreen()
  }
}

// 激活紧急消杀模式
const activateEmergencyMode = () => {
  // 这里添加实际的API调用逻辑
  ElMessage({
    message: '所有设备正在投入消杀作业，预计需要15分钟完成',
    type: 'success',
    duration: 5000
  })
  showEmergencyConfirm.value = false

  // 添加声音效果
  const audio = new Audio('/warning.mp3')
  audio.play().catch(e => console.log('音频播放失败:', e))
}

// 取消紧急消杀模式
const cancelEmergencyMode = () => {
  showEmergencyConfirm.value = false
  if (countdownTimer !== null) {
    window.clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 开始倒计时
const startCountdown = () => {
  let countdown = 10
  countdownTime.value = `${countdown}s`

  countdownTimer = window.setInterval(() => {
    countdown--
    countdownTime.value = `${countdown}s`

    if (countdown <= 0) {
      cancelEmergencyMode()
    }
  }, 1000) as unknown as number
}

// 监听对话框显示状态变化
watch(showEmergencyConfirm, (newValue: boolean) => {
  if (newValue) {
    startCountdown()
  } else if (countdownTimer !== null) {
    window.clearInterval(countdownTimer)
    countdownTimer = null
  }
})

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed;
}

// 组件挂载时处理路由
onMounted(() => {
  const path = router.currentRoute.value.path
  if (path.includes('/device-tracking')) {
    activeTab.value = 'devices'
  } else if (path.includes('/camera-wall')) {
    activeTab.value = 'cameras'
  } else {
    activeTab.value = 'map'
  }

  // 监听BaseLayout事件
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('fullscreen-change', (state: boolean) => {
      isFullscreen.value = state;
    });
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange);
  }
})

// 组件卸载时清理计时器
onUnmounted(() => {
  if (countdownTimer !== null) {
    window.clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
/* 紧急消杀弹窗样式 */
.emergency-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.emergency-dialog {
  width: 650px;
  background: linear-gradient(135deg, rgba(183, 28, 28, 0.95) 0%, rgba(121, 12, 12, 0.95) 100%);
  border: 2px solid rgba(255, 152, 0, 0.8);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 0 30px rgba(255, 152, 0, 0.5), 0 0 60px rgba(244, 67, 54, 0.3);
  animation: borderPulse 1.5s infinite;
  position: relative;
}

.action-btn {
  width: 100%;
  margin-bottom: 10px;
}

.warning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

@keyframes borderPulse {
  0% {
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.5), 0 0 30px rgba(244, 67, 54, 0.3);
  }

  50% {
    box-shadow: 0 0 30px rgba(255, 152, 0, 0.8), 0 0 60px rgba(244, 67, 54, 0.5);
  }

  100% {
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.5), 0 0 30px rgba(244, 67, 54, 0.3);
  }
}

.warning-sign {
  text-align: center;
  animation: rotate 3s infinite linear;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.warning-icon {
  font-size: 50px;
  color: #ff9800;
  filter: drop-shadow(0 0 8px rgba(255, 152, 0, 0.8));
}

.countdown-timer {
  font-size: 24px;
  font-weight: bold;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 15px;
  border-radius: 20px;
  animation: pulse 1s infinite;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dialog-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.warning-info {
  flex: 3;
}

.warning-info h2 {
  color: white;
  margin-bottom: 15px;
  font-size: 24px;
  text-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
}

.warning-info p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.device-preview {
  flex: 2;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.device-preview h3 {
  color: white;
  margin-bottom: 15px;
  font-size: 18px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 8px;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.device-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.device-item i {
  color: #ff9800;
  font-size: 18px;
}

.dialog-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .emergency-dialog {
    width: 90%;
    padding: 20px;
  }

  .dialog-content {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .emergency-dialog {
    padding: 15px;
  }

  .warning-info h2 {
    font-size: 20px;
  }
}
</style>
