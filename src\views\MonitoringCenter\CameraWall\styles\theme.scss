// 智慧农业监控主题

// 主色调
$primary-green: #1e8449; // 深绿色，代表农业
$tech-blue: #3498db;     // 科技蓝，代表智能监控
$wheat-yellow: #f39c12;  // 小麦黄
$soil-brown: #795548;    // 土壤棕

// 状态颜色
$status-normal: #2ecc71;    // 正常状态绿
$status-warning: #f39c12;   // 警告状态黄
$status-error: #e74c3c;     // 错误状态红
$status-offline: #95a5a6;   // 离线状态灰

// 背景色
$bg-dark: #0a1f18;          // 深绿背景
$bg-gradient-start: #0a1f18; // 渐变开始色
$bg-gradient-end: #1a3a2d;   // 渐变结束色

// 文字颜色
$text-light: #ecf0f1;       // 浅色文字
$text-dark: #2c3e50;        // 深色文字
$text-muted: #7f8c8d;       // 次要文字

// 边框与阴影
$border-radius: 12px;       // 圆角半径
$card-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); // 卡片阴影
$glow-green: 0 0 15px rgba(46, 204, 113, 0.4); // 绿色光晕
$glow-blue: 0 0 15px rgba(52, 152, 219, 0.4);  // 蓝色光晕

// 过渡动画
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 混合函数
@mixin glass-effect {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@mixin breathing-animation($color) {
  animation: breathing 2s infinite;
  @keyframes breathing {
    0% { box-shadow: 0 0 5px rgba($color, 0.3); }
    50% { box-shadow: 0 0 15px rgba($color, 0.6); }
    100% { box-shadow: 0 0 5px rgba($color, 0.3); }
  }
} 