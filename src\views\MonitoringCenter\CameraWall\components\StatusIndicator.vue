<template>
  <div class="status-indicator" :class="[statusClass]">
    <div class="indicator-light" :class="[statusClass, { 'breathing': isBreathing }]"></div>
    <div class="status-tooltip" v-if="showTooltip">
      {{ statusText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { StreamStatus } from '../types';

const props = defineProps<{
  status: StreamStatus;
}>();

const showTooltip = ref(false);

// 根据状态计算CSS类
const statusClass = computed(() => {
  switch (props.status) {
    case StreamStatus.NORMAL:
      return 'status-normal';
    case StreamStatus.STUTTERING:
      return 'status-stuttering';
    case StreamStatus.INTERRUPTED:
      return 'status-interrupted';
    default:
      return 'status-normal';
  }
});

// 状态文本
const statusText = computed(() => {
  switch (props.status) {
    case StreamStatus.NORMAL:
      return '视频流正常';
    case StreamStatus.STUTTERING:
      return '视频流卡顿';
    case StreamStatus.INTERRUPTED:
      return '视频流中断';
    default:
      return '未知状态';
  }
});

// 是否显示呼吸灯效果
const isBreathing = computed(() => props.status === StreamStatus.NORMAL);

// 鼠标悬停显示提示
const handleMouseEnter = () => {
  showTooltip.value = true;
};

const handleMouseLeave = () => {
  showTooltip.value = false;
};

onMounted(() => {
  const indicatorElement = document.querySelector('.status-indicator');
  if (indicatorElement) {
    indicatorElement.addEventListener('mouseenter', handleMouseEnter);
    indicatorElement.addEventListener('mouseleave', handleMouseLeave);
  }
});
</script>

<style scoped>
.status-indicator {
  position: absolute;
  right: 10px;
  bottom: 10px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  cursor: help;
  transition: all 0.3s ease;
}

.status-indicator:hover {
  transform: scale(1.2);
}

.indicator-light {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-normal {
  background-color: rgba(46, 204, 113, 0.3);
}

.status-normal .indicator-light {
  background-color: var(--status-normal, #2ecc71);
}

.status-stuttering {
  background-color: rgba(243, 156, 18, 0.3);
}

.status-stuttering .indicator-light {
  background-color: var(--status-warning, #f39c12);
  animation: blink-yellow 0.5s infinite;
}

.status-interrupted {
  background-color: rgba(231, 76, 60, 0.3);
}

.status-interrupted .indicator-light {
  background-color: var(--status-error, #e74c3c);
  animation: blink-red 0.3s infinite;
}

.breathing {
  animation: breathing 2s infinite;
}

.status-tooltip {
  position: absolute;
  bottom: 24px;
  right: 0;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transform: translateY(5px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.status-indicator:hover .status-tooltip {
  opacity: 1;
  transform: translateY(0);
}

@keyframes breathing {
  0% {
    box-shadow: 0 0 5px rgba(46, 204, 113, 0.3);
  }

  50% {
    box-shadow: 0 0 15px rgba(46, 204, 113, 0.6);
  }

  100% {
    box-shadow: 0 0 5px rgba(46, 204, 113, 0.3);
  }
}

@keyframes blink-yellow {
  0% {
    opacity: 0.5;
    box-shadow: 0 0 5px rgba(243, 156, 18, 0.3);
  }

  50% {
    opacity: 1;
    box-shadow: 0 0 15px rgba(243, 156, 18, 0.6);
  }

  100% {
    opacity: 0.5;
    box-shadow: 0 0 5px rgba(243, 156, 18, 0.3);
  }
}

@keyframes blink-red {
  0% {
    opacity: 0.5;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
  }

  50% {
    opacity: 1;
    box-shadow: 0 0 15px rgba(231, 76, 60, 0.6);
  }

  100% {
    opacity: 0.5;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
  }
}
</style>