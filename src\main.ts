// 使用ESM bundler版本的Vue，以确保完整的运行时编译功能
// 这对于某些需要运行时编译的功能是必要的
import { createApp } from 'vue/dist/vue.esm-bundler.js'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import './assets/main.css'
import './styles/dark-theme.css'

import App from './App.vue'
import router from './router'
import SvgIcon from './components/SvgIcon/index.vue'
import { preloadCommonIcons } from './components/SvgIcon/svgLoader'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册SvgIcon组件
app.component('SvgIcon', SvgIcon)

// 预加载常用SVG图标
preloadCommonIcons()

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app')
