<!-- 
  ExpertKnowledge.vue
  病虫害专家知识库模块
  提供病虫害识别、防治方法和专家知识查询功能
-->
<template>
  <div class="expert-knowledge">
    <!-- 页面标题 -->
    <PageHeader
      title="病虫害专家知识库"
      description="提供病虫害识别、防治方法和专家知识查询功能"
      icon="Document"
    >
      <template #actions>
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索病虫害名称、症状关键词"
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button :icon="Filter" @click="showFilterDrawer = true"></el-button>
              <el-button :icon="Search" @click="advancedSearch"></el-button>
            </template>
          </el-input>
        </div>
      </template>
    </PageHeader>

    <!-- 知识库内容 - 动态网格布局 -->
    <div class="knowledge-content">
      <el-row :gutter="20">
        <el-col v-for="(item, index) in atlasItems" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <KnowledgeCard
            :name="item.name"
            :latinName="item.latinName"
            :image="item.image"
            :crops="item.crops"
            :symptoms="item.symptoms"
            @click="showAtlasDetail(item)"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 筛选抽屉 -->
    <el-drawer 
      v-model="showFilterDrawer" 
      title="高级筛选" 
      direction="rtl" 
      size="25%"
    >
      <div class="filter-content">
        <h3>病虫害类型</h3>
        <el-radio-group v-model="filterType">
          <el-radio label="all">全部</el-radio>
          <el-radio label="insect">昆虫类</el-radio>
          <el-radio label="disease">病害类</el-radio>
          <el-radio label="mite">螨类</el-radio>
          <el-radio label="other">其他</el-radio>
        </el-radio-group>

        <h3>危害作物</h3>
        <el-select 
          v-model="filterCrops" 
          multiple 
          placeholder="选择作物" 
          style="width: 100%"
        >
          <el-option
            v-for="crop in cropOptions"
            :key="crop.value"
            :label="crop.label"
            :value="crop.value"
          />
        </el-select>

        <h3>发生季节</h3>
        <el-checkbox-group v-model="filterSeasons">
          <el-checkbox label="spring">春季</el-checkbox>
          <el-checkbox label="summer">夏季</el-checkbox>
          <el-checkbox label="autumn">秋季</el-checkbox>
          <el-checkbox label="winter">冬季</el-checkbox>
        </el-checkbox-group>

        <div class="filter-actions">
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="primary" @click="applyFilters">应用</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="病虫害详情"
      width="80%"
      class="atlas-detail-dialog"
      destroy-on-close
    >
      <div v-if="selectedAtlas" class="atlas-detail">
        <el-container>
          <el-aside width="250px" class="detail-sidebar">
            <div class="sidebar-section">
              <h3>分类导航</h3>
              <el-tree :data="classificationTree" @node-click="handleNodeClick"></el-tree>
            </div>
            <div class="sidebar-section">
              <h3>相关推荐</h3>
              <div v-for="(rec, idx) in relatedAtlases" :key="idx" class="recommended-item">
                <div class="rec-image">
                  <el-image :src="rec.image" fit="cover"></el-image>
                </div>
                <div class="rec-info">
                  <div class="rec-name">{{ rec.name }}</div>
                  <div class="rec-similarity">相似度: {{ rec.similarity }}%</div>
                </div>
              </div>
            </div>
          </el-aside>
          
          <el-main class="detail-main">
            <div class="detail-header">
              <h2>{{ selectedAtlas.name }} <span class="latin-name">{{ selectedAtlas.latinName }}</span></h2>
            </div>
            
            <div class="atlas-image-container">
              <el-image
                :src="selectedAtlas.detailImage || selectedAtlas.image"
                fit="contain"
                :preview-src-list="[selectedAtlas.detailImage || selectedAtlas.image]"
                :initial-index="0"
                class="detail-image"
              ></el-image>
            </div>
            
            <div class="detail-sections">
              <el-tabs type="border-card">
                <el-tab-pane label="症状描述">
                  <div class="symptom-description">
                    <div class="text-content" v-html="selectedAtlas.symptomDescription"></div>
                    <div class="image-gallery">
                      <el-image
                        v-for="(img, imgIdx) in selectedAtlas.symptomImages"
                        :key="imgIdx"
                        :src="img"
                        fit="cover"
                        class="symptom-image"
                        :preview-src-list="selectedAtlas.symptomImages"
                        :initial-index="imgIdx"
                      ></el-image>
                    </div>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="防治方法">
                  <div class="control-methods">
                    <el-steps direction="vertical" :active="selectedAtlas.controlMethods.length">
                      <el-step 
                        v-for="(method, stepIdx) in selectedAtlas.controlMethods" 
                        :key="stepIdx"
                        :title="method.title"
                        :description="method.description"
                      ></el-step>
                    </el-steps>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="相似病虫害对比">
                  <div class="comparison-table">
                    <el-table :data="similarPestsData" border style="width: 100%">
                      <el-table-column prop="feature" label="特征" width="180"></el-table-column>
                      <el-table-column prop="current" :label="selectedAtlas.name"></el-table-column>
                      <el-table-column 
                        v-for="(similar, simIdx) in selectedAtlas.similarPests" 
                        :key="simIdx"
                        :prop="'similar' + simIdx"
                        :label="similar.name"
                      ></el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-main>
        </el-container>
      </div>
    </el-dialog>

    <!-- 智能推荐面板 -->
    <div class="recommendation-panel" v-if="recommendations.length > 0">
      <div class="panel-header">
        <h3>智能推荐</h3>
        <el-button :icon="CloseBold" circle size="small" @click="recommendations = []"></el-button>
      </div>
      <div class="panel-content">
        <div class="recommendation-cards">
          <div v-for="(rec, recIdx) in recommendations" :key="recIdx" class="rec-card" @click="handleRecommendationClick(rec)">
            <el-image :src="rec.image" fit="cover" class="rec-image"></el-image>
            <div class="rec-info">
              <div class="rec-title">{{ rec.title }}</div>
              <div class="rec-type">{{ rec.type }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="最新知识库" />
        <StatusIndicator type="normal" label="实时更新中" />
        <StatusIndicator type="warning" label="AI分析中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Document, Search, Filter, CloseBold, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import KnowledgeCard from './components/KnowledgeCard.vue'

// 定义接口类型
interface ControlMethod {
  title: string
  description: string
}

interface Differences {
  color?: string
  size?: string
  shape?: string
  damage?: string
  location?: string
  movement?: string
  webbing?: string
  spots?: string
}

interface SimilarPest {
  name: string
  differences: Differences
}

interface AtlasItem {
  id: number
  name: string
  latinName: string
  image: string
  detailImage?: string
  type: string
  crops: string[]
  symptoms: string[]
  seasons: string[]
  symptomDescription: string
  symptomImages: string[]
  controlMethods: ControlMethod[]
  similarPests: SimilarPest[]
}

interface Recommendation {
  title: string
  type: string
  image: string
}

interface ComparisonRow {
  feature: string
  current: string
  [key: string]: string
}

// Search and filter states
const searchQuery = ref('')
const showFilterDrawer = ref(false)
const filterType = ref('all')
const filterCrops = ref<string[]>([])
const filterSeasons = ref<string[]>([])

// 最后更新时间
const lastUpdateTime = ref(new Date())

// 格式化时间
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 刷新数据
const refreshData = (): void => {
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
  // 在实际实现中，将从后端API刷新数据
}

// Atlas items state
const atlasItems = ref<AtlasItem[]>([
  {
    id: 1,
    name: '稻飞虱',
    latinName: 'Nilaparvata lugens',
    image: 'https://img.plantplus.cn/Pests/webp/90e28ccfc0d8e2a8a5494f26d71e7ee6.jpg.webp',
    detailImage: 'https://img.plantplus.cn/Pests/webp/90e28ccfc0d8e2a8a5494f26d71e7ee6.jpg.webp',
    type: 'insect',
    crops: ['水稻'],
    symptoms: ['枯黄', '吸汁', '生长不良'],
    seasons: ['summer', 'autumn'],
    symptomDescription: '稻飞虱以刺吸式口器刺入水稻茎叶吸取植物汁液，导致水稻生长不良，叶片变黄枯萎。严重时，植株逐渐枯黄，形成"虱烧"现象。',
    symptomImages: [
      'https://img.plantplus.cn/Pests/webp/90e28ccfc0d8e2a8a5494f26d71e7ee6.jpg.webp',
      'https://img.plantplus.cn/diseases/webp/0c8f28fa1db1a9c62dde8e9fc48d2335.jpg.webp'
    ],
    controlMethods: [
      { title: '农业防治', description: '合理栽培，选用抗虫品种，适时早栽，科学灌溉排水。' },
      { title: '生物防治', description: '保护和利用天敌，如蜘蛛、瓢虫等天敌捕食飞虱。' },
      { title: '化学防治', description: '合理使用吡蚜酮、吡虫啉等药剂进行喷雾防治。' }
    ],
    similarPests: [
      { name: '白背飞虱', differences: { color: '浅黄色', size: '较小', damage: '吸取汁液' } },
      { name: '灰飞虱', differences: { color: '灰褐色', size: '中等', damage: '传播病毒' } }
    ]
  },
  {
    id: 2,
    name: '小麦条锈病',
    latinName: 'Puccinia striiformis',
    image: 'https://img.plantplus.cn/diseases/webp/0c8f28fa1db1a9c62dde8e9fc48d2335.jpg.webp',
    type: 'disease',
    crops: ['小麦', '大麦'],
    symptoms: ['黄条纹', '叶片变黄', '产孢子'],
    seasons: ['spring', 'autumn'],
    symptomDescription: '小麦条锈病在叶片上产生黄色条纹状病斑，病斑上产生黄色夏孢子堆，严重时导致整个叶片枯黄，影响光合作用，降低产量。',
    symptomImages: [
      'https://img.plantplus.cn/diseases/webp/0c8f28fa1db1a9c62dde8e9fc48d2335.jpg.webp',
      'https://i-tw.plantplus.cn/dise/90d6ca4d6a4311e9bb72000c29ed795c-0-0-t.jpg'
    ],
    controlMethods: [
      { title: '农业防治', description: '种植抗病品种，适时早播，合理密植。' },
      { title: '化学防治', description: '发病初期喷施三唑酮、戊唑醇等杀菌剂。' },
      { title: '监测预警', description: '加强田间巡查，及时发现病情，早期防治。' }
    ],
    similarPests: [
      { name: '小麦叶锈病', differences: { color: '橙红色', location: '叶片上部', shape: '点状' } },
      { name: '小麦秆锈病', differences: { color: '黑褐色', location: '茎秆', shape: '长条形' } }
    ]
  },
  {
    id: 3,
    name: '棉花红蜘蛛',
    latinName: 'Tetranychus cinnabarinus',
    image: 'https://img.plantplus.cn/Pests/webp/9bb35bd4e59d56e46fc7a37453c58ddd.jpg.webp',
    type: 'mite',
    crops: ['棉花', '蔬菜', '果树'],
    symptoms: ['叶片泛黄', '网状丝', '干枯'],
    seasons: ['summer'],
    symptomDescription: '棉花红蜘蛛在植物叶片背面吸取汁液，受害叶片正面出现黄白色斑点，严重时叶片变为黄褐色并干枯脱落。叶片背面可见细小红色螨虫和白色丝网。',
    symptomImages: [
      'https://img.plantplus.cn/Pests/webp/9bb35bd4e59d56e46fc7a37453c58ddd.jpg.webp',
      'https://i-tw.plantplus.cn/pest/dea3264da49711eab9d7000c29ed795c-0-0-t.jpg'
    ],
    controlMethods: [
      { title: '物理防治', description: '适时喷水冲洗植株，降低虫口密度。' },
      { title: '生物防治', description: '释放捕食螨、瓢虫等天敌。' },
      { title: '化学防治', description: '使用阿维菌素、哒螨灵等药剂防治。' }
    ],
    similarPests: [
      { name: '二斑叶螨', differences: { color: '淡黄绿色', spots: '体侧两个黑斑', size: '较小' } },
      { name: '朱砂叶螨', differences: { color: '深红色', movement: '较为缓慢', webbing: '较少' } }
    ]
  }
])

// Dialog and detail state
const showDetailDialog = ref(false)
const selectedAtlas = ref<AtlasItem | null>(null)

// Recommendations
const recommendations = ref<Recommendation[]>([])

// Crop options for filter
const cropOptions = [
  { value: '水稻', label: '水稻' },
  { value: '小麦', label: '小麦' },
  { value: '大麦', label: '大麦' },
  { value: '棉花', label: '棉花' },
  { value: '蔬菜', label: '蔬菜' },
  { value: '果树', label: '果树' }
]

// Classification tree data
const classificationTree = [
  {
    label: '害虫类',
    children: [
      { label: '鳞翅目', children: [{ label: '稻纵卷叶螟' }, { label: '棉铃虫' }] },
      { label: '半翅目', children: [{ label: '稻飞虱' }, { label: '蝽象' }] }
    ]
  },
  {
    label: '病害类',
    children: [
      { label: '真菌病害', children: [{ label: '小麦条锈病' }, { label: '水稻稻瘟病' }] },
      { label: '细菌病害', children: [{ label: '水稻白叶枯病' }, { label: '番茄青枯病' }] },
      { label: '病毒病害', children: [{ label: '马铃薯Y病毒病' }, { label: '黄瓜花叶病毒病' }] }
    ]
  },
  {
    label: '螨类',
    children: [
      { label: '叶螨科', children: [{ label: '棉花红蜘蛛' }, { label: '二斑叶螨' }] }
    ]
  }
]

// Related atlases for detail view
const relatedAtlases = ref([
  {
    name: '白背飞虱',
    image: 'https://img.plantplus.cn/Pests/webp/c4ee8e47cf6eb2d4ba37f44f4f16f456.jpg.webp',
    similarity: 85
  },
  {
    name: '灰飞虱',
    image: 'https://img.plantplus.cn/Pests/webp/8de4a773a5fa92a12e9ecdb6f1c38532.jpg.webp',
    similarity: 72
  }
])

// Computed properties
const similarPestsData = computed<ComparisonRow[]>(() => {
  if (!selectedAtlas.value) return []
  
  const features = ['颜色', '形态', '危害方式']
  const data: ComparisonRow[] = []
  
  features.forEach(feature => {
    const row: ComparisonRow = { feature, current: '' }
    
    if (selectedAtlas.value) {
      row.current = getFeatureValue(selectedAtlas.value, feature)
      
      selectedAtlas.value.similarPests.forEach((pest, idx) => {
        const key = `similar${idx}`
        row[key] = getFeatureValue(pest, feature)
      })
    }
    
    data.push(row)
  })
  
  return data
})

// Helper function to get feature values
function getFeatureValue(pest: AtlasItem | SimilarPest, feature: string): string {
  if ('differences' in pest) {
    // 处理 SimilarPest 类型
    switch(feature) {
      case '颜色': return pest.differences.color || '-'
      case '形态': return pest.differences.shape || pest.differences.size || '-'
      case '危害方式': return pest.differences.damage || '-'
      default: return '-'
    }
  } else if (feature === '颜色' && pest.type === 'insect') {
    // 针对昆虫的颜色特征做默认处理
    return '黄褐色'
  } else if (feature === '形态' && pest.type === 'disease') {
    // 针对病害的形态特征做默认处理
    return '斑点状'
  } else if (feature === '危害方式') {
    // 根据类型推断危害方式
    switch(pest.type) {
      case 'insect': return '吸食汁液'
      case 'disease': return '侵染组织'
      case 'mite': return '吸取汁液'
      default: return '-'
    }
  }
  
  return '-'
}

// Methods
function showAtlasDetail(atlas: AtlasItem) {
  selectedAtlas.value = atlas
  showDetailDialog.value = true
  
  // Generate recommendations based on selected atlas
  generateRecommendations(atlas)
}

function handleNodeClick(data: { label: string }) {
  ElMessage.info(`选择了分类: ${data.label}`)
  // In real implementation, would filter atlas items based on classification
}

function advancedSearch() {
  ElMessage.success(`搜索: ${searchQuery.value}`)
  // In real implementation, would perform search against backend API
}

function resetFilters() {
  filterType.value = 'all'
  filterCrops.value = []
  filterSeasons.value = []
}

function applyFilters() {
  ElMessage.success('应用筛选')
  showFilterDrawer.value = false
  // In real implementation, would filter atlas items based on selected filters
}

function generateRecommendations(atlas: AtlasItem) {
  // In real implementation, would generate recommendations based on selected atlas from backend
  recommendations.value = [
    {
      title: '有机磷农药替代方案',
      type: '防治产品',
      image: 'https://s1.ax1x.com/2023/05/08/p9uCQl6.jpg'
    },
    {
      title: '稻飞虱综合防治策略',
      type: '专家建议',
      image: 'https://img.plantplus.cn/Pests/webp/90e28ccfc0d8e2a8a5494f26d71e7ee6.jpg.webp'
    },
    {
      title: '水稻绿色防控实践',
      type: '案例分享',
      image: 'https://i-tw.plantplus.cn/article/5b5a4730d91d11eab9d9000c29ed795c-0-0-t.jpg'
    }
  ]
}

function handleRecommendationClick(rec: Recommendation) {
  ElMessage.info(`查看推荐: ${rec.title}`)
  // In real implementation, would navigate to recommendation detail
}

// Lifecycle hooks
onMounted(() => {
  // In real implementation, would fetch atlas items from backend API
  lastUpdateTime.value = new Date()
})
</script>

<style scoped>
.expert-knowledge {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1f2937;
}

.search-box {
  min-width: 350px;
}

.search-input :deep(.el-input__wrapper) {
  background-color: rgba(31, 41, 55, 0.3);
}

.search-input :deep(.el-input__inner) {
  color: #e5e7eb;
}

.knowledge-content {
  flex: 1;
  margin-bottom: 20px;
  overflow-y: auto;
}

/* 筛选抽屉样式 */
.filter-content {
  padding: 0 10px;
}

.filter-content h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #3b82f6;
}

.filter-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 详情对话框样式 */
.atlas-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.atlas-detail {
  height: 70vh;
}

.detail-sidebar {
  background-color: rgba(30, 58, 138, 0.4);
  color: #ffffff;
  padding: 20px;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 20px;
}

.sidebar-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  border-bottom: 1px solid #3b82f6;
  padding-bottom: 5px;
}

.recommended-item {
  display: flex;
  margin-bottom: 15px;
  padding: 5px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.rec-image {
  width: 60px;
  height: 60px;
  overflow: hidden;
  margin-right: 10px;
}

.rec-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.rec-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.rec-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.rec-similarity {
  font-size: 12px;
  color: #d1d5db;
}

.detail-main {
  background-color: #1f2937;
  color: #ffffff;
  padding: 20px;
  overflow-y: auto;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-header h2 {
  margin: 0;
  font-size: 24px;
}

.detail-header .latin-name {
  font-size: 16px;
  margin-left: 10px;
  font-style: italic;
  color: #d1d5db;
}

.atlas-image-container {
  margin-bottom: 20px;
  text-align: center;
}

.detail-image {
  max-height: 400px;
  max-width: 100%;
}

.detail-sections {
  margin-top: 20px;
}

.detail-sections :deep(.el-tabs) {
  background-color: rgba(31, 41, 55, 0.5);
}

.detail-sections :deep(.el-tabs__header) {
  background-color: rgba(30, 58, 138, 0.4);
}

.detail-sections :deep(.el-tabs__item) {
  color: #d1d5db;
}

.detail-sections :deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

.symptom-description {
  display: flex;
  gap: 20px;
}

.text-content {
  flex: 1;
  line-height: 1.6;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  flex: 1;
}

.symptom-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.control-methods {
  padding: 20px 0;
}

.control-methods :deep(.el-step__title) {
  color: #e5e7eb;
}

.control-methods :deep(.el-step__description) {
  color: #9ca3af;
}

.comparison-table :deep(.el-table) {
  background-color: transparent;
  color: #e5e7eb;
}

.comparison-table :deep(.el-table tr) {
  background-color: transparent !important;
}

.comparison-table :deep(.el-table th) {
  background-color: rgba(30, 58, 138, 0.4);
  color: #e5e7eb;
}

.comparison-table :deep(.el-table td) {
  border-color: #4b5563;
}

/* 推荐面板样式 */
.recommendation-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 300px;
  background-color: rgba(30, 58, 138, 0.7);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 58, 138, 0.3);
  overflow: hidden;
  z-index: 100;
}

.panel-header {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
  margin: 0;
  color: #ffffff;
}

.panel-content {
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.recommendation-cards {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rec-card {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s;
}

.rec-card:hover {
  transform: translateY(-2px);
}

.rec-card .rec-image {
  width: 80px;
  height: 80px;
}

.rec-card .rec-info {
  padding: 10px;
}

.rec-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #ffffff;
}

.rec-type {
  font-size: 12px;
  color: #d1d5db;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .symptom-description {
    flex-direction: column;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .search-box {
    width: 100%;
  }
}
</style> 