<!-- 
  PestDetail.vue
  虫害详情页面模块
  展示单个虫害的详细信息、历史数据和防治建议
-->
<template>
  <div class="pest-detail">
    <!-- 页面标题 -->
    <PageHeader
      :title="pestData.name + ' 详细信息'"
      description="展示虫害的详细信息、分布趋势、生长规律和防治措施"
      icon="InfoFilled"
    >
      <template #actions>
        <el-button type="primary" size="small" @click="goBack">
          <el-icon><Back /></el-icon>
          返回列表
        </el-button>
      </template>
    </PageHeader>
    
    <!-- 基本信息和图片 -->
    <div class="info-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <DataPanel title="基本信息">
            <div class="basic-info">
              <div class="pest-image">
                <img v-if="pestData.imageUrl" :src="pestData.imageUrl" :alt="pestData.name" />
                <div v-else class="no-image">
                  <el-icon><Picture /></el-icon>
                </div>
              </div>
              
              <div class="info-list">
                <div class="info-item">
                  <span class="item-label">学名:</span>
                  <span class="item-value">{{ pestData.scientificName }}</span>
                </div>
                <div class="info-item">
                  <span class="item-label">分类:</span>
                  <span class="item-value">{{ pestData.category }}</span>
                </div>
                <div class="info-item">
                  <span class="item-label">危害作物:</span>
                  <span class="item-value">{{ pestData.affectedCrops }}</span>
                </div>
                <div class="info-item">
                  <span class="item-label">危害程度:</span>
                  <span class="item-value">
                    <el-tag :type="getDangerLevelType(pestData.dangerLevel)" effect="dark">
                      {{ pestData.dangerLevel }}
                    </el-tag>
                  </span>
                </div>
                <div class="info-item">
                  <span class="item-label">分布区域:</span>
                  <span class="item-value">{{ pestData.distribution }}</span>
                </div>
                <div class="info-item">
                  <span class="item-label">发现时间:</span>
                  <span class="item-value">{{ pestData.discoveryTime }}</span>
                </div>
              </div>
            </div>
          </DataPanel>
        </el-col>
        
        <el-col :span="16">
          <DataPanel title="虫害描述">
            <div class="description-content">
              <p>{{ pestData.description }}</p>
              
              <div class="feature-list">
                <h4>主要特征</h4>
                <ul>
                  <li v-for="(feature, index) in pestData.features" :key="index">
                    {{ feature }}
                  </li>
                </ul>
              </div>
              
              <div class="lifecycle">
                <h4>生活习性</h4>
                <p>{{ pestData.lifecycle }}</p>
              </div>
            </div>
          </DataPanel>
        </el-col>
      </el-row>
    </div>
    
    <!-- 分布和历史趋势 -->
    <div class="trend-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <DataPanel title="历史发生趋势">
            <div class="chart-container" ref="historyTrendChart"></div>
          </DataPanel>
        </el-col>
        
        <el-col :span="12">
          <DataPanel title="区域分布热力图">
            <div class="chart-container" ref="distributionChart"></div>
          </DataPanel>
        </el-col>
      </el-row>
    </div>
    
    <!-- 防治措施 -->
    <div class="control-section">
      <DataPanel title="防治措施与建议">
        <el-tabs type="border-card" class="dark-tabs">
          <el-tab-pane label="农业防治">
            <div class="measure-content">
              <h4>农业防治措施</h4>
              <ul>
                <li v-for="(measure, index) in pestData.controlMeasures.agricultural" :key="index">
                  {{ measure }}
                </li>
              </ul>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="化学防治">
            <div class="measure-content">
              <h4>推荐农药</h4>
              <el-table :data="pestData.controlMeasures.chemical" border stripe>
                <el-table-column prop="name" label="农药名称" width="180" />
                <el-table-column prop="dosage" label="用量" width="120" />
                <el-table-column prop="method" label="使用方法" />
                <el-table-column prop="precautions" label="注意事项" />
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="生物防治">
            <div class="measure-content">
              <h4>生物防治措施</h4>
              <ul>
                <li v-for="(measure, index) in pestData.controlMeasures.biological" :key="index">
                  {{ measure }}
                </li>
              </ul>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="物理防治">
            <div class="measure-content">
              <h4>物理防治措施</h4>
              <ul>
                <li v-for="(measure, index) in pestData.controlMeasures.physical" :key="index">
                  {{ measure }}
                </li>
              </ul>
            </div>
          </el-tab-pane>
        </el-tabs>
      </DataPanel>
    </div>
    
    <!-- 相关虫害 -->
    <div class="related-section">
      <DataPanel title="相关虫害">
        <div class="related-pests">
          <div class="related-pest-item" v-for="item in relatedPests" :key="item.id">
            <div class="pest-avatar">
              <img v-if="item.imageUrl" :src="item.imageUrl" :alt="item.name" />
              <div v-else class="no-avatar">
                <el-icon><PictureFilled /></el-icon>
              </div>
            </div>
            <div class="pest-name">{{ item.name }}</div>
            <div class="pest-category">{{ item.category }}</div>
            <el-button type="primary" size="small" link @click="navigateToPest(item.id)">
              查看详情
            </el-button>
          </div>
        </div>
      </DataPanel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { 
  InfoFilled, 
  Back, 
  Picture, 
  PictureFilled
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue';
import DataPanel from '../DeviceManagement/components/DataPanel.vue';

const route = useRoute();
const router = useRouter();

// 图表引用
const historyTrendChart = ref(null);
const distributionChart = ref(null);

// 图表实例
let trendChart = null;
let heatmapChart = null;

// 模拟虫害数据
const pestData = ref({
  id: route.params.id || '1',
  name: '稻飞虱',
  scientificName: 'Nilaparvata lugens',
  category: '半翅目叶蝉科',
  affectedCrops: '水稻',
  dangerLevel: '严重',
  distribution: '华南、华东、华中地区',
  discoveryTime: '2023-05-15',
  imageUrl: '',
  description: '稻飞虱是水稻上的主要害虫之一，主要通过吸取水稻汁液造成危害。严重时可导致水稻叶片变黄、枯萎，甚至造成"虱害"。此外，稻飞虱还是水稻病毒病的主要传播媒介。',
  features: [
    '成虫体长约2.5-4.0毫米，长翅型体色为黄褐色或淡褐色',
    '若虫体色浅黄，没有翅',
    '卵长椭圆形，白色半透明，产于水稻叶鞘或叶脉中',
    '喜欢群集于水稻基部吸食汁液'
  ],
  lifecycle: '稻飞虱一年可发生多代，在华南地区可达10-12代，在长江流域可达6-8代。冬季以成虫在稻桩、杂草上越冬。越冬成虫在春季气温回升后开始活动产卵。卵期4-8天，若虫期10-15天，成虫寿命可达30天左右。',
  controlMeasures: {
    agricultural: [
      '水稻收割后及时清除田间稻桩，减少越冬场所',
      '选用抗虫品种',
      '适当调整播种期，避开害虫高发期',
      '合理密植，改善田间小气候，降低虱害发生'
    ],
    chemical: [
      { name: '吡蚜酮', dosage: '10-15毫升/亩', method: '兑水30-40公斤喷雾', precautions: '收获前15天停止用药' },
      { name: '烯啶虫胺', dosage: '5-8克/亩', method: '兑水30公斤喷雾', precautions: '对蜜蜂有毒性，谨慎使用' },
      { name: '噻嗪酮', dosage: '20-30毫升/亩', method: '兑水30-40公斤喷雾', precautions: '禁止与碱性农药混用' }
    ],
    biological: [
      '释放稻飞虱赤眼蜂，每亩释放3-5万头',
      '保护田间蜘蛛等天敌',
      '使用苏云金杆菌等微生物农药'
    ],
    physical: [
      '设置黄板诱杀成虫',
      '安装杀虫灯，诱杀夜间活动的成虫',
      '田间设置防虫网，阻止成虫迁入'
    ]
  }
});

// 相关虫害
const relatedPests = ref([
  { id: '2', name: '稻纵卷叶螟', category: '鳞翅目螟蛾科', imageUrl: '' },
  { id: '3', name: '二化螟', category: '鳞翅目螟蛾科', imageUrl: '' },
  { id: '4', name: '水稻螟虫', category: '鳞翅目螟蛾科', imageUrl: '' },
  { id: '5', name: '褐飞虱', category: '半翅目叶蝉科', imageUrl: '' }
]);

// 获取危害程度标签类型
const getDangerLevelType = (level) => {
  switch(level) {
    case '轻微': return 'info';
    case '中等': return 'warning';
    case '严重': return 'danger';
    case '危急': return 'error';
    default: return 'info';
  }
};

// 返回列表
const goBack = () => {
  router.push('/pest-analysis/pest-database');
};

// 跳转到其他虫害详情
const navigateToPest = (id) => {
  router.push(`/pest-analysis/pest-detail/${id}`);
};

// 初始化历史趋势图
const initHistoryTrendChart = () => {
  if (historyTrendChart.value) {
    trendChart = echarts.init(historyTrendChart.value);
    
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    const historicalData = [10, 15, 25, 40, 90, 120, 150, 135, 80, 50, 30, 20];
    const predictedData = [null, null, null, null, null, null, null, 135, 90, 60, 35, 25];
    
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      legend: {
        data: ['历史数据', '预测趋势'],
        textStyle: {
          color: '#9ca3af'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: months,
        axisLabel: {
          color: '#9ca3af'
        }
      },
      yAxis: {
        type: 'value',
        name: '虫害数量',
        nameTextStyle: {
          color: '#9ca3af'
        },
        axisLabel: {
          color: '#9ca3af'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      },
      series: [
        {
          name: '历史数据',
          type: 'line',
          data: historicalData,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#3b82f6',
            shadowColor: 'rgba(59, 130, 246, 0.3)',
            shadowBlur: 10
          },
          itemStyle: {
            color: '#3b82f6',
            borderColor: '#1f2937',
            borderWidth: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(59, 130, 246, 0.5)' },
              { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
            ])
          }
        },
        {
          name: '预测趋势',
          type: 'line',
          data: predictedData,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#10b981',
            shadowColor: 'rgba(16, 185, 129, 0.3)',
            shadowBlur: 10,
            type: 'dashed'
          },
          itemStyle: {
            color: '#10b981',
            borderColor: '#1f2937',
            borderWidth: 2
          }
        }
      ]
    };
    
    trendChart.setOption(option);
    
    window.addEventListener('resize', () => {
      trendChart?.resize();
    });
  }
};

// 初始化分布热力图
const initDistributionChart = () => {
  if (distributionChart.value) {
    heatmapChart = echarts.init(distributionChart.value);
    
    // 创建虚拟数据
    const data = [];
    for (let i = 0; i < 50; i++) {
      data.push([
        Math.floor(Math.random() * 100),
        Math.floor(Math.random() * 100),
        Math.floor(Math.random() * 100)
      ]);
    }
    
    const option = {
      tooltip: {
        position: 'top',
        formatter: function (params) {
          return `坐标: (${params.value[0]}, ${params.value[1]})<br>密度: ${params.value[2]}`;
        },
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: Array.from({ length: 10 }, (_, i) => `区域${i+1}`),
        splitArea: {
          show: true
        },
        axisLabel: {
          color: '#9ca3af'
        }
      },
      yAxis: {
        type: 'category',
        data: Array.from({ length: 10 }, (_, i) => `区域${i+1}`),
        splitArea: {
          show: true
        },
        axisLabel: {
          color: '#9ca3af'
        }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '0%',
        textStyle: {
          color: '#9ca3af'
        },
        inRange: {
          color: ['#10b981', '#f59e0b', '#ef4444']
        }
      },
      series: [{
        name: '虫害分布热力图',
        type: 'heatmap',
        data: data,
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };
    
    heatmapChart.setOption(option);
    
    window.addEventListener('resize', () => {
      heatmapChart?.resize();
    });
  }
};

onMounted(() => {
  // 初始化图表
  initHistoryTrendChart();
  initDistributionChart();
});

onUnmounted(() => {
  // 销毁图表实例
  trendChart?.dispose();
  heatmapChart?.dispose();
  
  // 移除监听器
  window.removeEventListener('resize', () => {});
});
</script>

<style scoped>
.pest-detail {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.info-section,
.trend-section,
.control-section,
.related-section {
  margin-bottom: 20px;
}

.basic-info {
  display: flex;
  gap: 20px;
}

.pest-image {
  width: 150px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #1f2937;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.pest-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4b5563;
  font-size: 40px;
}

.info-list {
  flex: 1;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
}

.item-label {
  width: 80px;
  color: #9ca3af;
  flex-shrink: 0;
}

.item-value {
  color: #e5e7eb;
  flex: 1;
}

.description-content {
  color: #e5e7eb;
  line-height: 1.6;
}

.feature-list h4,
.lifecycle h4 {
  color: #3b82f6;
  margin-bottom: 10px;
}

.feature-list ul {
  padding-left: 20px;
  margin-bottom: 20px;
}

.feature-list li {
  margin-bottom: 5px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.measure-content h4 {
  color: #3b82f6;
  margin-bottom: 15px;
}

.measure-content ul {
  padding-left: 20px;
}

.measure-content li {
  margin-bottom: 10px;
  color: #e5e7eb;
  line-height: 1.6;
}

.related-pests {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.related-pest-item {
  min-width: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  transition: all 0.3s;
}

.related-pest-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.pest-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #1f2937;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.pest-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4b5563;
  font-size: 32px;
}

.pest-name {
  font-size: 16px;
  font-weight: 500;
  color: #e5e7eb;
  margin-bottom: 5px;
  text-align: center;
}

.pest-category {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 10px;
  text-align: center;
}

.dark-tabs :deep(.el-tabs__content) {
  color: #e5e7eb;
}

.dark-tabs :deep(.el-tabs__item) {
  color: #9ca3af;
}

.dark-tabs :deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

.dark-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: #3b4863;
}

.dark-tabs :deep(.el-tabs__active-bar) {
  background-color: #3b82f6;
}

.dark-tabs :deep(.el-tabs--border-card) {
  background: #1f2937;
  border-color: #3b4863;
}

.dark-tabs :deep(.el-tabs--border-card > .el-tabs__header) {
  background: #1a2234;
  border-color: #3b4863;
}

.dark-tabs :deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active) {
  background-color: #1f2937;
  border-right-color: #3b4863;
  border-left-color: #3b4863;
}

@media (max-width: 768px) {
  .info-section .el-row,
  .trend-section .el-row {
    flex-direction: column;
  }
  
  .info-section .el-col,
  .trend-section .el-col {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
  }
  
  .basic-info {
    flex-direction: column;
  }
  
  .pest-image {
    width: 100%;
    max-width: 200px;
    margin: 0 auto 20px;
  }
}
</style>

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save
--> 