<template>
  <div class="history-trajectory-container">
    <!-- 页面标题 -->
    <PageHeader
      title="历史轨迹回放"
      description="查看设备历史轨迹，支持按时间、设备和任务筛选，提供完整回放和关键点标记功能"
      icon="MapLocation"
    >
      <template #actions>
        <el-button type="primary" size="small" @click="showFilterPanel = !showFilterPanel">
          <el-icon><Filter /></el-icon>
          {{ showFilterPanel ? '隐藏筛选' : '显示筛选' }}
        </el-button>
      </template>
    </PageHeader>
    
    <!-- 筛选面板 -->
    <DataPanel v-if="showFilterPanel" title="轨迹筛选" class="filter-panel">
      <el-form :model="filterForm" label-width="80px" class="filter-form">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="设备类型">
              <el-select v-model="filterForm.deviceType" placeholder="请选择设备类型" clearable>
                <el-option label="全部设备" value="" />
                <el-option label="无人机" value="drone" />
                <el-option label="机器狗" value="robotDog" />
                <el-option label="履带机器人" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="设备">
              <el-select 
                v-model="filterForm.deviceId" 
                placeholder="请选择设备" 
                clearable
                :disabled="!filteredDevices.length"
              >
                <el-option 
                  v-for="device in filteredDevices" 
                  :key="device.id" 
                  :label="device.name" 
                  :value="device.id" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="任务类型">
              <el-select v-model="filterForm.taskType" placeholder="请选择任务类型" clearable>
                <el-option label="全部类型" value="" />
                <el-option label="喷洒任务" value="spray" />
                <el-option label="巡检任务" value="patrol" />
                <el-option label="消杀任务" value="disinfection" />
                <el-option label="采样任务" value="sampling" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="filterForm.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :shortcuts="dateShortcuts"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <div class="form-actions">
          <el-button type="primary" @click="searchTrajectories">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetFilter">
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
        </div>
      </el-form>
    </DataPanel>
    
    <el-row :gutter="24" class="main-content">
      <!-- 左侧轨迹列表 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="5">
        <DataPanel title="历史轨迹记录" class="trajectory-list-panel">
          <template #actions>
            <el-radio-group v-model="listViewMode" size="small">
              <el-radio-button label="card">卡片</el-radio-button>
              <el-radio-button label="list">列表</el-radio-button>
            </el-radio-group>
          </template>
          
          <div v-if="trajectories.length === 0" class="empty-list">
            <el-empty description="暂无轨迹数据" :image-size="80" />
          </div>
          
          <!-- 卡片视图 -->
          <div v-if="listViewMode === 'card'" class="trajectory-cards">
            <div 
              v-for="trajectory in trajectories" 
              :key="trajectory.trajectoryId"
              class="trajectory-card"
              :class="{ 'active': selectedTrajectoryId === trajectory.trajectoryId }"
              @click="selectTrajectory(trajectory.trajectoryId)"
            >
              <div class="card-header">
                <div class="device-icon" :class="getDeviceType(trajectory.deviceId)">
                  <el-icon><component :is="getDeviceIcon(trajectory.deviceId)"></component></el-icon>
                </div>
                <div class="device-info">
                  <div class="device-name">{{ getDeviceName(trajectory.deviceId) }}</div>
                  <div class="task-info" v-if="trajectory.taskId">
                    {{ getTaskName(trajectory.taskId) }}
                  </div>
                  <div class="task-info" v-else>
                    <el-tag size="small" effect="dark" type="info">自由任务</el-tag>
                  </div>
                </div>
              </div>
              
              <div class="card-body">
                <div class="info-item">
                  <span class="label">开始时间：</span>
                  <span class="value">{{ formatDate(trajectory.startTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">结束时间：</span>
                  <span class="value">{{ formatDate(trajectory.endTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">总距离：</span>
                  <span class="value">{{ formatDistance(trajectory.totalDistance) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">持续时间：</span>
                  <span class="value">{{ formatDuration(trajectory.duration) }}</span>
                </div>
              </div>
              
              <div class="card-footer">
                <el-button size="small" text type="info" @click.stop="viewTrajectoryDetail(trajectory.trajectoryId)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button size="small" type="primary" text @click.stop="playTrajectory(trajectory.trajectoryId)">
                  <el-icon><VideoPlay /></el-icon>
                  播放
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 列表视图 -->
          <div v-if="listViewMode === 'list'" class="trajectory-table">
            <el-table
              :data="trajectories"
              style="width: 100%"
              @row-click="row => selectTrajectory(row.trajectoryId)"
              :highlight-current-row="true"
              row-key="trajectoryId"
            >
              <el-table-column label="设备" min-width="120">
                <template #default="scope">
                  <div class="device-cell">
                    <div class="device-icon-small" :class="getDeviceType(scope.row.deviceId)">
                      <el-icon><component :is="getDeviceIcon(scope.row.deviceId)"></component></el-icon>
                    </div>
                    <span>{{ getDeviceName(scope.row.deviceId) }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="startTime" label="时间" min-width="120">
                <template #default="scope">
                  {{ formatSimpleDate(scope.row.startTime) }}
                </template>
              </el-table-column>
              
              <el-table-column label="距离" min-width="80">
                <template #default="scope">
                  {{ formatDistance(scope.row.totalDistance) }}
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <div class="table-actions">
                    <el-button size="small" circle @click.stop="viewTrajectoryDetail(scope.row.trajectoryId)">
                      <el-icon><View /></el-icon>
                    </el-button>
                    <el-button size="small" type="primary" circle @click.stop="playTrajectory(scope.row.trajectoryId)">
                      <el-icon><VideoPlay /></el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          
          <!-- 分页 -->
          <template #footer v-if="trajectories.length > 0">
            <div class="pagination">
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50]"
                layout="total, sizes, prev, pager, next"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </template>
        </DataPanel>
      </el-col>
      
      <!-- 右侧地图和回放控制 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="18" :xl="19">
        <DataPanel title="轨迹地图" class="map-container">
          <template #actions v-if="currentTrajectory">
            <el-tag type="info" effect="dark" size="small">{{ getDeviceName(currentTrajectory?.deviceId || '') }}</el-tag>
          </template>
          
          <!-- 空状态 -->
          <div v-if="!selectedTrajectoryId" class="empty-map">
            <el-empty 
              description="请选择轨迹进行查看和回放" 
              :image-size="128"
            >
              <template #image>
                <el-icon style="font-size: 64px; color: #9ca3af;"><MapLocation /></el-icon>
              </template>
            </el-empty>
          </div>
          
          <!-- 地图视图 -->
          <div v-else class="map-view">
            <div class="map-area" ref="mapRef">
              <!-- 这里将放置地图组件 -->
              <div class="map-placeholder">
                <p>此处将显示地图和轨迹，整合实际地图组件后移除此占位内容</p>
              </div>
            </div>
            
            <!-- 轨迹详情侧边栏 -->
            <div class="trajectory-sidebar" v-if="currentTrajectory">
              <div class="trajectory-header">
                <div class="info-section">
                  <h3>轨迹详情</h3>
                  <div class="device-badge" :class="getDeviceType(currentTrajectory.deviceId)">
                    {{ getDeviceName(currentTrajectory.deviceId) }}
                  </div>
                </div>
                
                <el-button size="small" circle @click="closeSidebar">
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
              
              <div class="trajectory-details">
                <div class="detail-item">
                  <div class="detail-label">开始时间</div>
                  <div class="detail-value">{{ formatDateTime(currentTrajectory.startTime) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">结束时间</div>
                  <div class="detail-value">{{ formatDateTime(currentTrajectory.endTime) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">总距离</div>
                  <div class="detail-value highlight">{{ formatDistance(currentTrajectory.totalDistance) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">平均速度</div>
                  <div class="detail-value">{{ formatSpeed(currentTrajectory.averageSpeed) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">行程时间</div>
                  <div class="detail-value">{{ formatDuration(currentTrajectory.duration) }}</div>
                </div>
                <div class="detail-item" v-if="currentTrajectory.taskId">
                  <div class="detail-label">关联任务</div>
                  <div class="detail-value">{{ getTaskName(currentTrajectory.taskId) }}</div>
                </div>
              </div>
              
              <div class="key-points" v-if="currentTrajectoryKeyPoints.length > 0">
                <h4>关键点 ({{ currentTrajectoryKeyPoints.length }})</h4>
                <el-timeline>
                  <el-timeline-item
                    v-for="(point, index) in currentTrajectoryKeyPoints"
                    :key="index"
                    :type="getKeyPointType(point.eventType)"
                    :color="getKeyPointColor(point.eventType)"
                    :timestamp="formatTime(point.timestamp)"
                    size="small"
                  >
                    <div class="keypoint-content">
                      <div class="keypoint-desc">{{ point.description }}</div>
                      <el-button 
                        size="small" 
                        text 
                        type="primary"
                        @click="focusOnKeyPoint(point)"
                      >
                        <el-icon><LocationInformation /></el-icon>
                        定位
                      </el-button>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
            
            <!-- 回放控制面板 -->
            <div class="playback-controls" v-if="isPlaying || currentTrajectory">
              <div class="time-display">
                <span class="current-time">{{ formatTime(currentPlaybackTime) }}</span>
                <span class="time-separator">/</span>
                <span class="total-time">{{ formatTime(currentTrajectory?.endTime || '') }}</span>
              </div>
              
              <el-slider 
                v-model="playbackProgress" 
                :max="100"
                :show-tooltip="false"
                @change="seekToPosition"
              />
              
              <div class="control-buttons">
                <el-button-group>
                  <el-tooltip content="跳到起点">
                    <el-button :disabled="!currentTrajectory" @click="skipToStart">
                      <el-icon><DArrowLeft /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip :content="isPlaying ? '暂停' : '播放'">
                    <el-button :disabled="!currentTrajectory" @click="togglePlayback">
                      <el-icon><component :is="isPlaying ? 'VideoPause' : 'VideoPlay'"></component></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="跳到终点">
                    <el-button :disabled="!currentTrajectory" @click="skipToEnd">
                      <el-icon><DArrowRight /></el-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
                
                <div class="speed-control">
                  <span class="speed-label">速度:</span>
                  <el-select v-model="playbackSpeed" size="small" class="speed-select">
                    <el-option label="0.5x" :value="0.5" />
                    <el-option label="1x" :value="1" />
                    <el-option label="2x" :value="2" />
                    <el-option label="4x" :value="4" />
                    <el-option label="8x" :value="8" />
                  </el-select>
                </div>
                
                <el-button-group class="view-controls">
                  <el-tooltip content="显示关键点">
                    <el-button 
                      :disabled="!currentTrajectory" 
                      :type="showKeyPoints ? 'primary' : 'default'"
                      @click="toggleKeyPoints"
                    >
                      <el-icon><Pointer /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="跟随轨迹">
                    <el-button 
                      :disabled="!currentTrajectory" 
                      :type="followTrajectory ? 'primary' : 'default'"
                      @click="toggleFollowTrajectory"
                    >
                      <el-icon><Location /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="导出轨迹">
                    <el-button :disabled="!currentTrajectory" @click="exportTrajectory">
                      <el-icon><Download /></el-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </div>
            </div>
          </div>
        </DataPanel>
      </el-col>
    </el-row>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="轨迹可用" />
        <StatusIndicator type="warning" label="分析中" />
        <StatusIndicator type="error" label="数据异常" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(new Date().toISOString()) }}</span>
        <el-button type="primary" size="small" plain @click="searchTrajectories">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { 
  Search, 
  RefreshRight, 
  View, 
  VideoPlay, 
  VideoPause, 
  MapLocation,
  LocationInformation,
  Close,
  Pointer,
  Location,
  Download,
  DArrowLeft,
  DArrowRight,
  Filter,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { TrajectoryData, TrajectoryKeyPoint, DeviceInfo } from '@/types/taskScheduling'

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue'
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue'
import DataPanel from '../DeviceManagement/components/DataPanel.vue'

// 筛选面板显示状态
const showFilterPanel = ref(true)

// 过滤表单
const filterForm = ref({
  deviceType: '',
  deviceId: '',
  taskType: '',
  timeRange: [] as string[]
})

// 日期快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date(new Date().setHours(0, 0, 0, 0))
      const end = new Date(new Date().setHours(23, 59, 59, 999))
      return [start, end]
    }
  },
  {
    text: '最近三天',
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 999))
      const start = new Date()
      start.setTime(start.getTime() - 3 * 24 * 60 * 60 * 1000)
      start.setHours(0, 0, 0, 0)
      return [start, end]
    }
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date(new Date().setHours(23, 59, 59, 999))
      const start = new Date()
      start.setTime(start.getTime() - 7 * 24 * 60 * 60 * 1000)
      start.setHours(0, 0, 0, 0)
      return [start, end]
    }
  },
  {
    text: '本月',
    value: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0)
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
      return [start, end]
    }
  }
]

// 分页设置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 视图相关状态
const listViewMode = ref('card')
const selectedTrajectoryId = ref<string | null>(null)
const mapRef = ref<HTMLElement | null>(null)
const isPlaying = ref(false)
const playbackSpeed = ref(1)
const playbackProgress = ref(0)
const currentPlaybackTime = ref('')
const playbackTimer = ref<number | null>(null)
const showKeyPoints = ref(true)
const followTrajectory = ref(true)

// 修复设备类型中的所需字段问题
const devices = ref<DeviceInfo[]>([
  {
    id: 'device-001',
    name: '无人机-01',
    type: 'drone',
    status: 'online',
    battery: 80,
    position: { x: 120, y: 150, z: 30 },
    lastActiveTime: new Date().toISOString(),
    capabilities: ['spray', 'camera']
  },
  {
    id: 'device-002',
    name: '无人机-02',
    type: 'drone',
    status: 'online',
    battery: 65,
    position: { x: 150, y: 180, z: 25 },
    lastActiveTime: new Date().toISOString(),
    capabilities: ['spray', 'camera']
  },
  {
    id: 'device-003',
    name: '机器狗-01',
    type: 'robotDog',
    status: 'online',
    battery: 75,
    position: { x: 200, y: 220, z: 0 },
    lastActiveTime: new Date().toISOString(),
    capabilities: ['patrol', 'camera']
  },
  {
    id: 'device-004',
    name: '机器狗-02',
    type: 'robotDog',
    status: 'offline',
    battery: 15,
    position: { x: 220, y: 280, z: 0 },
    lastActiveTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    capabilities: ['patrol', 'camera']
  },
  {
    id: 'device-005',
    name: '履带机器人-01',
    type: 'other',
    status: 'busy',
    battery: 42,
    position: { x: 250, y: 200, z: 0 },
    lastActiveTime: new Date().toISOString(),
    capabilities: ['spray', 'disinfection']
  }
])

// 模拟轨迹数据
const trajectories = ref<TrajectoryData[]>([
  {
    trajectoryId: 'trajectory-001',
    deviceId: 'device-001',
    taskId: 'task-001',
    startTime: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    points: generateRandomPoints(100, {x: 100, y: 100}, {x: 300, y: 200}),
    totalDistance: 1250,
    averageSpeed: 0.35,
    duration: 3600
  },
  {
    trajectoryId: 'trajectory-002',
    deviceId: 'device-002',
    taskId: 'task-002',
    startTime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - 3.5 * 60 * 60 * 1000).toISOString(),
    points: generateRandomPoints(80, {x: 150, y: 150}, {x: 350, y: 250}),
    totalDistance: 980,
    averageSpeed: 0.27,
    duration: 5400
  },
  {
    trajectoryId: 'trajectory-003',
    deviceId: 'device-003',
    taskId: 'task-003',
    startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString(),
    points: generateRandomPoints(120, {x: 200, y: 200}, {x: 400, y: 300}),
    totalDistance: 1580,
    averageSpeed: 0.44,
    duration: 3600
  },
  {
    trajectoryId: 'trajectory-004',
    deviceId: 'device-005',
    taskId: null, // 自由任务
    startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - 47 * 60 * 60 * 1000).toISOString(),
    points: generateRandomPoints(70, {x: 250, y: 250}, {x: 450, y: 350}),
    totalDistance: 850,
    averageSpeed: 0.32,
    duration: 2700
  },
  {
    trajectoryId: 'trajectory-005',
    deviceId: 'device-001',
    taskId: 'task-004',
    startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - 2.9 * 24 * 60 * 60 * 1000).toISOString(),
    points: generateRandomPoints(90, {x: 300, y: 300}, {x: 500, y: 400}),
    totalDistance: 1100,
    averageSpeed: 0.38,
    duration: 3000
  }
])

// 模拟轨迹关键点数据
const trajectoryKeyPoints = ref<Record<string, TrajectoryKeyPoint[]>>({
  'trajectory-001': [
    {
      pointIndex: 10,
      position: { x: 120, y: 120, z: 30 },
      timestamp: new Date(Date.now() - 2.9 * 60 * 60 * 1000).toISOString(),
      eventType: 'start',
      description: '开始任务执行'
    },
    {
      pointIndex: 35,
      position: { x: 180, y: 140, z: 30 },
      timestamp: new Date(Date.now() - 2.6 * 60 * 60 * 1000).toISOString(),
      eventType: 'checkpoint',
      description: '完成A区块巡检'
    },
    {
      pointIndex: 65,
      position: { x: 220, y: 170, z: 28 },
      timestamp: new Date(Date.now() - 2.3 * 60 * 60 * 1000).toISOString(),
      eventType: 'warning',
      description: '检测到植物生长异常',
      data: { anomalyType: 'growth', severity: 'medium' }
    },
    {
      pointIndex: 95,
      position: { x: 280, y: 190, z: 30 },
      timestamp: new Date(Date.now() - 2.05 * 60 * 60 * 1000).toISOString(),
      eventType: 'end',
      description: '完成任务执行'
    }
  ],
  'trajectory-002': [
    {
      pointIndex: 5,
      position: { x: 160, y: 160, z: 25 },
      timestamp: new Date(Date.now() - 4.9 * 60 * 60 * 1000).toISOString(),
      eventType: 'start',
      description: '开始喷洒作业'
    },
    {
      pointIndex: 40,
      position: { x: 240, y: 200, z: 25 },
      timestamp: new Date(Date.now() - 4.3 * 60 * 60 * 1000).toISOString(),
      eventType: 'warning',
      description: '药剂余量低于50%',
      data: { remaining: '45%' }
    },
    {
      pointIndex: 75,
      position: { x: 320, y: 240, z: 25 },
      timestamp: new Date(Date.now() - 3.6 * 60 * 60 * 1000).toISOString(),
      eventType: 'end',
      description: '完成喷洒作业'
    }
  ]
})

// 模拟任务数据，用于显示任务名称
const tasks = {
  'task-001': { name: '南区巡检任务', type: 'patrol' },
  'task-002': { name: '东区喷洒任务', type: 'spray' },
  'task-003': { name: '北区消杀任务', type: 'disinfection' },
  'task-004': { name: '西区采样任务', type: 'sampling' }
}

// 计算属性 - 根据设备类型过滤设备列表
const filteredDevices = computed(() => {
  if (!filterForm.value.deviceType) return devices.value
  return devices.value.filter(d => d.type === filterForm.value.deviceType)
})

// 计算属性 - 当前选中的轨迹
const currentTrajectory = computed(() => {
  if (!selectedTrajectoryId.value) return null
  return trajectories.value.find(t => t.trajectoryId === selectedTrajectoryId.value) || null
})

// 计算属性 - 当前轨迹的关键点
const currentTrajectoryKeyPoints = computed(() => {
  if (!selectedTrajectoryId.value) return []
  return trajectoryKeyPoints.value[selectedTrajectoryId.value] || []
})

// 生命周期钩子
onMounted(() => {
  // 初始化地图 (实际项目中应调用相应的地图API)
  initMap()
  
  // 更新分页总数
  pagination.value.total = trajectories.value.length
})

onBeforeUnmount(() => {
  // 清除定时器
  if (playbackTimer.value) {
    clearInterval(playbackTimer.value)
    playbackTimer.value = null
  }
})

// 方法 - 初始化地图
function initMap() {
  // 此处应当集成实际的地图库，例如高德地图、百度地图、Leaflet等
  console.log('地图初始化')
}

// 方法 - 生成随机轨迹点 (仅用于演示)
function generateRandomPoints(count: number, start: {x: number, y: number}, end: {x: number, y: number}) {
  const points = []
  
  // 起点
  points.push({
    position: { 
      x: start.x, 
      y: start.y, 
      z: Math.random() * 30
    },
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
    speed: 0
  })
  
  // 中间点
  for (let i = 1; i < count - 1; i++) {
    const progress = i / (count - 1)
    const randomX = Math.sin(i * 0.1) * 20 // 添加一些随机性，使轨迹不是直线
    const randomY = Math.cos(i * 0.1) * 15
    
    points.push({
      position: {
        x: start.x + (end.x - start.x) * progress + randomX,
        y: start.y + (end.y - start.y) * progress + randomY,
        z: Math.random() * 30 // 随机高度 (针对无人机)
      },
      timestamp: new Date(Date.now() - (3 - progress) * 60 * 60 * 1000).toISOString(),
      speed: 0.2 + Math.random() * 0.4, // 随机速度
      orientation: Math.random() * 360 // 随机方向
    })
  }
  
  // 终点
  points.push({
    position: { 
      x: end.x, 
      y: end.y, 
      z: Math.random() * 30
    },
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    speed: 0
  })
  
  return points
}

// 方法 - 查询轨迹
function searchTrajectories() {
  // 实际应该调用API获取轨迹数据
  ElMessage.success('查询成功')
  // 模拟查询处理
  if (filterForm.value.deviceId) {
    trajectories.value = trajectories.value.filter(t => t.deviceId === filterForm.value.deviceId)
  }
  pagination.value.total = trajectories.value.length
  pagination.value.currentPage = 1
}

// 方法 - 重置筛选条件
function resetFilter() {
  filterForm.value = {
    deviceType: '',
    deviceId: '',
    taskType: '',
    timeRange: []
  }
  // 重置数据
  // 实际应重新获取全部数据
  ElMessage.success('筛选条件已重置')
}

// 方法 - 选择轨迹
function selectTrajectory(trajectoryId: string) {
  selectedTrajectoryId.value = trajectoryId
  // 停止之前的播放
  if (isPlaying.value) {
    stopPlayback()
  }
  // 重置播放进度
  playbackProgress.value = 0
  currentPlaybackTime.value = currentTrajectory.value?.startTime || ''
  
  // 在地图上展示轨迹
  drawTrajectoryOnMap()
}

// 方法 - 查看轨迹详情
function viewTrajectoryDetail(trajectoryId: string) {
  selectedTrajectoryId.value = trajectoryId
  // 在地图上展示轨迹
  drawTrajectoryOnMap()
}

// 方法 - 播放轨迹
function playTrajectory(trajectoryId: string) {
  selectedTrajectoryId.value = trajectoryId
  // 在地图上展示轨迹
  drawTrajectoryOnMap()
  // 开始播放
  startPlayback()
}

// 方法 - 关闭侧边栏
function closeSidebar() {
  selectedTrajectoryId.value = null
  stopPlayback()
}

// 方法 - 获取设备类型
function getDeviceType(deviceId: string) {
  const device = devices.value.find(d => d.id === deviceId)
  return device ? device.type : 'other'
}

// 方法 - 获取设备图标
function getDeviceIcon(deviceId: string) {
  const type = getDeviceType(deviceId)
  switch (type) {
    case 'drone':
      return 'Aim'
    case 'robotDog':
      return 'Connection'
    default:
      return 'TakeawayBox'
  }
}

// 方法 - 获取设备名称
function getDeviceName(deviceId: string) {
  const device = devices.value.find(d => d.id === deviceId)
  return device ? device.name : '未知设备'
}

// 方法 - 获取任务名称
function getTaskName(taskId: string | null) {
  if (!taskId) return '自由任务'
  return (tasks as any)[taskId]?.name || '未知任务'
}

// 方法 - 格式化日期
function formatDate(dateStr: string) {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 方法 - 格式化简单日期 (仅显示月/日)
function formatSimpleDate(dateStr: string) {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

// 方法 - 格式化完整日期时间
function formatDateTime(dateStr: string) {
  const date = new Date(dateStr)
  return date.toLocaleString()
}

// 方法 - 格式化时间 (仅显示时:分)
function formatTime(dateStr: string) {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 方法 - 格式化距离
function formatDistance(distance: number) {
  return distance < 1000 ? `${distance.toFixed(0)}m` : `${(distance / 1000).toFixed(2)}km`
}

// 方法 - 格式化速度
function formatSpeed(speed: number) {
  return `${speed.toFixed(2)} m/s`
}

// 方法 - 格式化持续时间
function formatDuration(duration: number) {
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  const seconds = duration % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds}秒`
  } else {
    return `${seconds}秒`
  }
}

// 方法 - 获取关键点类型
function getKeyPointType(eventType: string) {
  const typeMap: Record<string, string> = {
    'start': 'success',
    'end': 'info',
    'checkpoint': 'primary',
    'warning': 'warning',
    'error': 'danger',
    'data': 'info'
  }
  return typeMap[eventType] || 'info'
}

// 方法 - 获取关键点颜色
function getKeyPointColor(eventType: string) {
  const colorMap: Record<string, string> = {
    'start': '#67c23a',
    'end': '#909399',
    'checkpoint': '#409eff',
    'warning': '#e6a23c',
    'error': '#f56c6c',
    'data': '#909399'
  }
  return colorMap[eventType] || '#909399'
}

// 方法 - 处理分页大小变化
function handleSizeChange(size: number) {
  pagination.value.pageSize = size
  // 实际应该重新获取数据
}

// 方法 - 处理页码变化
function handleCurrentChange(page: number) {
  pagination.value.currentPage = page
  // 实际应该重新获取数据
}

// 方法 - 在地图上绘制轨迹
function drawTrajectoryOnMap() {
  // 实际应该使用地图API绘制轨迹
  console.log('在地图上绘制轨迹:', currentTrajectory.value?.trajectoryId)
  
  // 显示关键点
  if (showKeyPoints.value && currentTrajectoryKeyPoints.value.length > 0) {
    console.log('显示关键点:', currentTrajectoryKeyPoints.value.length)
  }
}

// 方法 - 开始轨迹回放
function startPlayback() {
  if (!currentTrajectory.value) return
  
  isPlaying.value = true
  
  // 清除之前的定时器
  if (playbackTimer.value) {
    clearInterval(playbackTimer.value)
  }
  
  // 设置起始时间
  if (playbackProgress.value === 0) {
    currentPlaybackTime.value = currentTrajectory.value.startTime
  }
  
  // 根据播放速度设置定时器更新轨迹位置
  playbackTimer.value = window.setInterval(() => {
    // 计算下一个进度点
    const newProgress = Math.min(playbackProgress.value + (0.5 * playbackSpeed.value), 100)
    
    if (newProgress >= 100) {
      // 播放结束
      playbackProgress.value = 100
      currentPlaybackTime.value = currentTrajectory.value!.endTime
      stopPlayback()
    } else {
      // 更新进度和当前时间点
      playbackProgress.value = newProgress
      updateCurrentPlaybackTime(newProgress)
      updateMapPosition(newProgress)
    }
  }, 500) // 每500ms更新一次
}

// 方法 - 停止轨迹回放
function stopPlayback() {
  isPlaying.value = false
  if (playbackTimer.value) {
    clearInterval(playbackTimer.value)
    playbackTimer.value = null
  }
}

// 方法 - 切换播放状态
function togglePlayback() {
  if (isPlaying.value) {
    stopPlayback()
  } else {
    startPlayback()
  }
}

// 方法 - 跳到起点
function skipToStart() {
  playbackProgress.value = 0
  if (currentTrajectory.value) {
    currentPlaybackTime.value = currentTrajectory.value.startTime
    updateMapPosition(0)
  }
}

// 方法 - 跳到终点
function skipToEnd() {
  playbackProgress.value = 100
  if (currentTrajectory.value) {
    currentPlaybackTime.value = currentTrajectory.value.endTime
    updateMapPosition(100)
  }
}

// 方法 - 跳到指定位置
function seekToPosition(progress: number) {
  if (currentTrajectory.value) {
    playbackProgress.value = progress
    updateCurrentPlaybackTime(progress)
    updateMapPosition(progress)
  }
}

// 方法 - 根据进度更新当前播放时间
function updateCurrentPlaybackTime(progress: number) {
  if (!currentTrajectory.value) return
  
  const startTime = new Date(currentTrajectory.value.startTime).getTime()
  const endTime = new Date(currentTrajectory.value.endTime).getTime()
  const duration = endTime - startTime
  
  const currentTime = new Date(startTime + (duration * progress / 100))
  currentPlaybackTime.value = currentTime.toISOString()
}

// 方法 - 根据进度更新地图位置
function updateMapPosition(progress: number) {
  if (!currentTrajectory.value) return
  
  // 计算当前应该显示的轨迹点索引
  const points = currentTrajectory.value.points
  const index = Math.floor((points.length - 1) * progress / 100)
  
  // 实际应该调用地图API移动标记到对应位置
  console.log('移动到轨迹点:', index, points[index].position)
  
  // 如果设置了跟随轨迹，则移动地图中心
  if (followTrajectory.value) {
    // 实际应该调用地图API移动地图中心
    console.log('移动地图中心到:', points[index].position)
  }
}

// 方法 - 切换显示关键点
function toggleKeyPoints() {
  showKeyPoints.value = !showKeyPoints.value
  // 重新绘制轨迹和关键点
  drawTrajectoryOnMap()
}

// 方法 - 切换跟随轨迹
function toggleFollowTrajectory() {
  followTrajectory.value = !followTrajectory.value
}

// 方法 - 导出轨迹
function exportTrajectory() {
  if (!currentTrajectory.value) return
  
  ElMessage.success('轨迹导出功能待开发')
  // 实际应该实现导出为GeoJSON或其他格式
}

// 方法 - 定位到关键点
function focusOnKeyPoint(point: TrajectoryKeyPoint) {
  // 计算关键点对应的进度百分比
  if (!currentTrajectory.value) return
  
  const pointIndex = point.pointIndex
  const progress = (pointIndex / (currentTrajectory.value.points.length - 1)) * 100
  
  // 更新进度条和地图位置
  playbackProgress.value = progress
  currentPlaybackTime.value = point.timestamp
  updateMapPosition(progress)
  
  // 实际应该调用地图API聚焦到该关键点
  console.log('聚焦到关键点:', point.description, point.position)
}
</script>

<style scoped lang="scss">
.history-trajectory-container {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-panel {
  margin-bottom: 24px;
}

.filter-form {
  width: 100%;
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
    gap: 10px;
  }
}

.main-content {
  flex: 1;
  margin-bottom: 20px;
}

.trajectory-list-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .empty-list {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
  }
  
  // 卡片视图
  .trajectory-cards {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
    
    .trajectory-card {
      background: linear-gradient(145deg, #1a2234, #2a3349);
      border: 1px solid #3b4863;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      padding: 16px;
      margin-bottom: 16px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
        transform: translateY(-2px);
      }
      
      &.active {
        border-color: #3b82f6;
        box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
      }
      
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        .device-icon {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          
          &.drone {
            background-color: rgba(64, 158, 255, 0.2);
            color: #409eff;
          }
          
          &.robotDog {
            background-color: rgba(103, 194, 58, 0.2);
            color: #67c23a;
          }
          
          &.other {
            background-color: rgba(144, 147, 153, 0.2);
            color: #909399;
          }
        }
        
        .device-info {
          flex: 1;
          
          .device-name {
            font-weight: 600;
            color: #e5e7eb;
            margin-bottom: 4px;
            font-size: 16px;
          }
          
          .task-info {
            font-size: 12px;
            color: #9ca3af;
          }
        }
      }
      
      .card-body {
        margin-bottom: 12px;
        
        .info-item {
          display: flex;
          margin-bottom: 6px;
          
          .label {
            width: 80px;
            font-size: 13px;
            color: #9ca3af;
          }
          
          .value {
            font-size: 13px;
            color: #e5e7eb;
          }
        }
      }
      
      .card-footer {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        border-top: 1px solid rgba(59, 72, 99, 0.5);
        padding-top: 12px;
        margin-top: 4px;
      }
    }
  }
  
  // 列表视图
  .trajectory-table {
    flex: 1;
    
    .device-cell {
      display: flex;
      align-items: center;
      
      .device-icon-small {
        width: 24px;
        height: 24px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        
        &.drone {
          background-color: rgba(64, 158, 255, 0.2);
          color: #409eff;
        }
        
        &.robotDog {
          background-color: rgba(103, 194, 58, 0.2);
          color: #67c23a;
        }
        
        &.other {
          background-color: rgba(144, 147, 153, 0.2);
          color: #909399;
        }
      }
    }
    
    .table-actions {
      display: flex;
      gap: 4px;
    }
  }
  
  // 分页
  .pagination {
    margin-top: auto;
    display: flex;
    justify-content: center;
  }
}

// 地图容器
.map-container {
  height: 100%;
  position: relative;
  
  .empty-map {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
  
  .map-view {
    height: 100%;
    position: relative;
    
    .map-area {
      width: 100%;
      height: 100%;
      
      // 临时占位用，实际项目中移除
      .map-placeholder {
        background-color: rgba(31, 41, 55, 0.3);
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #9ca3af;
        font-style: italic;
        border-radius: 8px;
      }
    }
    
    // 轨迹侧边栏
    .trajectory-sidebar {
      position: absolute;
      top: 0;
      right: 0;
      width: 300px;
      height: calc(100% - 80px); // 减去播放控件高度
      background: linear-gradient(145deg, #1a2234, #2a3349);
      border-left: 1px solid #3b4863;
      z-index: 10;
      padding: 16px;
      overflow-y: auto;
      
      .trajectory-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        
        .info-section {
          flex: 1;
          
          h3 {
            font-size: 18px;
            font-weight: 600;
            color: #e5e7eb;
            margin: 0 0 8px;
          }
          
          .device-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            
            &.drone {
              background-color: #409eff;
            }
            
            &.robotDog {
              background-color: #67c23a;
            }
            
            &.other {
              background-color: #909399;
            }
          }
        }
      }
      
      .trajectory-details {
        margin-bottom: 20px;
        
        .detail-item {
          margin-bottom: 10px;
          
          .detail-label {
            font-size: 13px;
            color: #9ca3af;
            margin-bottom: 4px;
          }
          
          .detail-value {
            font-size: 14px;
            color: #e5e7eb;
            
            &.highlight {
              font-weight: 600;
              color: #3b82f6;
            }
          }
        }
      }
      
      .key-points {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #e5e7eb;
          margin-bottom: 12px;
        }
        
        .keypoint-content {
          .keypoint-desc {
            margin-bottom: 4px;
            color: #e5e7eb;
          }
        }
      }
    }
    
    // 播放控制面板
    .playback-controls {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 80px;
      background: rgba(31, 41, 55, 0.8);
      border-top: 1px solid #3b4863;
      z-index: 20;
      padding: 12px 20px;
      display: flex;
      flex-direction: column;
      
      .time-display {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 4px;
        font-size: 14px;
        
        .current-time, .total-time {
          color: #e5e7eb;
        }
        
        .time-separator {
          margin: 0 4px;
          color: #9ca3af;
        }
      }
      
      .control-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        
        .speed-control {
          display: flex;
          align-items: center;
          
          .speed-label {
            font-size: 13px;
            color: #9ca3af;
            margin-right: 8px;
          }
          
          .speed-select {
            width: 80px;
          }
        }
      }
    }
  }
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

// 响应式调整
@media (max-width: 768px) {
  .trajectory-sidebar {
    width: 100% !important;
    height: 50% !important;
    top: auto !important;
    bottom: 80px;
  }
  
  .playback-controls {
    padding: 8px 12px;
    height: 70px;
    
    .control-buttons {
      flex-wrap: wrap;
      gap: 8px;
    }
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style> 