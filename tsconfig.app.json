{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/types/patches.d.ts", "src/components.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "noImplicitAny": false, "strictNullChecks": false, "noImplicitThis": false, "allowJs": true, "skipLibCheck": true, "ignoreDeprecations": "5.0", "useUnknownInCatchVariables": false, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "paths": {"@/*": ["./src/*"]}}}