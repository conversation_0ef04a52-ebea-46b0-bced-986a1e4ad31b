<!-- 
  MultiDeviceCollaboration.vue
  多机协同工作模式模块
  管理多台设备的协同作业，提高工作效率，实现自动化分配与任务同步
-->
<template>
  <div class="multi-device-collaboration">
    <!-- 页面标题 -->
    <PageHeader
      title="多机协同工作模式"
      description="管理多台设备的协同作业，提高工作效率，实现自动化分配与任务同步"
      icon="Connection"
    >
      <template #actions>
        <div class="action-buttons">
          <el-button type="primary" @click="createCollaboration">
            <el-icon><Plus /></el-icon>
            创建协同任务
          </el-button>
          <el-button @click="switchViewMode">
            <el-icon v-if="isMapView"><View /></el-icon>
            <el-icon v-else><MapLocation /></el-icon>
            {{ isMapView ? '列表视图' : '地图视图' }}
          </el-button>
        </div>
      </template>
    </PageHeader>
    
    <!-- 列表视图 -->
    <div v-if="!isMapView" class="view-container">
      <el-row :gutter="20">
        <!-- 左侧设备列表 -->
        <el-col :span="6">
          <DataPanel title="可用设备">
            <template #actions>
              <el-badge :value="availableDevices.length" type="primary" />
            </template>
            
            <div class="device-filter">
              <el-input
                v-model="deviceSearchKeyword"
                placeholder="搜索设备"
                prefix-icon="Search"
                clearable
              />
              <el-select v-model="deviceTypeFilter" placeholder="设备类型" style="width: 100%; margin-top: 10px;">
                <el-option label="全部类型" value="" />
                <el-option label="无人机" value="drone" />
                <el-option label="机器狗" value="robot-dog" />
                <el-option label="履带机器人" value="track-robot" />
              </el-select>
            </div>
            
            <div class="device-list">
              <div 
                v-for="device in filteredDevices" 
                :key="device.id"
                class="device-item"
                :class="{ 
                  'device-selected': selectedDeviceIds.includes(device.id), 
                  'device-unavailable': device.status !== 'online' && device.status !== 'charging'
                }"
                @click="toggleDeviceSelection(device.id)"
              >
                <div class="device-avatar">
                  <el-avatar :size="40" :src="device.avatar">
                    {{ device.name.substring(0, 1) }}
                  </el-avatar>
                  <div class="device-status" :class="`status-${device.status}`"></div>
                </div>
                <div class="device-info">
                  <div class="device-name">{{ device.name }}</div>
                  <div class="device-type">{{ getDeviceTypeName(device.type) }}</div>
                </div>
                <div class="device-battery">
                  <el-progress 
                    type="circle" 
                    :percentage="device.battery" 
                    :width="36"
                    :stroke-width="4"
                    :color="getBatteryColor(device.battery)"
                  />
                </div>
              </div>
              
              <div class="empty-devices" v-if="filteredDevices.length === 0">
                <el-empty description="无匹配设备" :image-size="60" />
              </div>
            </div>
            
            <div class="selection-actions" v-if="selectedDeviceIds.length > 0">
              <el-divider>已选择 {{ selectedDeviceIds.length }} 台设备</el-divider>
              <el-button-group style="width: 100%">
                <el-button type="primary" @click="createCollaboration" style="width: 50%">
                  创建协同
                </el-button>
                <el-button type="danger" @click="clearSelection" style="width: 50%">
                  清除选择
                </el-button>
              </el-button-group>
            </div>
          </DataPanel>
        </el-col>
        
        <!-- 右侧协同任务 -->
        <el-col :span="18">
          <DataPanel title="协同任务">
            <template #title>
              <el-tabs v-model="activeTab" class="dark-tabs">
                <el-tab-pane label="进行中协同" name="active"></el-tab-pane>
                <el-tab-pane label="历史协同" name="history"></el-tab-pane>
              </el-tabs>
            </template>
            
            <!-- 进行中协同任务 -->
            <div v-if="activeTab === 'active'" class="active-collaborations">
              <div class="card-container">
                <el-empty v-if="activeCollaborations.length === 0" description="暂无进行中的协同任务" />
                
                <div 
                  v-for="collab in activeCollaborations" 
                  :key="collab.id"
                  class="collaboration-card"
                >
                  <div class="card-header">
                    <h4>{{ collab.name }}</h4>
                    <el-tag effect="dark" type="success" size="small">进行中</el-tag>
                  </div>
                  
                  <div class="card-body">
                    <div class="collab-info">
                      <div class="info-item">
                        <div class="info-label">工作区域</div>
                        <div class="info-value">{{ collab.workArea }}</div>
                      </div>
                      <div class="info-item">
                        <div class="info-label">参与设备</div>
                        <div class="info-value">
                          <el-avatar-group :size="30" :max="3">
                            <el-avatar 
                              v-for="deviceId in collab.deviceIds" 
                              :key="deviceId"
                              :src="getDeviceAvatar(deviceId)"
                            >
                              {{ getDeviceInitial(deviceId) }}
                            </el-avatar>
                          </el-avatar-group>
                        </div>
                      </div>
                      <div class="info-item">
                        <div class="info-label">工作类型</div>
                        <div class="info-value">{{ collab.taskType }}</div>
                      </div>
                      <div class="info-item">
                        <div class="info-label">开始时间</div>
                        <div class="info-value">{{ formatDateTime(collab.startTime) }}</div>
                      </div>
                    </div>
                    
                    <div class="collab-progress">
                      <div class="progress-header">
                        <span>总体进度</span>
                        <span>{{ collab.progress }}%</span>
                      </div>
                      <el-progress :percentage="collab.progress" :stroke-width="10" />
                      
                      <div class="progress-events">
                        <el-timeline>
                          <el-timeline-item
                            v-for="(event, index) in collab.recentEvents.slice(0, 3)"
                            :key="index"
                            :timestamp="formatTime(event.time)"
                            :type="getEventTypeIcon(event.type)"
                            size="small"
                            :color="getEventColor(event.type)"
                          >
                            {{ event.description }}
                          </el-timeline-item>
                        </el-timeline>
                      </div>
                    </div>
                  </div>
                  
                  <div class="card-footer">
                    <el-button size="small" @click="viewCollaborationDetail(collab.id)">
                      <el-icon><View /></el-icon>
                      查看详情
                    </el-button>
                    <el-button size="small" type="danger" @click="stopCollaboration(collab.id)">
                      <el-icon><CircleClose /></el-icon>
                      终止协同
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 历史协同任务 -->
            <div v-else class="history-collaborations">
              <el-table :data="paginatedHistoryCollaborations" border style="width: 100%" class="dark-table">
                <el-table-column prop="name" label="任务名称" min-width="180" />
                <el-table-column prop="workArea" label="工作区域" min-width="120" />
                <el-table-column prop="taskType" label="工作类型" min-width="120" />
                <el-table-column label="参与设备" min-width="150">
                  <template #default="scope">
                    <el-tag v-for="deviceId in scope.row.deviceIds.slice(0, 2)" :key="deviceId" class="device-tag">
                      {{ getDeviceName(deviceId) }}
                    </el-tag>
                    <el-tag v-if="scope.row.deviceIds.length > 2" type="info">
                      +{{ scope.row.deviceIds.length - 2 }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="开始时间" min-width="180">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.startTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="结束时间" min-width="180">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.endTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                      {{ getStatusLabel(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-button 
                      size="small" 
                      @click="viewCollaborationDetail(scope.row.id)"
                    >
                      查看详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="totalHistoryItems"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </DataPanel>
        </el-col>
      </el-row>
    </div>
    
    <!-- 地图视图 -->
    <div v-else class="map-view">
      <div class="map-container">
        <div class="map-placeholder">
          <!-- 此处应集成地图组件 -->
          <div class="map-overlay">地图组件加载中...</div>
        </div>
        
        <div class="map-sidebar">
          <DataPanel title="实时协同状态">
            <div class="active-devices">
              <div 
                v-for="(device, index) in activeDevices" 
                :key="device.id"
                class="active-device-item"
              >
                <div class="device-marker">
                  <div class="marker-dot" :style="{ backgroundColor: deviceMarkerColors[index % deviceMarkerColors.length] }"></div>
                  <div class="device-quick-info">
                    <div class="quick-info-name">{{ device.name }}</div>
                    <div class="quick-info-task">{{ device.currentTask }}</div>
                  </div>
                </div>
                
                <div class="device-realtime-data">
                  <div class="data-item">
                    <div class="data-label">速度</div>
                    <div class="data-value">{{ device.speed }} m/s</div>
                  </div>
                  <div class="data-item">
                    <div class="data-label">电量</div>
                    <div class="data-value">{{ device.battery }}%</div>
                  </div>
                  <div class="data-item">
                    <div class="data-label">状态</div>
                    <div class="data-value status-text" :class="`status-${device.status}`">
                      {{ getDeviceStatusName(device.status) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </DataPanel>
          
          <DataPanel title="协同事件" class="events-panel">
            <el-timeline>
              <el-timeline-item
                v-for="(event, index) in realtimeEvents"
                :key="index"
                :timestamp="formatTime(event.time)"
                :type="getEventTypeIcon(event.type)"
                :color="getEventColor(event.type)"
              >
                <div class="event-content">
                  <div class="event-title">{{ event.title }}</div>
                  <div class="event-description">{{ event.description }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </DataPanel>
        </div>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="工作中" />
        <StatusIndicator type="warning" label="低电量" />
        <StatusIndicator type="error" label="故障设备" />
        <StatusIndicator type="offline" label="离线设备" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime.toLocaleString()) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 创建协同任务对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建多机协同任务"
      width="650px"
      destroy-on-close
      class="dark-dialog"
      @open="onDialogOpen"
      @closed="onDialogClosed"
    >
      <el-form 
        ref="collaborationFormRef" 
        :model="collaborationForm" 
        :rules="formRules" 
        label-width="120px"
      >
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="collaborationForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-form-item label="工作区域" prop="workArea">
          <el-select v-model="collaborationForm.workArea" placeholder="请选择工作区域" style="width: 100%">
            <el-option label="北区农田" value="北区农田" />
            <el-option label="南区农田" value="南区农田" />
            <el-option label="东区农田" value="东区农田" />
            <el-option label="西区农田" value="西区农田" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="工作类型" prop="taskType">
          <el-select v-model="collaborationForm.taskType" placeholder="请选择工作类型" style="width: 100%">
            <el-option label="区域喷洒" value="区域喷洒" />
            <el-option label="定向消杀" value="定向消杀" />
            <el-option label="巡检监测" value="巡检监测" />
            <el-option label="环境采样" value="环境采样" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="参与设备" prop="deviceIds">
          <el-select 
            v-model="collaborationForm.deviceIds" 
            multiple 
            placeholder="请选择参与设备" 
            style="width: 100%"
            :disabled="selectedDeviceIds.length > 0"
          >
            <el-option 
              v-for="device in selectableDevices" 
              :key="device.id" 
              :label="device.name" 
              :value="device.id" 
            />
          </el-select>
          
          <div v-if="selectedDeviceIds.length > 0" class="selected-devices-info">
            已从列表选择 {{ selectedDeviceIds.length }} 台设备
            <el-button type="text" @click="modifySelection">修改选择</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input 
            v-model="collaborationForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入任务描述"
          />
        </el-form-item>
        
        <el-form-item label="协同策略" prop="strategy">
          <el-radio-group v-model="collaborationForm.strategy">
            <el-radio label="parallel">并行作业</el-radio>
            <el-radio label="sequential">顺序作业</el-radio>
            <el-radio label="mixed">混合模式</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="预计工作时间" prop="estimatedTime">
          <el-input-number 
            v-model="collaborationForm.estimatedTime" 
            :min="1" 
            :max="480" 
            style="width: 100%"
            controls-position="right"
          />
          <div class="form-tips">单位：分钟</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitCollaborationForm" :loading="submitting">
          创建任务
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 协同详情抽屉 -->
    <el-drawer
      v-model="showDetailDrawer"
      title="协同任务详情"
      direction="rtl"
      size="50%"
      class="dark-drawer"
    >
      <div v-if="currentCollaboration" class="collaboration-detail">
        <div class="detail-header">
          <h2>{{ currentCollaboration.name }}</h2>
          <el-tag :type="getStatusType(currentCollaboration.status)" effect="dark">
            {{ getStatusLabel(currentCollaboration.status) }}
          </el-tag>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作区域">{{ currentCollaboration.workArea }}</el-descriptions-item>
          <el-descriptions-item label="工作类型">{{ currentCollaboration.taskType }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDateTime(currentCollaboration.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ currentCollaboration.endTime ? formatDateTime(currentCollaboration.endTime) : '进行中' }}
          </el-descriptions-item>
          <el-descriptions-item label="协同策略">{{ getStrategyLabel(currentCollaboration.strategy) }}</el-descriptions-item>
          <el-descriptions-item label="任务进度">
            <el-progress :percentage="currentCollaboration.progress" />
          </el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">
            {{ currentCollaboration.description }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-section">
          <h3>参与设备</h3>
          <el-table :data="currentCollaborationDevices" border style="width: 100%" class="dark-table">
            <el-table-column prop="name" label="设备名称" />
            <el-table-column prop="type" label="设备类型">
              <template #default="scope">
                {{ getDeviceTypeName(scope.row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="当前状态">
              <template #default="scope">
                <el-tag :type="getDeviceStatusType(scope.row.status)">
                  {{ getDeviceStatusName(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="battery" label="电量">
              <template #default="scope">
                <el-progress 
                  :percentage="scope.row.battery" 
                  :color="getBatteryColor(scope.row.battery)"
                  :stroke-width="12"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div class="detail-section">
          <h3>协同事件记录</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(event, index) in currentCollaboration.events"
              :key="index"
              :timestamp="formatDateTime(event.time)"
              :type="getEventTypeIcon(event.type)"
              :color="getEventColor(event.type)"
            >
              <div class="event-content">
                <div class="event-title">{{ event.title }}</div>
                <div class="event-description">{{ event.description }}</div>
                <div class="event-device" v-if="event.deviceId">
                  设备：{{ getDeviceName(event.deviceId) }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { Plus, MapLocation, View, CircleClose, Refresh, Search, Connection } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { CollaborationEvent, DeviceInfo, CollaborationTask } from '@/types/taskScheduling'

// 自定义类型声明，解决类型错误
interface DevicePosition {
  x: number;
  y: number;
  z?: number;
}

interface DeviceInfoType {
  id: string;
  name: string;
  type: 'drone' | 'robotDog' | 'other';
  status: 'online' | 'offline' | 'busy' | 'charging' | 'error';
  battery: number;
  position: DevicePosition;
  capabilities: string[];
  avatar?: string;
  errorMessage?: string;
}

interface CollaborationEventType {
  id: string;
  taskId: string;
  type: string;
  time: string;
  title: string;
  description: string;
  deviceId: string;
  timestamp: number;
  eventType: string;
  coordinates: { lat: number; lng: number };
}

interface CollaborationTaskType {
  id: string;
  name: string;
  description: string;
  workArea: string;
  taskType: string;
  deviceIds: string[];
  strategy: string;
  status: string;
  progress: number;
  startTime: string;
  endTime: string | null;
  estimatedTime: number;
  recentEvents: {
    type: string;
    time: string;
    description: string;
  }[];
  events: CollaborationEventType[];
}

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue'
import DataPanel from '../DeviceManagement/components/DataPanel.vue'
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue'

// 状态数据
const isMapView = ref(false)
const activeTab = ref('active')
const deviceSearchKeyword = ref('')
const deviceTypeFilter = ref('')
const selectedDeviceIds = ref<string[]>([])
const showCreateDialog = ref(false)
const showDetailDrawer = ref(false)
const submitting = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const deviceMarkerColors = ['#f56c6c', '#409eff', '#67c23a', '#e6a23c', '#909399']
const lastUpdateTime = ref(new Date())

// 表单数据
const collaborationForm = reactive({
  name: '',
  workArea: '',
  taskType: '',
  deviceIds: [] as string[],
  description: '',
  strategy: 'parallel',
  estimatedTime: 60
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  workArea: [
    { required: true, message: '请选择工作区域', trigger: 'change' }
  ],
  taskType: [
    { required: true, message: '请选择工作类型', trigger: 'change' }
  ],
  deviceIds: [
    { required: true, message: '请选择参与设备', trigger: 'change' },
    { type: 'array', min: 1, message: '至少需要1台设备进行任务', trigger: 'change' }
  ],
  strategy: [
    { required: true, message: '请选择协同策略', trigger: 'change' }
  ],
  estimatedTime: [
    { required: true, message: '请输入预计工作时间', trigger: 'blur' },
    { type: 'number', min: 1, message: '工作时间必须大于0', trigger: 'blur' }
  ]
}

// 表单引用
const collaborationFormRef = ref<any>(null)

// 模拟数据 - 设备列表
const availableDevices = ref<DeviceInfoType[]>([
  {
    id: 'device-001',
    name: '无人机-01',
    type: 'drone',
    status: 'online',
    battery: 85,
    avatar: '',
    position: { x: 120, y: 150, z: 30 },
    capabilities: ['spray', 'monitor']
  },
  {
    id: 'device-002',
    name: '无人机-02',
    type: 'drone',
    status: 'busy',
    battery: 65,
    avatar: '',
    position: { x: 220, y: 180, z: 25 },
    capabilities: ['spray', 'monitor']
  },
  {
    id: 'device-003',
    name: '机器狗-01',
    type: 'robotDog',
    status: 'online',
    battery: 92,
    avatar: '',
    position: { x: 150, y: 130, z: 0 },
    capabilities: ['spray', 'patrol', 'sample']
  },
  {
    id: 'device-004',
    name: '机器狗-02',
    type: 'robotDog',
    status: 'charging',
    battery: 40,
    avatar: '',
    position: { x: 130, y: 160, z: 0 },
    capabilities: ['spray', 'patrol', 'sample']
  },
  {
    id: 'device-005',
    name: '履带机器人-01',
    type: 'other',
    status: 'online',
    battery: 78,
    avatar: '',
    position: { x: 180, y: 140, z: 0 },
    capabilities: ['spray', 'heavy-load']
  }
])

// 模拟数据 - 活动设备
const activeDevices = ref([
  {
    id: 'device-002',
    name: '无人机-02',
    type: 'drone',
    status: 'busy',
    battery: 65,
    speed: 3.2,
    position: { x: 220, y: 180, z: 25 },
    currentTask: '南区农田区域喷洒'
  }
])

// 模拟数据 - 实时事件
const realtimeEvents = ref<CollaborationEventType[]>([
  {
    id: 'event-001',
    taskId: 'collab-001',
    type: 'info',
    time: new Date().toISOString(),
    title: '协同任务开始',
    description: '南区农田区域喷洒任务开始执行',
    deviceId: '',
    timestamp: Date.now(),
    eventType: 'start',
    coordinates: { lat: 0, lng: 0 }
  },
  {
    id: 'event-002',
    taskId: 'collab-001',
    type: 'warning',
    time: new Date(Date.now() - 5 * 60000).toISOString(),
    title: '设备电量警告',
    description: '无人机-02电量低于70%',
    deviceId: 'device-002',
    timestamp: Date.now() - 5 * 60000,
    eventType: 'warning',
    coordinates: { lat: 0, lng: 0 }
  },
  {
    id: 'event-003',
    taskId: 'collab-001',
    type: 'success',
    time: new Date(Date.now() - 10 * 60000).toISOString(),
    title: '区域完成',
    description: '完成南区A3区块的喷洒任务',
    deviceId: 'device-002',
    timestamp: Date.now() - 10 * 60000,
    eventType: 'complete',
    coordinates: { lat: 0, lng: 0 }
  }
])

// 模拟数据 - 协同任务
const activeCollaborations = ref<CollaborationTaskType[]>([
  {
    id: 'collab-001',
    name: '南区农田协同喷洒',
    description: '对南区农田进行协同区域喷洒作业',
    workArea: '南区农田',
    taskType: '区域喷洒',
    deviceIds: ['device-002'],
    strategy: 'parallel',
    status: 'running',
    progress: 65,
    startTime: new Date(Date.now() - 30 * 60000).toISOString(),
    endTime: null,
    estimatedTime: 120,
    recentEvents: [
      {
        type: 'success',
        time: new Date(Date.now() - 10 * 60000).toISOString(),
        description: '完成南区A3区块的喷洒任务'
      },
      {
        type: 'warning',
        time: new Date(Date.now() - 15 * 60000).toISOString(),
        description: '无人机-02电量低于70%'
      },
      {
        type: 'info',
        time: new Date(Date.now() - 20 * 60000).toISOString(),
        description: '开始南区A3区块的喷洒任务'
      }
    ],
    events: []
  }
])

// 模拟数据 - 历史协同
const historyCollaborations = ref<CollaborationTaskType[]>([
  {
    id: 'collab-h001',
    name: '东区农田协同巡检',
    description: '对东区农田进行协同巡检监测作业',
    workArea: '东区农田',
    taskType: '巡检监测',
    deviceIds: ['device-001', 'device-003'],
    strategy: 'parallel',
    status: 'completed',
    progress: 100,
    startTime: new Date(Date.now() - 24 * 60000 * 60).toISOString(),
    endTime: new Date(Date.now() - 23 * 60000 * 60).toISOString(),
    estimatedTime: 60,
    recentEvents: [],
    events: []
  },
  {
    id: 'collab-h002',
    name: '北区农田协同采样',
    description: '对北区农田进行环境协同采样作业',
    workArea: '北区农田',
    taskType: '环境采样',
    deviceIds: ['device-003', 'device-004', 'device-005'],
    strategy: 'sequential',
    status: 'completed',
    progress: 100,
    startTime: new Date(Date.now() - 48 * 60000 * 60).toISOString(),
    endTime: new Date(Date.now() - 46 * 60000 * 60).toISOString(),
    estimatedTime: 90,
    recentEvents: [],
    events: []
  }
])

// 当前选中的协同任务
const currentCollaboration = ref<CollaborationTaskType | null>(null)
const currentCollaborationDevices = ref<DeviceInfoType[]>([])

// 计算属性 - 过滤后的设备列表
const filteredDevices = computed(() => {
  return availableDevices.value.filter(device => {
    // 根据搜索关键词过滤
    const matchesKeyword = device.name.toLowerCase().includes(deviceSearchKeyword.value.toLowerCase())
    
    // 根据设备类型过滤
    let matchesType = true
    if (deviceTypeFilter.value) {
      if (deviceTypeFilter.value === 'drone' && device.type === 'drone') {
        matchesType = true
      } else if (deviceTypeFilter.value === 'robot-dog' && device.type === 'robotDog') {
        matchesType = true
      } else if (deviceTypeFilter.value === 'track-robot' && device.type === 'other') {
        matchesType = true
      } else {
        matchesType = false
      }
    }
    
    return matchesKeyword && matchesType
  })
})

// 计算属性 - 分页后的历史协同任务
const paginatedHistoryCollaborations = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return historyCollaborations.value.slice(start, end)
})

// 计算属性 - 历史协同任务总数
const totalHistoryItems = computed(() => {
  return historyCollaborations.value.length
})

// 计算属性 - 可用于选择的设备列表
const selectableDevices = computed(() => {
  return availableDevices.value.filter(device => 
    device.status === 'online' || device.status === 'charging'
  )
})

// 生命周期钩子
onMounted(() => {
  // 启动数据自动更新
  dataUpdateInterval = window.setInterval(() => {
    updateDataWithSmallVariations()
    lastUpdateTime.value = new Date()
  }, 30000) // 每30秒更新一次
})

onUnmounted(() => {
  // 清除计时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval)
  }
})

// 数据更新计时器
let dataUpdateInterval: number | null = null

// 更新数据（小幅度变化）
const updateDataWithSmallVariations = () => {
  // 更新设备电量和状态
  availableDevices.value.forEach(device => {
    // 随机小幅度改变电量
    device.battery = Math.max(0, Math.min(100, device.battery + (Math.random() * 6 - 3)))
  })
  
  // 更新活动协同任务进度
  activeCollaborations.value.forEach(collab => {
    if (collab.status === 'running') {
      collab.progress = Math.min(100, collab.progress + Math.random() * 2)
      if (collab.progress >= 100) {
        collab.status = 'completed'
        collab.endTime = new Date().toISOString()
      }
    }
  })
}

// 方法 - 切换视图模式
const switchViewMode = () => {
  isMapView.value = !isMapView.value
}

// 方法 - 创建协同任务
const createCollaboration = () => {
  // 先重置表单，避免之前的数据残留
  Object.assign(collaborationForm, {
    name: '',
    workArea: '',
    taskType: '',
    deviceIds: [],
    description: '',
    strategy: 'parallel',
    estimatedTime: 60
  })
  
  // 如果已经选择了设备，则将选择的设备ID设置到表单中
  if (selectedDeviceIds.value.length > 0) {
    collaborationForm.deviceIds = [...selectedDeviceIds.value]
  }
  
  showCreateDialog.value = true
}

// 方法 - 切换设备选择
const toggleDeviceSelection = (deviceId: string) => {
  // 获取设备信息
  const device = availableDevices.value.find(d => d.id === deviceId)
  
  // 如果设备不存在或不可用（非空闲、非充电状态），则不允许选择
  if (!device || (device.status !== 'online' && device.status !== 'charging')) {
    ElMessage.warning(`设备"${device?.name || deviceId}"当前不可用于协同任务`)
    return
  }
  
  const index = selectedDeviceIds.value.indexOf(deviceId)
  
  if (index === -1) {
    // 选中设备
    selectedDeviceIds.value.push(deviceId)
  } else {
    // 取消选中
    selectedDeviceIds.value.splice(index, 1)
  }
}

// 方法 - 清空设备选择
const clearSelection = () => {
  selectedDeviceIds.value = []
}

// 方法 - 修改设备选择
const modifySelection = () => {
  // 清空表单中的设备选择
  collaborationForm.deviceIds = []
  // 清空从列表中选择的设备
  selectedDeviceIds.value = []
  // 保持对话框打开，让用户可以重新选择
}

// 方法 - 提交协同任务表单
const submitCollaborationForm = async () => {
  if (!collaborationFormRef.value) return
  
  try {
    await collaborationFormRef.value.validate()
    
    submitting.value = true
    
    // 在实际项目中调用API
    // const result = await createCollaborationTask(collaborationForm)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 创建新的协同任务对象
    const newCollaboration: CollaborationTaskType = {
      id: `collab-${Date.now()}`,
      name: collaborationForm.name,
      description: collaborationForm.description,
      workArea: collaborationForm.workArea,
      taskType: collaborationForm.taskType,
      deviceIds: collaborationForm.deviceIds,
      strategy: collaborationForm.strategy,
      status: 'running',
      progress: 0,
      startTime: new Date().toISOString(),
      endTime: null,
      estimatedTime: collaborationForm.estimatedTime,
      recentEvents: [
        {
          type: 'info',
          time: new Date().toISOString(),
          description: '协同任务开始执行'
        }
      ],
      events: []
    }
    
    // 添加到活动协同任务列表
    activeCollaborations.value.unshift(newCollaboration)
    
    // 更新设备状态
    for (const deviceId of collaborationForm.deviceIds) {
      const device = availableDevices.value.find(d => d.id === deviceId)
      if (device) {
        device.status = 'busy'
      }
    }
    
    // 清空表单
    Object.assign(collaborationForm, {
      name: '',
      workArea: '',
      taskType: '',
      deviceIds: [],
      description: '',
      strategy: 'parallel',
      estimatedTime: 60
    })
    
    // 清空选择的设备
    selectedDeviceIds.value = []
    
    showCreateDialog.value = false
    ElMessage.success('协同任务创建成功')
  } catch (error) {
    console.error('创建协同任务失败:', error)
    ElMessage.error('表单验证失败，请检查输入')
  } finally {
    submitting.value = false
  }
}

// 方法 - 停止协同任务
const stopCollaboration = (id: string) => {
  ElMessageBox.confirm(
    '确认要终止此协同任务吗？',
    '终止协同任务',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 在实际项目中调用API
      // await stopCollaborationTask(id)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 更新协同任务状态
      const collaboration = activeCollaborations.value.find(c => c.id === id)
      if (collaboration) {
        collaboration.status = 'terminated'
        collaboration.endTime = new Date().toISOString()
        
        // 从活动列表移到历史列表
        historyCollaborations.value.unshift({
          ...collaboration
        })
        activeCollaborations.value = activeCollaborations.value.filter(c => c.id !== id)
        
        // 释放设备状态
        for (const deviceId of collaboration.deviceIds) {
          const device = availableDevices.value.find(d => d.id === deviceId)
          if (device) {
            device.status = 'online'
          }
        }
      }
      
      ElMessage.success('协同任务已终止')
    } catch (error) {
      console.error('终止协同任务失败:', error)
      ElMessage.error('终止协同任务失败，请重试')
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 方法 - 查看协同任务详情
const viewCollaborationDetail = (id: string) => {
  // 查找任务
  const collaboration = [...activeCollaborations.value, ...historyCollaborations.value].find(c => c.id === id)
  
  if (collaboration) {
    currentCollaboration.value = collaboration
    
    // 获取参与的设备信息
    currentCollaborationDevices.value = availableDevices.value.filter(
      device => collaboration.deviceIds.includes(device.id)
    )
    
    showDetailDrawer.value = true
  }
}

// 方法 - 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  // 在实际项目中，这里应该重新加载数据
}

// 方法 - 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  // 在实际项目中，这里应该重新加载数据
}

// 方法 - 获取电量颜色
const getBatteryColor = (battery: number) => {
  if (battery <= 20) return '#f56c6c'
  if (battery <= 50) return '#e6a23c'
  return '#67c23a'
}

// 方法 - 获取设备类型名称
const getDeviceTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'drone': '无人机',
    'robotDog': '机器狗',
    'other': '履带机器人'
  }
  return typeMap[type] || '未知类型'
}

// 方法 - 获取设备状态名称
const getDeviceStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    'online': '空闲',
    'busy': '工作中',
    'charging': '充电中',
    'offline': '离线',
    'error': '故障'
  }
  return statusMap[status] || '未知状态'
}

// 方法 - 获取设备状态颜色
const getDeviceStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'online': 'info',
    'busy': 'success',
    'charging': 'warning',
    'offline': 'info',
    'error': 'danger'
  }
  return statusMap[status] || 'info'
}

// 方法 - 获取事件图标类型
const getEventTypeIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'info': 'info',
    'success': 'success',
    'warning': 'warning',
    'error': 'danger'
  }
  return iconMap[type] || 'info'
}

// 方法 - 获取事件颜色
const getEventColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'info': '#909399',
    'success': '#67c23a',
    'warning': '#e6a23c',
    'error': '#f56c6c'
  }
  return colorMap[type] || '#909399'
}

// 方法 - 获取协同策略标签
const getStrategyLabel = (strategy: string) => {
  const strategyMap: Record<string, string> = {
    'parallel': '并行作业',
    'sequential': '顺序作业',
    'mixed': '混合模式'
  }
  return strategyMap[strategy] || '未知策略'
}

// 方法 - 获取任务状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '等待中',
    'running': '进行中',
    'completed': '已完成',
    'terminated': '已终止',
    'failed': '失败'
  }
  return statusMap[status] || '未知状态'
}

// 方法 - 获取任务状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'info',
    'running': 'success',
    'completed': '',
    'terminated': 'warning',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

// 方法 - 格式化日期时间
const formatDateTime = (dateString: string | null) => {
  if (!dateString) return '未开始'
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 方法 - 格式化时间
const formatTime = (dateString: string | Date) => {
  const date = dateString instanceof Date ? dateString : new Date(dateString);
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
}

// 方法 - 获取设备名称（安全获取，避免空引用）
const getDeviceName = (deviceId: string) => {
  const device = availableDevices.value.find(device => device.id === deviceId)
  return device ? device.name : '未知设备'
}

// 方法 - 获取设备头像（安全获取，避免空引用）
const getDeviceAvatar = (deviceId: string) => {
  const device = availableDevices.value.find(device => device.id === deviceId)
  return device?.avatar || ''
}

// 方法 - 获取设备名称首字母（安全获取，避免空引用）
const getDeviceInitial = (deviceId: string) => {
  const device = availableDevices.value.find(device => device.id === deviceId)
  return device ? device.name.substring(0, 1) : '?'
}

// 刷新数据
const refreshData = () => {
  updateDataWithSmallVariations()
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
}

// 方法 - 对话框打开时
const onDialogOpen = () => {
  // 确保collaborationForm.deviceIds与selectedDeviceIds同步
  if (selectedDeviceIds.value.length > 0) {
    collaborationForm.deviceIds = [...selectedDeviceIds.value]
  }
}

// 方法 - 对话框关闭时
const onDialogClosed = () => {
  // 如果没有提交表单，重置表单数据
  if (submitting.value === false) {
    Object.assign(collaborationForm, {
      name: '',
      workArea: '',
      taskType: '',
      deviceIds: [],
      description: '',
      strategy: 'parallel',
      estimatedTime: 60
    })
  }
}
</script>

<style scoped>
.multi-device-collaboration {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

/* 设备过滤区域 */
.device-filter {
  margin-bottom: 15px;
}

/* 设备列表 */
.device-list {
  max-height: 500px;
  overflow-y: auto;
  margin-bottom: 15px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  background-color: rgba(31, 41, 55, 0.3);
  cursor: pointer;
  transition: all 0.2s;
}

.device-item:hover {
  background-color: rgba(31, 41, 55, 0.5);
}

.device-item.device-selected {
  background-color: rgba(59, 130, 246, 0.3);
  border: 1px solid #3b82f6;
}

.device-item.device-unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.device-item.device-unavailable:hover {
  background-color: rgba(31, 41, 55, 0.3);
}

.device-avatar {
  position: relative;
  margin-right: 12px;
}

.device-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #1f2937;
}

.device-status.status-online {
  background-color: #10b981;
}

.device-status.status-busy {
  background-color: #3b82f6;
}

.device-status.status-charging {
  background-color: #f59e0b;
}

.device-status.status-offline {
  background-color: #9ca3af;
}

.device-status.status-error {
  background-color: #ef4444;
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: bold;
  color: #e5e7eb;
  margin-bottom: 4px;
}

.device-type {
  font-size: 12px;
  color: #9ca3af;
}

.empty-devices {
  padding: 40px 0;
  display: flex;
  justify-content: center;
}

/* 协同任务卡片 */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 10px;
}

.collaboration-card {
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(31, 41, 55, 0.3);
  border: 1px solid rgba(75, 85, 99, 0.5);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: rgba(31, 41, 55, 0.5);
}

.card-header h4 {
  margin: 0;
  font-weight: bold;
  color: #e5e7eb;
}

.card-body {
  padding: 16px;
}

.collab-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.info-item .info-label {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 4px;
}

.info-item .info-value {
  font-weight: 500;
  color: #e5e7eb;
}

.collab-progress {
  margin-top: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #e5e7eb;
}

.progress-events {
  margin-top: 16px;
  font-size: 13px;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: rgba(31, 41, 55, 0.5);
  gap: 8px;
}

/* 历史协同表格 */
.history-collaborations {
  margin-top: 10px;
}

.dark-table {
  --el-table-border-color: #4b5563;
  --el-table-bg-color: transparent;
  --el-table-header-bg-color: rgba(31, 41, 55, 0.7);
  --el-table-tr-bg-color: rgba(31, 41, 55, 0.3);
  --el-table-hover-bg-color: rgba(31, 41, 55, 0.5);
  --el-table-text-color: #e5e7eb;
  --el-table-header-text-color: #e5e7eb;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 地图视图 */
.map-view {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.map-container {
  display: flex;
  height: 75vh;
  gap: 20px;
}

.map-placeholder {
  flex: 1;
  position: relative;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.map-overlay {
  font-size: 24px;
  color: #9ca3af;
}

.map-sidebar {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.active-device-item {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.device-marker {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.marker-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.device-quick-info .quick-info-name {
  font-weight: bold;
  color: #e5e7eb;
}

.device-quick-info .quick-info-task {
  font-size: 12px;
  color: #9ca3af;
}

.device-realtime-data {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.data-item .data-label {
  font-size: 12px;
  color: #9ca3af;
}

.data-item .data-value {
  font-weight: 500;
  color: #e5e7eb;
}

.data-value.status-text.status-busy {
  color: #3b82f6;
}

.data-value.status-text.status-charging {
  color: #f59e0b;
}

.data-value.status-text.status-error {
  color: #ef4444;
}

.events-panel {
  flex: 1;
  overflow-y: auto;
}

.event-content .event-title {
  font-weight: bold;
  margin-bottom: 4px;
  color: #e5e7eb;
}

.event-content .event-description {
  font-size: 13px;
  color: #9ca3af;
}

.event-content .event-device {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: 20px;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 对话框和抽屉自定义样式 */
:deep(.dark-dialog) {
  --el-dialog-bg-color: #1f2937;
  --el-dialog-text-color: #e5e7eb;
  --el-dialog-border-color: #4b5563;
}

:deep(.dark-dialog .el-form-item__label) {
  color: #e5e7eb;
}

:deep(.dark-dialog .el-input__inner),
:deep(.dark-dialog .el-textarea__inner) {
  background-color: rgba(31, 41, 55, 0.7);
  border-color: #4b5563;
  color: #e5e7eb;
}

:deep(.dark-drawer) {
  --el-drawer-bg-color: #1f2937;
  --el-drawer-text-color: #e5e7eb;
}

.collaboration-detail .detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.collaboration-detail .detail-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
  color: #e5e7eb;
}

.collaboration-detail .detail-section {
  margin-top: 24px;
}

.collaboration-detail .detail-section h3 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #e5e7eb;
}

:deep(.el-descriptions) {
  --el-descriptions-item-bordered-label-background: rgba(31, 41, 55, 0.7);
  --el-descriptions-border-color: #4b5563;
  --el-descriptions-text-color: #e5e7eb;
}

.selected-devices-info {
  margin-top: 8px;
  font-size: 12px;
  color: #9ca3af;
}

.form-tips {
  margin-top: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.dark-tabs {
  --el-tabs-header-bg-color: transparent;
  --el-tabs-border-color: #4b5563;
  --el-tabs-text-color: #9ca3af;
  --el-tabs-active-text-color: #3b82f6;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card-container {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .map-container {
    flex-direction: column;
  }
  
  .map-sidebar {
    width: 100%;
  }
}
</style>

<!--
注意: 此组件需要以下组件:
- PageHeader.vue
- DataPanel.vue
- StatusIndicator.vue

请确保这些组件存在于components目录下。
--> 