<!-- 
  DataPanel.vue
  通用数据面板组件，用于展示农药管理系统的各种数据
  支持标题、图表、数据列表等
-->
<template>
  <div class="data-panel" :class="{ 'is-dark': dark }">
    <div class="panel-header">
      <h3 class="panel-title">{{ title }}</h3>
      <div class="panel-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <div class="panel-content">
      <slot></slot>
    </div>
    
    <div class="panel-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true
  },
  dark: {
    type: Boolean,
    default: true
  }
});
</script>

<style scoped>
.data-panel {
  background: linear-gradient(145deg, #ffffff, #f3f4f6);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.data-panel.is-dark {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  border: 1px solid #3b4863;
}

.data-panel:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.data-panel.is-dark:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.panel-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.data-panel.is-dark .panel-header {
  border-bottom: 1px solid #3b4863;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.data-panel.is-dark .panel-title {
  color: #e5e7eb;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.panel-content {
  padding: 20px;
  flex: 1;
  overflow: auto;
}

.panel-footer {
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.data-panel.is-dark .panel-footer {
  border-top: 1px solid #3b4863;
  background-color: rgba(31, 41, 55, 0.7);
}
</style> 