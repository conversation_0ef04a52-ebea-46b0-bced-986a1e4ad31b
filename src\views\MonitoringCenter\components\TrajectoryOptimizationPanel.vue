<template>
  <div class="trajectory-optimization-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <div class="header-content">
        <el-icon class="header-icon">
          <TrendCharts />
        </el-icon>
        <h3 class="panel-title">轨迹优化控制</h3>
      </div>
      <el-tooltip content="轨迹数据平滑处理，减少定位偏移" placement="top">
        <el-icon class="help-icon">
          <QuestionFilled />
        </el-icon>
      </el-tooltip>
    </div>

    <!-- 主要控制区域 -->
    <div class="control-section">
      <!-- 平滑功能开关 -->
      <div class="control-item">
        <div class="control-label">
          <span class="label-text">轨迹平滑</span>
          <el-tag
            :type="smoothingEnabled ? 'success' : 'info'"
            size="small"
            class="status-tag"
          >
            {{ smoothingEnabled ? '已启用' : '已禁用' }}
          </el-tag>
        </div>
        <el-switch
          v-model="smoothingEnabled"
          @change="handleSmoothingToggle"
          :active-color="'#22c55e'"
          :inactive-color="'#6b7280'"
          size="large"
        />
      </div>

      <!-- 平滑参数控制 -->
      <div v-if="smoothingEnabled" class="parameters-section">
        <el-divider content-position="left">
          <span class="divider-text">平滑参数</span>
        </el-divider>

        <!-- 平滑强度 -->
        <div class="control-item">
          <div class="control-label">
            <span class="label-text">平滑强度</span>
            <span class="value-display">{{ Math.round(smoothingStrength * 100) }}%</span>
          </div>
          <el-slider
            v-model="smoothingStrength"
            :min="0.1"
            :max="1.0"
            :step="0.1"
            @change="handleConfigChange"
            :show-tooltip="false"
            class="parameter-slider"
          />
        </div>

        <!-- 移动平均窗口 -->
        <div class="control-item">
          <div class="control-label">
            <span class="label-text">平均窗口</span>
            <span class="value-display">{{ movingAverageWindow }}</span>
          </div>
          <el-slider
            v-model="movingAverageWindow"
            :min="3"
            :max="15"
            :step="1"
            @change="handleConfigChange"
            :show-tooltip="false"
            class="parameter-slider"
          />
        </div>

        <!-- 异常检测阈值 -->
        <div class="control-item">
          <div class="control-label">
            <span class="label-text">异常检测</span>
            <span class="value-display">{{ outlierThreshold.toFixed(1) }}σ</span>
          </div>
          <el-slider
            v-model="outlierThreshold"
            :min="1.0"
            :max="3.0"
            :step="0.1"
            @change="handleConfigChange"
            :show-tooltip="false"
            class="parameter-slider"
          />
        </div>

        <!-- 自适应窗口开关 -->
        <div class="control-item">
          <div class="control-label">
            <span class="label-text">自适应窗口</span>
            <el-tooltip content="根据设备速度自动调整平滑窗口大小" placement="top">
              <el-icon class="help-icon-small">
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </div>
          <el-switch
            v-model="adaptiveWindow"
            @change="handleConfigChange"
            size="small"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          size="small"
          @click="resetToDefaults"
          :icon="RefreshRight"
        >
          重置默认
        </el-button>
        <el-button
          type="success"
          size="small"
          @click="applyOptimization"
          :icon="Check"
          :disabled="!smoothingEnabled"
        >
          应用优化
        </el-button>
      </div>

      <!-- 效果预览 -->
      <div v-if="smoothingEnabled" class="preview-section">
        <el-divider content-position="left">
          <span class="divider-text">效果预览</span>
        </el-divider>

        <div class="preview-stats">
          <div class="stat-item">
            <span class="stat-label">原始点数</span>
            <span class="stat-value">{{ originalPointsCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">平滑点数</span>
            <span class="stat-value">{{ smoothedPointsCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">优化率</span>
            <span class="stat-value success">
              {{ optimizationRate }}%
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElNotification } from 'element-plus';
import {
  TrendCharts,
  QuestionFilled,
  InfoFilled,
  RefreshRight,
  Check
} from '@element-plus/icons-vue';
import type { SmoothingConfig } from '@/utils/trajectorySmoothing';
import { DEVICE_TRACKING_CONFIG } from '@/config/business';

// Props定义
interface Props {
  smoothingEnabled: boolean;
  smoothingConfig: SmoothingConfig;
  originalPointsCount?: number;
  smoothedPointsCount?: number;
}

// Emits定义
interface Emits {
  (e: 'toggle-smoothing', enabled: boolean): void;
  (e: 'update-config', config: Partial<SmoothingConfig>): void;
  (e: 'apply-optimization'): void;
}

const props = withDefaults(defineProps<Props>(), {
  originalPointsCount: 0,
  smoothedPointsCount: 0
});

const emit = defineEmits<Emits>();

// 响应式数据
const smoothingEnabled = ref(props.smoothingEnabled);
const smoothingStrength = ref(props.smoothingConfig.smoothingStrength);
const movingAverageWindow = ref(props.smoothingConfig.movingAverageWindow);
const outlierThreshold = ref(props.smoothingConfig.outlierThreshold);
const adaptiveWindow = ref(props.smoothingConfig.adaptiveWindow);

// 计算属性
const optimizationRate = computed(() => {
  if (props.originalPointsCount === 0) return 0;
  const reduction = Math.abs(props.originalPointsCount - props.smoothedPointsCount);
  return Math.round((reduction / props.originalPointsCount) * 100);
});

// 监听props变化
watch(() => props.smoothingEnabled, (newVal) => {
  smoothingEnabled.value = newVal;
});

watch(() => props.smoothingConfig, (newConfig) => {
  smoothingStrength.value = newConfig.smoothingStrength;
  movingAverageWindow.value = newConfig.movingAverageWindow;
  outlierThreshold.value = newConfig.outlierThreshold;
  adaptiveWindow.value = newConfig.adaptiveWindow;
}, { deep: true });

// 事件处理函数
const handleSmoothingToggle = (enabled: boolean) => {
  emit('toggle-smoothing', enabled);

  ElNotification({
    title: enabled ? '轨迹平滑已启用' : '轨迹平滑已禁用',
    message: enabled ? '设备轨迹将进行平滑处理' : '将显示原始轨迹数据',
    type: enabled ? 'success' : 'info',
    duration: 3000
  });
};

const handleConfigChange = () => {
  const newConfig: Partial<SmoothingConfig> = {
    smoothingStrength: smoothingStrength.value,
    movingAverageWindow: movingAverageWindow.value,
    outlierThreshold: outlierThreshold.value,
    adaptiveWindow: adaptiveWindow.value
  };

  emit('update-config', newConfig);
};

const resetToDefaults = () => {
  const defaultConfig = DEVICE_TRACKING_CONFIG.TRAJECTORY_SMOOTHING;

  smoothingStrength.value = defaultConfig.SMOOTHING_STRENGTH;
  movingAverageWindow.value = defaultConfig.MOVING_AVERAGE_WINDOW;
  outlierThreshold.value = defaultConfig.OUTLIER_THRESHOLD;
  adaptiveWindow.value = defaultConfig.ADAPTIVE_WINDOW;

  handleConfigChange();

  ElNotification({
    title: '参数已重置',
    message: '所有平滑参数已恢复为默认值',
    type: 'success',
    duration: 2000
  });
};

const applyOptimization = () => {
  emit('apply-optimization');

  ElNotification({
    title: '优化已应用',
    message: '轨迹平滑优化设置已生效',
    type: 'success',
    duration: 2000
  });
};
</script>

<style scoped lang="scss">
.trajectory-optimization-panel {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #374151;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  color: #f9fafb;
  min-width: 320px;
  max-width: 400px;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #374151;

    .header-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .header-icon {
        color: #22c55e;
        font-size: 20px;
      }

      .panel-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #f9fafb;
      }
    }

    .help-icon {
      color: #9ca3af;
      cursor: help;
      transition: color 0.2s;

      &:hover {
        color: #22c55e;
      }
    }
  }

  .control-section {
    .control-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .control-label {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .label-text {
          font-size: 14px;
          color: #e5e7eb;
          font-weight: 500;
        }

        .value-display {
          font-size: 12px;
          color: #22c55e;
          font-weight: 600;
          background: rgba(34, 197, 94, 0.1);
          padding: 2px 8px;
          border-radius: 4px;
        }

        .status-tag {
          margin-left: auto;
        }

        .help-icon-small {
          color: #9ca3af;
          font-size: 14px;
          cursor: help;
        }
      }

      .parameter-slider {
        width: 120px;
        margin-left: 16px;
      }
    }

    .parameters-section {
      margin-top: 16px;
      padding-top: 16px;
    }

    .divider-text {
      font-size: 12px;
      color: #9ca3af;
      font-weight: 500;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid #374151;
    }

    .preview-section {
      margin-top: 16px;

      .preview-stats {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 12px;

        .stat-item {
          text-align: center;
          padding: 8px;
          background: rgba(55, 65, 81, 0.5);
          border-radius: 6px;

          .stat-label {
            display: block;
            font-size: 11px;
            color: #9ca3af;
            margin-bottom: 4px;
          }

          .stat-value {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #f9fafb;

            &.success {
              color: #22c55e;
            }
          }
        }
      }
    }
  }
}

// Element Plus 组件样式覆盖
:deep(.el-switch) {
  --el-switch-on-color: #22c55e;
  --el-switch-off-color: #6b7280;
}

:deep(.el-slider__runway) {
  background-color: #374151;
}

:deep(.el-slider__bar) {
  background-color: #22c55e;
}

:deep(.el-slider__button) {
  border-color: #22c55e;
}

:deep(.el-button--primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;

  &:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }
}

:deep(.el-button--success) {
  background-color: #22c55e;
  border-color: #22c55e;

  &:hover {
    background-color: #16a34a;
    border-color: #16a34a;
  }
}

:deep(.el-divider__text) {
  background-color: #1f2937;
  color: #9ca3af;
}
</style>
