<!--
设备详情弹窗组件
功能：
1. 显示机器狗设备详细信息
2. 实时IMU数据展示和监控
3. HTTP API数据获取管理
4. 数据导出和历史查看
5. 响应式弹窗设计
-->

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    :max-width="1200"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    destroy-on-close
    class="device-detail-dialog"
    @close="handleClose"
  >
    <!-- 弹窗头部信息 -->
    <template #header>
      <div class="dialog-header">
        <div class="device-info">
          <el-icon class="device-icon"><Monitor /></el-icon>
          <div class="device-details">
            <h3 class="device-name">{{ deviceInfo?.name || '机器狗设备' }}</h3>
            <p class="device-id">设备ID: {{ deviceInfo?.tagId || 'N/A' }}</p>
          </div>
        </div>
        <div class="connection-status">
          <div class="status-indicator">
            <div class="status-dot" :class="getStatusClass()"></div>
            <span class="status-text">{{ getStatusText() }}</span>
          </div>
          <div class="action-buttons">
            <el-button
              v-if="!isReceiving"
              type="primary"
              size="small"
              :loading="connectionStatus === 'subscribing'"
              @click="startIMUSubscription"
              :disabled="false"
            >
              {{ connectionStatus === 'subscribing' ? '订阅中...' : '开始监控' }}
            </el-button>
            <el-button
              v-else
              type="warning"
              size="small"
              @click="stopIMUSubscription"
            >
              停止监控
            </el-button>
          </div>
        </div>
      </div>
    </template>

    <!-- 弹窗内容 -->
    <div class="dialog-content">
      <!-- 连接提示 -->
      <div v-if="false" class="connection-prompt">
        <el-alert
          title="设备连接中"
          description="正在通过HTTP API获取设备数据..."
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 错误提示 -->
      <div v-else-if="lastError" class="error-prompt">
        <el-alert
          :title="lastError"
          type="error"
          :closable="true"
          show-icon
          @close="clearError"
        />
      </div>

      <!-- IMU数据展示 -->
      <div class="imu-data-container">
        <!-- 数据状态栏 -->
        <div class="data-status-bar">
          <div class="status-info">
            <span class="info-item">
              <el-icon><Clock /></el-icon>
              最后更新: {{ formatLastUpdate() }}
            </span>
            <span class="info-item">
              <el-icon><DataAnalysis /></el-icon>
              数据记录: {{ stats.totalRecords }}
            </span>
            <span class="info-item">
              <el-icon><Connection /></el-icon>
              更新频率: {{ stats.dataRate.toFixed(1) }} Hz
            </span>
          </div>
          <div class="status-actions">
            <el-button
              size="small"
              type="text"
              @click="refreshData"
              :icon="Refresh"
              title="刷新数据"
            />
            <el-button
              size="small"
              type="text"
              @click="toggleAutoScroll"
              :icon="autoScroll ? 'VideoPause' : 'VideoPlay'"
              :title="autoScroll ? '暂停自动滚动' : '开启自动滚动'"
            />
          </div>
        </div>

        <!-- IMU数据展示组件 -->
        <IMUDataDisplay
          :current-data="currentData"
          :stats="stats"
          :data-history="dataHistory"
          @clear-history="handleClearHistory"
        />

        <!-- 数据质量报告 -->
        <div v-if="dataHistory.length > 0" class="data-quality-section">
          <div class="section-header">
            <el-icon class="section-icon"><TrendCharts /></el-icon>
            <span class="section-title">数据质量报告</span>
          </div>
          <div class="quality-content">
            <div class="quality-score">
              <span class="score-label">质量评分</span>
              <el-progress
                :percentage="qualityReport?.score || 0"
                :color="getQualityColor(qualityReport?.score || 0)"
                :stroke-width="8"
              />
            </div>
            <div class="quality-details">
              <div v-if="qualityReport?.issues?.length > 0" class="quality-issues">
                <h4>发现的问题:</h4>
                <ul>
                  <li v-for="issue in qualityReport.issues" :key="issue">{{ issue }}</li>
                </ul>
              </div>
              <div v-if="qualityReport?.recommendations?.length > 0" class="quality-recommendations">
                <h4>建议:</h4>
                <ul>
                  <li v-for="rec in qualityReport.recommendations" :key="rec">{{ rec }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗底部操作 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-info">
          <span v-if="isReceiving" class="recording-indicator">
            <div class="recording-dot"></div>
            正在监控数据...
          </span>
        </div>
        <div class="footer-actions">
          <el-button @click="handleClose">关闭</el-button>
          <el-button
            v-if="dataHistory.length > 0"
            type="primary"
            @click="exportAllData"
            :icon="Download"
          >
            导出完整数据
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Monitor,
  Clock,
  DataAnalysis,
  Connection,
  Refresh,
  TrendCharts,
  Download
} from '@element-plus/icons-vue'
import { useIMUData } from '../composables/useIMUData'
import { IMUDataService } from '../services/imuDataService'
import { robotDogApi } from '@/api/robotDog'
import IMUDataDisplay from './IMUDataDisplay.vue'
import type { DeviceDetailDialogProps } from '../types/imu'

// Props定义
const props = withDefaults(defineProps<DeviceDetailDialogProps>(), {
  visible: false,
  deviceInfo: null,
  rtcInstance: null, // 已废弃，保留兼容性
  config: () => ({
    autoSubscribe: true,
    showCharts: true,
    showHistory: true,
    maxHistoryRecords: 1000,
    updateInterval: 1000,
    chartRefreshRate: 500
  })
})

// Emits定义
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'data-received': [data: any]
  'connection-status-change': [status: string]
  'error': [error: Error]
}>()

// 使用IMU数据管理Hook
const {
  connectionStatus,
  isConnected,
  isReceiving,
  currentData,
  dataHistory,
  stats,
  lastError,
  startPolling,
  stopPolling,
  clearHistory,
  resetConnection,
  handleMessage
} = useIMUData()

// 弹窗状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 自动滚动状态
const autoScroll = ref(true)

// 计算属性
const dialogTitle = computed(() => {
  return `设备详情 - ${props.deviceInfo?.name || '机器狗'}`
})

const qualityReport = computed(() => {
  try {
    const report = IMUDataService.checkDataQuality(dataHistory.value)
    // 确保返回有效的报告对象
    return report || {
      score: 0,
      issues: ['暂无数据'],
      recommendations: ['等待数据获取']
    }
  } catch (error) {
    console.error('计算数据质量报告失败:', error)
    return {
      score: 0,
      issues: ['数据质量计算失败'],
      recommendations: ['请检查数据格式']
    }
  }
})

// 状态相关方法
const getStatusClass = () => {
  switch (connectionStatus.value) {
    case 'receiving': return 'connected'
    case 'connected': return 'connected'
    case 'subscribing': return 'connecting'
    case 'error': return 'error'
    default: return 'disconnected'
  }
}

const getStatusText = () => {
  switch (connectionStatus.value) {
    case 'receiving': return '正在接收数据'
    case 'connected': return '已连接'
    case 'subscribing': return '订阅中...'
    case 'error': return '连接错误'
    default: return '未连接'
  }
}

const getQualityColor = (score: number) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 格式化方法
const formatLastUpdate = () => {
  if (!stats.value.lastUpdateTime) return '无'
  return new Date(stats.value.lastUpdateTime).toLocaleTimeString()
}

// 事件处理方法
const startIMUSubscription = async () => {
  try {
    // 首先检查机器狗连接状态
    await checkConnectionStatus()

    // 开始IMU数据轮询
    const success = await startPolling()
    if (success) {
      emit('connection-status-change', connectionStatus.value)
    }
  } catch (error) {
    console.error('启动IMU订阅失败:', error)
    ElMessage.error('启动数据获取失败')
  }
}

const checkConnectionStatus = async () => {
  try {
    console.log('开始检查机器狗连接状态...')

    // 检查后端机器狗连接状态
    const status = await robotDogApi.getConnectionStatus()
    console.log('机器狗连接状态:', status)

    // 检查状态数据是否有效
    if (status && typeof status.connected === 'boolean') {
      if (!status.connected) {
        ElMessage.warning('机器狗未连接，正在尝试连接...')
        try {
          await robotDogApi.connect()
          // 等待连接建立
          await new Promise(resolve => setTimeout(resolve, 2000))
          console.log('✅ 机器狗连接尝试完成')
        } catch (connectError) {
          console.error('连接机器狗失败:', connectError)
          ElMessage.warning('连接机器狗失败，但将继续尝试获取数据')
        }
      } else {
        console.log('✅ 机器狗已连接')
      }
    } else {
      console.warn('连接状态数据格式异常:', status)
      ElMessage.warning('连接状态数据异常，将直接尝试获取数据')
    }

    console.log('✅ 机器狗连接状态检查完成')
  } catch (error) {
    console.error('检查连接状态失败:', error)
    // 不显示错误消息，直接继续尝试获取数据
    console.log('⚠️ 跳过连接状态检查，直接尝试获取数据')
  }
}

const stopIMUSubscription = async () => {
  await stopPolling()
  emit('connection-status-change', connectionStatus.value)
}

const handleClearHistory = () => {
  clearHistory()
  ElMessage.success('历史数据已清空')
}

const clearError = () => {
  // 错误清除逻辑
}

const refreshData = () => {
  ElMessage.info('数据已刷新')
}

const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value
  ElMessage.success(autoScroll.value ? '已开启自动滚动' : '已暂停自动滚动')
}

const exportAllData = () => {
  try {
    const jsonContent = IMUDataService.exportToJSON(dataHistory.value, props.deviceInfo?.tagId)
    const filename = `device_${props.deviceInfo?.tagId || 'unknown'}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
    IMUDataService.downloadData(jsonContent, filename, 'application/json')
    ElMessage.success('完整数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('数据导出失败')
  }
}

const handleClose = () => {
  // 停止数据获取
  if (isReceiving.value) {
    stopPolling()
  }

  // 重置连接状态
  resetConnection()

  // 关闭弹窗
  emit('update:visible', false)
}

// 监听器 (WebRTC相关代码已移除)
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 自动开始数据获取
    if (props.config?.autoSubscribe) {
      setTimeout(() => {
        startIMUSubscription()
      }, 1000)
    }
  }
}, { immediate: true })

watch(() => currentData.value, (newData) => {
  if (newData) {
    emit('data-received', newData)
  }
})

watch(() => connectionStatus.value, (newStatus) => {
  emit('connection-status-change', newStatus)
})

watch(() => lastError.value, (newError) => {
  if (newError) {
    emit('error', new Error(newError))
  }
})

// 生命周期
onMounted(() => {
  console.log('设备详情弹窗已挂载')
})

onUnmounted(() => {
  // 清理资源
  if (isReceiving.value) {
    stopPolling()
  }
  resetConnection()
})
</script>

<style lang="scss">
.device-detail-dialog {
  .el-dialog {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: 16px;
    backdrop-filter: blur(12px);
  }

  .el-dialog__header {
    background: rgba(55, 65, 81, 0.8);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: 16px 16px 0 0;
    padding: 16px 20px;
  }

  .el-dialog__body {
    padding: 0;
    color: #f3f4f6;
  }

  .el-dialog__footer {
    background: rgba(55, 65, 81, 0.8);
    border-top: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: 0 0 16px 16px;
    padding: 16px 20px;
  }
}
</style>

<style lang="scss" scoped>
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #f3f4f6;

  .device-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .device-icon {
      font-size: 24px;
      color: #3b82f6;
    }

    .device-details {
      .device-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #e5e7eb;
      }

      .device-id {
        margin: 4px 0 0 0;
        font-size: 12px;
        color: #9ca3af;
      }
    }
  }

  .connection-status {
    display: flex;
    align-items: center;
    gap: 16px;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6b7280;

        &.connected { background: #10b981; }
        &.connecting {
          background: #f59e0b;
          animation: pulse 2s infinite;
        }
        &.error { background: #ef4444; }
      }

      .status-text {
        font-size: 14px;
        color: #d1d5db;
      }
    }
  }
}

.dialog-content {
  .connection-prompt,
  .error-prompt {
    padding: 20px;
  }

  .imu-data-container {
    .data-status-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      background: rgba(55, 65, 81, 0.6);
      border-bottom: 1px solid rgba(75, 85, 99, 0.3);

      .status-info {
        display: flex;
        gap: 20px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #9ca3af;

          .el-icon {
            font-size: 14px;
          }
        }
      }

      .status-actions {
        display: flex;
        gap: 8px;
      }
    }

    .data-quality-section {
      margin: 20px;
      padding: 16px;
      background: rgba(55, 65, 81, 0.8);
      border-radius: 8px;
      border: 1px solid rgba(75, 85, 99, 0.3);

      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        .section-icon {
          color: #3b82f6;
          font-size: 18px;
        }

        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: #e5e7eb;
        }
      }

      .quality-content {
        .quality-score {
          margin-bottom: 16px;

          .score-label {
            display: block;
            font-size: 12px;
            color: #9ca3af;
            margin-bottom: 8px;
          }
        }

        .quality-details {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;

          h4 {
            margin: 0 0 8px 0;
            font-size: 13px;
            color: #e5e7eb;
          }

          ul {
            margin: 0;
            padding-left: 16px;
            font-size: 12px;
            color: #9ca3af;

            li {
              margin-bottom: 4px;
            }
          }

          .quality-issues ul {
            color: #fca5a5;
          }

          .quality-recommendations ul {
            color: #93c5fd;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-info {
    .recording-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #ef4444;

      .recording-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #ef4444;
        animation: pulse 2s infinite;
      }
    }
  }

  .footer-actions {
    display: flex;
    gap: 12px;
  }
}

// 动画
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dialog-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;

    .connection-status {
      width: 100%;
      justify-content: space-between;
    }
  }

  .data-status-bar {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start !important;

    .status-info {
      flex-wrap: wrap;
      gap: 12px !important;
    }
  }

  .quality-details {
    grid-template-columns: 1fr !important;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .footer-actions {
      justify-content: center;
    }
  }
}
</style>
