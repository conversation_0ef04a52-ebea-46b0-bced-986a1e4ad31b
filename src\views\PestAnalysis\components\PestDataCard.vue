<!-- 
  PestDataCard.vue
  虫害数据卡片组件，用于展示虫害数据和统计信息
-->
<template>
  <div class="pest-data-card" :class="type">
    <div class="card-icon">
      <el-icon :size="24">
        <component :is="icon"></component>
      </el-icon>
    </div>
    <div class="card-content">
      <div class="card-title">{{ title }}</div>
      <div class="card-value">{{ value }}</div>
      <div class="card-trend">
        <el-icon v-if="trend === 'up'"><ArrowUp /></el-icon>
        <el-icon v-else-if="trend === 'down'"><ArrowDown /></el-icon>
        <el-icon v-else><ArrowRight /></el-icon>
        <span>{{ trendText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowUp, ArrowDown, ArrowRight } from '@element-plus/icons-vue';

defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: String,
    required: true
  },
  trend: {
    type: String,
    default: 'stable',
    validator: (value: string) => {
      return ['up', 'down', 'stable'].includes(value);
    }
  },
  trendText: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'primary',
    validator: (value: string) => {
      return ['primary', 'success', 'warning', 'info', 'danger'].includes(value);
    }
  },
  icon: {
    type: String,
    required: true
  }
})
</script>

<style scoped>
.pest-data-card {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #3b82f6;
  height: 130px;
  transition: all 0.3s ease;
}

.pest-data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.pest-data-card.primary {
  border-top-color: #3b82f6;
}

.pest-data-card.primary .card-icon {
  color: #3b82f6;
}

.pest-data-card.success {
  border-top-color: #10b981;
}

.pest-data-card.success .card-icon {
  color: #10b981;
}

.pest-data-card.warning {
  border-top-color: #f59e0b;
}

.pest-data-card.warning .card-icon {
  color: #f59e0b;
}

.pest-data-card.info {
  border-top-color: #60a5fa;
}

.pest-data-card.info .card-icon {
  color: #60a5fa;
}

.pest-data-card.danger {
  border-top-color: #ef4444;
}

.pest-data-card.danger .card-icon {
  color: #ef4444;
}

.card-icon {
  font-size: 48px;
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-title {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #e5e7eb;
  margin-bottom: 5px;
}

.card-trend {
  font-size: 12px;
  color: #9ca3af;
  display: flex;
  align-items: center;
}

.card-trend .el-icon {
  margin-right: 5px;
}

.card-trend .up {
  color: #10b981;
}

.card-trend .down {
  color: #ef4444;
}
</style> 