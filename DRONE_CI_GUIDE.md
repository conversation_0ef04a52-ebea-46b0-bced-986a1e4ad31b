# 智慧农业前端 Drone CI/CD 部署指南

## 🎉 恭喜！基础触发已成功

你的Drone CI/CD现在已经可以成功触发和运行了！接下来我们来配置完整的部署流水线。

## 🏗️ 当前流水线架构

```
触发测试 → 依赖安装 → 代码检查 → 项目构建 → Docker构建 → 部署 → 健康检查
   ↓         ↓         ↓         ↓         ↓        ↓       ↓
 基础测试   npm install  ESLint    Vite     Docker   SSH部署  检查服务
```

## 🔧 必需的Drone Secrets配置

在Drone管理界面中配置以下Secrets（**必须配置才能部署**）：

### 开发服务器配置
```bash
dev_server_host=你的开发服务器IP地址    # 例如: *************
dev_server_user=你的SSH用户名          # 例如: root 或 deploy
dev_server_key=你的SSH私钥内容         # 完整的私钥内容，包括BEGIN和END行
```

### 获取SSH私钥的方法：
```bash
# 在你的本地机器上生成或查看SSH私钥
cat ~/.ssh/id_rsa

# 或者生成新的密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

### 2. 开发服务器准备

在开发服务器上执行以下准备工作：

```bash
# 创建项目目录
sudo mkdir -p /opt/smart-agriculture-frontend
cd /opt/smart-agriculture-frontend

# 创建环境变量文件
sudo cp .env.development.example .env.development
# 根据实际环境修改配置

# 创建 Docker 网络
docker network create smart-agriculture-dev-network

# 确保 Docker 服务运行
sudo systemctl enable docker
sudo systemctl start docker
```

## 🚀 使用流程

### 1. 触发构建

流水线会在以下情况自动触发：
- 推送代码到 `master`、`main` 或 `develop` 分支
- 创建 Pull Request

### 2. 流水线步骤详解

#### 步骤1: install-and-lint
- **功能**: 安装依赖并执行代码检查
- **镜像**: node:22-alpine
- **命令**: `npm install` + `npm run lint`
- **缓存**: npm 缓存卷

#### 步骤2: type-check
- **功能**: TypeScript 类型检查
- **镜像**: node:22-alpine
- **命令**: `npm run type-check`
- **依赖**: install-and-lint

#### 步骤3: build-project
- **功能**: 构建项目
- **镜像**: node:22-alpine
- **命令**: `npm run build-skip-check`
- **输出**: dist/ 目录
- **依赖**: type-check

#### 步骤4: docker-build
- **功能**: 构建 Docker 镜像
- **插件**: plugins/docker
- **标签**: latest, dev-{BUILD_NUMBER}, {COMMIT_SHA}
- **依赖**: build-project

#### 步骤5: deploy-dev
- **功能**: 部署到开发环境
- **插件**: appleboy/drone-ssh
- **操作**: 拉取镜像 → 停止旧容器 → 启动新容器
- **依赖**: docker-build

#### 步骤6: health-check
- **功能**: 健康检查
- **镜像**: curlimages/curl
- **检查**: HTTP GET 请求到服务端口
- **依赖**: deploy-dev

### 3. 部署结果

成功部署后：
- **访问地址**: http://服务器IP:5174
- **容器名称**: smart-agriculture-h5-dev
- **网络**: smart-agriculture-dev-network
- **重启策略**: unless-stopped

## 🔍 监控和调试

### 查看构建日志
```bash
# 在 Drone Web UI 中查看实时日志
# 或使用 Drone CLI
drone build logs your-repo 123
```

### 服务器端调试
```bash
# 查看容器状态
docker ps | grep smart-agriculture

# 查看容器日志
docker logs smart-agriculture-h5-dev

# 进入容器调试
docker exec -it smart-agriculture-h5-dev sh

# 查看网络连接
docker network ls
docker network inspect smart-agriculture-dev-network
```

### 常见问题排查

#### 问题1: 依赖安装失败
```bash
# 检查网络连接和npm镜像源
# 在 .drone.yml 中已配置国内镜像源
```

#### 问题2: Docker 构建失败
```bash
# 检查 Dockerfile 语法
# 确保基础镜像可访问
```

#### 问题3: SSH 部署失败
```bash
# 验证 SSH 密钥配置
# 检查服务器防火墙设置
# 确认用户权限
```

#### 问题4: 健康检查失败
```bash
# 检查服务启动时间（可能需要调整等待时间）
# 验证端口映射配置
# 检查防火墙规则
```

## 📊 性能优化

### 1. 缓存策略
- **npm 缓存**: 使用卷缓存加速依赖安装
- **Docker 层缓存**: 合理安排 Dockerfile 层级
- **构建缓存**: 缓存构建输出

### 2. 并行执行
- 当前配置为串行执行，确保稳定性
- 可根据需要调整为并行执行以提高速度

### 3. 镜像优化
- 使用 Alpine 基础镜像减小体积
- 多阶段构建分离构建和运行环境

## 🔗 相关链接

- [Drone 官方文档](https://docs.drone.io/)
- [Drone CLI 工具](https://docs.drone.io/cli/install/)
- [Docker 官方文档](https://docs.docker.com/)
- [项目 Docker 使用指南](./DOCKER_GUIDE.md)

## 📞 技术支持

如遇到问题，请检查：
1. Drone Secrets 配置是否正确
2. 开发服务器环境是否就绪
3. 网络连接和防火墙设置
4. Docker 服务状态

---

**祝你部署顺利！** 🚀
