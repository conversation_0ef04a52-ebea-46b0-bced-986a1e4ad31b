<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
  <defs>
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00e676" />
      <stop offset="100%" stop-color="#00c853" />
    </linearGradient>
  </defs>
  
  <!-- 用户图案装饰 -->
  <path d="M30,0 C25,0 20,5 20,10 C20,5 15,0 10,0 C5,0 0,5 0,10 C0,20 10,30 20,40 C30,30 40,20 40,10 C40,5 35,0 30,0 Z" fill="none" stroke="url(#userGradient)" stroke-width="0.5" />
  
  <!-- 内部装饰 -->
  <circle cx="20" cy="15" r="5" fill="none" stroke="url(#userGradient)" stroke-width="0.3" />
  <circle cx="20" cy="15" r="2" fill="none" stroke="url(#userGradient)" stroke-width="0.2" />
  
  <!-- 点缀 -->
  <circle cx="15" cy="10" r="1" fill="#00e676" opacity="0.5" />
  <circle cx="25" cy="10" r="1" fill="#00e676" opacity="0.5" />
  <circle cx="20" cy="20" r="1" fill="#00e676" opacity="0.5" />
</svg> 