/**
 * 应用常量配置
 * 存放不依赖环境变量的固定常量值
 */

// 移动命令常量
export const MOVE_CMD = {
  Move: 1008, // 移动命令ID
} as const;

// WebSocket状态枚举
export const WEBSOCKET_STATUS = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  ERROR: 'error',
  RECONNECTING: 'reconnecting'
} as const;

// 设备状态常量
export const DEVICE_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  STANDBY: 'standby',
  ERROR: 'error'
} as const;

// 任务状态常量
export const TASK_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const;

// 数据类型常量
export const DATA_TYPES = {
  TEMPERATURE: 'temperature',
  HUMIDITY: 'humidity',
  ILLUMINANCE: 'illuminance',
  RAINFALL: 'rainfall'
} as const;

// 图表颜色配置
export const CHART_COLORS = {
  PRIMARY: '#1890ff',
  SUCCESS: '#00ffaa',
  WARNING: '#ffcc00',
  DANGER: '#ff3b5c',
  INFO: '#909399',
  DEFAULT: '#606266'
} as const;

// 状态颜色映射
export const STATUS_COLORS = {
  online: '#2bff96',
  standby: '#ffcc00',
  offline: '#ff3b5c',
  error: '#ff3b5c'
} as const;

// 响应式断点
export const BREAKPOINTS = {
  SM: 768,
  MD: 1024,
  LG: 1440,
  XL: 1920
} as const;

// 文件类型常量
export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv'],
  AUDIO: ['mp3', 'wav', 'ogg']
} as const;

// HTTP状态码常量
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
} as const;

// 本地存储键名常量
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER: 'user',
  THEME: 'theme',
  LANGUAGE: 'language',
  SETTINGS: 'settings'
} as const;

// 路由路径常量
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  MONITORING: '/monitoring',
  DEVICE_MANAGEMENT: '/device-management',
  TASK_SCHEDULING: '/task-scheduling',
  PEST_ANALYSIS: '/pest-analysis',
  PESTICIDE_MANAGEMENT: '/pesticide-management',
  ENVIRONMENT: '/environment'
} as const;

// 权限常量
export const PERMISSIONS = {
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete',
  ADMIN: 'admin'
} as const;

// 消息类型常量
export const MESSAGE_TYPES = {
  SUCCESS: 'success',
  WARNING: 'warning',
  INFO: 'info',
  ERROR: 'error'
} as const;

// 导出所有常量的类型定义
export type MoveCmd = typeof MOVE_CMD[keyof typeof MOVE_CMD];
export type WebSocketStatus = typeof WEBSOCKET_STATUS[keyof typeof WEBSOCKET_STATUS];
export type DeviceStatus = typeof DEVICE_STATUS[keyof typeof DEVICE_STATUS];
export type TaskStatus = typeof TASK_STATUS[keyof typeof TASK_STATUS];
export type DataType = typeof DATA_TYPES[keyof typeof DATA_TYPES];
export type ChartColor = typeof CHART_COLORS[keyof typeof CHART_COLORS];
export type StatusColor = typeof STATUS_COLORS[keyof typeof STATUS_COLORS];
export type Breakpoint = typeof BREAKPOINTS[keyof typeof BREAKPOINTS];
export type HttpStatus = typeof HTTP_STATUS[keyof typeof HTTP_STATUS];
export type StorageKey = typeof STORAGE_KEYS[keyof typeof STORAGE_KEYS];
export type Route = typeof ROUTES[keyof typeof ROUTES];
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];
export type MessageType = typeof MESSAGE_TYPES[keyof typeof MESSAGE_TYPES];
