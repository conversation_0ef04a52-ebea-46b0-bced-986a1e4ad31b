/**
 * 设备追踪页面样式
 * 统一管理所有组件的样式变量和通用样式
 */

// 颜色变量
:root {
  // 主色调
  --primary-color: #3b82f6;
  --primary-light: rgba(59, 130, 246, 0.2);
  --primary-dark: #1e40af;

  // 成功色
  --success-color: #10b981;
  --success-light: rgba(16, 185, 129, 0.2);

  // 警告色
  --warning-color: #f59e0b;
  --warning-light: rgba(245, 158, 11, 0.2);

  // 错误色
  --error-color: #ef4444;
  --error-light: rgba(239, 68, 68, 0.2);

  // 中性色
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  // 背景色
  --bg-dark: #0f172a;
  --bg-panel: rgba(31, 41, 55, 0.8);
  --bg-card: rgba(31, 41, 55, 0.6);

  // 文本色
  --text-light: #f3f4f6;
  --text-secondary: #9ca3af;
  --text-muted: #6b7280;

  // 边框
  --border-color: rgba(75, 85, 99, 0.3);
  --border-light: rgba(255, 255, 255, 0.1);

  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  // 圆角
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  // 过渡
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

// 全局动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

// 移除了过度的动画效果以获得更简洁的用户体验

// 通用样式类
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all var(--transition-normal);
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 状态指示器样式
.status-indicator {
  &.active {
    color: var(--success-color);

    &::before {
      background: var(--success-color);
    }
  }

  &.idle {
    color: var(--primary-color);

    &::before {
      background: var(--primary-color);
    }
  }

  &.warning {
    color: var(--warning-color);

    &::before {
      background: var(--warning-color);
    }
  }

  &.error {
    color: var(--error-color);

    &::before {
      background: var(--error-color);
    }
  }

  &.offline {
    color: var(--gray-500);

    &::before {
      background: var(--gray-500);
    }
  }
}

// 卡片样式
.card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}

// 面板样式
.panel {
  background: var(--bg-panel);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
  padding: var(--spacing-md);
}

// 按钮增强样式
.btn-enhanced {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
  }
}

// 加载状态样式
.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--gray-800) 25%,
    var(--gray-700) 50%,
    var(--gray-800) 75%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

// 响应式断点
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}

// 滚动条样式
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 3px;
  }
}

// 文本截断
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 居中布局
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 网格布局
.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
}

// 响应式网格
@include mobile {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

@include tablet {
  .grid-3,
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}
