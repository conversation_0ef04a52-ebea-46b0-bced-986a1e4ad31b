<!--
数据导出对话框组件
功能：
1. 多种格式导出选择
2. 导出范围配置
3. 文件名设置
4. 批量导出功能
-->

<template>
  <el-dialog
    v-model="visible"
    title="数据导出"
    width="600px"
    :before-close="handleClose"
    class="export-dialog"
  >
    <!-- 导出统计信息 -->
    <div class="export-summary">
      <div class="summary-item">
        <el-icon class="summary-icon"><Document /></el-icon>
        <div class="summary-info">
          <div class="summary-label">可导出记录</div>
          <div class="summary-value">{{ recordCount }} 条</div>
        </div>
      </div>
      <div class="summary-item">
        <el-icon class="summary-icon"><Clock /></el-icon>
        <div class="summary-info">
          <div class="summary-label">数据时间范围</div>
          <div class="summary-value">{{ dataTimeRange }}</div>
        </div>
      </div>
    </div>

    <!-- 导出配置表单 -->
    <el-form :model="exportConfig" label-width="100px" class="export-form">
      <!-- 导出格式选择 -->
      <el-form-item label="导出格式">
        <el-checkbox-group v-model="exportConfig.formats">
          <el-checkbox label="excel">Excel (.xlsx)</el-checkbox>
          <el-checkbox label="csv">CSV (.csv)</el-checkbox>
          <el-checkbox label="json">JSON (.json)</el-checkbox>
          <el-checkbox label="gpx">GPX轨迹 (.gpx)</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 导出范围选择 -->
      <el-form-item label="导出范围">
        <el-radio-group v-model="exportConfig.range">
          <el-radio label="all">全部数据</el-radio>
          <el-radio label="time">时间范围</el-radio>
          <el-radio label="device">指定设备</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 时间范围选择 -->
      <el-form-item label="时间范围" v-if="exportConfig.range === 'time'">
        <el-date-picker
          v-model="exportConfig.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 设备选择 -->
      <el-form-item label="选择设备" v-if="exportConfig.range === 'device'">
        <el-select
          v-model="exportConfig.selectedDevices"
          multiple
          placeholder="请选择要导出的设备"
          style="width: 100%"
        >
          <el-option
            v-for="device in availableDevices"
            :key="device.tagId"
            :label="device.name"
            :value="device.tagId"
          />
        </el-select>
      </el-form-item>

      <!-- 文件名设置 -->
      <el-form-item label="文件名前缀">
        <el-input
          v-model="exportConfig.filename"
          placeholder="请输入文件名前缀"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <!-- 高级选项 -->
      <el-form-item label="高级选项">
        <el-checkbox v-model="exportConfig.includeMetadata">包含元数据</el-checkbox>
        <el-checkbox v-model="exportConfig.compressFiles">压缩文件</el-checkbox>
      </el-form-item>
    </el-form>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleExport"
          :loading="isExporting"
          :disabled="!canExport"
        >
          {{ isExporting ? '导出中...' : '开始导出' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Clock } from '@element-plus/icons-vue'

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  records: {
    type: Array,
    default: () => []
  },
  recordCount: {
    type: Number,
    default: 0
  },
  availableDevices: {
    type: Array,
    default: () => []
  }
})

// 组件事件
const emit = defineEmits(['update:visible', 'export'])

// 导出配置
const exportConfig = ref({
  formats: ['excel'],
  range: 'all',
  timeRange: null,
  selectedDevices: [],
  filename: `设备追踪数据_${new Date().toISOString().slice(0, 10)}`,
  includeMetadata: true,
  compressFiles: false
})

// 导出状态
const isExporting = ref(false)

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const canExport = computed(() => {
  return exportConfig.value.formats.length > 0 && props.recordCount > 0
})

const dataTimeRange = computed(() => {
  if (props.records.length === 0) return '无数据'

  const timestamps = props.records.map(record => new Date(record.timestamp))
  const minTime = new Date(Math.min(...timestamps))
  const maxTime = new Date(Math.max(...timestamps))

  return `${minTime.toLocaleString()} - ${maxTime.toLocaleString()}`
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleExport = async () => {
  if (!canExport.value) {
    ElMessage.warning('请选择导出格式')
    return
  }

  try {
    isExporting.value = true

    // 构建导出参数
    const exportParams = {
      ...exportConfig.value,
      records: props.records,
      recordCount: props.recordCount
    }

    // 触发导出事件
    await emit('export', exportParams)

    ElMessage.success('数据导出完成')

    // 导出完成后关闭对话框
    isExporting.value = false
    visible.value = false

  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
    isExporting.value = false
  }
}

// 监听对话框显示状态，重置表单
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 重置文件名
    exportConfig.value.filename = `设备追踪数据_${new Date().toISOString().slice(0, 10)}`
  }
})
</script>

<style scoped lang="scss">
.export-dialog {
  .export-summary {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
    padding: 16px;
    background: rgba(64, 158, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(64, 158, 255, 0.2);

    .summary-item {
      display: flex;
      align-items: center;
      gap: 12px;

      .summary-icon {
        font-size: 20px;
        color: #409eff;
      }

      .summary-info {
        .summary-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }

        .summary-value {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }

  .export-form {
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .el-radio-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
