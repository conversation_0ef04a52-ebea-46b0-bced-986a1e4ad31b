/*
  common.scss
  监控中心共享样式文件
  包含颜色变量、间距等共享样式
*/

// 引入全局变量
@use '../../../../styles/variables.scss' as vars;

// 颜色变量 - 使用全局变量或保持一致
$primary-color: vars.$primary-color;
$success-color: vars.$success-color;
$warning-color: vars.$warning-color;
$error-color: vars.$danger-color; // 使用danger-color对应error-color
$offline-color: #6b7280; // 保持原有定义

$bg-dark: vars.$bg-dark;
$bg-card: vars.$bg-card;
$bg-panel: rgba(31, 41, 55, 0.5); // 保持原有定义

$text-light: vars.$text-primary; // 使用text-primary对应text-light
$text-secondary: vars.$text-secondary;
$text-disabled: vars.$text-hint; // 使用text-hint对应text-disabled

// 间距变量 - 使用全局变量
$spacing-xs: vars.$spacing-xs;
$spacing-sm: vars.$spacing-sm;
$spacing-md: vars.$spacing-md;
$spacing-lg: vars.$spacing-lg;
$spacing-xl: vars.$spacing-xl;

// 边框圆角 - 使用全局变量
$border-radius-sm: vars.$border-radius-sm;
$border-radius-md: vars.$border-radius-md;
$border-radius-lg: vars.$border-radius-lg;

// 阴影 - 保持原有定义
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);

// 混合
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 使用全局变量中定义的flex-between混合器
@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin card {
  background: $bg-card;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  box-shadow: $shadow-md;
}

@mixin panel {
  background: $bg-panel;
  border-radius: $border-radius-md;
  padding: $spacing-md;
}

@mixin status-indicator($color) {
  position: relative;
  &::before {
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: $color;
    margin-right: $spacing-xs;
    box-shadow: 0 0 8px rgba($color, 0.6);
  }
}

// 通用类
.card {
  @include card;
}

.panel {
  @include panel;
}

.status-success {
  @include status-indicator($success-color);
}

.status-warning {
  @include status-indicator($warning-color);
}

.status-error {
  @include status-indicator($error-color);
}

.status-offline {
  @include status-indicator($offline-color);
}

.status-normal {
  @include status-indicator($primary-color);
}

// 响应式断点 - 使用全局变量并扩展
$breakpoint-xs: 480px;  // 超小屏幕
$breakpoint-sm: vars.$breakpoint-sm;
$breakpoint-md: vars.$breakpoint-md;
$breakpoint-lg: vars.$breakpoint-lg;
$breakpoint-xl: vars.$breakpoint-xl;
$breakpoint-xxl: 2560px; // 超大屏幕

// 响应式混合器
@mixin responsive-xs {
  @media (max-width: $breakpoint-xs) {
    @content;
  }
}

@mixin responsive-sm {
  @media (max-width: $breakpoint-sm) {
    @content;
  }
}

@mixin responsive-md {
  @media (max-width: $breakpoint-md) {
    @content;
  }
}

@mixin responsive-lg {
  @media (max-width: $breakpoint-lg) {
    @content;
  }
}

@mixin responsive-xl {
  @media (max-width: $breakpoint-xl) {
    @content;
  }
}

@mixin responsive-xxl {
  @media (min-width: $breakpoint-xxl) {
    @content;
  }
}

// 范围响应式混合器
@mixin responsive-between($min, $max) {
  @media (min-width: $min) and (max-width: $max) {
    @content;
  }
}

// 设备类型混合器
@mixin mobile-only {
  @media (max-width: $breakpoint-sm - 1px) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: $breakpoint-sm) and (max-width: $breakpoint-lg - 1px) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

// 触摸设备混合器
@mixin touch-device {
  @media (hover: none) and (pointer: coarse) {
    @content;
  }
}

// 高DPI屏幕混合器
@mixin high-dpi {
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    @content;
  }
}

// 减少动画偏好混合器
@mixin reduced-motion {
  @media (prefers-reduced-motion: reduce) {
    @content;
  }
}

// 高对比度模式混合器
@mixin high-contrast {
  @media (prefers-contrast: high) {
    @content;
  }
}

// 滚动条样式混合器
@mixin custom-scrollbar($width: 8px, $track-color: rgba(255, 255, 255, 0.05), $thumb-color: rgba(59, 130, 246, 0.3)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }

  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: calc($width / 2);
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: calc($width / 2);
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(59, 130, 246, 0.5);
    }
  }

  &::-webkit-scrollbar-corner {
    background: $track-color;
  }

  // Firefox滚动条样式
  scrollbar-width: thin;
  scrollbar-color: $thumb-color $track-color;
}

// 平滑过渡混合器
@mixin smooth-transition($properties: all, $duration: 0.3s, $timing: ease) {
  transition: $properties $duration $timing;

  @include reduced-motion {
    transition: none;
  }
}

// 悬浮效果混合器
@mixin hover-lift($lift: 2px, $shadow: 0 4px 12px rgba(0, 0, 0, 0.15)) {
  @include smooth-transition(transform, box-shadow);

  &:hover {
    transform: translateY(-$lift);
    box-shadow: $shadow;
  }
}

// 焦点样式混合器
@mixin focus-outline($color: $primary-color, $width: 2px, $offset: 2px) {
  outline: none;

  &:focus-visible {
    box-shadow: 0 0 0 $width $color;
    outline-offset: $offset;
  }
}
