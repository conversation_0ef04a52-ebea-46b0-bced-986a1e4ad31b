<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <!-- 叶子轮廓 -->
  <path d="M50,10 C30,10 10,30 10,50 C10,70 25,85 50,90 C75,85 90,70 90,50 C90,30 70,10 50,10 Z" 
        fill="none" 
        stroke="url(#leafGradient)" 
        stroke-width="2"
        stroke-linecap="round" />
  
  <!-- 叶脉主干 -->
  <path d="M50,10 C50,30 50,70 50,90" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1.5"
        stroke-linecap="round"
        opacity="0.8" />
  
  <!-- 叶脉支线 - 左侧 -->
  <path d="M50,25 C40,25 30,30 20,40" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        stroke-linecap="round"
        opacity="0.7" />
  
  <path d="M50,40 C40,40 30,45 20,55" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        stroke-linecap="round"
        opacity="0.7" />
  
  <path d="M50,55 C40,55 30,60 25,70" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        stroke-linecap="round"
        opacity="0.7" />
  
  <path d="M50,70 C45,70 40,75 35,80" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        stroke-linecap="round"
        opacity="0.7" />
  
  <!-- 叶脉支线 - 右侧 -->
  <path d="M50,25 C60,25 70,30 80,40" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        stroke-linecap="round"
        opacity="0.7" />
  
  <path d="M50,40 C60,40 70,45 80,55" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        stroke-linecap="round"
        opacity="0.7" />
  
  <path d="M50,55 C60,55 70,60 75,70" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        stroke-linecap="round"
        opacity="0.7" />
  
  <path d="M50,70 C55,70 60,75 65,80" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        stroke-linecap="round"
        opacity="0.7" />
  
  <!-- 科技回路线 -->
  <path d="M30,30 L40,30 L40,40 L30,40 Z" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        opacity="0.6" />
  
  <path d="M60,30 L70,30 L70,40 L60,40 Z" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        opacity="0.6" />
  
  <path d="M25,55 L35,55 L35,65 L25,65 Z" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        opacity="0.6" />
  
  <path d="M65,55 L75,55 L75,65 L65,65 Z" 
        fill="none" 
        stroke="#00ffc8" 
        stroke-width="1"
        opacity="0.6" />
  
  <!-- 连接点 - 脉络交叉点的亮点 -->
  <circle cx="50" cy="25" r="2" fill="#00ffc8" opacity="0.9" />
  <circle cx="50" cy="40" r="2" fill="#00ffc8" opacity="0.9" />
  <circle cx="50" cy="55" r="2" fill="#00ffc8" opacity="0.9" />
  <circle cx="50" cy="70" r="2" fill="#00ffc8" opacity="0.9" />
  
  <!-- 小型数据点 -->
  <circle cx="35" cy="35" r="1.5" fill="#00ffc8" opacity="0.8" />
  <circle cx="65" cy="35" r="1.5" fill="#00ffc8" opacity="0.8" />
  <circle cx="30" cy="60" r="1.5" fill="#00ffc8" opacity="0.8" />
  <circle cx="70" cy="60" r="1.5" fill="#00ffc8" opacity="0.8" />
  <circle cx="40" cy="75" r="1.5" fill="#00ffc8" opacity="0.8" />
  <circle cx="60" cy="75" r="1.5" fill="#00ffc8" opacity="0.8" />
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00ffa3" />
      <stop offset="100%" stop-color="#00b8ff" />
    </linearGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="2" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- 应用发光效果的外层 -->
  <g filter="url(#glow)">
    <path d="M50,10 C30,10 10,30 10,50 C10,70 25,85 50,90 C75,85 90,70 90,50 C90,30 70,10 50,10 Z" 
          fill="none" 
          stroke="url(#leafGradient)" 
          stroke-width="0.5"
          stroke-linecap="round"
          opacity="0.4" />
  </g>
</svg> 