/**
 * 坐标转换工具模块
 * 用于实现基站坐标系与画布坐标系之间的转换
 */

import type { AnchorData } from './websocketService';

// 坐标点接口
export interface Point {
  x: number;
  y: number;
}

// 地图边界接口
export interface MapBounds {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
  width: number;
  height: number;
}

// 画布配置接口
export interface CanvasConfig {
  width: number;
  height: number;
  padding: number; // 边距
}

/**
 * 坐标转换器类
 */
export class CoordinateTransform {
  private mapBounds: MapBounds;
  private canvasConfig: CanvasConfig;
  private scaleX: number = 1;
  private scaleY: number = 1;

  constructor(anchors: AnchorData[], canvasConfig: CanvasConfig) {
    this.canvasConfig = canvasConfig;
    this.mapBounds = this.calculateMapBounds(anchors);
    this.calculateScale();
  }

  /**
   * 根据基站坐标计算地图边界
   */
  private calculateMapBounds(anchors: AnchorData[]): MapBounds {
    if (anchors.length === 0) {
      return {
        minX: 0,
        maxX: 1,
        minY: 0,
        maxY: 1,
        width: 1,
        height: 1
      };
    }

    const xCoords = anchors.map(anchor => anchor.x);
    const yCoords = anchors.map(anchor => anchor.y);

    const minX = Math.min(...xCoords);
    const maxX = Math.max(...xCoords);
    const minY = Math.min(...yCoords);
    const maxY = Math.max(...yCoords);

    // 添加一些边距以确保基站不在边缘
    const marginX = (maxX - minX) * 0.1;
    const marginY = (maxY - minY) * 0.1;

    return {
      minX: minX - marginX,
      maxX: maxX + marginX,
      minY: minY - marginY,
      maxY: maxY + marginY,
      width: (maxX - minX) + 2 * marginX,
      height: (maxY - minY) + 2 * marginY
    };
  }

  /**
   * 计算缩放比例
   */
  private calculateScale(): void {
    const availableWidth = this.canvasConfig.width - 2 * this.canvasConfig.padding;
    const availableHeight = this.canvasConfig.height - 2 * this.canvasConfig.padding;

    this.scaleX = availableWidth / this.mapBounds.width;
    this.scaleY = availableHeight / this.mapBounds.height;

    // 使用相同的缩放比例以保持比例
    const scale = Math.min(this.scaleX, this.scaleY);
    this.scaleX = scale;
    this.scaleY = scale;
  }

  /**
   * 将基站坐标转换为画布坐标（基于中心点）
   */
  worldToCanvas(worldPoint: Point): Point {
    // 计算Canvas中心点
    const centerX = this.canvasConfig.width / 2;
    const centerY = this.canvasConfig.height / 2;

    // 计算地图中心点
    const mapCenterX = (this.mapBounds.minX + this.mapBounds.maxX) / 2;
    const mapCenterY = (this.mapBounds.minY + this.mapBounds.maxY) / 2;

    // 基于中心点进行坐标转换
    const x = centerX + (worldPoint.x - mapCenterX) * this.scaleX;
    const y = centerY - (worldPoint.y - mapCenterY) * this.scaleY; // Y轴翻转

    return { x, y };
  }

  /**
   * 将画布坐标转换为基站坐标（基于中心点）
   */
  canvasToWorld(canvasPoint: Point): Point {
    // 计算Canvas中心点
    const centerX = this.canvasConfig.width / 2;
    const centerY = this.canvasConfig.height / 2;

    // 计算地图中心点
    const mapCenterX = (this.mapBounds.minX + this.mapBounds.maxX) / 2;
    const mapCenterY = (this.mapBounds.minY + this.mapBounds.maxY) / 2;

    // 基于中心点进行坐标转换
    const x = mapCenterX + (canvasPoint.x - centerX) / this.scaleX;
    const y = mapCenterY - (canvasPoint.y - centerY) / this.scaleY; // Y轴翻转

    return { x, y };
  }

  /**
   * 获取地图边界
   */
  getMapBounds(): MapBounds {
    return { ...this.mapBounds };
  }

  /**
   * 获取画布配置
   */
  getCanvasConfig(): CanvasConfig {
    return { ...this.canvasConfig };
  }

  /**
   * 获取缩放比例
   */
  getScale(): { scaleX: number; scaleY: number } {
    return { scaleX: this.scaleX, scaleY: this.scaleY };
  }

  /**
   * 更新基站数据并重新计算边界
   */
  updateAnchors(anchors: AnchorData[]): void {
    this.mapBounds = this.calculateMapBounds(anchors);
    this.calculateScale();
  }

  /**
   * 更新画布配置
   */
  updateCanvasConfig(canvasConfig: CanvasConfig): void {
    this.canvasConfig = canvasConfig;
    this.calculateScale();
  }

  /**
   * 检查点是否在地图边界内
   */
  isPointInBounds(worldPoint: Point): boolean {
    return (
      worldPoint.x >= this.mapBounds.minX &&
      worldPoint.x <= this.mapBounds.maxX &&
      worldPoint.y >= this.mapBounds.minY &&
      worldPoint.y <= this.mapBounds.maxY
    );
  }

  /**
   * 计算两点之间的距离（基站坐标系）
   */
  calculateDistance(point1: Point, point2: Point): number {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 获取地图中心点（基站坐标系）
   */
  getMapCenter(): Point {
    return {
      x: (this.mapBounds.minX + this.mapBounds.maxX) / 2,
      y: (this.mapBounds.minY + this.mapBounds.maxY) / 2
    };
  }

  /**
   * 获取Canvas中心点（画布坐标系）
   */
  getCanvasCenter(): Point {
    return {
      x: this.canvasConfig.width / 2,
      y: this.canvasConfig.height / 2
    };
  }

  /**
   * 获取适合显示的网格间距（基站坐标系）
   */
  getGridSpacing(): number {
    const minDimension = Math.min(this.mapBounds.width, this.mapBounds.height);

    // 计算合适的网格间距，使得网格数量在5-10之间
    let spacing = minDimension / 8;

    // 将间距调整为合适的数值（0.1, 0.2, 0.5, 1, 2, 5等）
    const magnitude = Math.pow(10, Math.floor(Math.log10(spacing)));
    const normalized = spacing / magnitude;

    if (normalized <= 1) {
      spacing = magnitude;
    } else if (normalized <= 2) {
      spacing = 2 * magnitude;
    } else if (normalized <= 5) {
      spacing = 5 * magnitude;
    } else {
      spacing = 10 * magnitude;
    }

    return spacing;
  }

  /**
   * 生成网格线坐标（基站坐标系）
   */
  generateGridLines(): { vertical: number[]; horizontal: number[] } {
    const spacing = this.getGridSpacing();

    const vertical: number[] = [];
    const horizontal: number[] = [];

    // 生成垂直线
    const startX = Math.ceil(this.mapBounds.minX / spacing) * spacing;
    for (let x = startX; x <= this.mapBounds.maxX; x += spacing) {
      vertical.push(x);
    }

    // 生成水平线
    const startY = Math.ceil(this.mapBounds.minY / spacing) * spacing;
    for (let y = startY; y <= this.mapBounds.maxY; y += spacing) {
      horizontal.push(y);
    }

    return { vertical, horizontal };
  }
}

/**
 * 创建坐标转换器的工厂函数
 */
export function createCoordinateTransform(
  anchors: AnchorData[],
  canvasConfig: CanvasConfig
): CoordinateTransform {
  return new CoordinateTransform(anchors, canvasConfig);
}

/**
 * 计算基站的理想排列（用于验证基站数据）
 */
export function validateAnchorArrangement(anchors: AnchorData[]): {
  isValid: boolean;
  message: string;
} {
  if (anchors.length !== 4) {
    return {
      isValid: false,
      message: `基站数量错误，期望4个，实际${anchors.length}个`
    };
  }

  // 检查基站ID是否为0,1,2,3
  const expectedIds = [0, 1, 2, 3];
  const actualIds = anchors.map(anchor => anchor.anchorId).sort();

  if (!expectedIds.every((id, index) => id === actualIds[index])) {
    return {
      isValid: false,
      message: `基站ID错误，期望[0,1,2,3]，实际[${actualIds.join(',')}]`
    };
  }

  // 检查基站是否形成矩形（简单检查）
  const xCoords = anchors.map(anchor => anchor.x);
  const yCoords = anchors.map(anchor => anchor.y);

  const uniqueX = [...new Set(xCoords)];
  const uniqueY = [...new Set(yCoords)];

  if (uniqueX.length !== 2 || uniqueY.length !== 2) {
    return {
      isValid: false,
      message: '基站未形成标准矩形排列'
    };
  }

  return {
    isValid: true,
    message: '基站排列正常'
  };
}
