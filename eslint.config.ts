import { globalIgnores } from 'eslint/config'
import { vueTsConfigs } from '@vue/eslint-config-typescript'
import pluginVue from 'eslint-plugin-vue'
import pluginVitest from '@vitest/eslint-plugin'
import pluginPlaywright from 'eslint-plugin-playwright'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'

// To allow more languages other than `ts` in `.vue` files, uncomment the following lines:
// import { configureVueProject } from '@vue/eslint-config-typescript'
// configureVueProject({ scriptLangs: ['ts', 'tsx'] })
// More info at https://github.com/vuejs/eslint-config-typescript/#advanced-setup

// 创建一个标准的ESLint配置
export default [
  {
    ignores: ['**/dist/**', '**/dist-ssr/**', '**/coverage/**'],
  },
  {
    files: ['**/*.{ts,mts,tsx,vue}'],
    ...pluginVue.configs['flat/essential'],
    ...vueTsConfigs.recommended,
  },
  {
    files: ['src/**/__tests__/*'],
    ...pluginVitest.configs.recommended,
  },
  {
    files: ['e2e/**/*.{test,spec}.{js,ts,jsx,tsx}'],
    ...pluginPlaywright.configs['flat/recommended'],
  },
  skipFormatting,
]
