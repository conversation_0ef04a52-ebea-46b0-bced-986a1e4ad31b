<!-- 
  DeviceCard.vue
  通用设备卡片组件，用于在各个模块中显示设备信息
  支持自定义图标、状态指示、电量显示、信号强度等
-->
<template>
  <div class="device-card" :class="[type, { 'is-active': isActive }]">
    <div class="status-indicator" :class="statusClass"></div>
    <div class="card-header">
      <div class="card-title">
        <h3 class="device-name">{{ deviceName }}</h3>
        <div class="device-id">#{{ deviceId }}</div>
      </div>
      <div class="device-function">{{ deviceFunction }}</div>
    </div>
    
    <div class="status-info">
      <!-- 电量显示 -->
      <div class="status-item" v-if="showBattery">
        <div class="status-label">电量</div>
        <div class="battery-wrapper">
          <div class="battery-level" :style="{ width: `${batteryLevel}%` }" 
               :class="getBatteryClass(batteryLevel)"></div>
        </div>
        <div class="status-value">{{ batteryLevel }}%</div>
      </div>
      
      <!-- 信号强度 -->
      <div class="status-item" v-if="showSignal">
        <div class="status-label">信号强度</div>
        <div class="signal-bars">
          <div class="signal-bar" v-for="i in 5" :key="i" 
            :class="{ active: i <= Math.ceil(signalStrength / 20) }"></div>
        </div>
        <div class="status-value">{{ signalStrength }}%</div>
      </div>
      
      <!-- 工作模式 -->
      <div class="status-item" v-if="workMode">
        <div class="status-label">工作模式</div>
        <div class="work-mode" :class="workMode.toLowerCase().replace(/\s+/g, '-')">
          {{ workMode }}
        </div>
      </div>

      <!-- 自定义状态项插槽 -->
      <slot name="status-items"></slot>
    </div>
    
    <div class="device-micrograph">
      <slot name="device-icon">
        <div :class="`${type}-icon ${workMode ? workMode.toLowerCase().replace(/\s+/g, '-') : ''}`"></div>
      </slot>
    </div>

    <!-- 卡片底部操作区 -->
    <div class="card-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  deviceName: {
    type: String,
    required: true
  },
  deviceId: {
    type: String,
    required: true
  },
  deviceFunction: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'default'
  },
  isActive: {
    type: Boolean,
    default: false
  },
  batteryLevel: {
    type: Number,
    default: 100
  },
  signalStrength: {
    type: Number,
    default: 100
  },
  workMode: {
    type: String,
    default: ''
  },
  status: {
    type: String,
    default: 'normal' // normal, warning, error, offline
  },
  showBattery: {
    type: Boolean,
    default: true
  },
  showSignal: {
    type: Boolean,
    default: true
  }
});

// 计算设备状态类
const statusClass = computed(() => {
  if (props.status) return props.status;
  
  // 根据电量和信号强度自动计算状态
  if (props.batteryLevel < 20 || props.signalStrength < 20) return 'error';
  if (props.batteryLevel < 40 || props.signalStrength < 40) return 'warning';
  return 'normal';
});

// 电池电量样式类
const getBatteryClass = (level: number) => {
  if (level < 20) return 'critical';
  if (level < 40) return 'low';
  return 'normal';
};
</script>

<style scoped>
.device-card {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  padding: 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #3b4863;
}

.device-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.device-card.is-active {
  border-color: #3b82f6;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
}

.status-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 0 5px currentColor;
}

.status-indicator.normal {
  background-color: #10b981;
  color: #10b981;
}

.status-indicator.warning {
  background-color: #f59e0b;
  color: #f59e0b;
}

.status-indicator.error {
  background-color: #ef4444;
  color: #ef4444;
}

.status-indicator.offline {
  background-color: #6b7280;
  color: #6b7280;
}

.card-header {
  margin-bottom: 20px;
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.device-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #e5e7eb;
}

.device-id {
  margin-left: 10px;
  font-size: 12px;
  color: #9ca3af;
  background-color: rgba(71, 85, 105, 0.5);
  padding: 2px 6px;
  border-radius: 4px;
}

.device-function {
  font-size: 14px;
  color: #60a5fa;
}

.status-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-label {
  font-size: 12px;
  color: #9ca3af;
}

.status-value {
  font-size: 14px;
  color: #e5e7eb;
  font-weight: 500;
}

/* 电池样式 */
.battery-wrapper {
  height: 8px;
  background-color: rgba(71, 85, 105, 0.5);
  border-radius: 4px;
  overflow: hidden;
  width: 100%;
}

.battery-level {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.battery-level.normal {
  background: linear-gradient(90deg, #10b981, #34d399);
}

.battery-level.low {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.battery-level.critical {
  background: linear-gradient(90deg, #ef4444, #f87171);
}

/* 信号强度样式 */
.signal-bars {
  display: flex;
  gap: 2px;
}

.signal-bar {
  width: 4px;
  height: 15px;
  background-color: rgba(71, 85, 105, 0.5);
  border-radius: 2px;
}

.signal-bar.active {
  background-color: #60a5fa;
}

/* 工作模式样式 */
.work-mode {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: rgba(71, 85, 105, 0.5);
  color: #e5e7eb;
}

.work-mode.patrol {
  background-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

.work-mode.standby {
  background-color: rgba(107, 114, 128, 0.2);
  color: #9ca3af;
}

.work-mode.active {
  background-color: rgba(16, 185, 129, 0.2);
  color: #34d399;
}

.work-mode.charging {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

.work-mode.maintenance {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

/* 设备图标区域 */
.device-micrograph {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  margin-top: 10px;
}

/* 卡片底部 */
.card-footer {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(71, 85, 105, 0.5);
}

/* 各种设备类型的特殊样式 */
.device-card.robot-dog {
  border-left: 3px solid #3b82f6;
}

.device-card.drone {
  border-left: 3px solid #8b5cf6;
}

.device-card.insect-trap {
  border-left: 3px solid #10b981;
}

.device-card.ultrasonic {
  border-left: 3px solid #f59e0b;
}
</style> 