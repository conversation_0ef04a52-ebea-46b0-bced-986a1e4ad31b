/**
 * 农田地图组件响应式样式（更新版）
 */
@use 'variables.scss' as vars;

/* 超大屏幕 */
@media (min-width: 1921px) {
  .side-drawer {
    &.left-drawer.expanded,
    &.right-drawer.expanded {
      width: 420px;
    }
    
    .drawer-content {
      width: 420px;
    }
  }
  
  .map-card {
    max-width: 320px;
  }
  
  .status-indicators {
    .indicator-group {
      gap: 20px;
    }
  }
}

/* 大屏幕 */
@media (max-width: vars.$breakpoint-lg) {
  .side-drawer {
    &.left-drawer.expanded,
    &.right-drawer.expanded {
      width: 350px;
    }
    
    .drawer-content {
      width: 350px;
    }
  }
  
  .map-card {
    max-width: 260px;
  }
  
  .floating-controls {
    .control-group {
      &.top-right,
      &.bottom-right {
        right: 10px;
      }
    }
  }
}

/* 中等屏幕 */
@media (max-width: vars.$breakpoint-md) {
  .side-drawer {
    &.left-drawer.expanded,
    &.right-drawer.expanded {
      width: 320px;
    }
    
    .drawer-content {
      width: 320px;
    }
  }
  
  .map-card {
    max-width: 240px;
  }
  
  .status-indicators {
    .indicator-group {
      gap: 8px;
    }
  }
}

/* 小屏幕 (平板) */
@media (max-width: vars.$breakpoint-sm) {
  .farmland-map-container {
    height: calc(100vh - 60px);
  }
  
  .side-drawer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100% !important;
    height: 40vh;
    transform: translateY(100%);
    z-index: 100;
    
    &.expanded {
      transform: translateY(0);
      width: 100% !important;
    }
    
    &.left-drawer,
    &.right-drawer {
      border: none;
      border-top: 1px solid rgba(0, 255, 170, 0.15);
      border-radius: 15px 15px 0 0;
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
      
      .drawer-toggle {
        top: -15px;
        left: 50%;
        right: auto;
        transform: translateX(-50%);
      }
      
      .drawer-content {
        width: 100%;
        padding: 0 15px;
      }
    }
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 10px;
    padding: 5px;
    
    .indicator-group,
    .refresh-info {
      width: 100%;
      justify-content: center;
    }
    
    .refresh-info {
      flex-direction: column;
      gap: 5px;
    }
  }
  
  .floating-controls {
    .control-group {
      &.top-right {
        top: 10px;
        right: 10px;
      }
      
      &.bottom-right {
        bottom: 70px; // 避免与抽屉切换按钮重叠
        right: 10px;
      }
    }
  }
  
  .map-card {
    max-width: none;
    width: auto;
  }
  
  // 移动设备抽屉控制栏
  .mobile-drawer-controls {
    display: flex;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 50;
    gap: 15px;
    
    .drawer-button {
      padding: 8px 15px;
      background: rgba(0, 21, 65, 0.85);
      border: 1px solid rgba(0, 255, 170, 0.2);
      border-radius: 20px;
      display: flex;
      align-items: center;
      gap: 5px;
      color: vars.$text-light;
      backdrop-filter: blur(5px);
      
      .el-icon {
        color: vars.$primary-color;
      }
    }
  }
}

/* 超小屏幕 (手机) */
@media (max-width: vars.$breakpoint-xs) {
  .side-drawer {
    height: 50vh;
  }
  
  .status-indicators {
    .indicator-group {
      flex-wrap: wrap;
      justify-content: center;
    }
  }
  
  .floating-controls {
    .control-group {
      &.top-right,
      &.bottom-right {
        right: 5px;
      }
      
      .map-card {
        max-width: 200px;
      }
    }
  }
}

/* 面板控制按钮 */
.panel-controls {
  display: none; /* 默认隐藏，仅在移动设备上显示 */
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 15;
  gap: 10px;
}

.panel-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  background-color: rgba(0, 33, 91, 0.8);
  border: 1px solid vars.$primary-color-light;
  border-radius: 20px;
  color: vars.$text-light;
  @include vars.glass-effect;
  transition: vars.$transition-normal;
  
  &:hover {
    background-color: vars.$primary-color-dark;
    box-shadow: 0 0 10px vars.$primary-color-light;
  }
}

/* 面板头部 */
.panel-header {
  display: none; /* 默认隐藏，仅在移动设备上显示 */
} 