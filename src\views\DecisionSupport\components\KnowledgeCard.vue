<!-- 
  KnowledgeCard.vue
  知识卡片组件
  用于展示病虫害信息卡片
-->
<template>
  <div class="knowledge-card">
    <el-card :body-style="{ padding: '0' }" class="card-content">
      <div class="card-front">
        <el-image 
          :src="image" 
          fit="cover"
          class="card-image"
          loading="lazy"
        ></el-image>
        <div class="card-title">{{ name }}</div>
      </div>
      <div class="card-back">
        <h3>{{ name }}</h3>
        <p class="latin-name">{{ latinName }}</p>
        <p class="crop-info">危害作物: {{ crops.join(', ') }}</p>
        <div class="symptom-tags">
          <el-tag 
            v-for="(tag, i) in symptoms" 
            :key="i" 
            size="small" 
            effect="plain"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineProps({
  /** 病虫害名称 */
  name: {
    type: String,
    required: true
  },
  /** 拉丁学名 */
  latinName: {
    type: String,
    required: true
  },
  /** 图片URL */
  image: {
    type: String,
    required: true
  },
  /** 危害作物 */
  crops: {
    type: Array as () => string[],
    required: true
  },
  /** 症状标签 */
  symptoms: {
    type: Array as () => string[],
    required: true
  }
});
</script>

<style scoped>
.knowledge-card {
  height: 280px;
  margin-bottom: 20px;
  perspective: 1000px;
  cursor: pointer;
}

.card-content {
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  background: rgba(59, 130, 246, 0.1);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}

.knowledge-card:hover .card-content {
  transform: rotateY(180deg);
}

.card-front, .card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  overflow: hidden;
  border-radius: 4px;
}

.card-front {
  background-color: #1e3a8a;
}

.card-back {
  background-color: #1e3a8a;
  color: #ffffff;
  transform: rotateY(180deg);
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-title {
  padding: 10px;
  text-align: center;
  color: #ffffff;
  font-weight: bold;
}

.latin-name {
  font-style: italic;
  color: #d1d5db;
  margin: 0;
}

.crop-info {
  margin-top: 10px;
}

.symptom-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 10px;
}
</style> 