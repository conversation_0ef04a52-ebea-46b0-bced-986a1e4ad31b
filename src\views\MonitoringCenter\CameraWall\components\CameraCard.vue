<template>
  <div class="camera-card" :class="{ 'offline': isOffline, 'focused': isFocused }" @click="toggleFocus">
    <!-- 信息角标 -->
    <div class="info-badge" :class="statusClass">
      <span class="camera-name">{{ camera.name }}</span>
      <span class="camera-location" v-if="camera.location">{{ camera.location }}</span>
    </div>

    <!-- 区域图标 -->
    <div class="area-icon" :class="areaIconClass">
      <i :class="areaIcon"></i>
    </div>

    <!-- 视频区域 -->
    <div class="video-container">
      <video ref="videoElement" autoplay muted loop v-if="!isOffline"
        :class="{ 'video-stuttering': isStuttering }"></video>
      <div class="offline-placeholder" v-else>
        <i class="el-icon-video-camera-solid"></i>
        <span>摄像头离线</span>
      </div>

      <!-- 加载指示器 -->
      <div class="loading-indicator" v-if="isLoading && !isOffline">
        <div class="spinner">
          <div class="spinner-blade" v-for="n in 12" :key="n"></div>
        </div>
        <span>加载中...</span>
      </div>

      <!-- 状态指示器 -->
      <status-indicator :status="streamStatus" />

      <!-- 操作按钮 -->
      <div class="card-actions" v-if="isFocused || isHovered">
        <button class="action-button" title="全屏查看">
          <i class="el-icon-full-screen"></i>
        </button>
        <button class="action-button" title="查看历史记录">
          <i class="el-icon-time"></i>
        </button>
        <button class="action-button" title="设置">
          <i class="el-icon-setting"></i>
        </button>
      </div>
    </div>

    <!-- 底部信息栏 -->
    <div class="card-footer" v-if="isFocused || isHovered">
      <div class="footer-item">
        <i class="el-icon-location"></i>
        <span>{{ camera.location || '未知区域' }}</span>
      </div>
      <div class="footer-item">
        <i class="el-icon-refresh"></i>
        <span>{{ formatUpdateTime }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import StatusIndicator from './StatusIndicator.vue';
import { useCameraStream } from '../composables/useCameraStream';
import { CameraStatus, StreamStatus } from '../types';
import type { Camera } from '../types';

const props = defineProps<{
  camera: Camera;
}>();

// 使用摄像头流组合式函数
const { videoElement, streamStatus, isLoading, error, reconnect } = useCameraStream(props.camera.id);

// 计算摄像头是否离线
const isOffline = computed(() => props.camera.status === CameraStatus.OFFLINE);

// 计算视频流是否卡顿
const isStuttering = computed(() => streamStatus.value === StreamStatus.STUTTERING);

// 焦点和悬停状态
const isFocused = ref(false);
const isHovered = ref(false);

// 根据摄像头状态计算CSS类
const statusClass = computed(() => {
  switch (props.camera.status) {
    case CameraStatus.NORMAL:
      return 'status-normal';
    case CameraStatus.OFFLINE:
      return 'status-offline';
    case CameraStatus.LOW_BATTERY:
      return 'status-low-battery';
    default:
      return 'status-normal';
  }
});

// 根据摄像头区域计算图标
const areaIconClass = computed(() => {
  const location = props.camera.location || '';
  if (location.includes('温室')) return 'area-greenhouse';
  if (location.includes('农田')) return 'area-field';
  if (location.includes('仓库')) return 'area-warehouse';
  return 'area-default';
});

const areaIcon = computed(() => {
  const location = props.camera.location || '';
  if (location.includes('温室')) return 'el-icon-house';
  if (location.includes('农田')) return 'el-icon-tree';
  if (location.includes('仓库')) return 'el-icon-box';
  return 'el-icon-map-location';
});

// 格式化更新时间
const formatUpdateTime = computed(() => {
  if (!props.camera.lastUpdateTime) return '刚刚更新';
  const date = new Date(props.camera.lastUpdateTime);
  return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
});

// 切换焦点状态
const toggleFocus = () => {
  isFocused.value = !isFocused.value;
};

// 监听鼠标悬停
const handleMouseEnter = () => {
  isHovered.value = true;
};

const handleMouseLeave = () => {
  isHovered.value = false;
};

// 监听摄像头状态变化
watch(() => props.camera.status, (newStatus) => {
  if (newStatus === CameraStatus.OFFLINE) {
    // 如果摄像头变为离线，停止视频流
    if (videoElement.value) {
      videoElement.value.pause();
      videoElement.value.src = '';
    }
  } else if (videoElement.value && !videoElement.value.src) {
    // 如果摄像头恢复在线，重新连接视频流
    reconnect();
  }
}, { immediate: true });

onMounted(() => {
  const cardElement = document.querySelector('.camera-card');
  if (cardElement) {
    cardElement.addEventListener('mouseenter', handleMouseEnter);
    cardElement.addEventListener('mouseleave', handleMouseLeave);
  }
});
</script>

<style scoped>
.camera-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius, 12px);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(30, 132, 73, 0.3);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  background-color: #000;
  transform-origin: center;
}

.camera-card:hover {
  box-shadow: 0 6px 20px rgba(30, 132, 73, 0.4);
  transform: translateY(-2px) scale(1.01);
  z-index: 10;
}

.camera-card.focused {
  box-shadow: 0 8px 30px rgba(52, 152, 219, 0.5);
  transform: translateY(-4px) scale(1.03);
  z-index: 20;
  border-color: var(--tech-blue, #3498db);
}

.camera-card.offline {
  border-color: var(--status-offline, #95a5a6);
  opacity: 0.8;
}

.info-badge {
  position: absolute;
  top: 0;
  left: 0;
  padding: 6px 14px;
  border-bottom-right-radius: var(--border-radius, 12px);
  color: #ffffff;
  font-size: 12px;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.camera-card:hover .info-badge {
  padding-bottom: 8px;
  background-color: rgba(0, 0, 0, 0.8);
}

.camera-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.camera-location {
  font-size: 10px;
  opacity: 0.8;
}

.area-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  z-index: 10;
}

.area-greenhouse {
  background-color: rgba(46, 204, 113, 0.7);
}

.area-field {
  background-color: rgba(243, 156, 18, 0.7);
}

.area-warehouse {
  background-color: rgba(121, 85, 72, 0.7);
}

.status-normal {
  background-color: rgba(46, 204, 113, 0.7);
}

.status-offline {
  background-color: rgba(149, 165, 166, 0.7);
}

.status-low-battery {
  background-color: rgba(243, 156, 18, 0.7);
}

.video-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #111;
}

video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

video.video-stuttering {
  animation: stuttering 0.5s infinite;
}

@keyframes stuttering {
  0% {
    opacity: 1;
  }

  25% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }

  75% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

.offline-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  height: 100%;
  width: 100%;
  background-color: #1a1a1a;
  background-image: repeating-linear-gradient(45deg,
      #222,
      #222 10px,
      #1a1a1a 10px,
      #1a1a1a 20px);
}

.offline-placeholder i {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.spinner {
  position: relative;
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
}

.spinner-blade {
  position: absolute;
  top: 16.5px;
  left: 18.5px;
  width: 3px;
  height: 8px;
  border-radius: 2px;
  background-color: var(--primary-green, #1e8449);
  transform-origin: center -6.5px;
  animation: spinner-fade 1s infinite linear;
}

@keyframes spinner-fade {
  0% {
    opacity: 0.85;
  }

  50% {
    opacity: 0.25;
  }

  100% {
    opacity: 0.25;
  }
}

.spinner-blade:nth-child(1) {
  transform: rotate(0deg);
  animation-delay: 0s;
}

.spinner-blade:nth-child(2) {
  transform: rotate(30deg);
  animation-delay: -0.9167s;
}

.spinner-blade:nth-child(3) {
  transform: rotate(60deg);
  animation-delay: -0.833s;
}

.spinner-blade:nth-child(4) {
  transform: rotate(90deg);
  animation-delay: -0.7497s;
}

.spinner-blade:nth-child(5) {
  transform: rotate(120deg);
  animation-delay: -0.667s;
}

.spinner-blade:nth-child(6) {
  transform: rotate(150deg);
  animation-delay: -0.5837s;
}

.spinner-blade:nth-child(7) {
  transform: rotate(180deg);
  animation-delay: -0.5s;
}

.spinner-blade:nth-child(8) {
  transform: rotate(210deg);
  animation-delay: -0.4167s;
}

.spinner-blade:nth-child(9) {
  transform: rotate(240deg);
  animation-delay: -0.333s;
}

.spinner-blade:nth-child(10) {
  transform: rotate(270deg);
  animation-delay: -0.2497s;
}

.spinner-blade:nth-child(11) {
  transform: rotate(300deg);
  animation-delay: -0.167s;
}

.spinner-blade:nth-child(12) {
  transform: rotate(330deg);
  animation-delay: -0.0833s;
}

.card-actions {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  z-index: 15;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.camera-card:hover .card-actions,
.camera-card.focused .card-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--tech-blue, #3498db);
  transform: scale(1.1);
}

.card-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 8px 12px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
  display: flex;
  justify-content: space-between;
  color: white;
  font-size: 11px;
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
  z-index: 5;
}

.camera-card:hover .card-footer,
.camera-card.focused .card-footer {
  opacity: 1;
  transform: translateY(0);
}

.footer-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.footer-item i {
  font-size: 12px;
  opacity: 0.8;
}
</style>