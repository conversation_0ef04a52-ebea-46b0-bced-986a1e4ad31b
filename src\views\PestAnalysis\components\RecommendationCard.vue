<!-- 
  RecommendationCard.vue
  药物推荐卡片组件，用于展示推荐的用药方案
  展示推荐产品、使用剂量、成本、适用时期等信息
-->
<template>
  <div class="recommendation-card" :class="{ 'is-top': isTop }">
    <div class="card-header">
      <h3 class="card-title">{{ plan.title }}</h3>
      <el-tag :type="tagType" effect="dark" size="small">
        {{ tagText }}
      </el-tag>
    </div>
    
    <div class="card-content">
      <div class="plan-info">
        <div class="info-item">
          <span class="info-label">推荐产品</span>
          <span class="info-value">{{ plan.product }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">使用剂量</span>
          <span class="info-value">{{ plan.dosage }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">估计成本</span>
          <span class="info-value">{{ plan.cost }}元/亩</span>
        </div>
        <div class="info-item">
          <span class="info-label">适用时期</span>
          <span class="info-value">{{ plan.timing }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">防效评分</span>
          <div class="rating-wrapper">
            <el-rate 
              v-model="plan.effectivenessRating" 
              disabled 
              :colors="rateColors"
              text-color="#9ca3af"
            />
          </div>
        </div>
        <div class="info-item">
          <span class="info-label">使用说明</span>
          <span class="info-value instruction-text">{{ plan.instructions }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">环境影响</span>
          <div class="progress-wrapper">
            <el-progress 
              :percentage="plan.environmentalImpact" 
              :stroke-width="8"
              :color="getEnvironmentalColor" 
              :format="formatImpact"
            />
          </div>
        </div>
      </div>
      
      <div class="card-actions">
        <slot name="actions"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface PlanData {
  title: string;
  product: string;
  dosage: string;
  cost: number;
  timing: string;
  effectivenessRating: number;
  instructions: string;
  environmentalImpact: number;
}

const props = defineProps({
  plan: {
    type: Object as () => PlanData,
    required: true
  },
  isTop: {
    type: Boolean,
    default: false
  },
  tagText: {
    type: String,
    default: ''
  },
  tagType: {
    type: String,
    default: 'success'
  }
});

// 评分颜色
const rateColors = ['#F56C6C', '#E6A23C', '#67C23A'];

// 根据环境影响值获取对应的颜色
const getEnvironmentalColor = (percentage: number) => {
  if (percentage < 30) return '#67C23A';
  if (percentage < 60) return '#E6A23C';
  return '#F56C6C';
};

// 格式化环境影响显示
const formatImpact = (percentage: number) => {
  if (percentage < 30) return '低影响';
  if (percentage < 60) return '中等影响';
  return '高影响';
};
</script>

<style scoped>
.recommendation-card {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  border: 1px solid #3b4863;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recommendation-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transform: translateY(-3px);
}

.recommendation-card.is-top {
  border: 2px solid #F56C6C;
  position: relative;
}

.recommendation-card.is-top::before {
  content: '';
  position: absolute;
  top: 0;
  left: 16px;
  border-style: solid;
  border-width: 0 8px 8px 8px;
  border-color: transparent transparent #F56C6C transparent;
  transform: translateY(-100%);
}

.card-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3b4863;
  background-color: rgba(31, 41, 55, 0.5);
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #e5e7eb;
}

.card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.plan-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
  flex: 1;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 14px;
  color: #9ca3af;
}

.info-value {
  font-size: 15px;
  color: #e5e7eb;
  line-height: 1.4;
}

.instruction-text {
  font-size: 14px;
  line-height: 1.5;
}

.rating-wrapper {
  margin-top: 2px;
}

.progress-wrapper {
  margin-top: 6px;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #3b4863;
}
</style> 