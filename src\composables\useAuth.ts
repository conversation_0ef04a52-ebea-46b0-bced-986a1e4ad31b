import { ref, computed } from 'vue'
import { login as apiLogin, getCurrentUser, logoutApi, parseJwt, clearAuthStorage } from '@/api/auth'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import type { AgriLoginRequest, AgriUserInfo, RememberedUser } from '@/types/user'

// Token管理相关常量
const TOKEN_KEY = 'token'
const USER_KEY = 'user'
const REMEMBER_KEY = 'rememberedUser'

/**
 * 认证相关的组合式函数
 * 提供登录、注销和认证状态管理功能
 */
export function useAuth() {
  const router = useRouter()

  // 响应式状态
  const token = ref<string | null>(localStorage.getItem(TOKEN_KEY))
  const user = ref<AgriUserInfo | null>(null)
  const loading = ref(false)

  // 计算属性：是否已认证
  const isAuthenticated = computed(() => !!token.value)

  // 初始化用户信息
  const initUser = () => {
    const userJson = localStorage.getItem(USER_KEY)
    if (userJson) {
      try {
        user.value = JSON.parse(userJson) as AgriUserInfo
      } catch (e) {
        console.error('解析用户信息失败:', e)
        localStorage.removeItem(USER_KEY)
      }
    }
  }

  // 登录方法
  const login = async (username: string, password: string, remember: boolean): Promise<boolean> => {
    loading.value = true
    try {
      // 构建登录请求数据
      const loginRequest: AgriLoginRequest = {
        username,
        password,
        rememberMe: remember
      }

      // 调用登录API
      const loginResponse = await apiLogin(loginRequest)

      // 保存访问令牌
      const newToken = loginResponse.access_token
      localStorage.setItem(TOKEN_KEY, newToken)
      token.value = newToken

      // 构建用户信息
      const userInfo: AgriUserInfo = {
        id: loginResponse.userId,
        username: loginResponse.username,
        nickName: loginResponse.nickName,
        realName: loginResponse.realName,
        phone: loginResponse.phone
      }

      user.value = userInfo
      localStorage.setItem(USER_KEY, JSON.stringify(userInfo))

      // 如果选择了"记住我"，则保存用户名和密码
      if (remember) {
        const rememberedUser: RememberedUser = {
          username,
          password
        }
        localStorage.setItem(REMEMBER_KEY, JSON.stringify(rememberedUser))
      } else {
        localStorage.removeItem(REMEMBER_KEY)
      }

      ElMessage.success('登录成功')
      return true
    } catch (error: any) {
      console.error('登录错误:', error)
      ElMessage.error(error.message || '登录失败，请稍后再试')
      return false
    } finally {
      loading.value = false
    }
  }

  // 注销方法
  const logout = async (): Promise<void> => {
    try {
      // 调用服务端退出登录API
      await logoutApi()
    } catch (error) {
      // 服务端退出失败不影响本地清理
      console.warn('服务端退出登录失败:', error)
    }

    // 清除本地存储的认证信息
    clearAuthStorage()

    // 清除状态
    token.value = null
    user.value = null

    // 跳转到登录页
    router.push('/login')

    ElMessage.success('已安全退出系统')
  }

  // 检查是否有记住的用户信息
  const getRememberedUser = (): RememberedUser | null => {
    const rememberedJson = localStorage.getItem(REMEMBER_KEY)
    if (rememberedJson) {
      try {
        return JSON.parse(rememberedJson) as RememberedUser
      } catch (e) {
        console.error('解析记住的用户信息失败:', e)
        localStorage.removeItem(REMEMBER_KEY)
      }
    }
    return null
  }

  // 检查并刷新认证状态
  const checkAuth = (): boolean => {
    const currentToken = localStorage.getItem(TOKEN_KEY)
    if (!currentToken) {
      return false
    }

    // 这里可以添加令牌有效性检查逻辑
    // 例如检查JWT是否过期
    try {
      const payload = parseJwt(currentToken)
      const now = Date.now() / 1000

      // 如果令牌已过期
      if (payload.exp && payload.exp < now) {
        logout()
        ElMessage.warning('登录已过期，请重新登录')
        return false
      }

      // 如果未过期但状态不同步，同步状态
      if (!token.value) {
        token.value = currentToken
        initUser()
      }

      return true
    } catch (e) {
      console.error('验证令牌失败:', e)
      logout()
      return false
    }
  }

  // 初始化认证状态
  const initAuth = () => {
    if (token.value) {
      initUser()
      checkAuth()
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async (): Promise<void> => {
    try {
      const userInfo = await getCurrentUser()
      user.value = userInfo
      localStorage.setItem(USER_KEY, JSON.stringify(userInfo))
    } catch (error: any) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能token已过期，执行退出登录
      logout()
    }
  }

  // 初始化
  initAuth()

  // 返回状态和方法
  return {
    token,
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    checkAuth,
    getRememberedUser,
    fetchCurrentUser
  }
}
