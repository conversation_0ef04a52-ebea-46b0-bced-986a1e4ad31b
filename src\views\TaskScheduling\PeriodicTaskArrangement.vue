<!--
  PeriodicTaskArrangement.vue
  周期性巡航任务编排页面，用于管理智能农业设备的巡航任务
-->
<template>
  <div class="periodic-task-container">
    <!-- 页面标题 -->
    <PageHeader
      title="周期性巡航任务编排"
      description="创建、编辑和管理周期性巡航任务，如机器狗的定时巡逻任务、无人机的定期喷洒任务等"
      icon="Calendar"
    >
      <template #actions>
        <div class="header-actions">
          <el-input
            v-model="searchQuery"
            placeholder="搜索任务..."
            clearable
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="createNewTask">
            <el-icon><Plus /></el-icon>
            创建新任务
          </el-button>
        </div>
      </template>
    </PageHeader>

    <div class="task-content">
      <!-- 左侧任务导航栏 -->
      <div class="task-nav">
        <div class="nav-header">
          <el-input
            v-model="navSearchQuery"
            placeholder="搜索导航..."
            clearable
            class="nav-search"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="task-list">
          <div
            v-for="task in filteredTasks"
            :key="task.id"
            class="task-item"
            :class="{ active: selectedTaskId === task.id }"
            @click="selectTask(task.id)"
          >
            <div class="task-icon">
              <el-icon v-if="task.type === 'patrol'"><Van /></el-icon>
              <el-icon v-else-if="task.type === 'spray'"><Umbrella /></el-icon>
              <el-icon v-else-if="task.type === 'inspection'"><Monitor /></el-icon>
              <el-icon v-else><Operation /></el-icon>
            </div>
            <div class="task-info">
              <div class="task-name">{{ task.name }}</div>
              <div class="task-meta">
                <span class="task-type">{{ getTaskTypeLabel(task.type) }}</span>
                <span class="task-cycle">{{ getCycleLabel(task.cycleType, task.cycleValue) }}</span>
              </div>
            </div>
            <div class="task-status">
              <StatusIndicator
                :type="getStatusIndicatorType(task.status)"
                :label="getStatusLabel(task.status)"
                size="small"
              />
            </div>
          </div>

          <div v-if="filteredTasks.length === 0" class="empty-list">
            <el-empty description="暂无任务" />
          </div>
        </div>
      </div>

      <!-- 右侧任务编辑区 -->
      <div class="task-editor">
        <template v-if="selectedTaskId">
          <TaskDetailView
            v-if="viewMode === 'detail' && selectedTask"
            :task="selectedTask"
            @edit="switchToEditMode"
            @delete="confirmDeleteTask"
          />
          <TaskEditForm
            v-else-if="viewMode === 'edit' && selectedTask"
            :task="selectedTask"
            @save="saveTask"
            @cancel="switchToDetailMode"
          />
        </template>

        <template v-else-if="viewMode === 'create'">
          <TaskCreationWizard
            @save="saveNewTask"
            @cancel="cancelCreation"
          />
        </template>

        <div v-else class="welcome-panel">
          <div class="welcome-content">
            <el-icon class="welcome-icon"><Calendar /></el-icon>
            <h3>周期性巡航任务管理</h3>
            <p>点击左侧列表查看任务详情，或创建新的周期性任务</p>
            <el-button type="primary" @click="createNewTask">创建新任务</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务表格视图 -->
    <DataPanel title="任务列表" dark>
      <template #actions>
        <el-button-group>
          <el-button :disabled="!hasSelection" @click="batchEnable(true)">批量启用</el-button>
          <el-button :disabled="!hasSelection" @click="batchEnable(false)">批量禁用</el-button>
          <el-button :disabled="!hasSelection" type="danger" @click="batchDelete">批量删除</el-button>
        </el-button-group>
      </template>

      <el-table
        ref="taskTable"
        :data="taskList"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="任务名称" prop="name" sortable />
        <el-table-column label="任务类型" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ getTaskTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="执行周期" width="150">
          <template #default="{ row }">
            {{ getCycleLabel(row.cycleType, row.cycleValue) }}
          </template>
        </el-table-column>
        <el-table-column label="设备数量" width="100" prop="deviceIds.length" sortable />
        <el-table-column label="优先级" width="100" sortable>
          <template #default="{ row }">
            <el-rate
              :model-value="getPriorityValue(row.priority)"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="启用" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.enabled"
              @change="toggleTaskStatus(row.id, row.enabled)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="viewTask(row.id)">查看</el-button>
              <el-button size="small" type="primary" @click="editTask(row.id)">编辑</el-button>
              <el-button size="small" type="danger" @click="confirmDeleteTask(row.id)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-pagination
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          :page-sizes="pageSizeOptions"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </template>
    </DataPanel>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Plus,
  Search,
  Calendar,
  Van,
  Umbrella,
  Monitor,
  Operation
} from '@element-plus/icons-vue';
import { periodicTaskApi } from '@/api/taskScheduling';
import type { PeriodicTask, TaskStatus } from '@/types/taskScheduling';
import { PAGINATION_CONFIG } from '@/config/ui';

// 导入组件
import PageHeader from './components/PageHeader.vue';
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue';
import DataPanel from '../DeviceManagement/components/DataPanel.vue';
import TaskDetailView from './components/TaskDetailView.vue';
import TaskEditForm from './components/TaskEditForm.vue';
import TaskCreationWizard from './components/TaskCreationWizard.vue';

// 模拟任务数据
const taskList = ref<PeriodicTask[]>([
  {
    id: '1',
    name: '北区农田定时巡逻',
    description: '每天上午8点对北区农田进行巡逻检查',
    type: 'patrol',
    deviceIds: ['device-001', 'device-002'],
    cycleType: 'daily',
    cycleValue: 1,
    startTime: '2023-07-15T08:00:00Z',
    priority: 'medium',
    status: 'pending',
    enabled: true,
    createdAt: '2023-07-10T10:30:00Z',
    updatedAt: '2023-07-10T10:30:00Z',
    nextExecutionTime: '2023-07-16T08:00:00Z'
  },
  {
    id: '2',
    name: '周末喷洒任务',
    description: '每周六对全区农田进行农药喷洒',
    type: 'spray',
    deviceIds: ['device-003'],
    cycleType: 'weekly',
    cycleValue: 1,
    startTime: '2023-07-15T09:00:00Z',
    priority: 'high',
    status: 'running',
    enabled: true,
    createdAt: '2023-07-11T14:20:00Z',
    updatedAt: '2023-07-11T14:20:00Z',
    nextExecutionTime: '2023-07-22T09:00:00Z'
  },
  {
    id: '3',
    name: '月度病虫害检查',
    description: '每月对所有农田进行一次全面病虫害检查',
    type: 'inspection',
    deviceIds: ['device-001', 'device-004'],
    cycleType: 'monthly',
    cycleValue: 1,
    startTime: '2023-07-20T10:00:00Z',
    priority: 'low',
    status: 'completed',
    enabled: false,
    createdAt: '2023-07-05T09:15:00Z',
    updatedAt: '2023-07-20T15:45:00Z',
    lastExecutedAt: '2023-07-20T12:30:00Z',
    nextExecutionTime: '2023-08-20T10:00:00Z'
  }
]);

// 视图状态
const viewMode = ref<'detail' | 'edit' | 'create' | 'welcome'>('welcome');
const selectedTaskId = ref<string | null>(null);
const searchQuery = ref('');
const navSearchQuery = ref('');

// 表格相关状态
const taskTable = ref();
const selectedRows = ref<PeriodicTask[]>([]);
const hasSelection = computed(() => selectedRows.value.length > 0);

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: PAGINATION_CONFIG.DEFAULT_PAGE_SIZE,
  total: 3
});

// 分页配置
const pageSizeOptions = PAGINATION_CONFIG.PAGE_SIZE_OPTIONS;

// 计算属性
const selectedTask = computed(() => {
  return taskList.value.find(task => task.id === selectedTaskId.value) || null;
});

const filteredTasks = computed(() => {
  if (!navSearchQuery.value) return taskList.value;

  const query = navSearchQuery.value.toLowerCase();
  return taskList.value.filter(task =>
    task.name.toLowerCase().includes(query) ||
    task.description?.toLowerCase().includes(query)
  );
});

// 辅助函数
const getTaskTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    'patrol': '巡逻任务',
    'spray': '喷洒任务',
    'inspection': '检查任务',
    'other': '其他任务'
  };
  return map[type] || '未知类型';
};

const getCycleLabel = (cycleType: string, cycleValue: number) => {
  const map: Record<string, string> = {
    'once': '单次',
    'daily': `每${cycleValue > 1 ? cycleValue : ''}天`,
    'weekly': `每${cycleValue > 1 ? cycleValue : ''}周`,
    'monthly': `每${cycleValue > 1 ? cycleValue : ''}月`
  };
  return map[cycleType] || '未知周期';
};

const getStatusLabel = (status: TaskStatus) => {
  const map: Record<TaskStatus, string> = {
    'pending': '等待中',
    'running': '执行中',
    'paused': '已暂停',
    'completed': '已完成',
    'failed': '已失败'
  };
  return map[status] || '未知状态';
};

const getStatusTagType = (status: TaskStatus) => {
  const map: Record<TaskStatus, string> = {
    'pending': 'info',
    'running': 'success',
    'paused': 'warning',
    'completed': '',
    'failed': 'danger'
  };
  return map[status] || 'info';
};

const getStatusIndicatorType = (status: TaskStatus) => {
  const map: Record<TaskStatus, string> = {
    'pending': 'normal',
    'running': 'success',
    'paused': 'warning',
    'completed': 'normal',
    'failed': 'error'
  };
  return map[status] || 'normal';
};

const getPriorityValue = (priority: string) => {
  const priorityMap: Record<string, number> = {
    'low': 1,
    'medium': 2,
    'high': 3,
    'emergency': 4
  };
  return priorityMap[priority] || 1;
};

// 事件处理函数
const selectTask = (taskId: string) => {
  selectedTaskId.value = taskId;
  viewMode.value = 'detail';
};

const createNewTask = () => {
  selectedTaskId.value = null;
  viewMode.value = 'create';
};

const viewTask = (taskId: string) => {
  selectedTaskId.value = taskId;
  viewMode.value = 'detail';
};

const editTask = (taskId: string) => {
  selectedTaskId.value = taskId;
  viewMode.value = 'edit';
};

const switchToEditMode = () => {
  viewMode.value = 'edit';
};

const switchToDetailMode = () => {
  viewMode.value = 'detail';
};

const saveTask = async (task: PeriodicTask) => {
  try {
    // 在实际项目中调用API
    // await periodicTaskApi.updateTask(task.id, task)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 更新本地任务列表
    const index = taskList.value.findIndex(t => t.id === task.id);
    if (index !== -1) {
      taskList.value[index] = { ...task, updatedAt: new Date().toISOString() };
    }

    viewMode.value = 'detail';
    ElMessage.success('任务更新成功');
  } catch (error) {
    console.error('更新任务失败:', error);
    ElMessage.error('更新任务失败，请重试');
  }
};

const saveNewTask = async (task: PeriodicTask) => {
  try {
    // 在实际项目中调用API
    // const response = await periodicTaskApi.createTask(task)
    // const newTask = response.data

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模拟新任务数据
    const newTask: PeriodicTask = {
      ...task,
      id: 'task-' + Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 更新本地任务列表
    taskList.value.unshift(newTask);

    // 切换到新任务详情视图
    selectedTaskId.value = newTask.id;
    viewMode.value = 'detail';

    ElMessage.success('任务创建成功');
  } catch (error) {
    console.error('创建任务失败:', error);
    ElMessage.error('创建任务失败，请重试');
  }
};

const cancelCreation = () => {
  viewMode.value = 'welcome';
};

const confirmDeleteTask = (taskId: string) => {
  ElMessageBox.confirm(
    '确定删除此任务吗？此操作不可恢复。',
    '删除任务',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 在实际项目中调用API
      // await periodicTaskApi.deleteTask(taskId)

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 更新本地任务列表
      taskList.value = taskList.value.filter(task => task.id !== taskId);

      // 重置视图
      if (selectedTaskId.value === taskId) {
        selectedTaskId.value = null;
        viewMode.value = 'welcome';
      }

      ElMessage.success('任务已删除');
    } catch (error) {
      console.error('删除任务失败:', error);
      ElMessage.error('删除任务失败，请重试');
    }
  }).catch(() => {
    // 取消删除
  });
};

const toggleTaskStatus = async (taskId: string, enabled: boolean) => {
  try {
    // 在实际项目中调用API
    // await periodicTaskApi.toggleTaskStatus(taskId, enabled)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    // 更新本地任务状态
    const task = taskList.value.find(t => t.id === taskId);
    if (task) {
      task.enabled = enabled;
      task.updatedAt = new Date().toISOString();
    }

    ElMessage.success(`任务已${enabled ? '启用' : '禁用'}`);
  } catch (error) {
    console.error('更改任务状态失败:', error);
    ElMessage.error('更改任务状态失败，请重试');

    // 恢复任务状态
    const task = taskList.value.find(t => t.id === taskId);
    if (task) {
      task.enabled = !enabled;
    }
  }
};

// 表格相关函数
const handleSelectionChange = (rows: PeriodicTask[]) => {
  selectedRows.value = rows;
};

const batchEnable = async (enabled: boolean) => {
  if (selectedRows.value.length === 0) return;

  try {
    // 在实际项目中应该批量调用API
    // 这里简化处理，逐个调用
    for (const task of selectedRows.value) {
      // await periodicTaskApi.toggleTaskStatus(task.id, enabled)

      // 更新本地任务状态
      const localTask = taskList.value.find(t => t.id === task.id);
      if (localTask) {
        localTask.enabled = enabled;
        localTask.updatedAt = new Date().toISOString();
      }
    }

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success(`已${enabled ? '启用' : '禁用'} ${selectedRows.value.length} 个任务`);
    taskTable.value?.clearSelection();
  } catch (error) {
    console.error('批量操作失败:', error);
    ElMessage.error('批量操作失败，请重试');
  }
};

const batchDelete = () => {
  if (selectedRows.value.length === 0) return;

  ElMessageBox.confirm(
    `确定删除选中的 ${selectedRows.value.length} 个任务吗？此操作不可恢复。`,
    '批量删除',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 在实际项目中应该批量调用API
      const taskIds = selectedRows.value.map(task => task.id);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 更新本地任务列表
      taskList.value = taskList.value.filter(task => !taskIds.includes(task.id));

      // 重置视图，如果当前选中的任务被删除
      if (selectedTaskId.value && taskIds.includes(selectedTaskId.value)) {
        selectedTaskId.value = null;
        viewMode.value = 'welcome';
      }

      ElMessage.success(`已删除 ${taskIds.length} 个任务`);
      taskTable.value?.clearSelection();
    } catch (error) {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败，请重试');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 分页处理函数
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  // 重新加载数据
  loadTasks();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  // 重新加载数据
  loadTasks();
};

// 加载任务数据
const loadTasks = async () => {
  try {
    // 在实际项目中调用API
    // const response = await periodicTaskApi.getTaskList({
    //   page: pagination.currentPage,
    //   pageSize: pagination.pageSize
    // })
    // taskList.value = response.data.items
    // pagination.total = response.data.total

    // 这里使用模拟数据
    pagination.total = taskList.value.length;
  } catch (error) {
    console.error('加载任务失败:', error);
    ElMessage.error('加载任务列表失败，请重试');
  }
};

onMounted(() => {
  // 初始化加载数据
  loadTasks();
});
</script>

<style scoped>
.periodic-task-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-input {
  width: 240px;
}

.task-content {
  display: flex;
  gap: 20px;
  min-height: 500px;
  flex: 1;
}

.task-nav {
  width: 300px;
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.nav-header {
  padding: 16px;
  border-bottom: 1px solid #3b4863;
}

.nav-search :deep(.el-input__inner) {
  background-color: rgba(31, 41, 55, 0.3);
  border-color: #4b5563;
  color: #e5e7eb;
}

.nav-search :deep(.el-input__prefix) {
  color: #9ca3af;
}

.task-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  background-color: rgba(31, 41, 55, 0.3);
  transition: all 0.3s ease;
}

.task-item:hover {
  background-color: rgba(59, 130, 246, 0.2);
}

.task-item.active {
  background-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.task-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-right: 12px;
  color: #60a5fa;
  font-size: 18px;
}

.task-info {
  flex: 1;
  overflow: hidden;
}

.task-name {
  font-size: 14px;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-meta {
  display: flex;
  font-size: 12px;
  color: #9ca3af;
}

.task-type {
  margin-right: 8px;
}

.task-status {
  margin-left: 8px;
}

.empty-list {
  padding: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #9ca3af;
}

.task-editor {
  flex: 1;
  border-radius: 12px;
  overflow: hidden;
}

.welcome-panel {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
}

.welcome-content {
  text-align: center;
  max-width: 500px;
}

.welcome-icon {
  font-size: 48px;
  color: #3b82f6;
  margin-bottom: 16px;
}

.welcome-content h3 {
  font-size: 24px;
  font-weight: bold;
  color: #e5e7eb;
  margin-bottom: 16px;
}

.welcome-content p {
  font-size: 16px;
  color: #9ca3af;
  margin-bottom: 24px;
}
</style>
