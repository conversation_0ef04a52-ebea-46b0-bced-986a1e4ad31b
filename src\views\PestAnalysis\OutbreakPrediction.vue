<!-- 
  OutbreakPrediction.vue
  虫害爆发趋势预测模块
  基于气象数据和历史虫害数据预测未来虫害爆发趋势，为农业防控提供科学依据
-->
<template>
  <div class="outbreak-prediction">
    <!-- 页面标题 -->
    <PageHeader
      title="虫害爆发趋势预测"
      description="基于气象数据和历史虫害数据预测未来虫害爆发趋势，为农业防控提供科学依据"
      icon="TrendCharts"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ activeModels }}</span>
            <span class="summary-label">活跃预测模型</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ avgAccuracy }}%</span>
            <span class="summary-label">平均预测准确率</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 模型设置区域 -->
    <div class="model-settings">
      <DataPanel title="预测模型设置">
        <el-form :model="modelForm" label-width="120px" label-position="left">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="虫害类型">
                <el-select v-model="modelForm.pestType" placeholder="选择虫害类型" style="width: 100%">
                  <el-option 
                    v-for="option in pestTypeOptions" 
                    :key="option.value" 
                    :label="option.label" 
                    :value="option.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预测模型">
                <el-select v-model="modelForm.modelType" placeholder="选择预测模型" style="width: 100%">
                  <el-option 
                    v-for="option in modelTypeOptions" 
                    :key="option.value" 
                    :label="option.label" 
                    :value="option.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="预测周期(天)">
                <el-slider 
                  v-model="modelForm.predictionPeriod" 
                  :min="7" 
                  :max="90" 
                  :step="1" 
                  show-input>
                </el-slider>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="置信水平">
                <el-slider 
                  v-model="modelForm.confidenceLevel" 
                  :min="0.8" 
                  :max="0.99" 
                  :step="0.01" 
                  :format-tooltip="(val: number) => (val * 100).toFixed(0) + '%'"
                  show-input>
                </el-slider>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="包含气象数据">
                <el-switch v-model="modelForm.includeWeather"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="包含历史趋势">
                <el-switch v-model="modelForm.includeHistory"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item>
            <el-button 
              type="primary" 
              @click="runPrediction" 
              :loading="modelForm.loading">
              运行预测
            </el-button>
            <el-button @click="resetSettings">重置设置</el-button>
            <el-button type="success" @click="exportReport">导出预测报告</el-button>
          </el-form-item>
        </el-form>
      </DataPanel>
    </div>
    
    <!-- 数据统计区域 -->
    <div class="data-statistics">
      <DataPanel title="虫害风险统计">
        <div class="statistics-header">
          <div class="overall-stats">
            <div class="stat-item">
              <div class="stat-label">历史记录总数</div>
              <div class="stat-value">{{ pestHistoryStats.total }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">本月记录</div>
              <div class="stat-value">{{ pestHistoryStats.thisMonth }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">环比增长</div>
              <div class="stat-value" :style="{color: pestHistoryStats.growthRate > 0 ? '#f56c6c' : '#67c23a'}">
                {{ pestHistoryStats.growthRate > 0 ? '+' : '' }}{{ pestHistoryStats.growthRate }}%
              </div>
            </div>
          </div>
        </div>
        
        <el-divider content-position="left">风险级别分布</el-divider>
        
        <div class="risk-levels">
          <div v-for="(item, index) in riskLevels" :key="index" class="risk-item">
            <div class="risk-badge" :style="{backgroundColor: item.color}"></div>
            <div class="risk-label">{{ item.level }}</div>
            <div class="risk-count">{{ item.count }}</div>
          </div>
        </div>
        
        <el-divider content-position="left">区域爆发概率</el-divider>
        
        <div class="region-stats">
          <div v-for="(region, index) in regionStats" :key="index" class="region-item">
            <div class="region-name">{{ region.region }}</div>
            <div class="region-probability">
              <el-progress 
                :percentage="region.probability * 100" 
                :color="region.probability > 0.6 ? '#f56c6c' : region.probability > 0.4 ? '#e6a23c' : '#67c23a'" 
                :format="(val: number) => val.toFixed(0) + '%'"
                :stroke-width="10">
              </el-progress>
            </div>
            <div class="region-trend" :style="{color: getTrendColor(region.trend)}">
              <i :class="getTrendIcon(region.trend)"></i>
            </div>
          </div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 图表区域 -->
    <div class="chart-panels">
      <!-- 趋势预测图表 -->
      <DataPanel title="虫害爆发趋势预测">
        <template #actions>
          <div class="chart-legend">
            <div v-for="(item, index) in chartLegend" :key="index" class="legend-item">
              <div class="legend-color" :style="{background: item.color, opacity: item.active ? 1 : 0.3}"></div>
              <el-checkbox v-model="item.active" @change="handleLegendChange">{{ item.name }}</el-checkbox>
            </div>
          </div>
        </template>
        <div class="chart-container">
          <div ref="trendChartRef" class="trend-chart"></div>
          <div class="chart-controls">
            <el-button-group>
              <el-button type="primary" size="small" @click="handleZoomIn">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
              <el-button type="primary" size="small" @click="handleZoomOut">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button type="primary" size="small" @click="handleResetZoom">
                <el-icon><FullScreen /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 预测指标面板 -->
    <div class="prediction-metrics">
      <el-row :gutter="20">
        <el-col :span="8" v-for="(metric, index) in predictionMetrics" :key="index">
          <DataPanel :title="metric.title">
            <div class="metric-card">
              <div class="metric-value" :class="metric.trend">{{ metric.value }}</div>
              <div class="metric-trend">
                <el-icon v-if="metric.trend === 'up'"><CaretTop /></el-icon>
                <el-icon v-else-if="metric.trend === 'down'"><CaretBottom /></el-icon>
                <el-icon v-else><Connection /></el-icon>
                {{ metric.trendValue }}
              </div>
            </div>
          </DataPanel>
        </el-col>
      </el-row>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="模型正常" />
        <StatusIndicator type="warning" label="数据分析中" />
        <StatusIndicator type="normal" label="自动更新已开启" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  TrendCharts,
  Refresh,
  ZoomIn,
  ZoomOut,
  FullScreen,
  CaretTop,
  CaretBottom,
  Connection,
  Setting
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import DataPanel from './components/DataPanel.vue';
import StatusIndicator from './components/StatusIndicator.vue';

// 页面摘要数据
const activeModels = ref(3);
const avgAccuracy = ref(87);

// 类型定义
interface PestLegendItem {
  name: string;
  color: string;
  active: boolean;
}

interface PredictionMetric {
  title: string;
  value: string;
  trend: 'up' | 'down' | 'stable';
  trendValue: string;
}

interface ModelMetric {
  name: string;
  value: number;
}

interface ModelUpdate {
  date: string;
  content: string;
}

interface PredictionModel {
  id: string;
  name: string;
  description: string;
  algorithmType: string;
  trainingData: string;
  lastUpdated: string;
  metrics: ModelMetric[];
  updates: ModelUpdate[];
}

interface PestData {
  historical: (number | null)[];
  prediction: (number | null)[];
  confidenceUpper: (number | null)[];
  confidenceLower: (number | null)[];
}

interface PestDataCollection {
  [key: string]: PestData;
}

interface ChartData {
  dates: string[];
  pestData: PestDataCollection;
}

// 图表相关
const trendChartRef = ref<HTMLElement | null>(null);
let trendChart: echarts.ECharts | null = null;

// 添加 resizeHandler 引用
let resizeHandler: (() => void) | null = null;

// 模型和预测设置
const modelForm = reactive({
  pestType: 'all',
  modelType: 'arima',
  predictionPeriod: 30,
  confidenceLevel: 0.95,
  includeWeather: true,
  includeHistory: true,
  loading: false
});

// 虫害类型选项
const pestTypeOptions = [
  { value: 'all', label: '所有虫害' },
  { value: 'aphid', label: '蚜虫' },
  { value: 'bollworm', label: '棉铃虫' },
  { value: 'leafminer', label: '潜叶蛾' },
  { value: 'whitefly', label: '粉虱' },
  { value: 'thrips', label: '蓟马' }
];

// 预测模型选项
const modelTypeOptions = [
  { value: 'arima', label: 'ARIMA时间序列模型' },
  { value: 'lstm', label: 'LSTM深度学习模型' },
  { value: 'prophet', label: 'Prophet预测模型' },
  { value: 'xgboost', label: 'XGBoost集成模型' }
];

// 虫害爆发风险级别
const riskLevels = reactive([
  { level: '高风险', count: 3, color: '#f56c6c' },
  { level: '中风险', count: 8, color: '#e6a23c' },
  { level: '低风险', count: 14, color: '#67c23a' }
]);

// 区域爆发概率统计
const regionStats = reactive([
  { region: '华北区域', probability: 0.78, trend: 'up' },
  { region: '东北区域', probability: 0.45, trend: 'down' },
  { region: '华东区域', probability: 0.62, trend: 'up' },
  { region: '华中区域', probability: 0.53, trend: 'stable' },
  { region: '华南区域', probability: 0.39, trend: 'down' },
  { region: '西南区域', probability: 0.28, trend: 'stable' },
  { region: '西北区域', probability: 0.15, trend: 'down' }
]);

// 虫害历史数据统计
const pestHistoryStats = reactive({
  total: 1245,
  thisMonth: 152,
  lastMonth: 128,
  growthRate: 18.75
});

// 图表图例
const chartLegend = ref<PestLegendItem[]>([
  { name: '蚜虫', color: '#3b82f6', active: true },
  { name: '飞虱', color: '#10b981', active: true },
  { name: '螟虫', color: '#f59e0b', active: true },
  { name: '叶螨', color: '#ef4444', active: false },
  { name: '粘虫', color: '#8b5cf6', active: false }
]);

// 预测模型数据
const predictionModels: PredictionModel[] = [
  {
    id: 'lstm',
    name: 'LSTM时间序列预测模型',
    description: '基于长短期记忆神经网络的虫害爆发趋势预测模型，适用于复杂的时间序列数据分析',
    algorithmType: '神经网络 (LSTM)',
    trainingData: '2019年1月 - 2023年12月',
    lastUpdated: '2024年3月15日',
    metrics: [
      { name: '准确率', value: 87 },
      { name: '召回率', value: 82 },
      { name: 'F1分数', value: 84 }
    ],
    updates: [
      { date: '2024-03-15', content: '更新训练数据至2023年底' },
      { date: '2023-12-10', content: '优化模型参数，提高预测准确率' },
      { date: '2023-09-22', content: '增加环境因素变量，提高模型泛化能力' }
    ]
  },
  {
    id: 'arima',
    name: 'ARIMA季节性预测模型',
    description: '自回归积分移动平均模型，特别适合具有季节性变化特征的虫害爆发预测',
    algorithmType: '时间序列分析 (ARIMA)',
    trainingData: '2020年3月 - 2023年9月',
    lastUpdated: '2024年2月20日',
    metrics: [
      { name: '准确率', value: 81 },
      { name: '召回率', value: 78 },
      { name: 'F1分数', value: 79 }
    ],
    updates: [
      { date: '2024-02-20', content: '调整季节性参数' },
      { date: '2023-11-05', content: '修复数据预处理问题' },
      { date: '2023-08-17', content: '模型首次发布' }
    ]
  },
  {
    id: 'ensemble',
    name: '集成学习混合预测模型',
    description: '结合多种机器学习算法的优势，通过投票或加权方式得出综合预测结果',
    algorithmType: '集成学习 (Ensemble)',
    trainingData: '2018年5月 - 2023年12月',
    lastUpdated: '2024年4月2日',
    metrics: [
      { name: '准确率', value: 92 },
      { name: '召回率', value: 89 },
      { name: 'F1分数', value: 90 }
    ],
    updates: [
      { date: '2024-04-02', content: '加入XGBoost模型到集成中' },
      { date: '2024-01-15', content: '优化模型权重分配' },
      { date: '2023-10-28', content: '增加随机森林算法' }
    ]
  }
];

// 计算属性 - 当前选中的模型
const selectedModel = computed(() => {
  return predictionModels.find(model => model.id === modelForm.modelType) || predictionModels[0];
});

// 处理模型变更
const handleModelChange = () => {
  ElMessage.info(`已切换至${selectedModel.value.name}`);
  runPrediction();
};

// 处理图例变更
const handleLegendChange = () => {
  renderTrendChart();
};

// 处理图表缩放
const handleZoomIn = () => {
  trendChart?.dispatchAction({
    type: 'dataZoom',
    start: 20,
    end: 80
  });
};

const handleZoomOut = () => {
  trendChart?.dispatchAction({
    type: 'dataZoom',
    start: 0,
    end: 100
  });
};

const handleResetZoom = () => {
  trendChart?.dispatchAction({
    type: 'dataZoom',
    start: 0,
    end: 100
  });
};

// 运行预测
const runPrediction = () => {
  modelForm.loading = true;
  
  // 模拟API请求延迟
  setTimeout(() => {
    // 在实际应用中，这里会调用后端API
    console.log('运行预测，参数:', modelForm);
    
    // 重新初始化图表
    nextTick(() => {
      initChart();
      
      // 更新统计数据
      updateStatistics();
      
      // 显示成功消息
      ElMessage({
        message: '预测模型运行成功！',
        type: 'success'
      });
      
      modelForm.loading = false;
    });
  }, 1500);
};

// 重置设置
const resetSettings = () => {
  // 重置表单
  modelForm.pestType = 'all';
  modelForm.modelType = 'arima';
  modelForm.predictionPeriod = 30;
  modelForm.confidenceLevel = 0.95;
  modelForm.includeWeather = true;
  modelForm.includeHistory = true;
  
  // 显示消息
  ElMessage({
    message: '设置已重置为默认值',
    type: 'info'
  });
};

// 更新统计数据
const updateStatistics = () => {
  // 更新风险级别统计
  riskLevels[0].count = Math.floor(Math.random() * 5) + 1;
  riskLevels[1].count = Math.floor(Math.random() * 10) + 5;
  riskLevels[2].count = Math.floor(Math.random() * 15) + 10;
  
  // 更新区域概率
  regionStats.forEach(region => {
    region.probability = Math.random() * 0.5 + 0.3;
    region.trend = ['up', 'down', 'stable'][Math.floor(Math.random() * 3)];
  });
  
  // 对区域按概率排序
  regionStats.sort((a, b) => b.probability - a.probability);
};

// 获取趋势图标
const getTrendIcon = (trend: string) => {
  switch (trend) {
    case 'up':
      return 'el-icon-arrow-up';
    case 'down':
      return 'el-icon-arrow-down';
    default:
      return 'el-icon-minus';
  }
};

// 获取趋势颜色
const getTrendColor = (trend: string) => {
  switch (trend) {
    case 'up':
      return '#f56c6c';
    case 'down':
      return '#67c23a';
    default:
      return '#909399';
  }
};

// 导出预测报告
const exportReport = () => {
  ElMessage({
    message: '预测报告导出功能正在开发中...',
    type: 'info'
  });
};

// 预测指标数据
const predictionMetrics = ref<PredictionMetric[]>([
  {
    title: '最高爆发概率',
    value: '76%',
    trend: 'up',
    trendValue: '相比上月增加12%'
  },
  {
    title: '爆发高峰期',
    value: '预计6月15日',
    trend: 'stable',
    trendValue: '与去年同期相近'
  },
  {
    title: '影响农田比例',
    value: '约41%',
    trend: 'down',
    trendValue: '相比去年减少8%'
  }
]);

// 最后更新时间
const lastUpdateTime = ref(new Date());

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 生成预测图表数据
const generateChartData = (): ChartData => {
  // 模拟日期数据 - 过去3个月加上预测时间范围
  const dates: string[] = [];
  const now = new Date();
  
  // 过去3个月
  for (let i = 90; i >= 0; i--) {
    const d = new Date();
    d.setDate(now.getDate() - i);
    dates.push(d.toISOString().slice(0, 10));
  }
  
  // 未来预测时间
  for (let i = 1; i <= modelForm.predictionPeriod; i++) {
    const d = new Date();
    d.setDate(now.getDate() + i);
    dates.push(d.toISOString().slice(0, 10));
  }
  
  // 生成虫害数据
  const pestData: PestDataCollection = {};
  
  chartLegend.value.forEach(pest => {
    const historicalData: (number | null)[] = [];
    const predictionData: (number | null)[] = [];
    const confidenceUpperData: (number | null)[] = [];
    const confidenceLowerData: (number | null)[] = [];
    
    // 历史数据
    for (let i = 0; i < 91; i++) {
      const baseValue = Math.random() * 30 + (i % 30); // 添加一些周期性
      historicalData.push(baseValue);
      predictionData.push(null);
      confidenceUpperData.push(null);
      confidenceLowerData.push(null);
    }
    
    // 预测数据
    for (let i = 0; i < modelForm.predictionPeriod; i++) {
      const lastValue = historicalData[historicalData.length - 1] || 0;
      const newValue = lastValue + (Math.random() - 0.4) * 10;
      
      historicalData.push(null);
      predictionData.push(newValue > 0 ? newValue : 0);
      
      // 置信区间
      const confidenceRange = newValue * (1 - modelForm.confidenceLevel) * 2;
      confidenceUpperData.push(newValue + confidenceRange);
      confidenceLowerData.push(Math.max(0, newValue - confidenceRange));
    }
    
    pestData[pest.name] = {
      historical: historicalData,
      prediction: predictionData,
      confidenceUpper: confidenceUpperData,
      confidenceLower: confidenceLowerData
    };
  });
  
  return {
    dates,
    pestData
  };
};

// 渲染趋势图表
const renderTrendChart = () => {
  if (!trendChart) return;
  
  const options = {
    title: {
      text: '虫害爆发趋势预测',
      left: 'center',
      textStyle: {
        color: '#eee'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['历史数据', '预测数据'],
      top: 30,
      textStyle: {
        color: '#eee'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [...generateDateArray(12, -6), ...generateDateArray(6, 0, true)],
      axisLabel: {
        color: '#eee',
        formatter: (value: string) => {
          return value.substring(5); // 只显示月-日
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '虫害数量',
      nameTextStyle: {
        color: '#eee'
      },
      axisLabel: {
        color: '#eee'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: [
      {
        name: '历史数据',
        type: 'line',
        stack: '总量',
        smooth: true,
        lineStyle: {
          width: 2
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.2,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(55, 162, 255, 0.6)'
            },
            {
              offset: 1,
              color: 'rgba(55, 162, 255, 0.1)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: generateRandomData(12, 30, 200)
      },
      {
        name: '预测数据',
        type: 'line',
        stack: '总量',
        smooth: true,
        lineStyle: {
          width: 2,
          type: 'dashed'
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.2,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(255, 144, 70, 0.6)'
            },
            {
              offset: 1,
              color: 'rgba(255, 144, 70, 0.1)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: generateRandomData(6, 100, 300)
      }
    ],
    animation: true,
    animationDuration: 1000,
    backgroundColor: 'transparent'
  };
  
  trendChart.setOption(options);
};

// 生成日期数组
const generateDateArray = (count: number, monthOffset: number, isFuture = false) => {
  const result = [];
  const now = new Date();
  const startMonth = new Date(now.getFullYear(), now.getMonth() + monthOffset, 1);
  
  for (let i = 0; i < count; i++) {
    const date = new Date(startMonth);
    date.setDate(date.getDate() + i * Math.floor(30 / count));
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // 为未来日期添加标记
    let dateStr = `${date.getFullYear()}-${month}-${day}`;
    if (isFuture) {
      dateStr = `${dateStr} (预测)`;
    }
    result.push(dateStr);
  }
  
  return result;
};

// 生成随机数据
const generateRandomData = (count: number, min: number, max: number) => {
  const result = [];
  
  // 生成带趋势的随机数据
  let value = Math.floor(Math.random() * (max - min)) + min;
  
  for (let i = 0; i < count; i++) {
    // 添加一些波动，但保持整体趋势
    const change = Math.floor(Math.random() * 20) - 5;
    value = Math.max(min, Math.min(max, value + change));
    result.push(value);
  }
  
  return result;
};

// 初始化图表
const initChart = () => {
  // 确保DOM元素存在
  if (!trendChartRef.value) {
    console.error('图表容器引用不存在');
    return;
  }
  
  // 确保先销毁之前的实例，避免重复初始化
  if (trendChart) {
    trendChart.dispose();
    trendChart = null;
  }
  
  // 使用明确的类型转换
  const chartContainer = trendChartRef.value as HTMLDivElement;
  
  // 确保容器在DOM中并且可见
  if (!document.body.contains(chartContainer)) {
    console.error('图表容器不在DOM中');
    return;
  }
  
  // 强制设置容器样式，确保有宽高
  chartContainer.style.width = '100%';
  chartContainer.style.height = '300px';
  
  // 等待DOM更新
  setTimeout(() => {
    try {
      if (chartContainer.clientWidth === 0 || chartContainer.clientHeight === 0) {
        console.error(`图表容器尺寸为0，width: ${chartContainer.clientWidth}, height: ${chartContainer.clientHeight}`);
        return;
      }
      
      console.log(`初始化图表，容器尺寸: ${chartContainer.clientWidth} x ${chartContainer.clientHeight}`);
      
      trendChart = echarts.init(chartContainer);
      renderTrendChart();
      
      // 窗口大小变化时调整图表大小
      resizeHandler = () => {
        if (trendChart) {
          trendChart.resize();
        }
      };
      
      window.addEventListener('resize', resizeHandler);
      
      console.log('趋势图表初始化成功');
    } catch (error) {
      console.error('初始化图表失败:', error);
    }
  }, 100);
};

// 刷新数据
const refreshData = () => {
  renderTrendChart();
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

// 组件挂载和卸载
onMounted(() => {
  console.log('组件挂载，准备初始化图表');
  // 使用 nextTick 确保 DOM 已经渲染
  nextTick(() => {
    initChart();
  });
});

onUnmounted(() => {
  // 销毁图表实例
  if (trendChart) {
    console.log('组件卸载，销毁图表实例');
    trendChart.dispose();
    trendChart = null;
  }
  
  // 移除窗口大小变化监听
  if (resizeHandler) {
    window.removeEventListener('resize', resizeHandler);
    resizeHandler = null;
  }
});
</script>

<style scoped>
.outbreak-prediction {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 模型设置 */
.model-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.model-description {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.model-description h4 {
  margin: 0 0 10px;
  color: #3b82f6;
  font-size: 16px;
}

.model-description p {
  margin: 0;
  color: #e5e7eb;
  font-size: 14px;
  line-height: 1.5;
}

.slider-tooltip {
  color: #3b82f6;
}

/* 图表区域 */
.chart-panels {
  flex: 1;
  margin-top: 20px;
  min-height: 0;
}

.chart-container {
  width: 100%;
  height: 400px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 10px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.trend-chart {
  width: 100%;
  min-height: 300px;
  height: 100%;
  
  /* 添加调试样式，当图表为空时显示 */
  &:empty::before {
    content: "加载图表中...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
  }
}

.chart-controls {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* 预测指标面板 */
.prediction-metrics {
  margin-top: 20px;
}

.metric-card {
  text-align: center;
  padding: 15px;
}

.metric-title {
  font-size: 16px;
  color: #9ca3af;
  margin-bottom: 10px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
}

.metric-value.up {
  color: #10b981;
}

.metric-value.down {
  color: #ef4444;
}

.metric-value.stable {
  color: #3b82f6;
}

.metric-trend {
  font-size: 14px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: 20px;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .prediction-metrics .el-row {
    flex-direction: column;
  }
  
  .prediction-metrics .el-col {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .chart-legend {
    flex-wrap: wrap;
  }
}

.statistics-header {
  margin-bottom: 20px;
}

.overall-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  color: #409eff;
}

.risk-levels {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.risk-item {
  display: flex;
  align-items: center;
  padding: 10px;
}

.risk-badge {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.risk-label {
  font-size: 14px;
  color: #dcdfe6;
  margin-right: 8px;
}

.risk-count {
  font-size: 16px;
  font-weight: bold;
  color: #e4e7ed;
}

.region-stats {
  margin-top: 10px;
}

.region-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.region-name {
  width: 100px;
  font-size: 14px;
  color: #dcdfe6;
}

.region-probability {
  flex: 1;
  margin: 0 10px;
}

.region-trend {
  width: 20px;
  text-align: center;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 8px;
  cursor: pointer;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 5px;
}

.legend-name {
  font-size: 12px;
  color: #dcdfe6;
}

.legend-item.inactive .legend-name {
  color: #606266;
  text-decoration: line-through;
}

.legend-item.inactive .legend-color {
  opacity: 0.4;
}
</style> 