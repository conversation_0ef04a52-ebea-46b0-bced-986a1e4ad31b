<template>
  <div class="connection-status-container">
    <div class="status-header">
      <h3>连接状态</h3>
      <el-tag :type="connected ? 'success' : connecting ? 'warning' : 'danger'">
        {{ connected ? '已连接' : connecting ? '连接中' : '未连接' }}
      </el-tag>
    </div>

    <div class="status-details">
      <div class="detail-item">
        <span class="item-label">机器狗IP:</span>
        <span class="item-value">{{ robotIP }}</span>
      </div>
      <div class="detail-item">
        <span class="item-label">连接时间:</span>
        <span class="item-value">{{ connectionTime }}</span>
      </div>
    </div>

    <div class="log-section">
      <div class="log-header">
        <h4>连接日志</h4>
        <el-button 
          type="text" 
          size="small" 
          @click="$emit('clear-log')"
          :disabled="!logContent"
        >
          清空日志
        </el-button>
      </div>
      <div class="log-content" ref="logContentRef">
        <pre>{{ logContent || '无日志记录' }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';

const props = defineProps({
  connected: {
    type: Boolean,
    default: false
  },
  connecting: {
    type: Boolean,
    default: false
  },
  robotIP: {
    type: String,
    default: '-'
  },
  connectionTime: {
    type: String,
    default: '-'
  },
  logContent: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['clear-log']);

// 自动滚动日志到底部
const logContentRef = ref<HTMLElement | null>(null);

const scrollToBottom = () => {
  if (logContentRef.value) {
    logContentRef.value.scrollTop = logContentRef.value.scrollHeight;
  }
};

// 监听日志内容变化，自动滚动到底部
watch(() => props.logContent, () => {
  setTimeout(scrollToBottom, 50);
});

onMounted(() => {
  scrollToBottom();
});
</script>

<style scoped>
.connection-status-container {
  background: linear-gradient(135deg, rgba(10, 31, 24, 0.85), rgba(26, 58, 45, 0.95));
  border-radius: 12px;
  border: 1px solid rgba(46, 204, 113, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-header h3 {
  margin: 0;
  color: #e2e8f0;
  font-size: 18px;
  font-weight: 600;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.item-label {
  color: #94a3b8;
}

.item-value {
  color: #e2e8f0;
  font-family: monospace;
}

.log-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-header h4 {
  margin: 0;
  color: #e2e8f0;
  font-size: 16px;
  font-weight: 500;
}

.log-content {
  flex: 1;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 12px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
  color: #94a3b8;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

@media (max-width: 768px) {
  .connection-status-container {
    padding: 12px;
    border-radius: 8px;
  }
  
  .status-header h3 {
    font-size: 16px;
  }
  
  .status-details {
    padding: 8px;
  }
  
  .detail-item {
    font-size: 12px;
  }
  
  .log-header h4 {
    font-size: 14px;
  }
  
  .log-content {
    padding: 8px;
    font-size: 11px;
  }
}
</style> 