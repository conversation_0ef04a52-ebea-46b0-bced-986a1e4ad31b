// 登录页面专用样式

// 颜色变量
$primary-color: #00ffaa;
$primary-dark: #00aa83;
$bg-dark: #001041;
$bg-darker: #000820;
$text-light: #ffffff;
$text-secondary: rgba(255, 255, 255, 0.8);
$text-hint: rgba(255, 255, 255, 0.5);
$border-light: rgba(255, 255, 255, 0.1);
$border-highlight: rgba(0, 255, 170, 0.3);
$shadow-color: rgba(0, 0, 0, 0.3);
$glow-color: rgba(0, 255, 170, 0.2);

// 混合器
@mixin glassmorphism {
  background: rgba(0, 16, 65, 0.5);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(0, 255, 170, 0.1);
  box-shadow: 0 8px 32px $shadow-color,
              0 0 20px rgba(0, 255, 170, 0.1);
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin primary-gradient {
  background: linear-gradient(90deg, $primary-color, $primary-dark);
}

@mixin hover-lift {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 170, 0.2);
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

@keyframes sway {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(5deg);
  }
}

// 响应式断点
$breakpoint-sm: 480px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1440px; 