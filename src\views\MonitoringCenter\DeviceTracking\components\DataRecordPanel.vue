<!--
数据记录控制面板组件
功能：
1. WebSocket数据记录开关
2. 记录状态显示
3. 数据统计信息
4. 记录配置管理
-->

<template>
  <div class="data-record-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <div class="header-left">
        <el-icon class="header-icon"><DataAnalysis /></el-icon>
        <span class="header-title">数据记录</span>
      </div>
      <div class="header-right">
        <el-badge :value="recordCount" :max="9999" type="primary">
          <el-button
            :type="isRecording ? 'danger' : 'primary'"
            :icon="isRecording ? VideoPause : VideoPlay"
            @click="toggleRecording"
            size="small"
          >
            {{ isRecording ? '停止记录' : '开始记录' }}
          </el-button>
        </el-badge>
      </div>
    </div>

    <!-- 记录状态 -->
    <div class="record-status">
      <div class="status-item">
        <div class="status-indicator" :class="{ active: isRecording }">
          <div class="indicator-dot"></div>
        </div>
        <div class="status-info">
          <div class="status-label">记录状态</div>
          <div class="status-value" :class="{ recording: isRecording }">
            {{ isRecording ? '正在记录' : '已停止' }}
          </div>
        </div>
      </div>

      <div class="status-item">
        <el-icon class="status-icon"><Timer /></el-icon>
        <div class="status-info">
          <div class="status-label">记录时长</div>
          <div class="status-value">{{ recordingDuration }}</div>
        </div>
      </div>
    </div>

    <!-- 数据统计 -->
    <div class="data-statistics">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ statistics.totalRecords }}</div>
          <div class="stat-label">总记录数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ statistics.deviceCount }}</div>
          <div class="stat-label">设备数量</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ statistics.averageFrequency }}</div>
          <div class="stat-label">平均频率/分钟</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ statistics.dataSize }}</div>
          <div class="stat-label">数据大小</div>
        </div>
      </div>
    </div>

    <!-- 记录配置 -->
    <div class="record-config">
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="记录配置" name="config">
          <div class="config-form">
            <el-form :model="configForm" label-width="100px" size="small">
              <el-form-item label="最大记录数">
                <el-input-number
                  v-model="configForm.maxRecords"
                  :min="1000"
                  :max="100000"
                  :step="1000"
                  @change="updateRecordConfig"
                />
              </el-form-item>

              <el-form-item label="自动保存">
                <el-switch
                  v-model="configForm.autoSave"
                  @change="updateRecordConfig"
                />
              </el-form-item>

              <el-form-item label="保存间隔" v-if="configForm.autoSave">
                <el-select v-model="configForm.saveInterval" @change="updateRecordConfig">
                  <el-option label="1分钟" :value="1" />
                  <el-option label="5分钟" :value="5" />
                  <el-option label="10分钟" :value="10" />
                  <el-option label="30分钟" :value="30" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 操作按钮 -->
    <div class="panel-actions">
      <el-button
        type="warning"
        :icon="Delete"
        @click="handleClearRecords"
        size="small"
        :disabled="recordCount === 0"
      >
        清空记录
      </el-button>

      <el-button
        type="info"
        :icon="Refresh"
        @click="handleRefreshStats"
        size="small"
      >
        刷新统计
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  DataAnalysis,
  VideoPlay,
  VideoPause,
  Timer,
  Delete,
  Refresh
} from '@element-plus/icons-vue'
import { useDataRecording } from '../composables/useDataRecording'

// 组件属性
const props = defineProps<{
  dataRecordingInstance?: ReturnType<typeof useDataRecording>
}>()

// 使用数据记录功能 - 优先使用传入的实例，否则创建新实例
const dataRecording = props.dataRecordingInstance || useDataRecording()
const {
  config,
  isRecording,
  recordCount,
  recordingDuration,
  statistics,
  startRecording,
  stopRecording,
  toggleRecording,
  clearRecords,
  updateConfig
} = dataRecording

// 组件状态
const activeCollapse = ref<string[]>([])

// 配置表单
const configForm = reactive({
  maxRecords: config.maxRecords,
  autoSave: config.autoSave,
  saveInterval: config.saveInterval
})

// 监听配置变化，同步到表单
watch(config, (newConfig) => {
  configForm.maxRecords = newConfig.maxRecords
  configForm.autoSave = newConfig.autoSave
  configForm.saveInterval = newConfig.saveInterval
}, { deep: true })

/**
 * 更新记录配置
 */
const updateRecordConfig = () => {
  updateConfig({
    maxRecords: configForm.maxRecords,
    autoSave: configForm.autoSave,
    saveInterval: configForm.saveInterval
  })

  ElMessage({
    message: '记录配置已成功更新',
    type: 'success',
    duration: 2000
  })
}

/**
 * 清空记录确认
 */
const handleClearRecords = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要清空所有 ${recordCount.value} 条记录吗？此操作不可恢复。`,
      '清空记录确认',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    clearRecords()
  } catch {
    // 用户取消操作
  }
}

/**
 * 刷新统计信息
 */
const handleRefreshStats = () => {
  ElNotification({
    title: '统计已刷新',
    message: '数据统计信息已更新',
    type: 'success',
    duration: 2000
  })
}

// 暴露给父组件的方法
defineExpose({
  startRecording,
  stopRecording,
  toggleRecording,
  clearRecords
})
</script>

<style lang="scss" scoped>
.data-record-panel {
  background: transparent;
  border-radius: 0;
  padding: 20px;
  border: none;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(59, 130, 246, 0.15);

    .header-left {
      display: flex;
      align-items: center;
      gap: 10px;

      .header-icon {
        color: #3b82f6;
        font-size: 20px;
        padding: 8px;
        background: rgba(59, 130, 246, 0.1);
        border-radius: 8px;
      }

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #f3f4f6;
      }
    }
  }

  .record-status {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .status-item {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .status-indicator {
        position: relative;
        width: 12px;
        height: 12px;

        .indicator-dot {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background: #6b7280;
          transition: all 0.3s ease;
        }

        &.active .indicator-dot {
          background: #ef4444;
        }
      }

      .status-icon {
        color: #3b82f6;
        font-size: 16px;
      }

      .status-info {
        .status-label {
          font-size: 12px;
          color: #9ca3af;
          margin-bottom: 2px;
        }

        .status-value {
          font-size: 14px;
          font-weight: 500;
          color: #f3f4f6;

          &.recording {
            color: #ef4444;
          }
        }
      }
    }
  }

  .data-statistics {
    margin-bottom: 20px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 14px;

      .stat-item {
        text-align: center;
        padding: 16px 12px;
        background: rgba(31, 41, 55, 0.6);
        border-radius: 12px;
        border: 1px solid rgba(75, 85, 99, 0.3);
        position: relative;

        .stat-value {
          font-size: 20px;
          font-weight: 700;
          color: #3b82f6;
          margin-bottom: 6px;
        }

        .stat-label {
          font-size: 12px;
          color: #9ca3af;
          font-weight: 500;
        }
      }
    }
  }

  .record-config {
    margin-bottom: 16px;

    :deep(.el-collapse) {
      border: none;
      background: transparent;

      .el-collapse-item__header {
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 6px;
        padding: 0 12px;
        color: #f3f4f6;
        font-size: 14px;
      }

      .el-collapse-item__content {
        padding: 12px 0 0 0;
        border: none;
      }
    }

    .config-form {
      :deep(.el-form-item__label) {
        color: #d1d5db;
      }
    }
  }

  .panel-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}
</style>
