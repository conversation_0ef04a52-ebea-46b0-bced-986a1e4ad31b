/**
 * SVG图标加载工具
 * 该文件负责导入和注册SVG图标，供SvgIcon组件使用
 */

import { ref } from 'vue';

// 已加载的图标缓存
const loadedIcons = ref<Set<string>>(new Set());

// SVG图标导入路径映射
const iconMap: Record<string, () => Promise<any>> = {
  // 设备图标
  'device-sensor': () => import('@/assets/icons/device-sensor.svg?raw'),
  'device-camera': () => import('@/assets/icons/device-camera.svg?raw'),
  'device-controller': () => import('@/assets/icons/device-controller.svg?raw'),
  'device-gateway': () => import('@/assets/icons/device-gateway.svg?raw'),
  'device-unknown': () => import('@/assets/icons/device-unknown.svg?raw'),
  'device-none': () => import('@/assets/icons/device-none.svg?raw'),
  
  // 警报图标
  'alert-critical': () => import('@/assets/icons/alert-critical.svg?raw'),
  'alert-warning': () => import('@/assets/icons/alert-warning.svg?raw'),
  'alert-info': () => import('@/assets/icons/alert-info.svg?raw'),
  'no-alerts': () => import('@/assets/icons/no-alerts.svg?raw'),
  
  // 其他已有图标
  'drone': () => import('@/assets/icons/drone.svg?raw'),
  'robot-dog': () => import('@/assets/icons/robot-dog.svg?raw'),
  'ultrasonic': () => import('@/assets/icons/ultrasonic.svg?raw'),
  'insect-trap': () => import('@/assets/icons/insect-trap.svg?raw'),
};

/**
 * 检查图标是否存在
 * @param name 图标名称
 * @returns 图标是否存在
 */
export const hasIcon = (name: string): boolean => {
  return name in iconMap || loadedIcons.value.has(name);
};

/**
 * 加载SVG图标
 * @param name 图标名称
 * @returns Promise<boolean> 加载成功返回true，否则返回false
 */
export const loadIcon = async (name: string): Promise<boolean> => {
  // 如果图标已加载，直接返回true
  if (loadedIcons.value.has(name)) {
    return true;
  }
  
  // 如果图标不存在，返回false
  if (!(name in iconMap)) {
    console.warn(`Icon "${name}" not found in icon map.`);
    return false;
  }
  
  try {
    // 动态导入SVG内容
    const svg = await iconMap[name]();
    
    // 将SVG内容添加到DOM中
    if (typeof document !== 'undefined') {
      // 创建一个临时div来解析SVG内容
      const div = document.createElement('div');
      div.innerHTML = svg.default || svg;
      
      const svgElement = div.querySelector('svg');
      if (!svgElement) {
        console.error(`Invalid SVG content for icon "${name}"`);
        return false;
      }
      
      // 设置SVG属性
      svgElement.setAttribute('id', `icon-${name}`);
      svgElement.style.display = 'none';
      
      // 检查是否已有SVG符号容器
      let symbolsContainer = document.getElementById('svg-symbols');
      if (!symbolsContainer) {
        // 创建SVG符号容器
        symbolsContainer = document.createElement('div');
        symbolsContainer.id = 'svg-symbols';
        symbolsContainer.style.display = 'none';
        document.body.appendChild(symbolsContainer);
      }
      
      // 添加SVG到容器
      symbolsContainer.appendChild(svgElement);
      
      // 标记图标为已加载
      loadedIcons.value.add(name);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Failed to load icon "${name}":`, error);
    return false;
  }
};

/**
 * 注册多个图标
 * @param names 图标名称数组
 */
export const registerIcons = async (names: string[]): Promise<void> => {
  const promises = names.map(name => loadIcon(name));
  await Promise.all(promises);
};

// 预加载常用图标
export const preloadCommonIcons = (): void => {
  if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
      registerIcons([
        'device-sensor',
        'device-camera',
        'device-controller',
        'device-gateway',
        'device-unknown',
        'device-none',
        'alert-critical',
        'alert-warning',
        'alert-info',
        'no-alerts',
      ]);
    });
  }
};

export default {
  hasIcon,
  loadIcon,
  registerIcons,
  preloadCommonIcons,
}; 