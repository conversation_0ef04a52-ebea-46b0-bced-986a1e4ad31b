import axios from 'axios'
import { ElMessage } from 'element-plus'
import envConfig from '@/config/env'

// 创建专门用于AI API的axios实例
const aiRequest = axios.create({
  baseURL: envConfig.apiBaseUrl,
  timeout: envConfig.aiApiTimeout, // AI API超时时间，长文本TTS需要更多时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// AI API响应拦截器
aiRequest.interceptors.response.use(
  response => {
    // 直接返回响应数据
    return response.data
  },
  error => {
    console.error('AI API请求失败:', error)

    if (error.response) {
      const errorMsg = error.response.data?.error || '服务器错误'
      ElMessage.error(`AI服务错误: ${errorMsg}`)
    } else if (error.request) {
      ElMessage.error('无法连接到AI服务，请检查网络连接')
    } else {
      ElMessage.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

// AI聊天相关的API接口
export interface ChatRequest {
  message: string
  session_id?: string
  new_conversation?: boolean
  voice_id?: string
  generate_audio?: boolean
}

export interface ChatResponse {
  success: boolean
  answer: string
  session_id: string
  conversation_id: string
  audio_available?: boolean
  audio_data?: string
  audio_url?: string
  audio_format?: string
  audio_size?: number
}

export interface Voice {
  id: string
  name: string
}

export interface VoicesResponse {
  success: boolean
  voices: Voice[]
  current_voice: string
}

export interface StatusResponse {
  success: boolean
  paddlespeech_available: boolean
  pyttsx3_available: boolean
  tts_ready: boolean
  asr_available?: boolean
  asr_ready?: boolean
  gpu_available: boolean
  active_sessions: number
}

export interface NewSessionResponse {
  success: boolean
  session_id: string
}

export interface SpeechToTextResponse {
  success: boolean
  text?: string
  error?: string
  processing_time?: {
    total: number
    asr: number
  }
}

// 基础聊天接口 - 只返回文字
export const sendChatMessage = async (data: ChatRequest): Promise<ChatResponse> => {
  return await aiRequest.post('/api/chat', data)
}

// 语音合成接口 - 将文本转为语音文件
export const textToSpeech = async (text: string, voice_id: string = '1') => {
  const response = await fetch(`${envConfig.apiBaseUrl || ''}/api/tts`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      text,
      voice_id
    })
  })

  if (!response.ok) {
    throw new Error('语音合成失败')
  }

  return response.blob()
}

// 语音识别接口 - 将音频转为文字
export const speechToText = async (audioBlob: Blob): Promise<string> => {
  const formData = new FormData()
  formData.append('audio', audioBlob, 'voice_input.wav')

  try {
    const response = await fetch(`${envConfig.apiBaseUrl || ''}/api/speech-to-text`, {
      method: 'POST',
      body: formData,
      // 语音识别可能需要更长时间
      signal: AbortSignal.timeout(60000) // 60秒超时
    })

    // 检查响应是否为JSON格式
    const contentType = response.headers.get('content-type')
    const isJson = contentType && contentType.includes('application/json')

    if (!response.ok) {
      let errorMessage = '语音识别失败'

      if (isJson) {
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorMessage
        } catch (jsonError) {
          console.warn('无法解析错误响应JSON:', jsonError)
        }
      } else {
        // 非JSON响应，可能是HTML错误页面
        const textResponse = await response.text()
        console.error('服务器返回非JSON响应:', textResponse.substring(0, 200))
        errorMessage = `服务器错误 (${response.status}): 请检查后端服务状态`
      }

      throw new Error(errorMessage)
    }

    if (!isJson) {
      throw new Error('服务器返回了非JSON格式的响应')
    }

    const result: SpeechToTextResponse = await response.json()

    if (result.success && result.text) {
      return result.text
    } else {
      throw new Error(result.error || '语音识别失败')
    }
  } catch (error) {
    console.error('语音识别API调用失败:', error)
    if (error instanceof Error) {
      throw error
    } else {
      throw new Error('语音识别服务异常')
    }
  }
}

// 聊天+语音一体化接口
export const chatWithSpeech = async (data: ChatRequest): Promise<ChatResponse> => {
  return await aiRequest.post('/api/chat-with-speech', data)
}

// 获取可用音色列表
export const getVoices = async (): Promise<VoicesResponse> => {
  return await aiRequest.get('/api/voices')
}

// 获取服务状态
export const getServiceStatus = async (): Promise<StatusResponse> => {
  return await aiRequest.get('/api/status')
}

// 创建新会话
export const createNewSession = async (): Promise<NewSessionResponse> => {
  return await aiRequest.post('/api/new-session')
}
