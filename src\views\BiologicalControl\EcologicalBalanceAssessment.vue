<!-- 
  EcologicalBalanceAssessment.vue
  生态平衡指数评估模块
  监控并评估农田生态系统的平衡状态，为生物防治措施提供依据
-->
<template>
  <div class="ecological-balance-assessment">
    <!-- 页面标题 -->
    <PageHeader
      title="生态平衡指数评估"
      description="监控并评估农田生态系统的平衡状态，为生物防治措施提供依据"
      icon="DataAnalysis"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ ecologicalIndex }}</span>
            <span class="summary-label">生态指数</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getIndexStatusText(ecologicalIndex) }}</span>
            <span class="summary-label">健康状态</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 评估周期选择器 -->
    <div class="period-selector">
      <el-radio-group v-model="assessmentPeriod" size="small">
        <el-radio-button label="week">近一周</el-radio-button>
        <el-radio-button label="month">近一月</el-radio-button>
        <el-radio-button label="quarter">近一季</el-radio-button>
        <el-radio-button label="year">近一年</el-radio-button>
      </el-radio-group>
    </div>
    
    <!-- 指标卡片区域 -->
    <div class="metrics-panels">
      <!-- 生物多样性指数 -->
      <DataPanel title="生物多样性指数">
        <template #actions>
          <el-tag 
            :type="ecologicalMetrics[0].value >= 80 ? 'success' : 
                  ecologicalMetrics[0].value >= 60 ? 'warning' : 'danger'" 
            effect="dark" 
            size="small"
          >
            {{ ecologicalMetrics[0].value >= 80 ? '丰富' : 
               ecologicalMetrics[0].value >= 60 ? '一般' : '贫乏' }}
          </el-tag>
        </template>
        <div class="metric-data">
          <div class="current-values">
            <div class="value-item">
              <div class="value-label">当前指数</div>
              <div class="value-number">{{ ecologicalMetrics[0].value }}{{ ecologicalMetrics[0].unit }}</div>
            </div>
            <div class="trend-indicator" :class="getTrendClass(ecologicalMetrics[0].trend)">
              <el-icon v-if="ecologicalMetrics[0].trend === 'up'"><ArrowUp /></el-icon>
              <el-icon v-else-if="ecologicalMetrics[0].trend === 'down'"><ArrowDown /></el-icon>
              <el-icon v-else><Minus /></el-icon>
            </div>
          </div>
          <div ref="biodiversityChart" class="chart-container"></div>
          <div class="metric-description">
            {{ ecologicalMetrics[0].description }}
          </div>
        </div>
      </DataPanel>
      
      <!-- 土壤健康评分 -->
      <DataPanel title="土壤健康评分">
        <template #actions>
          <el-tag 
            :type="ecologicalMetrics[1].value >= 80 ? 'success' : 
                  ecologicalMetrics[1].value >= 60 ? 'warning' : 'danger'" 
            effect="dark" 
            size="small"
          >
            {{ ecologicalMetrics[1].value >= 80 ? '健康' : 
               ecologicalMetrics[1].value >= 60 ? '一般' : '不健康' }}
          </el-tag>
        </template>
        <div class="metric-data">
          <div class="current-values">
            <div class="value-item">
              <div class="value-label">当前评分</div>
              <div class="value-number">{{ ecologicalMetrics[1].value }}{{ ecologicalMetrics[1].unit }}</div>
            </div>
            <div class="trend-indicator" :class="getTrendClass(ecologicalMetrics[1].trend)">
              <el-icon v-if="ecologicalMetrics[1].trend === 'up'"><ArrowUp /></el-icon>
              <el-icon v-else-if="ecologicalMetrics[1].trend === 'down'"><ArrowDown /></el-icon>
              <el-icon v-else><Minus /></el-icon>
            </div>
          </div>
          <div ref="soilHealthChart" class="chart-container"></div>
          <div class="metric-description">
            {{ ecologicalMetrics[1].description }}
          </div>
        </div>
      </DataPanel>
      
      <!-- 水质污染指数 -->
      <DataPanel title="水质污染指数">
        <template #actions>
          <el-tag 
            :type="ecologicalMetrics[2].value <= 20 ? 'success' : 
                  ecologicalMetrics[2].value <= 40 ? 'warning' : 'danger'" 
            effect="dark" 
            size="small"
          >
            {{ ecologicalMetrics[2].value <= 20 ? '清洁' : 
               ecologicalMetrics[2].value <= 40 ? '轻度污染' : '严重污染' }}
          </el-tag>
        </template>
        <div class="metric-data">
          <div class="current-values">
            <div class="value-item">
              <div class="value-label">污染程度</div>
              <div class="value-number">{{ ecologicalMetrics[2].value }}{{ ecologicalMetrics[2].unit }}</div>
            </div>
            <div class="trend-indicator" :class="getTrendClass(ecologicalMetrics[2].trend === 'down' ? 'up' : 'down')">
              <el-icon v-if="ecologicalMetrics[2].trend === 'down'"><ArrowUp /></el-icon>
              <el-icon v-else-if="ecologicalMetrics[2].trend === 'up'"><ArrowDown /></el-icon>
              <el-icon v-else><Minus /></el-icon>
            </div>
          </div>
          <div ref="waterQualityChart" class="chart-container"></div>
          <div class="metric-description">
            {{ ecologicalMetrics[2].description }}
          </div>
        </div>
      </DataPanel>
      
      <!-- 天敌丰富度 -->
      <DataPanel title="天敌丰富度">
        <template #actions>
          <el-tag 
            :type="ecologicalMetrics[3].value >= 70 ? 'success' : 
                  ecologicalMetrics[3].value >= 50 ? 'warning' : 'danger'" 
            effect="dark" 
            size="small"
          >
            {{ ecologicalMetrics[3].value >= 70 ? '丰富' : 
               ecologicalMetrics[3].value >= 50 ? '一般' : '不足' }}
          </el-tag>
        </template>
        <div class="metric-data">
          <div class="current-values">
            <div class="value-item">
              <div class="value-label">当前丰富度</div>
              <div class="value-number">{{ ecologicalMetrics[3].value }}{{ ecologicalMetrics[3].unit }}</div>
            </div>
            <div class="trend-indicator" :class="getTrendClass(ecologicalMetrics[3].trend)">
              <el-icon v-if="ecologicalMetrics[3].trend === 'up'"><ArrowUp /></el-icon>
              <el-icon v-else-if="ecologicalMetrics[3].trend === 'down'"><ArrowDown /></el-icon>
              <el-icon v-else><Minus /></el-icon>
            </div>
          </div>
          <div ref="predatorChart" class="chart-container"></div>
          <div class="metric-description">
            {{ ecologicalMetrics[3].description }}
          </div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="生态健康" />
        <StatusIndicator type="warning" label="需要干预" />
        <StatusIndicator type="error" label="系统失衡" />
        <StatusIndicator type="normal" label="数据分析中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  DataAnalysis,
  Refresh,
  ArrowUp,
  ArrowDown,
  Minus,
  InfoFilled,
  Download,
  Document,
  ArrowRight
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue';
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue';
import DataPanel from '../DeviceManagement/components/DataPanel.vue';

// 生态平衡指数数据
const ecologicalIndex = ref(78); // 当前生态平衡指数
const lastUpdateTime = ref(new Date());

// 评估周期
const assessmentPeriod = ref('month'); // 'week', 'month', 'quarter', 'year'

// 生态指标数据
const ecologicalMetrics = ref([
  {
    id: 1,
    name: '生物多样性指数',
    value: 82,
    unit: '%',
    trend: 'up',
    chartData: [75, 77, 80, 82],
    description: '衡量农田中生物种类的丰富度和均匀度。当前生态系统中记录有42种昆虫和28种植物，生物多样性较高。'
  },
  {
    id: 2,
    name: '土壤健康评分',
    value: 76,
    unit: '分',
    trend: 'up',
    chartData: [70, 72, 75, 76],
    description: '评估土壤有机质含量、微生物活性和肥力。土壤有机质含量3.8%，氮磷钾元素均衡，pH值在6.2-6.8之间。'
  },
  {
    id: 3,
    name: '水质污染指数',
    value: 15,
    unit: '%',
    trend: 'down',
    chartData: [25, 20, 18, 15],
    description: '测量水体中农药、化肥等污染物的含量。与化学农药使用期相比，当前农药残留降低85%，水生生物多样性提高40%。'
  },
  {
    id: 4,
    name: '天敌丰富度',
    value: 68,
    unit: '%',
    trend: 'up',
    chartData: [60, 63, 65, 68],
    description: '衡量农田中天敌昆虫的种类和数量。当前系统中捕食性天敌昆虫数量稳定增长，捕食者与猎物比例合理。'
  }
]);

// 图表引用
const biodiversityChart = ref<HTMLElement | null>(null);
const soilHealthChart = ref<HTMLElement | null>(null);
const waterQualityChart = ref<HTMLElement | null>(null);
const predatorChart = ref<HTMLElement | null>(null);

// 图表实例
let bioChart: echarts.ECharts | null = null;
let soilChart: echarts.ECharts | null = null;
let waterChart: echarts.ECharts | null = null;
let predChart: echarts.ECharts | null = null;

// 获取指标变化趋势样式
const getTrendClass = (trend: string) => {
  switch (trend) {
    case 'up':
      return 'trend-up';
    case 'down':
      return 'trend-down';
    case 'stable':
      return 'trend-stable';
    default:
      return '';
  }
};

// 根据指数值获取状态文本
const getIndexStatusText = (index: number) => {
  if (index >= 80) {
    return '良好';
  } else if (index >= 60) {
    return '一般';
  } else if (index >= 40) {
    return '较差';
  } else {
    return '恶劣';
  }
};

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 刷新数据
const refreshData = () => {
  // 模拟数据更新
  ecologicalIndex.value = Math.floor(Math.random() * 20) + 65; // 随机65-85之间
  
  // 更新生态指标数据
  ecologicalMetrics.value.forEach((metric, index) => {
    // 随机更新值，保持在合理范围内
    const random = Math.floor(Math.random() * 6) - 2; // -2到3之间的随机数
    if (index === 2) { // 水质污染指数应该越低越好
      metric.value = Math.max(5, Math.min(30, metric.value + random));
    } else {
      metric.value = Math.max(50, Math.min(95, metric.value + random));
    }
    
    // 更新图表数据
    metric.chartData.push(metric.value);
    if (metric.chartData.length > 4) {
      metric.chartData.shift();
    }
    
    // 更新趋势
    const newValue = metric.chartData[metric.chartData.length - 1];
    const oldValue = metric.chartData[0];
    if (index === 2) { // 水质污染指数应该越低越好
      if (newValue < oldValue) {
        metric.trend = 'down';
      } else if (newValue > oldValue) {
        metric.trend = 'up';
      } else {
        metric.trend = 'stable';
      }
    } else {
      if (newValue > oldValue) {
        metric.trend = 'up';
      } else if (newValue < oldValue) {
        metric.trend = 'down';
      } else {
        metric.trend = 'stable';
      }
    }
  });
  
  // 更新图表
  updateCharts();
  
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

// 更新所有图表
const updateCharts = () => {
  if (bioChart) {
    bioChart.setOption({
      series: [{
        data: ecologicalMetrics.value[0].chartData
      }]
    });
  }
  
  if (soilChart) {
    soilChart.setOption({
      series: [{
        data: ecologicalMetrics.value[1].chartData
      }]
    });
  }
  
  if (waterChart) {
    waterChart.setOption({
      series: [{
        data: ecologicalMetrics.value[2].chartData
      }]
    });
  }
  
  if (predChart) {
    predChart.setOption({
      series: [{
        data: ecologicalMetrics.value[3].chartData
      }]
    });
  }
};

// 初始化生物多样性图表
const initBiodiversityChart = () => {
  if (biodiversityChart.value) {
    bioChart = echarts.init(biodiversityChart.value as HTMLDivElement);
    
    const option = {
      grid: {
        top: 10,
        bottom: 20,
        left: 50,
        right: 20
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br />{a}: {c}%',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#10b981',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      xAxis: {
        type: 'category',
        data: ['第一周', '第二周', '第三周', '第四周'],
        axisLabel: {
          color: '#9ca3af',
          fontSize: 10
        },
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        }
      },
      yAxis: {
        type: 'value',
        min: 50,
        max: 100,
        axisLabel: {
          color: '#9ca3af',
          formatter: '{value}%'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#10b981'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      },
      series: [
        {
          name: '生物多样性',
          type: 'line',
          data: ecologicalMetrics.value[0].chartData,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#10b981',
            shadowColor: 'rgba(16, 185, 129, 0.3)',
            shadowBlur: 10
          },
          itemStyle: {
            color: '#10b981',
            borderColor: '#1f2937',
            borderWidth: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(16, 185, 129, 0.5)' },
              { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
            ])
          }
        }
      ]
    };

    bioChart.setOption(option);
    
    window.addEventListener('resize', () => {
      bioChart?.resize();
    });
  }
};

// 初始化土壤健康图表
const initSoilHealthChart = () => {
  if (soilHealthChart.value) {
    soilChart = echarts.init(soilHealthChart.value as HTMLDivElement);
    
    const option = {
      grid: {
        top: 10,
        bottom: 20,
        left: 50,
        right: 20
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br />{a}: {c}分',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      xAxis: {
        type: 'category',
        data: ['第一周', '第二周', '第三周', '第四周'],
        axisLabel: {
          color: '#9ca3af',
          fontSize: 10
        },
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        }
      },
      yAxis: {
        type: 'value',
        min: 50,
        max: 100,
        axisLabel: {
          color: '#9ca3af',
          formatter: '{value}分'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#3b82f6'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      },
      series: [
        {
          name: '土壤健康',
          type: 'line',
          data: ecologicalMetrics.value[1].chartData,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#3b82f6',
            shadowColor: 'rgba(59, 130, 246, 0.3)',
            shadowBlur: 10
          },
          itemStyle: {
            color: '#3b82f6',
            borderColor: '#1f2937',
            borderWidth: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(59, 130, 246, 0.5)' },
              { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
            ])
          }
        }
      ]
    };

    soilChart.setOption(option);
    
    window.addEventListener('resize', () => {
      soilChart?.resize();
    });
  }
};

// 初始化水质污染图表
const initWaterQualityChart = () => {
  if (waterQualityChart.value) {
    waterChart = echarts.init(waterQualityChart.value as HTMLDivElement);
    
    const option = {
      grid: {
        top: 10,
        bottom: 20,
        left: 50,
        right: 20
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br />{a}: {c}%',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#ef4444',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      xAxis: {
        type: 'category',
        data: ['第一周', '第二周', '第三周', '第四周'],
        axisLabel: {
          color: '#9ca3af',
          fontSize: 10
        },
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 50,
        axisLabel: {
          color: '#9ca3af',
          formatter: '{value}%'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#ef4444'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      },
      series: [
        {
          name: '污染指数',
          type: 'line',
          data: ecologicalMetrics.value[2].chartData,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#ef4444',
            shadowColor: 'rgba(239, 68, 68, 0.3)',
            shadowBlur: 10
          },
          itemStyle: {
            color: '#ef4444',
            borderColor: '#1f2937',
            borderWidth: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(239, 68, 68, 0.5)' },
              { offset: 1, color: 'rgba(239, 68, 68, 0.1)' }
            ])
          }
        }
      ]
    };

    waterChart.setOption(option);
    
    window.addEventListener('resize', () => {
      waterChart?.resize();
    });
  }
};

// 初始化天敌丰富度图表
const initPredatorChart = () => {
  if (predatorChart.value) {
    predChart = echarts.init(predatorChart.value as HTMLDivElement);
    
    const option = {
      grid: {
        top: 10,
        bottom: 20,
        left: 50,
        right: 20
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br />{a}: {c}%',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#f59e0b',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      xAxis: {
        type: 'category',
        data: ['第一周', '第二周', '第三周', '第四周'],
        axisLabel: {
          color: '#9ca3af',
          fontSize: 10
        },
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        }
      },
      yAxis: {
        type: 'value',
        min: 50,
        max: 100,
        axisLabel: {
          color: '#9ca3af',
          formatter: '{value}%'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#f59e0b'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      },
      series: [
        {
          name: '天敌丰富度',
          type: 'line',
          data: ecologicalMetrics.value[3].chartData,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#f59e0b',
            shadowColor: 'rgba(245, 158, 11, 0.3)',
            shadowBlur: 10
          },
          itemStyle: {
            color: '#f59e0b',
            borderColor: '#1f2937',
            borderWidth: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(245, 158, 11, 0.5)' },
              { offset: 1, color: 'rgba(245, 158, 11, 0.1)' }
            ])
          }
        }
      ]
    };

    predChart.setOption(option);
    
    window.addEventListener('resize', () => {
      predChart?.resize();
    });
  }
};

// 数据更新计时器
let dataUpdateInterval: number | null = null;

onMounted(() => {
  // 初始化图表
  initBiodiversityChart();
  initSoilHealthChart();
  initWaterQualityChart();
  initPredatorChart();
  
  // 启动数据更新计时器
  dataUpdateInterval = window.setInterval(() => {
    // 小幅度随机波动数据
    ecologicalIndex.value = Math.max(60, Math.min(90, ecologicalIndex.value + (Math.random() * 6 - 3)));
    
    // 更新生态指标数据
    ecologicalMetrics.value.forEach((metric, index) => {
      const random = Math.floor(Math.random() * 4) - 2; // -2到1之间的随机数
      if (index === 2) { // 水质污染指数应该越低越好
        metric.value = Math.max(5, Math.min(30, metric.value + random));
      } else {
        metric.value = Math.max(50, Math.min(95, metric.value + random));
      }
    });
    
    lastUpdateTime.value = new Date();
  }, 60000); // 每60秒更新一次
});

onUnmounted(() => {
  // 清除计时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
  
  // 销毁图表实例
  if (bioChart) bioChart.dispose();
  if (soilChart) soilChart.dispose();
  if (waterChart) waterChart.dispose();
  if (predChart) predChart.dispose();
});
</script>

<style scoped>
.ecological-balance-assessment {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 评估周期选择器 */
.period-selector {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 指标卡片网格 */
.metrics-panels {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

/* 指标数据 */
.metric-data {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.current-values {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.value-item {
  text-align: center;
}

.value-label {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 5px;
}

.value-number {
  font-size: 24px;
  font-weight: 600;
  color: #e5e7eb;
}

.trend-indicator {
  font-size: 24px;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

.trend-stable {
  color: #9ca3af;
}

.chart-container {
  height: 200px;
  width: 100%;
  margin: 10px 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(31, 41, 55, 0.3);
  flex: 1;
}

.metric-description {
  margin-top: 10px;
  font-size: 14px;
  color: #9ca3af;
  line-height: 1.5;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .metrics-panels {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save
--> 