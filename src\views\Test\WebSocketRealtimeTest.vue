<template>
  <div class="websocket-realtime-test">
    <div class="test-header">
      <h2>WebSocket实时数据测试</h2>
      <p>测试WebSocket实时IMU数据获取功能</p>
    </div>

    <!-- 连接状态 -->
    <div class="connection-status">
      <div class="status-card">
        <h3>连接状态</h3>
        <div class="status-indicators">
          <div class="status-item">
            <span class="label">WebSocket:</span>
            <span class="status" :class="wsConnected ? 'connected' : 'disconnected'">
              {{ wsConnected ? '已连接' : '未连接' }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">数据流:</span>
            <span class="status" :class="getDataStatusClass()">
              {{ getDataStatusText() }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">错误次数:</span>
            <span class="error-count">{{ stats.errorCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="control-buttons">
      <el-button 
        type="primary" 
        @click="startDataStream"
        :disabled="isReceiving"
        :loading="isConnecting"
      >
        开始数据流
      </el-button>
      <el-button 
        type="danger" 
        @click="stopDataStream"
        :disabled="!isReceiving"
      >
        停止数据流
      </el-button>
      <el-button @click="clearData">清空数据</el-button>
      <el-button @click="exportData">导出数据</el-button>
    </div>

    <!-- 实时数据显示 -->
    <div class="data-display">
      <div class="data-section">
        <h3>实时IMU数据</h3>
        <div v-if="currentData" class="data-grid">
          <!-- 姿态角度 -->
          <div class="data-item">
            <h4>姿态角度</h4>
            <div class="attitude-data">
              <div class="angle-item">
                <span class="label">Roll:</span>
                <span class="value">{{ currentData.attitude.roll.toFixed(2) }}°</span>
              </div>
              <div class="angle-item">
                <span class="label">Pitch:</span>
                <span class="value">{{ currentData.attitude.pitch.toFixed(2) }}°</span>
              </div>
              <div class="angle-item">
                <span class="label">Yaw:</span>
                <span class="value">{{ currentData.attitude.yaw.toFixed(2) }}°</span>
              </div>
            </div>
          </div>

          <!-- 电池状态 -->
          <div class="data-item">
            <h4>电池状态</h4>
            <div class="battery-data">
              <div class="battery-item">
                <span class="label">电量:</span>
                <span class="value">{{ currentData.battery.soc }}%</span>
              </div>
              <div class="battery-item">
                <span class="label">电流:</span>
                <span class="value">{{ currentData.battery.current.toFixed(2) }}A</span>
              </div>
              <div class="battery-item">
                <span class="label">循环:</span>
                <span class="value">{{ currentData.battery.cycle }}</span>
              </div>
            </div>
          </div>

          <!-- 传感器数据 -->
          <div class="data-item">
            <h4>传感器数据</h4>
            <div class="sensor-data">
              <div class="sensor-item">
                <span class="label">温度:</span>
                <span class="value">{{ currentData.sensors.temperatureNtc1.toFixed(1) }}°C</span>
              </div>
              <div class="sensor-item">
                <span class="label">电压:</span>
                <span class="value">{{ currentData.sensors.powerVoltage.toFixed(2) }}V</span>
              </div>
              <div class="sensor-item">
                <span class="label">电机最高温:</span>
                <span class="value">{{ currentData.sensors.motorMaxTemp.toFixed(1) }}°C</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-data">
          <p>暂无数据</p>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="stats-section">
        <h3>数据统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="label">总记录数:</span>
            <span class="value">{{ stats.totalRecords }}</span>
          </div>
          <div class="stat-item">
            <span class="label">数据频率:</span>
            <span class="value">{{ stats.dataRate.toFixed(1) }} Hz</span>
          </div>
          <div class="stat-item">
            <span class="label">连接时长:</span>
            <span class="value">{{ formatDuration(stats.connectionDuration) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">最后更新:</span>
            <span class="value">{{ formatLastUpdate() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据历史 -->
    <div class="history-section">
      <h3>数据历史 (最近10条)</h3>
      <div class="history-list">
        <div 
          v-for="record in recentHistory" 
          :key="record.id"
          class="history-item"
        >
          <div class="timestamp">{{ formatTime(record.timestamp) }}</div>
          <div class="data-summary">
            Roll: {{ record.attitude.roll.toFixed(1) }}° | 
            Pitch: {{ record.attitude.pitch.toFixed(1) }}° | 
            Yaw: {{ record.attitude.yaw.toFixed(1) }}° | 
            电量: {{ record.battery.soc }}%
          </div>
          <div class="quality-score" :class="getQualityClass(record.quality)">
            质量: {{ record.quality }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useIMUData } from '@/views/MonitoringCenter/DeviceTracking/composables/useIMUData'

// 使用IMU数据管理Hook
const {
  connectionStatus,
  isConnected,
  isReceiving,
  currentData,
  dataHistory,
  stats,
  lastError,
  startPolling,
  stopPolling,
  clearHistory,
  resetConnection
} = useIMUData()

// 本地状态
const wsConnected = ref(false)
const isConnecting = computed(() => connectionStatus.value === 'connecting')

// 最近历史记录（最多10条）
const recentHistory = computed(() => {
  return dataHistory.slice(-10).reverse()
})

// 获取数据状态样式类
const getDataStatusClass = () => {
  switch (connectionStatus.value) {
    case 'receiving': return 'connected'
    case 'connected': return 'connected'
    case 'connecting': return 'connecting'
    case 'error': return 'error'
    default: return 'disconnected'
  }
}

// 获取数据状态文本
const getDataStatusText = () => {
  switch (connectionStatus.value) {
    case 'receiving': return '正在接收'
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'error': return '错误'
    default: return '未连接'
  }
}

// 格式化持续时间
const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds.toFixed(0)}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds.toFixed(0)}秒`
}

// 格式化最后更新时间
const formatLastUpdate = (): string => {
  if (!stats.value.lastUpdateTime) return '无'
  return new Date(stats.value.lastUpdateTime).toLocaleTimeString()
}

// 格式化时间
const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}

// 获取质量分数样式类
const getQualityClass = (score: number): string => {
  if (score >= 80) return 'good'
  if (score >= 60) return 'warning'
  return 'error'
}

// 开始数据流
const startDataStream = async () => {
  try {
    const success = await startPolling()
    if (success) {
      ElMessage.success('数据流已启动')
    }
  } catch (error) {
    ElMessage.error('启动数据流失败')
  }
}

// 停止数据流
const stopDataStream = async () => {
  try {
    await stopPolling()
    ElMessage.info('数据流已停止')
  } catch (error) {
    ElMessage.error('停止数据流失败')
  }
}

// 清空数据
const clearData = () => {
  clearHistory()
  ElMessage.success('数据已清空')
}

// 导出数据
const exportData = () => {
  if (dataHistory.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }

  const data = {
    exportTime: new Date().toISOString(),
    totalRecords: dataHistory.length,
    stats: stats.value,
    data: dataHistory
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `websocket-imu-data-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('数据导出成功')
}

// 生命周期
onMounted(() => {
  console.log('WebSocket实时数据测试页面已加载')
})

onUnmounted(() => {
  // 清理资源
  if (isReceiving.value) {
    stopPolling()
  }
  resetConnection()
})
</script>

<style lang="scss" scoped>
.websocket-realtime-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  .test-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      color: #2c3e50;
      margin-bottom: 10px;
    }

    p {
      color: #7f8c8d;
      font-size: 14px;
    }
  }

  .connection-status {
    margin-bottom: 20px;

    .status-card {
      background: #fff;
      border: 1px solid #e1e8ed;
      border-radius: 8px;
      padding: 20px;

      h3 {
        margin: 0 0 15px 0;
        color: #2c3e50;
      }

      .status-indicators {
        display: flex;
        gap: 30px;

        .status-item {
          display: flex;
          align-items: center;
          gap: 10px;

          .label {
            font-weight: 500;
            color: #5a6c7d;
          }

          .status {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;

            &.connected {
              background: #d4edda;
              color: #155724;
            }

            &.connecting {
              background: #fff3cd;
              color: #856404;
            }

            &.disconnected {
              background: #f8d7da;
              color: #721c24;
            }

            &.error {
              background: #f8d7da;
              color: #721c24;
            }
          }

          .error-count {
            color: #dc3545;
            font-weight: 500;
          }
        }
      }
    }
  }

  .control-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
  }

  .data-display {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;

    .data-section, .stats-section {
      background: #fff;
      border: 1px solid #e1e8ed;
      border-radius: 8px;
      padding: 20px;

      h3 {
        margin: 0 0 15px 0;
        color: #2c3e50;
      }

      .data-grid {
        display: grid;
        gap: 20px;

        .data-item {
          h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
          }

          .attitude-data, .battery-data, .sensor-data {
            display: grid;
            gap: 8px;

            .angle-item, .battery-item, .sensor-item {
              display: flex;
              justify-content: space-between;
              padding: 8px 12px;
              background: #f8f9fa;
              border-radius: 4px;

              .label {
                color: #6c757d;
                font-size: 13px;
              }

              .value {
                font-weight: 500;
                color: #2c3e50;
                font-family: monospace;
              }
            }
          }
        }
      }

      .no-data {
        text-align: center;
        color: #6c757d;
        padding: 40px 0;
      }

      .stats-grid {
        display: grid;
        gap: 12px;

        .stat-item {
          display: flex;
          justify-content: space-between;
          padding: 10px 12px;
          background: #f8f9fa;
          border-radius: 4px;

          .label {
            color: #6c757d;
            font-size: 13px;
          }

          .value {
            font-weight: 500;
            color: #2c3e50;
            font-family: monospace;
          }
        }
      }
    }
  }

  .history-section {
    background: #fff;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 20px;

    h3 {
      margin: 0 0 15px 0;
      color: #2c3e50;
    }

    .history-list {
      max-height: 300px;
      overflow-y: auto;

      .history-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 10px 12px;
        border-bottom: 1px solid #f1f3f4;
        font-size: 13px;

        &:last-child {
          border-bottom: none;
        }

        .timestamp {
          color: #6c757d;
          font-family: monospace;
          min-width: 80px;
        }

        .data-summary {
          flex: 1;
          color: #495057;
          font-family: monospace;
        }

        .quality-score {
          padding: 2px 8px;
          border-radius: 3px;
          font-size: 11px;
          font-weight: 500;

          &.good {
            background: #d4edda;
            color: #155724;
          }

          &.warning {
            background: #fff3cd;
            color: #856404;
          }

          &.error {
            background: #f8d7da;
            color: #721c24;
          }
        }
      }
    }
  }
}
</style>
