<template>
  <BaseLayout
    title="AI智能问答助手"
    theme="aiAssistant"
    themeColor="#6366f1"
    moduleIcon="ChatRound"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="智能问答"
        icon="ChatRound"
        route="/ai-assistant"
        :collapsed="isAsideCollapsed"
        themeColor="#6366f1"
      />
      <TechMenuItem
        title="历史会话"
        icon="Collection"
        route="/ai-assistant/history"
        :collapsed="isAsideCollapsed"
        themeColor="#6366f1"
      />
      <TechMenuItem
        title="农业知识库"
        icon="Document"
        route="/ai-assistant/knowledge"
        :collapsed="isAsideCollapsed"
        themeColor="#6366f1"
      />
    </template>

    <template #header-actions>
      <div class="ai-status-indicator">
        <div class="status-badge online">
          <span class="status-dot"></span>
          <span>AI系统在线</span>
        </div>
      </div>

      <TechActionButton
        type="primary"
        size="small"
        icon="Microphone"
        text="开始语音对话"
        @click="startVoiceChat"
      />
    </template>

    <router-view />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ChatRound,
  Collection,
  Document,
  ArrowLeft,
  Microphone
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'
import { getModuleTheme } from './components/layoutConfig'

const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 开始语音对话
const startVoiceChat = () => {
  ElMessage({
    message: '语音助手：正在启动语音对话模式',
    type: 'info',
    duration: 3000
  })

  // 这里可以添加实际的语音对话功能
}

// 组件挂载
onMounted(() => {
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<style scoped>
.ai-status-indicator {
  margin-right: 20px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  font-size: 14px;
  transition: all 0.3s ease;
}

.status-badge:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-2px);
}

.status-badge.online {
  color: #ffffff;
}

.status-dot {
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
  box-shadow: 0 0 10px #10b981;
  position: relative;
}

.status-dot::after {
  content: "";
  position: absolute;
  top: -4px;
  left: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #10b981;
  opacity: 0.5;
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.7);
    opacity: 0.7;
  }
  50% {
    transform: scale(1);
    opacity: 0.2;
  }
  100% {
    transform: scale(0.7);
    opacity: 0.7;
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .ai-status-indicator {
    display: none;
  }
}
</style>
