<template>
  <div class="chat-container">
    <div class="chat-header">
      <div class="header-title">
        <el-icon class="ai-icon"><ChatRound /></el-icon>
        智能农业AI助手
      </div>
      <div class="header-actions">
        <el-tooltip content="创建新会话" placement="bottom" :show-after="500">
          <el-button type="success" size="small" plain circle @click="$emit('create-session')">
            <el-icon><Plus /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="清空当前会话" placement="bottom" :show-after="500">
          <el-button 
            type="danger" 
            size="small" 
            plain 
            circle 
            :disabled="messages.length === 0"
            @click="$emit('clear-session')"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <div class="messages-container" ref="messagesContainer">
      <div class="welcome-message" v-if="messages.length === 0">
        <div class="welcome-icon">
          <img src="/ai-avatar.svg" alt="AI农业助手" />
        </div>
        <h2>欢迎使用智慧农业AI助手</h2>
        <p>您可以向我询问任何关于农业技术、病虫害防治、农作物管理等方面的问题</p>
        <div class="suggestion-chips">
          <div 
            v-for="(suggestion, index) in suggestions" 
            :key="index" 
            class="suggestion-chip"
            @click="handleSuggestionClick(suggestion)"
          >
            {{ suggestion }}
          </div>
        </div>
      </div>

      <div v-else class="messages-list">
        <template v-for="(message, index) in messages" :key="message.id">
          <message-bubble 
            :message="message"
            :isLastMessage="index === messages.length - 1"
          />
        </template>
        
        <div class="ai-thinking" v-if="loading">
          <div class="thinking-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div class="thinking-text">AI正在思考...</div>
        </div>
      </div>
    </div>
    
    <div class="chat-input-container">
      <chat-input @send="handleSendMessage" :disabled="loading" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from 'vue';
import { ChatRound, Plus, Delete } from '@element-plus/icons-vue';
import type { ChatMessage } from '@/types/chat';
import MessageBubble from './MessageBubble.vue';
import ChatInput from './ChatInput.vue';

const props = defineProps<{
  messages: ChatMessage[];
  loading?: boolean;
}>();

const emit = defineEmits<{
  (e: 'send-message', content: string): void;
  (e: 'create-session'): void;
  (e: 'clear-session'): void;
}>();

const messagesContainer = ref<HTMLElement | null>(null);

// 建议问题列表
const suggestions = [
  '智慧农业中有哪些常见的病虫害防治技术？',
  '如何使用无人机进行农田监测？',
  '有机农业种植有哪些关键技术？',
  '如何科学施肥以提高作物产量？'
];

// 当消息更新时，自动滚动到最新消息
watch(() => props.messages.length, () => {
  nextTick(() => {
    scrollToBottom();
  });
});

// 处理发送消息
const handleSendMessage = (content: string) => {
  emit('send-message', content);
};

// 处理建议点击
const handleSuggestionClick = (suggestion: string) => {
  emit('send-message', suggestion);
};

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 组件挂载后执行一次滚动
onMounted(() => {
  scrollToBottom();
});
</script>

<style lang="scss" scoped>
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  z-index: 5;
  
  .chat-header {
    height: 60px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 255, 170, 0.15);
    background: rgba(0, 21, 65, 0.4);
    backdrop-filter: blur(10px);
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      
      .ai-icon {
        color: #00ffaa;
        font-size: 20px;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
      
      :deep(.el-button) {
        transition: all 0.3s;
        
        &:hover {
          transform: scale(1.05);
        }
        
        &.is-disabled {
          opacity: 0.5;
        }
      }
    }
  }
  
  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    scroll-behavior: smooth;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 255, 170, 0.3);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background-color: rgba(0, 0, 0, 0.1);
    }
    
    .welcome-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(100% - 40px);
      text-align: center;
      padding: 20px;
      color: rgba(255, 255, 255, 0.9);
      
      .welcome-icon {
        margin-bottom: 20px;
        
        img {
          width: 80px;
          height: 80px;
          animation: pulse 2s infinite alternate;
        }
      }
      
      h2 {
        background: linear-gradient(90deg, #00ffaa, #1890ff);
        -webkit-background-clip: text;
        color: transparent;
        margin-bottom: 16px;
      }
      
      p {
        font-size: 14px;
        max-width: 400px;
        line-height: 1.5;
        margin-bottom: 30px;
        color: rgba(255, 255, 255, 0.7);
      }
      
      .suggestion-chips {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
        max-width: 600px;
        
        .suggestion-chip {
          background: rgba(0, 255, 170, 0.1);
          border: 1px solid rgba(0, 255, 170, 0.3);
          color: rgba(255, 255, 255, 0.9);
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 13px;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(0, 255, 170, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 255, 170, 0.2);
          }
        }
      }
    }
    
    .messages-list {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
    
    .ai-thinking {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 10px;
      
      .thinking-dots {
        display: flex;
        gap: 4px;
        
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #00ffaa;
          opacity: 0.6;
          animation: dot-pulse 1.5s infinite ease-in-out;
          
          &:nth-child(1) {
            animation-delay: 0s;
          }
          
          &:nth-child(2) {
            animation-delay: 0.2s;
          }
          
          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }
      
      .thinking-text {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
  
  .chat-input-container {
    padding: 15px 20px;
    border-top: 1px solid rgba(0, 255, 170, 0.15);
    background: rgba(0, 21, 65, 0.4);
    backdrop-filter: blur(10px);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: scale(0.97);
  }
  100% {
    opacity: 1;
    transform: scale(1.03);
  }
}

@keyframes dot-pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}
</style> 