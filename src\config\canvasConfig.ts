/**
 * Canvas配置管理
 * 根据屏幕尺寸和设备类型动态调整Canvas配置
 */

export interface CanvasSize {
  width: number;
  height: number;
  padding: number;
}

export interface ResponsiveCanvasConfig {
  desktop: CanvasSize;
  tablet: CanvasSize;
  mobile: CanvasSize;
  small: CanvasSize;
}

/**
 * 响应式Canvas配置
 */
export const RESPONSIVE_CANVAS_CONFIG: ResponsiveCanvasConfig = {
  // 桌面端 (>= 1200px)
  desktop: {
    width: 800,
    height: 500,
    padding: 50
  },
  
  // 平板端 (768px - 1199px)
  tablet: {
    width: 600,
    height: 400,
    padding: 40
  },
  
  // 移动端 (480px - 767px)
  mobile: {
    width: 400,
    height: 300,
    padding: 30
  },
  
  // 小屏幕 (< 480px)
  small: {
    width: 320,
    height: 240,
    padding: 20
  }
};

/**
 * 屏幕尺寸断点
 */
export const BREAKPOINTS = {
  SMALL: 480,
  MOBILE: 768,
  TABLET: 1200,
  DESKTOP: 1920
} as const;

/**
 * 根据屏幕尺寸获取Canvas配置
 */
export function getCanvasConfig(windowWidth: number, windowHeight: number): CanvasSize {
  // 根据屏幕宽度确定基础配置
  let baseConfig: CanvasSize;
  
  if (windowWidth >= BREAKPOINTS.TABLET) {
    baseConfig = RESPONSIVE_CANVAS_CONFIG.desktop;
  } else if (windowWidth >= BREAKPOINTS.MOBILE) {
    baseConfig = RESPONSIVE_CANVAS_CONFIG.tablet;
  } else if (windowWidth >= BREAKPOINTS.SMALL) {
    baseConfig = RESPONSIVE_CANVAS_CONFIG.mobile;
  } else {
    baseConfig = RESPONSIVE_CANVAS_CONFIG.small;
  }
  
  // 根据屏幕高度调整配置
  const adjustedConfig = { ...baseConfig };
  
  // 如果屏幕高度较小，进一步缩小Canvas高度
  if (windowHeight < 700) {
    adjustedConfig.height = Math.min(adjustedConfig.height, windowHeight * 0.4);
    adjustedConfig.padding = Math.max(20, adjustedConfig.padding * 0.8);
  } else if (windowHeight < 800) {
    adjustedConfig.height = Math.min(adjustedConfig.height, windowHeight * 0.5);
    adjustedConfig.padding = Math.max(25, adjustedConfig.padding * 0.9);
  }
  
  return adjustedConfig;
}

/**
 * 获取最适合的Canvas尺寸（基于容器尺寸）
 */
export function getOptimalCanvasSize(
  containerWidth: number, 
  containerHeight: number,
  windowWidth: number,
  windowHeight: number
): CanvasSize {
  // 获取基础配置
  const baseConfig = getCanvasConfig(windowWidth, windowHeight);
  
  // 确保Canvas不超过容器尺寸
  const maxWidth = containerWidth - baseConfig.padding * 2;
  const maxHeight = containerHeight - baseConfig.padding * 2;
  
  return {
    width: Math.min(baseConfig.width, maxWidth),
    height: Math.min(baseConfig.height, maxHeight),
    padding: baseConfig.padding
  };
}

/**
 * 检查是否为移动设备
 */
export function isMobileDevice(): boolean {
  return window.innerWidth < BREAKPOINTS.MOBILE;
}

/**
 * 检查是否为平板设备
 */
export function isTabletDevice(): boolean {
  return window.innerWidth >= BREAKPOINTS.MOBILE && window.innerWidth < BREAKPOINTS.TABLET;
}

/**
 * 检查是否为桌面设备
 */
export function isDesktopDevice(): boolean {
  return window.innerWidth >= BREAKPOINTS.TABLET;
}

/**
 * 获取设备类型
 */
export function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
  if (isMobileDevice()) return 'mobile';
  if (isTabletDevice()) return 'tablet';
  return 'desktop';
}
