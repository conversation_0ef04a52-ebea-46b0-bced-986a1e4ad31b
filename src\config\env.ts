/**
 * 环境配置模块
 * 根据不同的环境提供不同的配置
 */

// 获取当前环境
const env = import.meta.env.MODE;

// 环境配置映射
interface EnvConfig {
  // 基础环境信息
  apiBaseUrl: string;
  isProd: boolean;
  isDev: boolean;
  isStage: boolean;

  // 网络配置
  apiTimeout: number;
  aiApiTimeout: number;
  devServerHost: string;
  devServerPort: number;
  aiApiProxyTarget: string;
  mainApiProxyTarget: string;

  // WebSocket配置
  websocketBaseUrl: string;
  websocketRobotLocationPath: string;
  websocketRobotControlPath: string;
  websocketCameraPath: string;
  websocketJizhanPath: string;
  websocketReconnectAttempts: number;
  websocketReconnectInterval: number;
  websocketHeartbeatInterval: number;

  // 设备配置
  robotIP: string;
  robotToken: string;
  signalServerPort: number;
  pythonServerPort: number;

  // 视频流服务配置
  videoStreamHost: string;
  videoStreamPort: number;

  // 摄像头直播墙配置
  aiDetectionWsUrl: string;
  signalServerHost: string;
  cameraApiBaseUrl: string;

  // 第三方服务配置
  amapKey: string;

  // UI配置
  defaultPageSize: number;
  pageSizeOptions: number[];
  autoRefreshInterval: number;
  chartAnimationDuration: number;

  // 业务配置
  simulatedDelay: number;
  maxTrackPoints: number;
  dataRecordingMaxRecords: number;
  dataRecordingSaveInterval: number;

  // 机器狗控制配置
  robotControlMoveSpeed: number;
  robotControlRotateSpeed: number;
  robotControlHeartbeatInterval: number;
  robotControlKeyboardEnabled: boolean;
}

// 从环境变量中获取配置值的辅助函数
const getEnvNumber = (key: string, defaultValue: number): number => {
  const value = import.meta.env[key];
  return value ? parseInt(value, 10) : defaultValue;
};

const getEnvString = (key: string, defaultValue: string): string => {
  return import.meta.env[key] || defaultValue;
};

const getEnvArray = (key: string, defaultValue: number[]): number[] => {
  const value = import.meta.env[key];
  return value ? value.split(',').map((v: string) => parseInt(v.trim(), 10)) : defaultValue;
};

// 从环境变量中获取API基础URL
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 环境配置
const envConfig: EnvConfig = {
  // 基础环境信息
  apiBaseUrl: env === 'development' ? '' : (apiBaseUrl || 'http://localhost:8080'),
  isProd: env === 'production',
  isDev: env === 'development',
  isStage: env === 'staging',

  // 网络配置
  apiTimeout: getEnvNumber('VITE_API_TIMEOUT', 15000),
  aiApiTimeout: getEnvNumber('VITE_AI_API_TIMEOUT', 120000),
  devServerHost: getEnvString('VITE_DEV_SERVER_HOST', '0.0.0.0'),
  devServerPort: getEnvNumber('VITE_DEV_SERVER_PORT', 5174),
  aiApiProxyTarget: getEnvString('VITE_AI_API_PROXY_TARGET', 'http://***********:5000'),
  mainApiProxyTarget: getEnvString('VITE_MAIN_API_PROXY_TARGET', 'http://localhost:8080'),

  // WebSocket配置
  websocketBaseUrl: getEnvString('VITE_WEBSOCKET_BASE_URL', 'ws://localhost:8080'),
  websocketRobotLocationPath: getEnvString('VITE_WEBSOCKET_ROBOT_LOCATION_PATH', '/robot-location'),
  websocketRobotControlPath: getEnvString('VITE_WEBSOCKET_ROBOT_CONTROL_PATH', '/robot-control'),
  websocketCameraPath: getEnvString('VITE_WEBSOCKET_CAMERA_PATH', '/camera'),
  websocketJizhanPath: getEnvString('VITE_WEBSOCKET_JIZHAN_PATH', '/jizhan'),
  websocketReconnectAttempts: getEnvNumber('VITE_WEBSOCKET_RECONNECT_ATTEMPTS', 5),
  websocketReconnectInterval: getEnvNumber('VITE_WEBSOCKET_RECONNECT_INTERVAL', 1000),
  websocketHeartbeatInterval: getEnvNumber('VITE_WEBSOCKET_HEARTBEAT_INTERVAL', 10000),

  // 设备配置
  robotIP: getEnvString('VITE_ROBOT_IP', '************'),
  robotToken: getEnvString('VITE_ROBOT_TOKEN', ''),
  signalServerPort: getEnvNumber('VITE_SIGNAL_SERVER_PORT', 8081),
  pythonServerPort: getEnvNumber('VITE_PYTHON_SERVER_PORT', 8081),

  // 视频流服务配置
  videoStreamHost: getEnvString('VITE_VIDEO_STREAM_HOST', '***********'),
  videoStreamPort: getEnvNumber('VITE_VIDEO_STREAM_PORT', 8000),

  // 摄像头直播墙配置
  aiDetectionWsUrl: getEnvString('VITE_AI_DETECTION_WS_URL', 'ws://***********:8000/ws/detect'),
  signalServerHost: getEnvString('VITE_SIGNAL_SERVER_HOST', '************'),
  cameraApiBaseUrl: getEnvString('VITE_CAMERA_API_BASE_URL', 'http://************:8080'),

  // 第三方服务配置
  amapKey: getEnvString('VITE_AMAP_KEY', ''),

  // UI配置
  defaultPageSize: getEnvNumber('VITE_DEFAULT_PAGE_SIZE', 10),
  pageSizeOptions: getEnvArray('VITE_PAGE_SIZE_OPTIONS', [10, 20, 50, 100]),
  autoRefreshInterval: getEnvNumber('VITE_AUTO_REFRESH_INTERVAL', 30),
  chartAnimationDuration: getEnvNumber('VITE_CHART_ANIMATION_DURATION', 1000),

  // 业务配置
  simulatedDelay: getEnvNumber('VITE_SIMULATED_DELAY', 1000),
  maxTrackPoints: getEnvNumber('VITE_MAX_TRACK_POINTS', 1000),
  dataRecordingMaxRecords: getEnvNumber('VITE_DATA_RECORDING_MAX_RECORDS', 10000),
  dataRecordingSaveInterval: getEnvNumber('VITE_DATA_RECORDING_SAVE_INTERVAL', 5),

  // 机器狗控制配置
  robotControlMoveSpeed: getEnvNumber('VITE_ROBOT_CONTROL_MOVE_SPEED', 0.5),
  robotControlRotateSpeed: getEnvNumber('VITE_ROBOT_CONTROL_ROTATE_SPEED', 0.5),
  robotControlHeartbeatInterval: getEnvNumber('VITE_ROBOT_CONTROL_HEARTBEAT_INTERVAL', 5000),
  robotControlKeyboardEnabled: getEnvString('VITE_ROBOT_CONTROL_KEYBOARD_ENABLED', 'true') === 'true',
};

// WebSocket URL构建工具函数
export const buildWebSocketUrl = (path: string): string => {
  return `${envConfig.websocketBaseUrl}${path}`;
};

// 预定义的WebSocket URL
export const WEBSOCKET_URLS = {
  ROBOT_LOCATION: buildWebSocketUrl(envConfig.websocketRobotLocationPath),
  ROBOT_CONTROL: buildWebSocketUrl(envConfig.websocketRobotControlPath),
  CAMERA: (cameraId: string) => buildWebSocketUrl(`${envConfig.websocketCameraPath}/${cameraId}`),
  JIZHAN: buildWebSocketUrl(envConfig.websocketJizhanPath)
} as const;

export default envConfig;
