<!--
导出功能面板组件
功能：
1. Excel数据导出
2. 多种格式导出选择
3. 导出配置管理
4. 批量导出功能
-->

<template>
  <div class="export-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <div class="header-left">
        <el-icon class="header-icon"><Download /></el-icon>
        <span class="header-title">数据导出</span>
      </div>
      <div class="header-right">
        <el-tag :type="canExport ? 'success' : 'warning'" size="small">
          {{ canExport ? '就绪' : '导出中' }}
        </el-tag>
      </div>
    </div>

    <!-- 导出统计 -->
    <div class="export-stats">
      <div class="stat-item">
        <el-icon class="stat-icon"><Document /></el-icon>
        <div class="stat-info">
          <div class="stat-label">可导出记录</div>
          <div class="stat-value">{{ recordCount }} 条</div>
        </div>
      </div>

      <div class="stat-item">
        <el-icon class="stat-icon"><Clock /></el-icon>
        <div class="stat-info">
          <div class="stat-label">最后导出</div>
          <div class="stat-value">{{ lastExportTimeText }}</div>
        </div>
      </div>
    </div>

    <!-- 快速导出按钮 -->
    <div class="quick-export">
      <el-button
        type="primary"
        :icon="Download"
        @click="handleQuickExport"
        :loading="isExporting"
        :disabled="recordCount === 0"
        size="default"
        style="width: 100%"
      >
        {{ isExporting ? '导出中...' : '快速导出Excel' }}
      </el-button>
    </div>

    <!-- 高级导出选项 -->
    <div class="advanced-export">
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="高级导出选项" name="advanced">
          <div class="export-options">
            <!-- 导出格式选择 -->
            <div class="option-group">
              <div class="option-label">导出格式</div>
              <el-checkbox-group v-model="selectedFormats">
                <el-checkbox label="excel">Excel (.xlsx)</el-checkbox>
                <el-checkbox label="csv">CSV (.csv)</el-checkbox>
                <el-checkbox label="json">JSON (.json)</el-checkbox>
                <el-checkbox label="gpx">GPX轨迹 (.gpx)</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 导出范围选择 -->
            <div class="option-group">
              <div class="option-label">导出范围</div>
              <el-radio-group v-model="exportRange">
                <el-radio label="all">全部数据</el-radio>
                <el-radio label="time">时间范围</el-radio>
                <el-radio label="device">指定设备</el-radio>
              </el-radio-group>
            </div>

            <!-- 时间范围选择 -->
            <div class="option-group" v-if="exportRange === 'time'">
              <div class="option-label">时间范围</div>
              <el-date-picker
                v-model="timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                size="small"
                style="width: 100%"
              />
            </div>

            <!-- 设备选择 -->
            <div class="option-group" v-if="exportRange === 'device'">
              <div class="option-label">选择设备</div>
              <el-select
                v-model="selectedDevices"
                multiple
                placeholder="请选择设备"
                size="small"
                style="width: 100%"
              >
                <el-option
                  v-for="device in availableDevices"
                  :key="device.tagId"
                  :label="device.name"
                  :value="device.tagId"
                />
              </el-select>
            </div>

            <!-- 导出配置 -->
            <div class="option-group">
              <div class="option-label">文件配置</div>
              <el-form :model="exportConfigForm" size="small">
                <el-form-item label="文件名前缀">
                  <el-input
                    v-model="exportConfigForm.filename"
                    placeholder="设备追踪数据"
                  />
                </el-form-item>
                <el-form-item label="工作表名称">
                  <el-input
                    v-model="exportConfigForm.sheetName"
                    placeholder="追踪记录"
                  />
                </el-form-item>
              </el-form>
            </div>

            <!-- 高级导出按钮 -->
            <div class="advanced-actions">
              <el-button
                type="success"
                :icon="Download"
                @click="handleAdvancedExport"
                :loading="isExporting"
                :disabled="recordCount === 0 || selectedFormats.length === 0"
                size="small"
              >
                执行导出
              </el-button>

              <el-button
                type="warning"
                :icon="FolderOpened"
                @click="handleBatchExport"
                :loading="isExporting"
                :disabled="recordCount === 0"
                size="small"
              >
                按设备批量导出
              </el-button>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 导出历史 -->
    <div class="export-history" v-if="exportHistory.length > 0">
      <div class="history-title">最近导出</div>
      <div class="history-list">
        <div
          v-for="item in exportHistory.slice(0, 3)"
          :key="item.id"
          class="history-item"
        >
          <div class="history-info">
            <div class="history-name">{{ item.filename }}</div>
            <div class="history-time">{{ item.time }}</div>
          </div>
          <div class="history-size">{{ item.recordCount }} 条</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElNotification } from 'element-plus'
import {
  Download,
  Document,
  Clock,
  FolderOpened
} from '@element-plus/icons-vue'
import { useExcelExport } from '../composables/useExcelExport'
import { DataExportService } from '../services/dataExportService'
import type { DataRecord } from '../types'

// Props
interface Props {
  records: DataRecord[]
  recordCount: number
}

const props = defineProps<Props>()

// 使用导出功能
const {
  exportConfig,
  isExporting,
  lastExportTimeText,
  canExport,
  exportToExcel,
  exportByTimeRange,
  exportByDevice,
  exportByDeviceGroups,
  updateExportConfig
} = useExcelExport()

// 组件状态
const activeCollapse = ref<string[]>([])
const selectedFormats = ref(['excel'])
const exportRange = ref('all')
const timeRange = ref<[string, string] | null>(null)
const selectedDevices = ref<number[]>([])

// 导出配置表单
const exportConfigForm = reactive({
  filename: exportConfig.value.filename,
  sheetName: exportConfig.value.sheetName
})

// 导出历史记录
const exportHistory = ref<Array<{
  id: string
  filename: string
  time: string
  recordCount: number
}>>([])

// 计算可用设备列表
const availableDevices = computed(() => {
  const deviceMap = new Map()
  props.records.forEach(record => {
    if (!deviceMap.has(record.data.tagId)) {
      deviceMap.set(record.data.tagId, {
        tagId: record.data.tagId,
        name: record.deviceName
      })
    }
  })
  return Array.from(deviceMap.values())
})

// 监听配置表单变化
watch(exportConfigForm, (newConfig) => {
  updateExportConfig(newConfig)
}, { deep: true })

/**
 * 快速导出Excel
 */
const handleQuickExport = async () => {
  const success = await exportToExcel(props.records)
  if (success) {
    addToHistory('快速导出', props.recordCount)
  }
}

/**
 * 高级导出处理
 */
const handleAdvancedExport = async () => {
  let recordsToExport = props.records

  // 根据导出范围筛选数据
  if (exportRange.value === 'time' && timeRange.value) {
    const [startTime, endTime] = timeRange.value
    recordsToExport = props.records.filter(record => {
      const recordTime = new Date(record.timestamp).toISOString().slice(0, 19)
      return recordTime >= startTime && recordTime <= endTime
    })
  } else if (exportRange.value === 'device' && selectedDevices.value.length > 0) {
    recordsToExport = props.records.filter(record =>
      selectedDevices.value.includes(record.data.tagId)
    )
  }

  if (recordsToExport.length === 0) {
    ElNotification({
      title: '导出失败',
      message: '没有符合条件的数据',
      type: 'warning'
    })
    return
  }

  // 执行多格式导出
  try {
    for (const format of selectedFormats.value) {
      switch (format) {
        case 'excel':
          await exportToExcel(recordsToExport)
          break
        case 'csv':
          DataExportService.exportToCSV(recordsToExport)
          break
        case 'json':
          DataExportService.exportToJSON(recordsToExport)
          break
        case 'gpx':
          DataExportService.exportToGPX(recordsToExport)
          break
      }
    }

    addToHistory(`高级导出(${selectedFormats.value.join(',')})`, recordsToExport.length)

  } catch (error) {
    ElNotification({
      title: '导出失败',
      message: error instanceof Error ? error.message : '未知错误',
      type: 'error'
    })
  }
}

/**
 * 批量导出处理
 */
const handleBatchExport = async () => {
  const success = await exportByDeviceGroups(props.records)
  if (success) {
    addToHistory('批量导出', props.recordCount)
  }
}

/**
 * 添加到导出历史
 */
const addToHistory = (type: string, recordCount: number) => {
  const historyItem = {
    id: Date.now().toString(),
    filename: `${type}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`,
    time: new Date().toLocaleString('zh-CN'),
    recordCount
  }

  exportHistory.value.unshift(historyItem)

  // 保持历史记录不超过10条
  if (exportHistory.value.length > 10) {
    exportHistory.value = exportHistory.value.slice(0, 10)
  }
}

// 暴露给父组件的方法
defineExpose({
  exportToExcel: handleQuickExport,
  exportAdvanced: handleAdvancedExport,
  exportBatch: handleBatchExport
})
</script>

<style lang="scss" scoped>
.export-panel {
  background: transparent;
  border-radius: 0;
  padding: 20px;
  border: none;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(16, 185, 129, 0.15);

    .header-left {
      display: flex;
      align-items: center;
      gap: 10px;

      .header-icon {
        color: #10b981;
        font-size: 20px;
        padding: 8px;
        background: rgba(16, 185, 129, 0.1);
        border-radius: 8px;
      }

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #f3f4f6;
      }
    }
  }

  .export-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .stat-icon {
        color: #10b981;
        font-size: 16px;
      }

      .stat-info {
        .stat-label {
          font-size: 12px;
          color: #9ca3af;
          margin-bottom: 2px;
        }

        .stat-value {
          font-size: 14px;
          font-weight: 500;
          color: #f3f4f6;
        }
      }
    }
  }

  .quick-export {
    margin-bottom: 20px;

    .el-button {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      background: #10b981;
      border: none;
      border-radius: 12px;

      &.is-loading {
        background: #6b7280;
      }
    }
  }

  .advanced-export {
    margin-bottom: 16px;

    :deep(.el-collapse) {
      border: none;
      background: transparent;

      .el-collapse-item__header {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.2);
        border-radius: 6px;
        padding: 0 12px;
        color: #f3f4f6;
        font-size: 14px;
      }

      .el-collapse-item__content {
        padding: 12px 0 0 0;
        border: none;
      }
    }

    .export-options {
      .option-group {
        margin-bottom: 16px;

        .option-label {
          font-size: 14px;
          color: #d1d5db;
          margin-bottom: 8px;
          font-weight: 500;
        }
      }

      .advanced-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        margin-top: 16px;
      }
    }
  }

  .export-history {
    .history-title {
      font-size: 14px;
      color: #d1d5db;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .history-list {
      .history-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: rgba(16, 185, 129, 0.05);
        border-radius: 6px;
        margin-bottom: 4px;
        border: 1px solid rgba(16, 185, 129, 0.1);

        .history-info {
          .history-name {
            font-size: 13px;
            color: #f3f4f6;
            margin-bottom: 2px;
          }

          .history-time {
            font-size: 11px;
            color: #9ca3af;
          }
        }

        .history-size {
          font-size: 12px;
          color: #10b981;
          font-weight: 500;
        }
      }
    }
  }
}

:deep(.el-form-item__label) {
  color: #d1d5db !important;
}

:deep(.el-checkbox__label) {
  color: #d1d5db !important;
}

:deep(.el-radio__label) {
  color: #d1d5db !important;
}
</style>
