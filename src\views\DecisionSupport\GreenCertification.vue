<!-- 
  GreenCertification.vue
  绿色认证管理模块
  管理农业绿色食品、有机认证、低碳认证和生态认证的申请与监控
-->
<template>
  <div class="green-certification">
    <!-- 页面标题 -->
    <PageHeader
      title="绿色防控认证管理"
      description="管理农业绿色食品、有机认证、低碳认证和生态认证的申请与监控"
      icon="Medal"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ activeCertifications.length }}</span>
            <span class="summary-label">有效认证</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ pendingCertifications.length }}</span>
            <span class="summary-label">申请中</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ expiringCertifications.length }}</span>
            <span class="summary-label">即将到期</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 认证状态卡片网格 -->
    <div class="certification-cards">
      <!-- 绿色食品认证卡片 -->
      <div class="cert-card green-food">
        <div class="cert-card-header">
          <div class="cert-icon">
            <span class="icon-text">G</span>
          </div>
          <div class="cert-info">
            <h3>绿色食品认证</h3>
            <p>无污染农产品生产标准</p>
          </div>
        </div>
        <div class="cert-card-content">
          <div class="cert-metrics">
            <div class="metric-item">
              <span class="metric-value">{{ certifications.filter(c => c.type === 'green_food' && c.status === 'active').length }}</span>
              <span class="metric-label">有效认证</span>
            </div>
            <div class="metric-item">
              <span class="metric-value">{{ certifications.filter(c => c.type === 'green_food' && c.status === 'pending').length }}</span>
              <span class="metric-label">申请中</span>
            </div>
          </div>
          <div class="cert-progress">
            <span class="progress-label">认证有效率</span>
            <el-progress 
              :percentage="Math.round(certifications.filter(c => c.type === 'green_food' && c.status === 'active').length / Math.max(1, certifications.filter(c => c.type === 'green_food').length) * 100)" 
              :stroke-width="10"
              :show-text="false"
              status="success"
            ></el-progress>
            <span class="progress-value">{{ Math.round(certifications.filter(c => c.type === 'green_food' && c.status === 'active').length / Math.max(1, certifications.filter(c => c.type === 'green_food').length) * 100) }}%</span>
          </div>
        </div>
        <div class="cert-card-footer">
          <el-button type="primary" size="small" @click="typeFilter = 'green_food'">
            查看详情
          </el-button>
          <el-button type="success" size="small" @click="applyNewCertification('green_food')">
            申请认证
          </el-button>
        </div>
      </div>
      
      <!-- 有机认证卡片 -->
      <div class="cert-card organic">
        <div class="cert-card-header">
          <div class="cert-icon">
            <span class="icon-text">O</span>
          </div>
          <div class="cert-info">
            <h3>有机认证</h3>
            <p>不使用化学合成物质的种植</p>
          </div>
        </div>
        <div class="cert-card-content">
          <div class="cert-metrics">
            <div class="metric-item">
              <span class="metric-value">{{ certifications.filter(c => c.type === 'organic' && c.status === 'active').length }}</span>
              <span class="metric-label">有效认证</span>
            </div>
            <div class="metric-item">
              <span class="metric-value">{{ certifications.filter(c => c.type === 'organic' && c.status === 'pending').length }}</span>
              <span class="metric-label">申请中</span>
            </div>
          </div>
          <div class="cert-progress">
            <span class="progress-label">认证有效率</span>
            <el-progress 
              :percentage="Math.round(certifications.filter(c => c.type === 'organic' && c.status === 'active').length / Math.max(1, certifications.filter(c => c.type === 'organic').length) * 100)" 
              :stroke-width="10"
              :show-text="false"
              status="warning"
            ></el-progress>
            <span class="progress-value">{{ Math.round(certifications.filter(c => c.type === 'organic' && c.status === 'active').length / Math.max(1, certifications.filter(c => c.type === 'organic').length) * 100) }}%</span>
          </div>
        </div>
        <div class="cert-card-footer">
          <el-button type="primary" size="small" @click="typeFilter = 'organic'">
            查看详情
          </el-button>
          <el-button type="success" size="small" @click="applyNewCertification('organic')">
            申请认证
          </el-button>
        </div>
      </div>
      
      <!-- 低碳认证卡片 -->
      <div class="cert-card low-carbon">
        <div class="cert-card-header">
          <div class="cert-icon">
            <span class="icon-text">LC</span>
          </div>
          <div class="cert-info">
            <h3>低碳认证</h3>
            <p>低碳环保农业生产方式</p>
          </div>
        </div>
        <div class="cert-card-content">
          <div class="cert-metrics">
            <div class="metric-item">
              <span class="metric-value">{{ certifications.filter(c => c.type === 'low_carbon' && c.status === 'active').length }}</span>
              <span class="metric-label">有效认证</span>
            </div>
            <div class="metric-item">
              <span class="metric-value">{{ certifications.filter(c => c.type === 'low_carbon' && c.status === 'pending').length }}</span>
              <span class="metric-label">申请中</span>
            </div>
          </div>
          <div class="cert-progress">
            <span class="progress-label">认证有效率</span>
            <el-progress 
              :percentage="Math.round(certifications.filter(c => c.type === 'low_carbon' && c.status === 'active').length / Math.max(1, certifications.filter(c => c.type === 'low_carbon').length) * 100)" 
              :stroke-width="10"
              :show-text="false"
              status="info"
            ></el-progress>
            <span class="progress-value">{{ Math.round(certifications.filter(c => c.type === 'low_carbon' && c.status === 'active').length / Math.max(1, certifications.filter(c => c.type === 'low_carbon').length) * 100) }}%</span>
          </div>
        </div>
        <div class="cert-card-footer">
          <el-button type="primary" size="small" @click="typeFilter = 'low_carbon'">
            查看详情
          </el-button>
          <el-button type="success" size="small" @click="applyNewCertification('low_carbon')">
            申请认证
          </el-button>
        </div>
      </div>
      
      <!-- 生态认证卡片 -->
      <div class="cert-card eco">
        <div class="cert-card-header">
          <div class="cert-icon">
            <span class="icon-text">E</span>
          </div>
          <div class="cert-info">
            <h3>生态认证</h3>
            <p>生态平衡的农业生产系统</p>
          </div>
        </div>
        <div class="cert-card-content">
          <div class="cert-metrics">
            <div class="metric-item">
              <span class="metric-value">{{ certifications.filter(c => c.type === 'eco' && c.status === 'active').length }}</span>
              <span class="metric-label">有效认证</span>
            </div>
            <div class="metric-item">
              <span class="metric-value">{{ certifications.filter(c => c.type === 'eco' && c.status === 'pending').length }}</span>
              <span class="metric-label">申请中</span>
            </div>
          </div>
          <div class="cert-progress">
            <span class="progress-label">认证有效率</span>
            <el-progress 
              :percentage="Math.round(certifications.filter(c => c.type === 'eco' && c.status === 'active').length / Math.max(1, certifications.filter(c => c.type === 'eco').length) * 100)" 
              :stroke-width="10"
              :show-text="false"
              status="success"
            ></el-progress>
            <span class="progress-value">{{ Math.round(certifications.filter(c => c.type === 'eco' && c.status === 'active').length / Math.max(1, certifications.filter(c => c.type === 'eco').length) * 100) }}%</span>
          </div>
        </div>
        <div class="cert-card-footer">
          <el-button type="primary" size="small" @click="typeFilter = 'eco'">
            查看详情
          </el-button>
          <el-button type="success" size="small" @click="applyNewCertification('eco')">
            申请认证
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 认证列表 -->
    <DataPanel title="认证列表">
      <template #actions>
        <div class="filter-actions">
          <el-select v-model="statusFilter" placeholder="认证状态" clearable size="small">
            <el-option label="有效" value="active"></el-option>
            <el-option label="申请中" value="pending"></el-option>
            <el-option label="即将到期" value="expiring"></el-option>
            <el-option label="已过期" value="expired"></el-option>
          </el-select>
          
          <el-select v-model="typeFilter" placeholder="认证类型" clearable size="small">
            <el-option label="绿色食品" value="green_food"></el-option>
            <el-option label="有机认证" value="organic"></el-option>
            <el-option label="低碳认证" value="low_carbon"></el-option>
            <el-option label="生态认证" value="eco"></el-option>
          </el-select>
          
          <el-input
            v-model="searchQuery"
            placeholder="搜索认证名称"
            clearable
            size="small"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </template>
      
      <el-table :data="filteredCertifications" style="width: 100%">
        <el-table-column label="认证名称" prop="name">
          <template #default="{ row }">
            <div class="cert-name-cell">
              <el-avatar 
                :size="32" 
                :src="row.logo" 
                fit="contain"
                :style="{ backgroundColor: 'white', padding: '2px' }"
              />
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="认证类型" prop="type">
          <template #default="{ row }">
            <el-tag 
              :type="getCertTypeTagType(row.type)" 
              effect="plain"
            >
              {{ getCertTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="认证机构" prop="issuer" />
        
        <el-table-column label="有效期" width="220">
          <template #default="{ row }">
            <div>
              {{ formatDate(row.issueDate) }} - {{ formatDate(row.expiryDate) }}
            </div>
            <el-progress 
              :percentage="getCertValidityPercentage(row)" 
              :status="getCertValidityStatus(row)"
              :striped="isExpiring(row)"
              striped-flow
              :show-text="false"
            ></el-progress>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getCertStatusTagType(row)" 
              effect="dark"
            >
              {{ getCertStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              link 
              type="primary" 
              @click="viewCertificate(row)"
            >
              查看
            </el-button>
            
            <el-button 
              v-if="row.status === 'active' && isExpiring(row)"
              link 
              type="success" 
              @click="renewCertificate(row)"
            >
              更新
            </el-button>
            
            <el-button 
              v-if="row.status === 'pending'"
              link 
              type="info" 
              @click="checkProgress(row)"
            >
              进度
            </el-button>
            
            <el-popconfirm
              title="确定要删除这个认证记录吗？"
              @confirm="deleteCertificate(row)"
            >
              <template #reference>
                <el-button 
                  link 
                  type="danger"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </DataPanel>
    
    <!-- 状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="有效认证" />
        <StatusIndicator type="warning" label="即将到期" />
        <StatusIndicator type="error" label="已过期" />
        <StatusIndicator type="normal" label="申请中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="success" size="small" @click="showApplyDialog = true">
          <el-icon><Plus /></el-icon>
          申请新认证
        </el-button>
      </div>
    </div>
    
    <!-- Apply Dialog -->
    <el-dialog
      v-model="showApplyDialog"
      title="申请新认证"
      width="500px"
      destroy-on-close
    >
      <el-form :model="newCertForm" label-width="100px">
        <el-form-item label="认证名称">
          <el-input v-model="newCertForm.name" placeholder="请输入认证名称"></el-input>
        </el-form-item>
        
        <el-form-item label="认证类型">
          <el-select v-model="newCertForm.type" placeholder="请选择认证类型" style="width: 100%">
            <el-option label="绿色食品" value="green_food"></el-option>
            <el-option label="有机认证" value="organic"></el-option>
            <el-option label="低碳认证" value="low_carbon"></el-option>
            <el-option label="生态认证" value="eco"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="认证机构">
          <el-select v-model="newCertForm.issuer" placeholder="请选择认证机构" style="width: 100%">
            <el-option label="中国绿色食品发展中心" value="CGFDC"></el-option>
            <el-option label="中国质量认证中心" value="CQC"></el-option>
            <el-option label="有机产品认证中心" value="OFDC"></el-option>
            <el-option label="国际有机农业运动联盟" value="IFOAM"></el-option>
            <el-option label="生态农业研究所" value="EAI"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="申请日期">
          <el-date-picker
            v-model="newCertForm.applicationDate"
            type="date"
            placeholder="选择申请日期"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showApplyDialog = false">取消</el-button>
          <el-button type="primary" @click="submitApplication">提交申请</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- Certificate View Modal -->
    <el-dialog
      v-model="showCertificateModal"
      :title="selectedCertificate?.name"
      width="700px"
      v-if="selectedCertificate"
      destroy-on-close
    >
      <div class="certificate-modal">
        <div class="certificate-preview">
          <div class="certificate-document">
            <div class="cert-header">
              <img src="https://placeholder.pics/svg/80x80/DEDEDE/555555/LOGO" alt="Logo" class="cert-logo" />
              <h2>{{ getCertTypeName(selectedCertificate.type) }}</h2>
            </div>
            
            <div class="cert-body">
              <h3>证书编号: {{ selectedCertificate.id }}</h3>
              <p>兹证明:</p>
              <h4>{{ selectedCertificate.name }}</h4>
              <p>符合{{ selectedCertificate.issuer }}关于{{ getCertTypeName(selectedCertificate.type) }}的全部要求和标准。</p>
              
              <div class="cert-validity">
                <p>发证日期: {{ formatDate(selectedCertificate.issueDate) }}</p>
                <p>有效期至: {{ formatDate(selectedCertificate.expiryDate) }}</p>
              </div>
              
              <div class="cert-footer">
                <div class="cert-seal">
                  <div class="seal-inner">{{ selectedCertificate.issuer }}</div>
                </div>
                <div class="cert-signature">签发人: 张明</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="certificate-info">
          <h4>证书信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="证书ID">{{ selectedCertificate.id }}</el-descriptions-item>
            <el-descriptions-item label="证书类型">{{ getCertTypeName(selectedCertificate.type) }}</el-descriptions-item>
            <el-descriptions-item label="发证机构">{{ selectedCertificate.issuer }}</el-descriptions-item>
            <el-descriptions-item label="发证日期">{{ formatDate(selectedCertificate.issueDate) }}</el-descriptions-item>
            <el-descriptions-item label="到期日期">{{ formatDate(selectedCertificate.expiryDate) }}</el-descriptions-item>
            <el-descriptions-item label="认证状态">
              <el-tag :type="getCertStatusTagType(selectedCertificate)">
                {{ getCertStatusText(selectedCertificate) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="certificate-actions">
            <el-button type="primary">
              <el-icon><Download /></el-icon>下载证书
            </el-button>
            <el-button>
              <el-icon><Printer /></el-icon>打印证书
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Plus, 
  Refresh, 
  Search, 
  Download, 
  Printer,
  Medal,
  View
} from '@element-plus/icons-vue';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import StatusIndicator from './components/StatusIndicator.vue';
import DataPanel from './components/DataPanel.vue';

// 类型定义
interface Certificate {
  id: string;
  name: string;
  type: string;
  issuer: string;
  status: string;
  issueDate?: string;
  expiryDate?: string;
  applicationDate?: string;
  logo: string;
}

// Filters and search
const statusFilter = ref('');
const typeFilter = ref('');
const searchQuery = ref('');

// Modals
const showApplyDialog = ref(false);
const showCertificateModal = ref(false);
const selectedCertificate = ref<Certificate | null>(null);

// 最后更新时间
const lastUpdateTime = ref(new Date());

// Form
const newCertForm = reactive({
  name: '',
  type: '',
  issuer: '',
  applicationDate: new Date()
});

// Mock data
const certifications = ref<Certificate[]>([
  {
    id: 'GC-2023-001',
    name: '绿色水稻种植认证',
    type: 'green_food',
    issuer: '中国绿色食品发展中心',
    status: 'active',
    issueDate: '2023-04-15',
    expiryDate: '2024-04-15',
    logo: 'https://placeholder.pics/svg/32/4CAF50/FFFFFF/G'
  },
  {
    id: 'OC-2023-002',
    name: '有机小麦种植认证',
    type: 'organic',
    issuer: '中国质量认证中心',
    status: 'active',
    issueDate: '2023-03-10',
    expiryDate: '2025-03-10',
    logo: 'https://placeholder.pics/svg/32/8BC34A/FFFFFF/O'
  },
  {
    id: 'LC-2023-003',
    name: '低碳农业方法认证',
    type: 'low_carbon',
    issuer: '国际有机农业运动联盟',
    status: 'active',
    issueDate: '2023-01-20',
    expiryDate: '2024-01-20',
    logo: 'https://placeholder.pics/svg/32/00BCD4/FFFFFF/LC'
  },
  {
    id: 'EC-2023-004',
    name: '生态鱼塘管理认证',
    type: 'eco',
    issuer: '生态农业研究所',
    status: 'pending',
    applicationDate: '2023-08-05',
    logo: 'https://placeholder.pics/svg/32/009688/FFFFFF/E'
  },
  {
    id: 'GC-2022-005',
    name: '绿色蔬菜种植认证',
    type: 'green_food',
    issuer: '中国绿色食品发展中心',
    status: 'active',
    issueDate: '2022-11-15',
    expiryDate: '2023-11-15',
    logo: 'https://placeholder.pics/svg/32/4CAF50/FFFFFF/G'
  },
  {
    id: 'OC-2022-006',
    name: '有机水果园认证',
    type: 'organic',
    issuer: '有机产品认证中心',
    status: 'expired',
    issueDate: '2021-09-22',
    expiryDate: '2023-09-22',
    logo: 'https://placeholder.pics/svg/32/8BC34A/FFFFFF/O'
  }
]);

// Computed properties
const activeCertifications = computed(() => {
  return certifications.value.filter(cert => 
    cert.status === 'active' && !isExpiring(cert)
  );
});

const pendingCertifications = computed(() => {
  return certifications.value.filter(cert => cert.status === 'pending');
});

const expiringCertifications = computed(() => {
  return certifications.value.filter(cert => 
    cert.status === 'active' && isExpiring(cert)
  );
});

const filteredCertifications = computed(() => {
  let filtered = [...certifications.value];
  
  // Apply status filter
  if (statusFilter.value) {
    if (statusFilter.value === 'expiring') {
      filtered = filtered.filter(cert => 
        cert.status === 'active' && isExpiring(cert)
      );
    } else if (statusFilter.value === 'active') {
      filtered = filtered.filter(cert => 
        cert.status === 'active' && !isExpiring(cert)
      );
    } else {
      filtered = filtered.filter(cert => cert.status === statusFilter.value);
    }
  }
  
  // Apply type filter
  if (typeFilter.value) {
    filtered = filtered.filter(cert => cert.type === typeFilter.value);
  }
  
  // Apply search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(cert => 
      cert.name.toLowerCase().includes(query) || 
      cert.issuer.toLowerCase().includes(query)
    );
  }
  
  return filtered;
});

// Methods
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

const refreshData = () => {
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已刷新');
};

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '待定';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN');
};

const isExpiring = (certificate: Certificate) => {
  if (certificate.status !== 'active' || !certificate.expiryDate) return false;
  
  const expiry = new Date(certificate.expiryDate);
  const now = new Date();
  const threeMonths = 90 * 24 * 60 * 60 * 1000;
  
  return expiry.getTime() - now.getTime() < threeMonths && expiry > now;
};

const getCertValidityPercentage = (certificate: Certificate) => {
  if (certificate.status !== 'active' || !certificate.issueDate || !certificate.expiryDate) return 0;
  
  const issueDate = new Date(certificate.issueDate);
  const expiryDate = new Date(certificate.expiryDate);
  const now = new Date();
  
  const totalDuration = expiryDate.getTime() - issueDate.getTime();
  const elapsed = now.getTime() - issueDate.getTime();
  
  return Math.max(0, Math.min(100, 100 - (elapsed / totalDuration * 100)));
};

const getCertValidityStatus = (certificate: Certificate) => {
  if (certificate.status === 'expired') return 'exception';
  if (isExpiring(certificate)) return 'warning';
  return 'success';
};

const getCertTypeName = (type?: string) => {
  if (!type) return '';
  const typeMap: Record<string, string> = {
    'green_food': '绿色食品',
    'organic': '有机认证',
    'low_carbon': '低碳认证',
    'eco': '生态认证'
  };
  return typeMap[type] || type;
};

const getCertTypeTagType = (type?: string) => {
  if (!type) return 'info';
  const typeTagMap: Record<string, string> = {
    'green_food': 'success',
    'organic': 'warning',
    'low_carbon': 'info',
    'eco': 'success'
  };
  return typeTagMap[type] || 'info';
};

const getCertStatusText = (certificate: Certificate) => {
  if (certificate.status === 'active') {
    return isExpiring(certificate) ? '即将到期' : '有效';
  }
  if (certificate.status === 'pending') return '申请中';
  if (certificate.status === 'expired') return '已过期';
  return certificate.status;
};

const getCertStatusTagType = (certificate: Certificate) => {
  if (certificate.status === 'active') {
    return isExpiring(certificate) ? 'warning' : 'success';
  }
  if (certificate.status === 'pending') return 'info';
  if (certificate.status === 'expired') return 'danger';
  return 'info';
};

const viewCertificate = (certificate: Certificate) => {
  selectedCertificate.value = certificate;
  showCertificateModal.value = true;
};

const renewCertificate = (certificate: Certificate) => {
  ElMessage.success(`已开始更新认证: ${certificate.name}`);
};

const checkProgress = (certificate: Certificate) => {
  ElMessage.info(`${certificate.name} 的认证申请正在处理中，预计还需要1-2周`);
};

const deleteCertificate = (certificate: Certificate) => {
  const index = certifications.value.findIndex(c => c.id === certificate.id);
  if (index > -1) {
    certifications.value.splice(index, 1);
    ElMessage.success(`已删除认证: ${certificate.name}`);
  }
};

const applyNewCertification = (type: string) => {
  newCertForm.type = type;
  showApplyDialog.value = true;
};

const submitApplication = () => {
  // Validate form
  if (!newCertForm.name || !newCertForm.type || !newCertForm.issuer || !newCertForm.applicationDate) {
    ElMessage.warning('请填写所有必填项');
    return;
  }
  
  // Create new certification record
  const newCert: Certificate = {
    id: `${newCertForm.type.charAt(0).toUpperCase()}C-${new Date().getFullYear()}-${(certifications.value.length + 1).toString().padStart(3, '0')}`,
    name: newCertForm.name,
    type: newCertForm.type,
    issuer: newCertForm.issuer,
    status: 'pending',
    applicationDate: newCertForm.applicationDate.toISOString().split('T')[0],
    logo: `https://placeholder.pics/svg/32/${newCertForm.type === 'organic' ? '8BC34A' : newCertForm.type === 'low_carbon' ? '00BCD4' : '4CAF50'}/FFFFFF/${newCertForm.type.charAt(0).toUpperCase()}`
  };
  
  certifications.value.push(newCert);
  ElMessage.success(`已提交认证申请: ${newCert.name}`);
  
  // Reset form and close dialog
  Object.keys(newCertForm).forEach(key => {
    if (key !== 'applicationDate') {
      (newCertForm as any)[key] = '';
    }
  });
  showApplyDialog.value = false;
};

// 数据自动更新定时器
let dataUpdateInterval: number | null = null;

onMounted(() => {
  // 每隔一段时间自动更新时间
  dataUpdateInterval = window.setInterval(() => {
    lastUpdateTime.value = new Date();
  }, 60000); // 每分钟更新一次
});

onUnmounted(() => {
  // 清除定时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
});
</script>

<style scoped>
.green-certification {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 认证卡片网格 */
.certification-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* 认证卡片样式 */
.cert-card {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #3b4863;
}

.cert-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.cert-card.green-food {
  border-left: 4px solid #4CAF50;
}

.cert-card.organic {
  border-left: 4px solid #8BC34A;
}

.cert-card.low-carbon {
  border-left: 4px solid #00BCD4;
}

.cert-card.eco {
  border-left: 4px solid #009688;
}

.cert-card-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.cert-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  background-color: rgba(255, 255, 255, 0.9);
}

.cert-card.green-food .cert-icon {
  color: #4CAF50;
}

.cert-card.organic .cert-icon {
  color: #8BC34A;
}

.cert-card.low-carbon .cert-icon {
  color: #00BCD4;
}

.cert-card.eco .cert-icon {
  color: #009688;
}

.cert-info h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #e5e7eb;
}

.cert-info p {
  margin: 0;
  font-size: 14px;
  color: #9ca3af;
}

.cert-card-content {
  flex: 1;
  margin-bottom: 20px;
}

.cert-metrics {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #e5e7eb;
}

.metric-label {
  font-size: 14px;
  color: #9ca3af;
}

.cert-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-label {
  font-size: 14px;
  color: #9ca3af;
}

.progress-value {
  font-size: 14px;
  font-weight: 500;
  color: #e5e7eb;
  align-self: flex-end;
}

.cert-card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 过滤器操作 */
.filter-actions {
  display: flex;
  gap: 10px;
}

/* 认证名称单元格 */
.cert-name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 证书模态框 */
.certificate-modal {
  display: flex;
  gap: 20px;
}

.certificate-preview {
  flex: 2;
  padding: 20px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
}

.certificate-document {
  background-color: white;
  border: 1px solid #ddd;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-height: 400px;
  position: relative;
}

.cert-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.cert-logo {
  margin-right: 20px;
}

.cert-body {
  text-align: center;
  color: #333;
}

.cert-validity {
  margin: 30px 0;
}

.cert-footer {
  margin-top: 50px;
  display: flex;
  justify-content: space-between;
}

.cert-seal {
  width: 80px;
  height: 80px;
  border: 2px solid #f44336;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f44336;
  font-size: 10px;
  font-weight: bold;
  position: relative;
}

.seal-inner {
  width: 70px;
  height: 70px;
  border: 1px dashed #f44336;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 5px;
}

.cert-signature {
  margin-top: 50px;
  border-top: 1px solid #555;
  padding-top: 5px;
  font-style: italic;
}

.certificate-info {
  flex: 1;
}

.certificate-info h4 {
  margin-top: 0;
  color: #e5e7eb;
  margin-bottom: 15px;
}

.certificate-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .certification-cards {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .filter-actions {
    flex-wrap: wrap;
  }
  
  .certificate-modal {
    flex-direction: column;
  }
}</style> 
