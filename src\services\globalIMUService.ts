/**
 * 全局IMU数据服务
 * 提供全局的IMU数据访问，供地图显示、自动巡航等功能使用
 */

import { ref, reactive, computed, watch } from 'vue'
import { robotDogApi } from '@/api/robotDog'
import { robotWebSocketService, type RobotControlMessage } from '@/services/robotWebSocketService'
import type { IMUData, AttitudeData, BatteryState, SensorData } from '@/views/MonitoringCenter/DeviceTracking/types/imu'

// 全局IMU数据状态
const globalIMUData = ref<IMUData | null>(null)
const isIMUConnected = ref(false)
const isIMUReceiving = ref(false)
const lastUpdateTime = ref(0)
const errorCount = ref(0)

// IMU数据统计
const imuStats = reactive({
  totalUpdates: 0,
  updateRate: 0,
  connectionDuration: 0,
  lastErrorTime: 0
})

// WebSocket连接状态
let wsConnected = false
let dataTimer: number | null = null
let statsTimer: number | null = null
let connectionStartTime = 0

/**
 * 弧度转角度
 */
const radToDeg = (rad: number): number => {
  return rad * 180 / Math.PI
}

/**
 * 处理WebSocket IMU数据
 */
const processWebSocketIMUData = (wsData: any): void => {
  try {
    console.log('🔄 全局IMU服务收到数据:', wsData)
    
    // 适配不同的数据包装格式
    let imuData = wsData
    if (wsData.data) {
      imuData = wsData.data
    }
    
    // 处理数据
    processIMUData(imuData)
    
    // 重置错误计数
    if (errorCount.value > 0) {
      errorCount.value = Math.max(0, errorCount.value - 1)
    }
    
  } catch (error) {
    console.error('处理WebSocket IMU数据失败:', error)
    errorCount.value++
  }
}

/**
 * 处理IMU数据（通用处理函数）
 */
const processIMUData = (apiData: any): void => {
  try {
    const timestamp = Date.now()

    // 解析姿态角度数据
    const attitude: AttitudeData = {
      roll: 0,
      pitch: 0,
      yaw: 0
    }

    if (apiData.imu_state?.rpy) {
      const rpy = apiData.imu_state.rpy
      attitude.roll = radToDeg(rpy[0])
      attitude.pitch = radToDeg(rpy[1])
      attitude.yaw = radToDeg(rpy[2])
    }

    // 解析电池状态
    const battery: BatteryState = {
      soc: apiData.bms_state?.soc || 0,
      current: apiData.bms_state?.current || 0,
      cycle: apiData.bms_state?.cycle || 0,
      bqTemp: apiData.bms_state?.bq_ntc || [0, 0],
      mcuTemp: apiData.bms_state?.mcu_ntc || [0, 0]
    }

    // 解析传感器数据
    const sensors: SensorData = {
      temperatureNtc1: apiData.temperature_ntc1 || 0,
      powerVoltage: apiData.power_v || 0,
      motorMaxTemp: 0,
      motorAvgTemp: 0
    }

    // 处理电机温度数据
    if (apiData.motor_state && Array.isArray(apiData.motor_state)) {
      let maxTemp = 0
      let totalTemp = 0
      let activeMotors = 0

      for (const motor of apiData.motor_state) {
        if (motor && motor.temperature > 0) {
          maxTemp = Math.max(maxTemp, motor.temperature)
          totalTemp += motor.temperature
          activeMotors++
        }
      }

      sensors.motorMaxTemp = maxTemp
      sensors.motorAvgTemp = activeMotors > 0 ? totalTemp / activeMotors : 0
    }

    // 创建IMU数据对象
    const imuData: IMUData = {
      timestamp,
      attitude,
      battery,
      footForce: {
        frontLeft: apiData.foot_force?.[0] || 0,
        frontRight: apiData.foot_force?.[1] || 0,
        rearLeft: apiData.foot_force?.[2] || 0,
        rearRight: apiData.foot_force?.[3] || 0
      },
      sensors,
      motors: apiData.motor_state || []
    }

    // 更新全局数据
    globalIMUData.value = imuData
    lastUpdateTime.value = timestamp
    imuStats.totalUpdates++
    
    // 更新接收状态
    isIMUReceiving.value = true

    console.log('✅ 全局IMU数据更新成功:', {
      yaw: attitude.yaw.toFixed(1) + '°',
      battery: battery.soc + '%',
      timestamp: new Date(timestamp).toLocaleTimeString()
    })
  } catch (error) {
    console.error('处理IMU数据失败:', error)
    errorCount.value++
  }
}

/**
 * 初始化WebSocket连接
 */
const initWebSocketConnection = async (): Promise<void> => {
  try {
    // 设置WebSocket消息回调
    robotWebSocketService.setMessageCallback((message: RobotControlMessage) => {
      if (message.type === 'IMU_DATA' && message.data) {
        processWebSocketIMUData(message.data)
      }
    })

    // 设置连接状态回调
    robotWebSocketService.setStatusChangeCallback((status) => {
      wsConnected = status === 'CONNECTED'
      isIMUConnected.value = wsConnected
      console.log('全局IMU服务 - WebSocket连接状态:', status)
    })

    // 设置错误回调
    robotWebSocketService.setErrorCallback((error) => {
      console.error('全局IMU服务 - WebSocket错误:', error)
      errorCount.value++
    })

    // 连接WebSocket
    await robotWebSocketService.connect()
    
    console.log('✅ 全局IMU服务 - WebSocket连接初始化成功')
  } catch (error) {
    console.error('全局IMU服务 - WebSocket连接初始化失败:', error)
    throw error
  }
}

/**
 * 开始实时数据获取
 */
const startRealtimeDataFetch = (): void => {
  // 立即获取一次数据
  requestIMUData()

  // 设置定时器，每500ms请求一次数据
  dataTimer = window.setInterval(() => {
    if (wsConnected) {
      requestIMUData()
    } else {
      console.warn('全局IMU服务 - WebSocket未连接，跳过数据请求')
    }
  }, 500) // 500ms间隔，提供高频实时更新

  // 启动统计更新定时器
  startStatsTimer()
}

/**
 * 请求IMU数据
 */
const requestIMUData = async (): Promise<void> => {
  try {
    await robotWebSocketService.getIMUData()
  } catch (error) {
    console.error('全局IMU服务 - 请求IMU数据失败:', error)
    errorCount.value++
  }
}

/**
 * 启动统计更新定时器
 */
const startStatsTimer = (): void => {
  statsTimer = window.setInterval(() => {
    // 计算更新频率
    if (connectionStartTime > 0) {
      const duration = (Date.now() - connectionStartTime) / 1000
      imuStats.updateRate = imuStats.totalUpdates / duration
      imuStats.connectionDuration = duration
    }

    // 检查数据超时
    if (isIMUReceiving.value && lastUpdateTime.value > 0) {
      const timeSinceLastUpdate = Date.now() - lastUpdateTime.value
      if (timeSinceLastUpdate > 3000) { // 3秒超时
        isIMUReceiving.value = false
        console.warn('全局IMU服务 - 数据接收超时')
      }
    }
  }, 1000) // 每秒更新统计
}

/**
 * 停止统计更新定时器
 */
const stopStatsTimer = (): void => {
  if (statsTimer) {
    clearInterval(statsTimer)
    statsTimer = null
  }
}

/**
 * 全局IMU数据服务类
 */
export class GlobalIMUService {
  /**
   * 启动全局IMU数据服务
   */
  static async start(): Promise<boolean> {
    try {
      console.log('🚀 启动全局IMU数据服务...')
      
      // 初始化WebSocket连接
      await initWebSocketConnection()
      
      // 开始实时数据获取
      startRealtimeDataFetch()
      
      connectionStartTime = Date.now()
      
      console.log('✅ 全局IMU数据服务启动成功')
      return true
    } catch (error) {
      console.error('❌ 全局IMU数据服务启动失败:', error)
      return false
    }
  }

  /**
   * 停止全局IMU数据服务
   */
  static stop(): void {
    console.log('🛑 停止全局IMU数据服务...')
    
    // 停止数据获取定时器
    if (dataTimer) {
      clearInterval(dataTimer)
      dataTimer = null
    }
    
    // 停止统计定时器
    stopStatsTimer()
    
    // 断开WebSocket连接
    robotWebSocketService.disconnect()
    wsConnected = false
    
    // 重置状态
    isIMUConnected.value = false
    isIMUReceiving.value = false
    globalIMUData.value = null
    lastUpdateTime.value = 0
    errorCount.value = 0
    connectionStartTime = 0
    
    // 重置统计
    imuStats.totalUpdates = 0
    imuStats.updateRate = 0
    imuStats.connectionDuration = 0
    imuStats.lastErrorTime = 0
    
    console.log('✅ 全局IMU数据服务已停止')
  }

  /**
   * 获取当前IMU数据
   */
  static getCurrentData(): IMUData | null {
    return globalIMUData.value
  }

  /**
   * 获取当前朝向角度（度数）
   */
  static getCurrentYaw(): number | null {
    return globalIMUData.value?.attitude?.yaw || null
  }

  /**
   * 获取当前电池电量
   */
  static getBatteryLevel(): number | null {
    return globalIMUData.value?.battery?.soc || null
  }

  /**
   * 获取连接状态
   */
  static getConnectionStatus(): {
    connected: boolean
    receiving: boolean
    lastUpdate: number
    errorCount: number
  } {
    return {
      connected: isIMUConnected.value,
      receiving: isIMUReceiving.value,
      lastUpdate: lastUpdateTime.value,
      errorCount: errorCount.value
    }
  }

  /**
   * 获取统计信息
   */
  static getStats() {
    return { ...imuStats }
  }
}

// 导出响应式数据供组件使用
export const useGlobalIMU = () => {
  return {
    // 响应式数据
    currentData: computed(() => globalIMUData.value),
    isConnected: computed(() => isIMUConnected.value),
    isReceiving: computed(() => isIMUReceiving.value),
    lastUpdate: computed(() => lastUpdateTime.value),
    errorCount: computed(() => errorCount.value),
    stats: computed(() => ({ ...imuStats })),
    
    // 计算属性
    currentYaw: computed(() => globalIMUData.value?.attitude?.yaw),
    batteryLevel: computed(() => globalIMUData.value?.battery?.soc),
    motorMaxTemp: computed(() => globalIMUData.value?.sensors?.motorMaxTemp),
    
    // 方法
    start: GlobalIMUService.start,
    stop: GlobalIMUService.stop,
    getCurrentData: GlobalIMUService.getCurrentData,
    getCurrentYaw: GlobalIMUService.getCurrentYaw,
    getBatteryLevel: GlobalIMUService.getBatteryLevel,
    getConnectionStatus: GlobalIMUService.getConnectionStatus,
    getStats: GlobalIMUService.getStats
  }
}

// 默认导出
export default GlobalIMUService
