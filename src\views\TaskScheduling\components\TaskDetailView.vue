<!-- 
  TaskDetailView.vue
  任务详情视图组件，用于显示周期性巡航任务的详细信息
-->
<template>
  <div class="task-detail-view">
    <div class="detail-header">
      <div class="task-title">
        <h3>{{ task.name }}</h3>
        <el-tag :type="getStatusTagType(task.status)" effect="dark">
          {{ getStatusLabel(task.status) }}
        </el-tag>
      </div>
      
      <div class="header-actions">
        <el-button type="primary" @click="$emit('edit')">
          <el-icon><Edit /></el-icon>
          编辑任务
        </el-button>
        <el-button type="danger" @click="$emit('delete', task.id)">
          <el-icon><Delete /></el-icon>
          删除任务
        </el-button>
      </div>
    </div>
    
    <div class="detail-content">
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">任务ID</div>
            <div class="info-value">{{ task.id }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">任务类型</div>
            <div class="info-value">{{ getTaskTypeLabel(task.type) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">优先级</div>
            <div class="info-value">
              <el-rate 
                v-model="priorityValue" 
                disabled 
                show-score 
                text-color="#ff9900"
                score-template="{value}"
              />
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">任务状态</div>
            <div class="info-value">
              <el-switch
                v-model="task.enabled"
                disabled
                active-text="已启用"
                inactive-text="已禁用"
              />
            </div>
          </div>
        </div>
      </div>
      
      <div class="detail-section">
        <h4 class="section-title">任务描述</h4>
        <div class="description-box">
          {{ task.description || '暂无描述' }}
        </div>
      </div>
      
      <div class="detail-section">
        <h4 class="section-title">执行计划</h4>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">执行周期</div>
            <div class="info-value">{{ getCycleLabel(task.cycleType, task.cycleValue) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">开始时间</div>
            <div class="info-value">{{ formatDateTime(task.startTime) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">上次执行</div>
            <div class="info-value">{{ task.lastExecutedAt ? formatDateTime(task.lastExecutedAt) : '尚未执行' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">下次执行</div>
            <div class="info-value">{{ formatDateTime(task.nextExecutionTime) }}</div>
          </div>
        </div>
      </div>
      
      <div class="detail-section">
        <h4 class="section-title">执行设备</h4>
        <div class="devices-list">
          <el-tag
            v-for="deviceId in task.deviceIds"
            :key="deviceId"
            class="device-tag"
            effect="dark"
            type="info"
          >
            {{ deviceId }}
          </el-tag>
          <div v-if="!task.deviceIds || task.deviceIds.length === 0" class="no-devices">
            暂无关联设备
          </div>
        </div>
      </div>
    </div>
    
    <div class="detail-footer">
      <div class="footer-info">
        <div class="info-item">
          <span class="label">创建时间:</span>
          <span class="value">{{ formatDateTime(task.createdAt) }}</span>
        </div>
        <div class="info-item">
          <span class="label">更新时间:</span>
          <span class="value">{{ formatDateTime(task.updatedAt) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Edit, Delete } from '@element-plus/icons-vue';
import type { PeriodicTask, TaskStatus } from '@/types/taskScheduling';

const props = defineProps<{
  task: PeriodicTask
}>();

defineEmits<{
  (e: 'edit'): void
  (e: 'delete', id: string): void
}>();

// 计算优先级对应的数值
const priorityValue = computed(() => {
  const priorityMap: Record<string, number> = {
    'low': 1,
    'medium': 2,
    'high': 3,
    'emergency': 4
  };
  return priorityMap[props.task.priority] || 1;
});

// 获取任务类型标签
const getTaskTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    'patrol': '巡逻任务',
    'spray': '喷洒任务',
    'inspection': '检查任务',
    'other': '其他任务'
  };
  return map[type] || '未知类型';
};

// 获取周期标签
const getCycleLabel = (cycleType: string, cycleValue: number) => {
  const map: Record<string, string> = {
    'once': '单次',
    'daily': `每${cycleValue > 1 ? cycleValue : ''}天`,
    'weekly': `每${cycleValue > 1 ? cycleValue : ''}周`,
    'monthly': `每${cycleValue > 1 ? cycleValue : ''}月`
  };
  return map[cycleType] || '未知周期';
};

// 获取状态标签
const getStatusLabel = (status: TaskStatus) => {
  const map: Record<TaskStatus, string> = {
    'pending': '等待中',
    'running': '执行中',
    'paused': '已暂停',
    'completed': '已完成',
    'failed': '已失败'
  };
  return map[status] || '未知状态';
};

// 获取状态标签类型
const getStatusTagType = (status: TaskStatus) => {
  const map: Record<TaskStatus, string> = {
    'pending': 'info',
    'running': 'success',
    'paused': 'warning',
    'completed': '',
    'failed': 'danger'
  };
  return map[status] || 'info';
};

// 格式化日期时间
const formatDateTime = (dateTimeStr: string | undefined) => {
  if (!dateTimeStr) return '未设置';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<style scoped>
.task-detail-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  overflow: hidden;
}

.detail-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3b4863;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #e5e7eb;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.detail-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #60a5fa;
  border-bottom: 1px solid #3b4863;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 14px;
  color: #9ca3af;
}

.info-value {
  font-size: 16px;
  color: #e5e7eb;
}

.description-box {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 6px;
  padding: 12px;
  color: #e5e7eb;
  min-height: 80px;
  white-space: pre-wrap;
}

.devices-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.device-tag {
  margin-right: 0;
}

.no-devices {
  color: #9ca3af;
  font-style: italic;
}

.detail-footer {
  padding: 16px 20px;
  border-top: 1px solid #3b4863;
  background-color: rgba(31, 41, 55, 0.7);
}

.footer-info {
  display: flex;
  justify-content: space-between;
}

.footer-info .info-item {
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.footer-info .label {
  color: #9ca3af;
  font-size: 14px;
}

.footer-info .value {
  color: #e5e7eb;
  font-size: 14px;
}
</style> 