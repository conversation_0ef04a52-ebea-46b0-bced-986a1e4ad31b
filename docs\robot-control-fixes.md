# 机器狗控制系统问题修复说明

## 修复的问题

### 问题1: 视频流MJPEG格式支持

**问题描述**：
- 原始实现使用HTML `<video>` 元素显示视频流
- 但机器狗提供的是MJPEG格式的视频流
- MJPEG格式需要使用 `<img>` 元素而不是 `<video>` 元素

**解决方案**：
1. **更改显示元素**：将 `<video>` 元素改为 `<img>` 元素
2. **更新事件处理**：适配img元素的事件（load, error, loadstart）
3. **添加流监控**：实现MJPEG流状态监控机制
4. **优化错误处理**：针对MJPEG流的特殊错误处理

**技术细节**：
```html
<!-- 修改前 -->
<video
  ref="videoElement"
  class="video-stream"
  :src="videoStreamUrl"
  autoplay
  playsinline
  muted
  @loadstart="handleVideoLoadStart"
  @canplay="handleVideoCanPlay"
  @error="handleVideoError"
  @waiting="handleVideoWaiting"
  @playing="handleVideoPlaying"
></video>

<!-- 修改后 -->
<img
  ref="videoElement"
  class="video-stream"
  :src="videoStreamUrl"
  @load="handleVideoLoad"
  @error="handleVideoError"
  @loadstart="handleVideoLoadStart"
/>
```

**新增功能**：
- **流监控机制**：每2秒检查一次流状态
- **自动重试**：错误时5秒后自动重试
- **缓存避免**：刷新时添加时间戳避免缓存问题

### 问题2: 持续按键控制问题

**问题描述**：
- 用户按住键盘时，机器狗只移动一小段距离就停止
- 没有实现真正的"持续按键控制"
- 原始实现只在按键状态改变时发送一次命令

**根本原因**：
- 只在 `keydown` 事件触发时发送一次移动命令
- 没有持续发送移动命令的机制
- 浏览器的 `keydown` 事件重复触发可能被阻止

**解决方案**：
1. **添加定时器机制**：使用 `setInterval` 持续发送移动命令
2. **优化按键逻辑**：区分按键按下和释放的处理
3. **状态管理**：正确管理键盘控制定时器的生命周期

**技术实现**：

#### 1. 添加定时器变量
```typescript
// 控制定时器
let manualControlTimer: number | null = null
let keyboardControlTimer: number | null = null
```

#### 2. 修改按键处理逻辑
```typescript
const handleKeyDown = (event: KeyboardEvent) => {
  if (!keyboardEnabled.value || !isConnected.value) return
  
  const key = event.key.toLowerCase()
  if (key in keyStates && !keyStates[key as keyof typeof keyStates]) {
    keyStates[key as keyof typeof keyStates] = true
    
    // 立即发送命令
    updateMovementFromKeyboard()
    
    // 开始持续控制
    startKeyboardControlTimer()
    
    event.preventDefault()
  }
}

const handleKeyUp = (event: KeyboardEvent) => {
  if (!keyboardEnabled.value) return
  
  const key = event.key.toLowerCase()
  if (key in keyStates && keyStates[key as keyof typeof keyStates]) {
    keyStates[key as keyof typeof keyStates] = false
    
    // 检查是否还有其他按键按下
    const hasActiveKeys = Object.values(keyStates).some(state => state)
    
    if (hasActiveKeys) {
      // 还有其他按键，更新移动参数
      updateMovementFromKeyboard()
    } else {
      // 没有按键了，停止移动
      stopKeyboardControlTimer()
      stopMovement()
    }
    
    event.preventDefault()
  }
}
```

#### 3. 实现持续控制定时器
```typescript
// 开始键盘控制定时器
const startKeyboardControlTimer = () => {
  // 如果定时器已存在，不重复创建
  if (keyboardControlTimer) return
  
  // 持续发送移动命令，确保机器狗持续移动
  keyboardControlTimer = window.setInterval(() => {
    if (keyboardEnabled.value && isConnected.value) {
      const hasActiveKeys = Object.values(keyStates).some(state => state)
      if (hasActiveKeys) {
        updateMovementFromKeyboard()
      } else {
        // 没有按键时停止定时器
        stopKeyboardControlTimer()
        stopMovement()
      }
    }
  }, 100) // 每100ms发送一次命令，确保持续控制
}

// 停止键盘控制定时器
const stopKeyboardControlTimer = () => {
  if (keyboardControlTimer) {
    clearInterval(keyboardControlTimer)
    keyboardControlTimer = null
  }
}
```

**控制频率优化**：
- **命令发送频率**：每100ms发送一次移动命令
- **响应性**：确保按键按下时立即响应
- **停止及时性**：按键释放时立即停止

## 修复效果

### 视频流显示
- ✅ **正确显示MJPEG流**：使用img元素正确显示MJPEG格式视频
- ✅ **实时监控**：监控流状态，及时发现连接问题
- ✅ **自动恢复**：连接断开时自动重试
- ✅ **错误提示**：清晰的错误信息和状态指示

### 持续按键控制
- ✅ **真正持续控制**：按住键盘时机器狗持续移动
- ✅ **即时停止**：松开键盘时机器狗立即停止
- ✅ **组合按键支持**：支持多个按键同时按下（如W+A斜向移动）
- ✅ **安全机制**：窗口失焦、连接断开时自动停止

## 测试验证

### 视频流测试
1. **连接测试**：
   ```bash
   # 测试MJPEG流是否可访问
   curl -I http://***********:8000/camera/stream
   ```

2. **浏览器测试**：
   - 直接在浏览器中访问视频流URL
   - 应该能看到连续的MJPEG图像

### 持续控制测试
1. **单键测试**：
   - 按住W键，机器狗应持续前进
   - 松开W键，机器狗应立即停止

2. **组合键测试**：
   - 同时按住W+A，机器狗应斜向前左移动
   - 松开任一键，移动方向应相应改变

3. **安全测试**：
   - 按住移动键后切换窗口，机器狗应自动停止
   - 按空格键，应立即紧急停止

## 配置说明

### 环境变量
```bash
# 视频流配置
VITE_VIDEO_STREAM_HOST=***********
VITE_VIDEO_STREAM_PORT=8000

# 控制参数
VITE_ROBOT_CONTROL_MOVE_SPEED=0.5
VITE_ROBOT_CONTROL_ROTATE_SPEED=0.5
```

### 控制参数调整
- **移动速度**：0.1-1.0，建议0.5
- **旋转速度**：0.1-1.0，建议0.5
- **命令频率**：100ms（固定，不建议修改）

## 注意事项

### MJPEG流
- 确保视频流服务器正常运行
- 检查网络连接和防火墙设置
- MJPEG流比普通视频流消耗更多带宽

### 持续控制
- 长时间按键可能导致机器狗电池消耗较快
- 建议适当休息，避免过度使用
- 紧急情况下使用空格键或紧急停止按钮

### 浏览器兼容性
- 建议使用Chrome、Firefox等现代浏览器
- 确保浏览器允许自动播放媒体内容
- 某些浏览器可能对MJPEG流有特殊处理

## 故障排除

### 视频流问题
1. **无法显示视频**：
   - 检查视频流URL是否正确
   - 确认视频流服务器是否运行
   - 检查网络连接

2. **视频卡顿**：
   - 检查网络带宽
   - 尝试刷新视频流
   - 检查服务器性能

### 控制问题
1. **按键无响应**：
   - 确认键盘控制已启用
   - 检查WebSocket连接状态
   - 确认浏览器焦点在控制面板上

2. **移动不连续**：
   - 检查网络延迟
   - 确认WebSocket连接稳定
   - 检查机器狗硬件状态

通过这些修复，机器狗控制系统现在能够：
- 正确显示MJPEG格式的视频流
- 实现真正的持续按键控制
- 提供更好的用户体验和安全保障
