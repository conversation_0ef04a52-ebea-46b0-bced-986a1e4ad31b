// 全局组件的类型声明
import { DefineComponent } from 'vue'

// 为环境组件声明类型
declare module 'src/views/environment/*' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 为所有视图组件声明类型
declare module 'src/views/*/*' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 为TaskScheduling声明类型
declare module 'src/types/taskScheduling' {
  export interface PeriodicTask {
    id?: string;
    name: string;
    description: string;
    type: string;
    deviceIds: any[];
    cycleType: string;
    cycleValue: number;
    startTime: Date | string;
    priority: string;
    status: string;
    enabled: boolean;
    createdAt?: string;
    updatedAt?: string;
    [key: string]: any; // 允许任意额外属性
  }

  export interface TrajectoryData {
    trajectoryId: string;
    taskId?: string | null;
    deviceId: string;
    deviceName: string;
    startTime: string;
    endTime: string;
    pathPoints: any[];
    distance: number;
    duration: number;
    status: string;
    [key: string]: any; // 允许任意额外属性
  }
} 