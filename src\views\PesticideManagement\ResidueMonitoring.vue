<!-- 
  ResidueMonitoring.vue
  农药残留监测模块
  实时监控农田各监测点的农药残留数据，确保农产品安全
-->
<template>
  <div class="residue-monitoring">
    <!-- 页面标题 -->
    <PageHeader
      title="农药残留监测"
      description="实时监控农田各监测点的农药残留数据，确保农产品安全"
      icon="Monitor"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ monitoringPoints.length }}</span>
            <span class="summary-label">监测点总数</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getNormalPointsCount() }}</span>
            <span class="summary-label">正常</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getWarningPointsCount() }}</span>
            <span class="summary-label">警告</span>
          </div>
        </div>
      </template>
    </PageHeader>

    <!-- 监测数据看板 -->
    <div class="monitoring-dashboard">
      <div class="dashboard-cards">
        <div class="dashboard-card">
          <div class="card-header">
            <span class="card-title">监测点总数</span>
            <el-icon class="card-icon"><LocationInformation /></el-icon>
          </div>
          <div class="card-value">{{ monitoringPoints.length }}</div>
          <div class="card-footer">
            <span :class="['trend', { up: getNewPointsCount() > 0 }]">
              <el-icon v-if="getNewPointsCount() > 0"><ArrowUp /></el-icon>
              <el-icon v-else><Minus /></el-icon>
              {{ getNewPointsCount() }} 个新增
            </span>
          </div>
        </div>
        
        <div class="dashboard-card">
          <div class="card-header">
            <span class="card-title">正常监测点</span>
            <el-icon class="card-icon success"><SuccessFilled /></el-icon>
          </div>
          <div class="card-value">{{ getNormalPointsCount() }}</div>
          <div class="card-footer">
            <span>占比 {{ getNormalPointsPercentage() }}%</span>
          </div>
        </div>
        
        <div class="dashboard-card">
          <div class="card-header">
            <span class="card-title">警告监测点</span>
            <el-icon class="card-icon warning"><WarningFilled /></el-icon>
          </div>
          <div class="card-value">{{ getWarningPointsCount() }}</div>
          <div class="card-footer">
            <span>占比 {{ getWarningPointsPercentage() }}%</span>
          </div>
        </div>
        
        <div class="dashboard-card">
          <div class="card-header">
            <span class="card-title">危险监测点</span>
            <el-icon class="card-icon danger"><CircleCloseFilled /></el-icon>
          </div>
          <div class="card-value">{{ getDangerPointsCount() }}</div>
          <div class="card-footer">
            <span>占比 {{ getDangerPointsPercentage() }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="monitoring-content">
      <!-- 监测点列表 -->
      <div class="monitoring-points">
        <div class="list-header">
          <h3>监测点列表</h3>
          <div class="list-filters">
            <el-select v-model="filterType" placeholder="监测类型" size="small">
              <el-option label="全部" value="" />
              <el-option label="土壤" value="soil" />
              <el-option label="水质" value="water" />
              <el-option label="空气" value="air" />
            </el-select>
            <el-select v-model="filterStatus" placeholder="状态" size="small">
              <el-option label="全部" value="" />
              <el-option label="正常" value="normal" />
              <el-option label="警告" value="warning" />
              <el-option label="危险" value="danger" />
            </el-select>
            <el-button type="primary" size="small" @click="addMonitoringPoint">
              <el-icon><Plus /></el-icon> 添加监测点
            </el-button>
          </div>
        </div>
        
        <div class="points-table">
          <el-table
            v-loading="loading"
            :data="filteredPoints"
            style="width: 100%"
            @row-click="selectPoint"
            :highlight-current-row="true"
          >
            <el-table-column prop="name" label="监测点名称" min-width="150">
              <template #default="{ row }">
                <div class="point-name">
                  <div :class="['status-dot', row.status]"></div>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getTypeTagType(row.type)" size="small" effect="dark">{{ getTypeLabel(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="currentValue" label="当前值" width="120">
              <template #default="{ row }">
                <span>{{ row.currentValue }} {{ row.unit }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)" size="small" effect="dark">{{ getStatusLabel(row.status) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastSampledAt" label="最近采样" width="120">
              <template #default="{ row }">
                <span>{{ formatDate(row.lastSampledAt) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="趋势" width="80">
              <template #default="{ row }">
                <span :class="['trend', row.trend]">
                  <el-icon v-if="row.trend === 'rising'"><ArrowUp /></el-icon>
                  <el-icon v-else-if="row.trend === 'falling'"><ArrowDown /></el-icon>
                  <el-icon v-else><Minus /></el-icon>
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <!-- 监测点详情 -->
      <div class="point-detail" v-if="selectedPoint">
        <div class="detail-header">
          <div class="detail-title">
            <div :class="['status-dot', selectedPoint.status]"></div>
            <h3>{{ selectedPoint.name }}</h3>
          </div>
          <div class="detail-actions">
            <el-button size="small" @click="editPoint(selectedPoint)">
              <el-icon><Edit /></el-icon> 编辑
            </el-button>
            <el-button size="small" type="danger" @click="confirmDeletePoint">
              <el-icon><Delete /></el-icon> 删除
            </el-button>
          </div>
        </div>
        
        <div class="detail-info">
          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="监测类型">
              {{ getTypeLabel(selectedPoint.type) }}
            </el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getStatusTagType(selectedPoint.status)" size="small" effect="dark">
                {{ getStatusLabel(selectedPoint.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="当前值">
              {{ selectedPoint.currentValue }} {{ selectedPoint.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="标准值">
              {{ selectedPoint.standardValue }} {{ selectedPoint.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="最近采样">
              {{ formatDate(selectedPoint.lastSampledAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="趋势">
              <span :class="['trend', selectedPoint.trend]">
                {{ getTrendLabel(selectedPoint.trend) }}
              </span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="detail-chart">
          <h4>历史趋势</h4>
          <div id="chartContainer" class="chart"></div>
        </div>
        
        <div class="detail-actions-bottom">
          <el-button type="primary" size="small" @click="recordNewSample">
            <el-icon><Plus /></el-icon> 记录新样本
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="正常监测点" />
        <StatusIndicator type="warning" label="警告监测点" />
        <StatusIndicator type="error" label="危险监测点" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Monitor, 
  Plus, 
  ArrowUp, 
  ArrowDown, 
  Minus,
  LocationInformation,
  Edit,
  Delete,
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  Refresh
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue';
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue';

// 模拟数据类型
interface HistoricalDataPoint {
  date: string;
  value: number;
}

interface MonitoringPoint {
  id: string;
  name: string;
  type: string;
  status: string;
  currentValue: number;
  standardValue: number;
  unit: string;
  lastSampledAt: string;
  createdAt: string;
  trend: string;
  historicalData?: HistoricalDataPoint[];
}

// 数据状态
const loading = ref(false)
const monitoringPoints = ref<MonitoringPoint[]>([])
const selectedPoint = ref<MonitoringPoint | null>(null)
const chartInstance = ref<echarts.ECharts | null>(null)
const lastUpdateTime = ref(new Date())

// 筛选条件
const filterType = ref('')
const filterStatus = ref('')

// 筛选后的监测点
const filteredPoints = computed(() => {
  return monitoringPoints.value.filter(point => {
    const typeMatch = !filterType.value || point.type === filterType.value
    const statusMatch = !filterStatus.value || point.status === filterStatus.value
    return typeMatch && statusMatch
  })
})

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 获取监测点数据 - 模拟数据
const fetchMonitoringPoints = async () => {
  loading.value = true;
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据
    const mockData: MonitoringPoint[] = [
      {
        id: 'MP001',
        name: '东区水稻田A-1',
        type: 'soil',
        status: 'normal',
        currentValue: 0.03,
        standardValue: 0.05,
        unit: 'mg/kg',
        lastSampledAt: new Date(Date.now() - 3600000).toISOString(),
        createdAt: new Date(Date.now() - 30 * 24 * 3600000).toISOString(),
        trend: 'stable',
        historicalData: Array(14).fill(0).map((_, i) => ({
          date: new Date(Date.now() - (13 - i) * 24 * 3600000).toLocaleDateString('zh-CN'),
          value: 0.02 + Math.random() * 0.02
        }))
      },
      {
        id: 'MP002',
        name: '西区蔬菜大棚B-3',
        type: 'soil',
        status: 'warning',
        currentValue: 0.06,
        standardValue: 0.05,
        unit: 'mg/kg',
        lastSampledAt: new Date(Date.now() - 7200000).toISOString(),
        createdAt: new Date(Date.now() - 60 * 24 * 3600000).toISOString(),
        trend: 'rising',
        historicalData: Array(14).fill(0).map((_, i) => ({
          date: new Date(Date.now() - (13 - i) * 24 * 3600000).toLocaleDateString('zh-CN'),
          value: 0.04 + (i * 0.002) + (Math.random() * 0.01)
        }))
      },
      {
        id: 'MP003',
        name: '南区果园C-2',
        type: 'water',
        status: 'normal',
        currentValue: 0.008,
        standardValue: 0.01,
        unit: 'mg/L',
        lastSampledAt: new Date(Date.now() - 1800000).toISOString(),
        createdAt: new Date(Date.now() - 45 * 24 * 3600000).toISOString(),
        trend: 'falling',
        historicalData: Array(14).fill(0).map((_, i) => ({
          date: new Date(Date.now() - (13 - i) * 24 * 3600000).toLocaleDateString('zh-CN'),
          value: 0.012 - (i * 0.0003) + (Math.random() * 0.002)
        }))
      },
      {
        id: 'MP004',
        name: '北区大棚D-5',
        type: 'air',
        status: 'danger',
        currentValue: 0.12,
        standardValue: 0.08,
        unit: 'mg/m³',
        lastSampledAt: new Date(Date.now() - 900000).toISOString(),
        createdAt: new Date(Date.now() - 20 * 24 * 3600000).toISOString(),
        trend: 'rising',
        historicalData: Array(14).fill(0).map((_, i) => ({
          date: new Date(Date.now() - (13 - i) * 24 * 3600000).toLocaleDateString('zh-CN'),
          value: 0.07 + (i * 0.004) + (Math.random() * 0.01)
        }))
      },
      {
        id: 'MP005',
        name: '中心区水源E-1',
        type: 'water',
        status: 'normal',
        currentValue: 0.005,
        standardValue: 0.01,
        unit: 'mg/L',
        lastSampledAt: new Date(Date.now() - 5400000).toISOString(),
        createdAt: new Date(Date.now() - 5 * 24 * 3600000).toISOString(),
        trend: 'stable',
        historicalData: Array(14).fill(0).map((_, i) => ({
          date: new Date(Date.now() - (13 - i) * 24 * 3600000).toLocaleDateString('zh-CN'),
          value: 0.004 + Math.random() * 0.003
        }))
      }
    ];
    
    monitoringPoints.value = mockData;
    
    if (mockData.length > 0 && !selectedPoint.value) {
      selectPoint(mockData[0]);
    }
    
    lastUpdateTime.value = new Date();
  } catch (error) {
    console.error('Failed to fetch monitoring points:', error);
    ElMessage.error('获取监测点数据失败');
  } finally {
    loading.value = false;
  }
};

// 选择监测点
const selectPoint = (point: MonitoringPoint) => {
  selectedPoint.value = point;
};

// 添加监测点
const addMonitoringPoint = () => {
  ElMessage.info('添加监测点功能待实现');
};

// 编辑监测点
const editPoint = (point: MonitoringPoint) => {
  ElMessage.info(`编辑监测点 "${point.name}" 功能待实现`);
};

// 确认删除监测点
const confirmDeletePoint = () => {
  if (!selectedPoint.value) return;
  
  ElMessageBox.confirm(
    `确定要删除监测点 "${selectedPoint.value.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    deletePoint();
  }).catch(() => {
    // 取消删除，不做任何操作
  });
};

// 删除监测点
const deletePoint = async () => {
  if (!selectedPoint.value) return;
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.success('监测点已删除');
    
    // 从列表中移除
    monitoringPoints.value = monitoringPoints.value.filter(
      point => point.id !== selectedPoint.value?.id
    );
    selectedPoint.value = null;
  } catch (error) {
    console.error('Failed to delete monitoring point:', error);
    ElMessage.error('删除监测点失败');
  }
};

// 记录新样本
const recordNewSample = () => {
  ElMessage.info('记录新样本功能待实现');
};

// 初始化图表
const initChart = () => {
  const chartContainer = document.getElementById('chartContainer');
  if (!chartContainer) return;
  
  chartInstance.value = echarts.init(chartContainer);
  
  // 在选择监测点时更新图表
  updateChart();
  
  window.addEventListener('resize', () => {
    chartInstance.value?.resize();
  });
};

// 更新图表
const updateChart = () => {
  if (!chartInstance.value || !selectedPoint.value) return;
  
  const historicalData = selectedPoint.value.historicalData || [];
  
  const dates = historicalData.map(item => item.date);
  const values = historicalData.map(item => item.value);
  
  const option = {
    grid: {
      top: 40,
      bottom: 30,
      left: 50,
      right: 30
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        const data = params[0];
        return `${data.name}<br />${data.value.toFixed(4)} ${selectedPoint.value?.unit}`;
      },
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        color: '#9ca3af',
        fontSize: 10,
        rotate: 45
      },
      axisLine: {
        lineStyle: {
          color: '#4b5563'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: `残留量(${selectedPoint.value.unit})`,
      nameTextStyle: {
        color: '#9ca3af'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#6b7280'
        }
      },
      axisLabel: {
        color: '#9ca3af',
        formatter: function(value: number) {
          return value.toFixed(4);
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(75, 85, 99, 0.1)'
        }
      }
    },
    visualMap: {
      show: false,
      pieces: [{
        gt: selectedPoint.value.standardValue,
        lte: selectedPoint.value.standardValue * 1.5,
        color: '#f59e0b'
      }, {
        gt: selectedPoint.value.standardValue * 1.5,
        color: '#ef4444'
      }, {
        lte: selectedPoint.value.standardValue,
        color: '#10b981'
      }]
    },
    series: [{
      name: '残留量',
      data: values,
      type: 'line',
      smooth: true,
      symbol: 'emptyCircle',
      symbolSize: 8,
      lineStyle: {
        width: 3,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 10
      },
      itemStyle: {
        borderColor: '#1f2937',
        borderWidth: 2
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(59, 130, 246, 0.5)' },
          { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
        ])
      },
      markLine: {
        silent: true,
        lineStyle: {
          color: '#f59e0b',
          type: 'dashed'
        },
        data: [
          {
            yAxis: selectedPoint.value.standardValue,
            name: '标准值'
          }
        ],
        label: {
          formatter: '标准值',
          position: 'insideEndTop',
          color: '#f59e0b'
        }
      }
    }]
  };
  
  chartInstance.value.setOption(option);
};

// 监听窗口大小变化，调整图表
window.addEventListener('resize', () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
});

// 监听选中的监测点变化
watch(selectedPoint, () => {
  if (selectedPoint.value) {
    // 等待 DOM 更新后再初始化或更新图表
    nextTick(() => {
      if (!chartInstance.value) {
        initChart();
      } else {
        updateChart();
      }
    });
  }
});

// 获取类型标签样式
const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    'soil': 'success',
    'water': 'info',
    'air': 'warning'
  };
  return types[type] || 'info';
};

// 获取状态标签样式
const getStatusTagType = (status: string) => {
  const statuses: Record<string, string> = {
    'normal': 'success',
    'warning': 'warning',
    'danger': 'danger'
  };
  return statuses[status] || 'info';
};

// 获取类型显示名称
const getTypeLabel = (type: string) => {
  const types: Record<string, string> = {
    'soil': '土壤',
    'water': '水质',
    'air': '空气'
  };
  return types[type] || type;
};

// 获取状态显示名称
const getStatusLabel = (status: string) => {
  const statuses: Record<string, string> = {
    'normal': '正常',
    'warning': '警告',
    'danger': '危险'
  };
  return statuses[status] || status;
};

// 获取趋势显示名称
const getTrendLabel = (trend: string) => {
  const trends: Record<string, string> = {
    'rising': '上升',
    'falling': '下降',
    'stable': '稳定'
  };
  return trends[trend] || trend;
};

// 获取正常监测点数量
const getNormalPointsCount = () => {
  return monitoringPoints.value.filter(point => point.status === 'normal').length;
};

// 获取警告监测点数量
const getWarningPointsCount = () => {
  return monitoringPoints.value.filter(point => point.status === 'warning').length;
};

// 获取危险监测点数量
const getDangerPointsCount = () => {
  return monitoringPoints.value.filter(point => point.status === 'danger').length;
};

// 获取新增监测点数量
const getNewPointsCount = () => {
  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  
  return monitoringPoints.value.filter(point => {
    const createdAt = new Date(point.createdAt);
    return createdAt >= oneWeekAgo;
  }).length;
};

// 获取正常监测点百分比
const getNormalPointsPercentage = () => {
  if (monitoringPoints.value.length === 0) return 0;
  return Math.round((getNormalPointsCount() / monitoringPoints.value.length) * 100);
};

// 获取警告监测点百分比
const getWarningPointsPercentage = () => {
  if (monitoringPoints.value.length === 0) return 0;
  return Math.round((getWarningPointsCount() / monitoringPoints.value.length) * 100);
};

// 获取危险监测点百分比
const getDangerPointsPercentage = () => {
  if (monitoringPoints.value.length === 0) return 0;
  return Math.round((getDangerPointsCount() / monitoringPoints.value.length) * 100);
};

// 刷新数据
const refreshData = () => {
  fetchMonitoringPoints();
  ElMessage.success('数据已更新');
};

// 数据更新计时器
let dataUpdateInterval: number | null = null;

// 组件挂载时
onMounted(async () => {
  await fetchMonitoringPoints();
  
  // 启动数据自动更新
  dataUpdateInterval = window.setInterval(() => {
    // 模拟数据小幅度波动
    monitoringPoints.value.forEach(point => {
      if (point.historicalData && point.historicalData.length > 0) {
        const lastValue = point.historicalData[point.historicalData.length - 1].value;
        const fluctuation = (Math.random() - 0.5) * 0.005;
        
        // 根据趋势调整波动方向
        let newValue;
        if (point.trend === 'rising') {
          newValue = lastValue + Math.abs(fluctuation);
        } else if (point.trend === 'falling') {
          newValue = lastValue - Math.abs(fluctuation);
        } else {
          newValue = lastValue + fluctuation;
        }
        
        point.currentValue = Math.max(0, newValue);
        
        // 更新状态
        if (point.currentValue > point.standardValue * 1.5) {
          point.status = 'danger';
        } else if (point.currentValue > point.standardValue) {
          point.status = 'warning';
        } else {
          point.status = 'normal';
        }
      }
    });
    
    lastUpdateTime.value = new Date();
    
    // 如果有选中的点，更新图表
    if (selectedPoint.value && chartInstance.value) {
      updateChart();
    }
  }, 30000); // 每30秒更新一次
});

onUnmounted(() => {
  // 清除计时器
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
  
  // 销毁图表实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});
</script>

<style scoped>
.residue-monitoring {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 监测数据看板 */
.monitoring-dashboard {
  margin-bottom: 20px;
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.dashboard-card {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 20px;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  color: #e5e7eb;
}

.card-icon {
  font-size: 24px;
  color: #3b82f6;
}

.card-icon.success {
  color: #10b981;
}

.card-icon.warning {
  color: #f59e0b;
}

.card-icon.danger {
  color: #ef4444;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12px;
}

.card-footer {
  color: #9ca3af;
  font-size: 14px;
}

.trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend.up {
  color: #10b981;
}

.trend.rising {
  color: #ef4444;
}

.trend.falling {
  color: #10b981;
}

.trend.stable {
  color: #9ca3af;
}

/* 主内容区域 */
.monitoring-content {
  display: flex;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

/* 监测点列表 */
.monitoring-points {
  flex: 3;
  display: flex;
  flex-direction: column;
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  overflow: hidden;
}

.list-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(75, 85, 99, 0.4);
}

.list-header h3 {
  margin: 0;
  color: #e5e7eb;
}

.list-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.points-table {
  flex: 1;
  overflow: auto;
  padding: 0 16px 16px;
}

.point-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-table) {
  --el-table-header-bg-color: rgba(31, 41, 55, 0.7);
  --el-table-border-color: rgba(75, 85, 99, 0.4);
  --el-table-row-hover-bg-color: rgba(59, 130, 246, 0.1);
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(31, 41, 55, 0.7);
  color: #e5e7eb;
  font-weight: 500;
}

:deep(.el-table tr) {
  background-color: rgba(31, 41, 55, 0.3);
}

:deep(.el-table td) {
  color: #e5e7eb;
  border-bottom-color: rgba(75, 85, 99, 0.4);
}

/* 监测点详情 */
.point-detail {
  flex: 2;
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-title h3 {
  margin: 0;
  color: #e5e7eb;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.detail-info {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 10px;
}

:deep(.el-descriptions) {
  --el-descriptions-item-bordered-label-background: rgba(31, 41, 55, 0.6);
  --el-descriptions-border-color: rgba(75, 85, 99, 0.4);
}

:deep(.el-descriptions__label) {
  color: #9ca3af;
}

:deep(.el-descriptions__content) {
  color: #e5e7eb;
}

.detail-chart {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.detail-chart h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #e5e7eb;
}

.chart {
  flex: 1;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.detail-actions-bottom {
  display: flex;
  justify-content: flex-end;
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: 20px;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.status-dot.normal {
  background-color: #10b981;
}

.status-dot.warning {
  background-color: #f59e0b;
}

.status-dot.danger {
  background-color: #ef4444;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .dashboard-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .monitoring-content {
    flex-direction: column;
  }
  
  .monitoring-points, 
  .point-detail {
    flex: none;
  }
  
  .point-detail {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .dashboard-cards {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .status-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .summary-item {
    flex-direction: row;
    gap: 10px;
  }
}
</style>

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save

同时需要从DeviceManagement模块导入以下组件：
- PageHeader.vue
- StatusIndicator.vue
--> 