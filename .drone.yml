kind: pipeline
type: docker
name: smart-agriculture-frontend-dev

trigger:
  branch:
    - master
    - main
    - develop
  event:
    - push

steps:
  - name: test-trigger
    image: alpine:latest
    commands:
      - echo "Drone trigger successful"
      - date
      - echo "Test completed"

  - name: install-dependencies
    image: node:22-alpine
    environment:
      YARN_REGISTRY: https://registry.npmmirror.com/
      YARN_CACHE_FOLDER: /drone/src/.yarn-cache
      SASS_BINARY_SITE: https://npmmirror.com/mirrors/node-sass/
      ELECTRON_MIRROR: https://npmmirror.com/mirrors/electron/
      PUPPETEER_DOWNLOAD_HOST: https://npmmirror.com/mirrors
      CHROMEDRIVER_CDNURL: https://npmmirror.com/mirrors/chromedriver
    commands:
      - echo "Installing yarn for faster dependency management"
      - npm install -g yarn --registry=https://registry.npmmirror.com/
      - echo "Configuring yarn with China mirror"
      - yarn config set registry https://registry.npmmirror.com/
      - yarn config set cache-folder /drone/src/.yarn-cache
      - echo "Installing dependencies with yarn (much faster than npm)"
      - yarn install --frozen-lockfile --network-timeout 60000 --network-concurrency 1
      - echo "Dependencies installed successfully with yarn"
    volumes:
      - name: yarn-cache
        path: /drone/src/.yarn-cache
    depends_on:
      - test-trigger

  - name: lint-check
    image: node:22-alpine
    environment:
      YARN_REGISTRY: https://registry.npmmirror.com/
      YARN_CACHE_FOLDER: /drone/src/.yarn-cache
    commands:
      - echo "Installing yarn"
      - npm install -g yarn --registry=https://registry.npmmirror.com/
      - echo "Running code lint check"
      - yarn lint
      - echo "Lint check passed"
    volumes:
      - name: yarn-cache
        path: /drone/src/.yarn-cache
    depends_on:
      - install-dependencies

  - name: build-project
    image: node:22-alpine
    environment:
      YARN_REGISTRY: https://registry.npmmirror.com/
      YARN_CACHE_FOLDER: /drone/src/.yarn-cache
      NODE_ENV: production
    commands:
      - echo "Installing yarn"
      - npm install -g yarn --registry=https://registry.npmmirror.com/
      - echo "Building project"
      - yarn build-skip-check
      - echo "Build completed successfully"
      - ls -la dist/
    volumes:
      - name: yarn-cache
        path: /drone/src/.yarn-cache
    depends_on:
      - lint-check

  - name: docker-build
    image: plugins/docker
    settings:
      repo: smart-agriculture-frontend
      tags:
        - latest
        - dev-${DRONE_BUILD_NUMBER}
      dockerfile: Dockerfile
      build_args:
        - BUILD_MODE=build
      context: .
    depends_on:
      - build-project

  - name: deploy-to-dev
    image: appleboy/drone-ssh
    settings:
      host:
        from_secret: dev_server_host
      username:
        from_secret: dev_server_user
      key:
        from_secret: dev_server_key
      port: 22
      script:
        - echo "Starting deployment to development environment"
        - cd /opt/smart-agriculture-frontend
        - docker pull smart-agriculture-frontend:latest
        - docker stop smart-agriculture-h5-dev || true
        - docker rm smart-agriculture-h5-dev || true
        - docker run -d --name smart-agriculture-h5-dev --restart unless-stopped -p 5174:5174 smart-agriculture-frontend:latest
        - echo "Deployment completed successfully"
    depends_on:
      - docker-build

  - name: health-check
    image: curlimages/curl:latest
    commands:
      - echo "Performing health check"
      - sleep 30
      - echo "Health check completed"
    depends_on:
      - deploy-to-dev

volumes:
  - name: yarn-cache
    temp: {}
