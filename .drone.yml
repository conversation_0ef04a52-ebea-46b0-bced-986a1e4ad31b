# 智慧农业前端 Drone CI/CD 配置 - 简化测试版
kind: pipeline
type: docker
name: smart-agriculture-frontend-dev

# 流水线触发条件
trigger:
  branch:
    - master
    - main
    - develop
  event:
    - push

# 流水线步骤
steps:
  # 测试步骤: 验证触发是否正常
  - name: test-trigger
    image: alpine:latest
    commands:
      - echo "🎉 Drone 触发成功！"
      - echo "分支: $DRONE_BRANCH"
      - echo "提交: $DRONE_COMMIT"
      - echo "构建号: $DRONE_BUILD_NUMBER"
      - date
      - echo "✅ 基础测试通过，Drone 配置正常"

  # 步骤2: 简单的Node.js测试
  - name: node-test
    image: node:22-alpine
    commands:
      - echo "📦 Node.js 环境测试..."
      - node --version
      - npm --version
      - echo "✅ Node.js 环境正常"
    depends_on:
      - test-trigger
