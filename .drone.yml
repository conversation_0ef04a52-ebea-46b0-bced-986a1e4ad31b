kind: pipeline
type: docker
name: smart-agriculture-frontend-dev

trigger:
  branch:
    - master
    - main
    - develop
  event:
    - push

steps:
  - name: test-trigger
    image: alpine:latest
    commands:
      - echo "Drone trigger successful"
      - date
      - echo "Test completed"

  - name: docker-build
    image: plugins/docker
    settings:
      repo: smart-agriculture-frontend
      tags:
        - latest
        - dev-${DRONE_BUILD_NUMBER}
      dockerfile: Dockerfile
      build_args:
        - BUILD_MODE=build
        - NPM_REGISTRY=https://registry.npm.taobao.org/
      context: .
    depends_on:
      - test-trigger

  - name: deploy-to-dev
    image: appleboy/drone-ssh
    settings:
      host:
        from_secret: dev_server_host
      username:
        from_secret: dev_server_user
      key:
        from_secret: dev_server_key
      port: 22
      script:
        - echo "Starting deployment to development environment"
        - cd /opt/smart-agriculture-frontend
        - docker pull smart-agriculture-frontend:latest
        - docker stop smart-agriculture-h5-dev || true
        - docker rm smart-agriculture-h5-dev || true
        - docker run -d --name smart-agriculture-h5-dev --restart unless-stopped -p 5174:5174 smart-agriculture-frontend:latest
        - echo "Deployment completed successfully"
    depends_on:
      - docker-build

  - name: health-check
    image: curlimages/curl:latest
    commands:
      - echo "Performing health check"
      - sleep 30
      - echo "Health check completed"
    depends_on:
      - deploy-to-dev


