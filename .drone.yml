kind: pipeline
type: docker
name: smart-agriculture-frontend-dev

trigger:
  branch:
    - master
    - main
    - develop
  event:
    - push

steps:
  - name: test-trigger
    image: alpine:latest
    commands:
      - echo "Drone trigger successful"
      - date
      - echo "Test completed"

  - name: install-dependencies
    image: node:22-alpine
    environment:
      NPM_CONFIG_REGISTRY: https://registry.npmmirror.com/
      NPM_CONFIG_DISTURL: https://npmmirror.com/dist
      NPM_CONFIG_CACHE: /drone/src/.npm-cache
    commands:
      - echo "Configuring npm with China mirror"
      - npm config set registry https://registry.npmmirror.com/
      - npm config set disturl https://npmmirror.com/dist
      - npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
      - npm config set electron_mirror https://npmmirror.com/mirrors/electron/
      - npm config set puppeteer_download_host https://npmmirror.com/mirrors
      - npm config set chromedriver_cdnurl https://npmmirror.com/mirrors/chromedriver
      - npm config set operadriver_cdnurl https://npmmirror.com/mirrors/operadriver
      - npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs
      - npm config set selenium_cdnurl https://npmmirror.com/mirrors/selenium
      - npm config set node_inspector_cdnurl https://npmmirror.com/mirrors/node-inspector
      - echo "Installing dependencies with timeout settings"
      - npm install --registry=https://registry.npmmirror.com/ --network-timeout=300000 --fetch-timeout=300000 --fetch-retries=3
      - echo "Dependencies installed successfully"
    volumes:
      - name: npm-cache
        path: /drone/src/.npm-cache
    depends_on:
      - test-trigger

  - name: lint-check
    image: node:22-alpine
    environment:
      NPM_CONFIG_REGISTRY: https://registry.npmmirror.com/
      NPM_CONFIG_CACHE: /drone/src/.npm-cache
    commands:
      - echo "Running code lint check"
      - npm run lint
      - echo "Lint check passed"
    volumes:
      - name: npm-cache
        path: /drone/src/.npm-cache
    depends_on:
      - install-dependencies

  - name: build-project
    image: node:22-alpine
    environment:
      NPM_CONFIG_REGISTRY: https://registry.npmmirror.com/
      NPM_CONFIG_CACHE: /drone/src/.npm-cache
      NODE_ENV: production
    commands:
      - echo "Building project"
      - npm run build-skip-check
      - echo "Build completed successfully"
      - ls -la dist/
    volumes:
      - name: npm-cache
        path: /drone/src/.npm-cache
    depends_on:
      - lint-check

  - name: docker-build
    image: plugins/docker
    settings:
      repo: smart-agriculture-frontend
      tags:
        - latest
        - dev-${DRONE_BUILD_NUMBER}
      dockerfile: Dockerfile
      build_args:
        - BUILD_MODE=build
      context: .
    depends_on:
      - build-project

  - name: deploy-to-dev
    image: appleboy/drone-ssh
    settings:
      host:
        from_secret: dev_server_host
      username:
        from_secret: dev_server_user
      key:
        from_secret: dev_server_key
      port: 22
      script:
        - echo "Starting deployment to development environment"
        - cd /opt/smart-agriculture-frontend
        - docker pull smart-agriculture-frontend:latest
        - docker stop smart-agriculture-h5-dev || true
        - docker rm smart-agriculture-h5-dev || true
        - docker run -d --name smart-agriculture-h5-dev --restart unless-stopped -p 5174:5174 smart-agriculture-frontend:latest
        - echo "Deployment completed successfully"
    depends_on:
      - docker-build

  - name: health-check
    image: curlimages/curl:latest
    commands:
      - echo "Performing health check"
      - sleep 30
      - echo "Health check completed"
    depends_on:
      - deploy-to-dev

volumes:
  - name: npm-cache
    temp: {}
