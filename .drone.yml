kind: pipeline
type: docker
name: smart-agriculture-frontend-dev

trigger:
  branch:
    - master
    - main
    - develop
  event:
    - push

steps:
  - name: test-trigger
    image: alpine:latest
    commands:
      - echo "Drone trigger successful"
      - date
      - echo "Test completed"

  - name: install-dependencies
    image: node:22-alpine
    environment:
      NPM_CONFIG_REGISTRY: https://registry.npm.taobao.org/
      NPM_CONFIG_CACHE: /drone/src/.npm-cache
      SASS_BINARY_SITE: https://npm.taobao.org/mirrors/node-sass/
      ELECTRON_MIRROR: https://npm.taobao.org/mirrors/electron/
      PUPPETEER_DOWNLOAD_HOST: https://npm.taobao.org/mirrors
    commands:
      - echo "Using stable Taobao npm registry"
      - npm config set registry https://registry.npm.taobao.org/
      - npm config set cache /drone/src/.npm-cache
      - echo "Installing dependencies with npm and optimized settings"
      - npm install --prefer-offline --no-audit --no-fund --network-timeout=120000 --fetch-timeout=120000 --fetch-retries=5
      - echo "Dependencies installed successfully"
    volumes:
      - name: npm-cache
        path: /drone/src/.npm-cache
    depends_on:
      - test-trigger

  - name: lint-check
    image: node:22-alpine
    environment:
      NPM_CONFIG_REGISTRY: https://registry.npm.taobao.org/
      NPM_CONFIG_CACHE: /drone/src/.npm-cache
    commands:
      - echo "Running code lint check"
      - npm run lint
      - echo "Lint check passed"
    volumes:
      - name: npm-cache
        path: /drone/src/.npm-cache
    depends_on:
      - install-dependencies

  - name: build-project
    image: node:22-alpine
    environment:
      NPM_CONFIG_REGISTRY: https://registry.npm.taobao.org/
      NPM_CONFIG_CACHE: /drone/src/.npm-cache
      NODE_ENV: production
    commands:
      - echo "Building project"
      - npm run build-skip-check
      - echo "Build completed successfully"
      - ls -la dist/
    volumes:
      - name: npm-cache
        path: /drone/src/.npm-cache
    depends_on:
      - lint-check

  - name: docker-build
    image: plugins/docker
    settings:
      repo: smart-agriculture-frontend
      tags:
        - latest
        - dev-${DRONE_BUILD_NUMBER}
      dockerfile: Dockerfile
      build_args:
        - BUILD_MODE=build
      context: .
    depends_on:
      - build-project

  - name: deploy-to-dev
    image: appleboy/drone-ssh
    settings:
      host:
        from_secret: dev_server_host
      username:
        from_secret: dev_server_user
      key:
        from_secret: dev_server_key
      port: 22
      script:
        - echo "Starting deployment to development environment"
        - cd /opt/smart-agriculture-frontend
        - docker pull smart-agriculture-frontend:latest
        - docker stop smart-agriculture-h5-dev || true
        - docker rm smart-agriculture-h5-dev || true
        - docker run -d --name smart-agriculture-h5-dev --restart unless-stopped -p 5174:5174 smart-agriculture-frontend:latest
        - echo "Deployment completed successfully"
    depends_on:
      - docker-build

  - name: health-check
    image: curlimages/curl:latest
    commands:
      - echo "Performing health check"
      - sleep 30
      - echo "Health check completed"
    depends_on:
      - deploy-to-dev

volumes:
  - name: npm-cache
    temp: {}
