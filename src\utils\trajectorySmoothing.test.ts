/**
 * 轨迹平滑算法测试文件
 * 用于验证轨迹平滑功能的正确性
 */

import { TrajectorySmoothing, createTrajectorySmoothing, type TrajectoryPoint } from './trajectorySmoothing';

/**
 * 生成测试轨迹数据
 * 模拟设备从右上角开始沿逆时针方向移动一圈的轨迹
 */
function generateTestTrajectoryData(): TrajectoryPoint[] {
  const testData: TrajectoryPoint[] = [
    // 右上角开始
    { x: 2.467, y: 2.492, timestamp: Date.now(), speed: 0.5 },
    { x: 2.463, y: 2.492, timestamp: Date.now() + 1000, speed: 0.5 },
    { x: 2.459, y: 2.491, timestamp: Date.now() + 2000, speed: 0.5 },
    { x: 2.455, y: 2.490, timestamp: Date.now() + 3000, speed: 0.5 },
    
    // 向左移动
    { x: 2.450, y: 2.489, timestamp: Date.now() + 4000, speed: 0.6 },
    { x: 2.445, y: 2.488, timestamp: Date.now() + 5000, speed: 0.6 },
    { x: 2.440, y: 2.487, timestamp: Date.now() + 6000, speed: 0.6 },
    
    // 向下移动（左上角到左下角）
    { x: 2.435, y: 2.480, timestamp: Date.now() + 7000, speed: 0.7 },
    { x: 2.430, y: 2.470, timestamp: Date.now() + 8000, speed: 0.7 },
    { x: 2.425, y: 2.460, timestamp: Date.now() + 9000, speed: 0.7 },
    { x: 2.420, y: 2.450, timestamp: Date.now() + 10000, speed: 0.7 },
    
    // 向右移动（左下角到右下角）
    { x: 2.430, y: 2.445, timestamp: Date.now() + 11000, speed: 0.8 },
    { x: 2.440, y: 2.440, timestamp: Date.now() + 12000, speed: 0.8 },
    { x: 2.450, y: 2.435, timestamp: Date.now() + 13000, speed: 0.8 },
    { x: 2.460, y: 2.430, timestamp: Date.now() + 14000, speed: 0.8 },
    
    // 向上移动（右下角到右上角）
    { x: 2.465, y: 2.440, timestamp: Date.now() + 15000, speed: 0.6 },
    { x: 2.467, y: 2.450, timestamp: Date.now() + 16000, speed: 0.6 },
    { x: 2.468, y: 2.460, timestamp: Date.now() + 17000, speed: 0.6 },
    { x: 2.469, y: 2.470, timestamp: Date.now() + 18000, speed: 0.6 },
    { x: 2.470, y: 2.480, timestamp: Date.now() + 19000, speed: 0.5 },
    { x: 2.469, y: 2.490, timestamp: Date.now() + 20000, speed: 0.5 },
  ];
  
  return testData;
}

/**
 * 添加噪声到轨迹数据
 */
function addNoiseToTrajectory(data: TrajectoryPoint[], noiseLevel: number = 0.01): TrajectoryPoint[] {
  return data.map(point => ({
    ...point,
    x: point.x + (Math.random() - 0.5) * noiseLevel,
    y: point.y + (Math.random() - 0.5) * noiseLevel
  }));
}

/**
 * 计算轨迹的总变化量（用于评估平滑效果）
 */
function calculateTrajectoryVariation(data: TrajectoryPoint[]): number {
  if (data.length < 2) return 0;
  
  let totalVariation = 0;
  for (let i = 1; i < data.length; i++) {
    const dx = data[i].x - data[i-1].x;
    const dy = data[i].y - data[i-1].y;
    totalVariation += Math.sqrt(dx * dx + dy * dy);
  }
  
  return totalVariation;
}

/**
 * 测试轨迹平滑功能
 */
export function testTrajectorySmoothing(): void {
  console.log('🧪 开始轨迹平滑算法测试...');
  
  // 1. 生成测试数据
  const originalData = generateTestTrajectoryData();
  const noisyData = addNoiseToTrajectory(originalData, 0.02);
  
  console.log(`📊 原始数据点数: ${originalData.length}`);
  console.log(`📊 噪声数据点数: ${noisyData.length}`);
  
  // 2. 创建轨迹平滑处理器
  const smoother = createTrajectorySmoothing({
    smoothingStrength: 0.7,
    movingAverageWindow: 5,
    outlierThreshold: 2.0,
    adaptiveWindow: true
  });
  
  // 3. 处理轨迹数据
  console.log('🔄 开始处理轨迹数据...');
  const startTime = performance.now();
  
  let smoothedData: TrajectoryPoint[] = [];
  noisyData.forEach(point => {
    smoothedData = smoother.addPoint(point);
  });
  
  const endTime = performance.now();
  const processingTime = endTime - startTime;
  
  // 4. 计算性能指标
  const originalVariation = calculateTrajectoryVariation(noisyData);
  const smoothedVariation = calculateTrajectoryVariation(smoothedData);
  const smoothingReduction = ((originalVariation - smoothedVariation) / originalVariation * 100).toFixed(2);
  
  // 5. 输出测试结果
  console.log('\n📈 测试结果:');
  console.log(`✅ 平滑后数据点数: ${smoothedData.length}`);
  console.log(`⏱️  处理时间: ${processingTime.toFixed(2)}ms`);
  console.log(`📉 轨迹变化量减少: ${smoothingReduction}%`);
  console.log(`🎯 平均处理时间: ${(processingTime / noisyData.length).toFixed(3)}ms/点`);
  
  // 6. 性能验证
  if (processingTime < 50) {
    console.log('✅ 性能测试通过: 处理时间在可接受范围内');
  } else {
    console.log('⚠️  性能警告: 处理时间较长，可能需要优化');
  }
  
  // 7. 平滑效果验证
  if (parseFloat(smoothingReduction) > 10) {
    console.log('✅ 平滑效果测试通过: 成功减少轨迹噪声');
  } else {
    console.log('⚠️  平滑效果警告: 平滑效果不明显');
  }
  
  // 8. 数据完整性验证
  if (smoothedData.length > 0 && smoothedData.every(point => 
    typeof point.x === 'number' && 
    typeof point.y === 'number' && 
    typeof point.timestamp === 'number'
  )) {
    console.log('✅ 数据完整性测试通过: 所有数据点格式正确');
  } else {
    console.log('❌ 数据完整性测试失败: 存在无效数据点');
  }
  
  console.log('\n🎉 轨迹平滑算法测试完成!');
  
  return {
    originalCount: originalData.length,
    noisyCount: noisyData.length,
    smoothedCount: smoothedData.length,
    processingTime,
    smoothingReduction: parseFloat(smoothingReduction),
    performancePass: processingTime < 50,
    smoothingPass: parseFloat(smoothingReduction) > 10,
    dataIntegrityPass: smoothedData.length > 0
  };
}

/**
 * 在浏览器控制台中运行测试
 */
if (typeof window !== 'undefined') {
  // 将测试函数暴露到全局作用域，方便在浏览器控制台中调用
  (window as any).testTrajectorySmoothing = testTrajectorySmoothing;
  console.log('💡 提示: 在浏览器控制台中运行 testTrajectorySmoothing() 来测试轨迹平滑功能');
}

// 如果在Node.js环境中，直接运行测试
if (typeof process !== 'undefined' && process.versions && process.versions.node) {
  testTrajectorySmoothing();
}
