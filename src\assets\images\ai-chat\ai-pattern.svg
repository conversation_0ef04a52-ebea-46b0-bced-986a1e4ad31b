<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
  <defs>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00e676" />
      <stop offset="100%" stop-color="#00c853" />
    </linearGradient>
  </defs>
  
  <!-- AI图案装饰 - 大脑/电路风格 -->
  <rect x="5" y="5" width="30" height="30" fill="none" stroke="url(#aiGradient)" stroke-width="0.3" rx="2" />
  
  <!-- 中心处理器 -->
  <rect x="15" y="15" width="10" height="10" fill="none" stroke="url(#aiGradient)" stroke-width="0.5" rx="1" />
  
  <!-- 连接线 -->
  <line x1="20" y1="5" x2="20" y2="15" stroke="url(#aiGradient)" stroke-width="0.5" />
  <line x1="20" y1="25" x2="20" y2="35" stroke="url(#aiGradient)" stroke-width="0.5" />
  <line x1="5" y1="20" x2="15" y2="20" stroke="url(#aiGradient)" stroke-width="0.5" />
  <line x1="25" y1="20" x2="35" y2="20" stroke="url(#aiGradient)" stroke-width="0.5" />
  
  <!-- 对角线连接 -->
  <path d="M5,5 L15,15" stroke="url(#aiGradient)" stroke-width="0.3" />
  <path d="M35,5 L25,15" stroke="url(#aiGradient)" stroke-width="0.3" />
  <path d="M5,35 L15,25" stroke="url(#aiGradient)" stroke-width="0.3" />
  <path d="M35,35 L25,25" stroke="url(#aiGradient)" stroke-width="0.3" />
  
  <!-- 节点 -->
  <circle cx="20" cy="5" r="1" fill="#00e676" opacity="0.5" />
  <circle cx="20" cy="35" r="1" fill="#00e676" opacity="0.5" />
  <circle cx="5" cy="20" r="1" fill="#00e676" opacity="0.5" />
  <circle cx="35" cy="20" r="1" fill="#00e676" opacity="0.5" />
  
  <!-- 中心点 -->
  <circle cx="20" cy="20" r="2" fill="#00e676" opacity="0.5" />
</svg> 