<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleVisibleChange"
    title="编辑巡航路径"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="path-edit-container">
      <!-- 路径信息概览 -->
      <div class="path-overview">
        <el-alert
          :title="`当前路径包含 ${editablePoints.length} 个路径点`"
          type="info"
          :closable="false"
          show-icon
        />
        <div class="overview-stats">
          <el-statistic title="路径点数量" :value="editablePoints.length" />
          <el-statistic title="预计总距离" :value="totalDistance" suffix="米" :precision="1" />
          <el-statistic
            v-if="currentPosition && (currentPosition.x !== 0 || currentPosition.y !== 0)"
            title="机器狗位置"
            :value="`(${currentPosition.x.toFixed(1)}, ${currentPosition.y.toFixed(1)})`"
          />
          <el-statistic
            v-if="isActive && nearestPathPoint"
            title="最近路径点"
            :value="nearestPathPoint.name || '未知'"
          />
        </div>
      </div>

      <!-- 路径点列表 -->
      <div class="path-points-section">
        <div class="section-header">
          <h4>路径点列表</h4>
          <div class="header-actions">
            <el-button size="small" @click="addNewPoint">
              <el-icon><Plus /></el-icon>
              添加路径点
            </el-button>
            <el-button size="small" @click="resetToOriginal" :disabled="!hasChanges">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </div>
        </div>

        <div class="points-list">
          <div
            v-for="(point, index) in editablePoints"
            :key="point.id || index"
            class="point-item"
            :class="{ 'point-error': !isPointValid(point) }"
          >
            <div class="point-header">
              <div class="point-index">{{ index + 1 }}</div>
              <div class="point-actions">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="movePointUp(index)"
                  :disabled="index === 0"
                >
                  <el-icon><ArrowUp /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="movePointDown(index)"
                  :disabled="index === editablePoints.length - 1"
                >
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  text
                  @click="removePoint(index)"
                  :disabled="editablePoints.length <= 2"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>

            <div class="point-content">
              <div class="point-field">
                <label>名称</label>
                <el-input
                  v-model="point.name"
                  placeholder="路径点名称"
                  size="small"
                  maxlength="20"
                  show-word-limit
                />
              </div>

              <div class="point-coordinates">
                <div class="point-field">
                  <label>X坐标 (米)</label>
                  <el-input-number
                    v-model="point.x"
                    :precision="2"
                    :step="0.1"
                    size="small"
                    :min="anchorBounds?.minX || -100"
                    :max="anchorBounds?.maxX || 100"
                    @change="validatePoint(point, index)"
                  />
                </div>

                <div class="point-field">
                  <label>Y坐标 (米)</label>
                  <el-input-number
                    v-model="point.y"
                    :precision="2"
                    :step="0.1"
                    size="small"
                    :min="anchorBounds?.minY || -100"
                    :max="anchorBounds?.maxY || 100"
                    @change="validatePoint(point, index)"
                  />
                </div>
              </div>

              <!-- 路径点状态和距离信息 -->
              <div class="point-info">
                <div class="point-status">
                  <el-tag
                    :type="isPointInBounds(point) ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ isPointInBounds(point) ? '安全区域内' : '超出边界' }}
                  </el-tag>
                  <span v-if="index > 0" class="distance-info">
                    距离上一点: {{ getDistanceToLastPoint(index).toFixed(1) }}m
                  </span>
                  <span
                    v-if="currentPosition && (currentPosition.x !== 0 || currentPosition.y !== 0)"
                    class="distance-info robot-distance"
                  >
                    距离机器狗: {{ calculateDistance(point, currentPosition).toFixed(1) }}m
                  </span>
                </div>

                <div v-if="!isPointValid(point)" class="point-errors">
                  <el-alert
                    v-for="error in getPointErrors(point)"
                    :key="error"
                    :title="error"
                    type="error"
                    size="small"
                    :closable="false"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 路径预览 -->
      <div class="path-preview-section">
        <h4>路径预览</h4>
        <div class="preview-container">
          <div class="preview-canvas" ref="previewCanvas">
            <svg
              :width="canvasWidth"
              :height="canvasHeight"
              viewBox="0 0 400 300"
              class="path-svg"
            >
              <!-- 基站边界 -->
              <rect
                v-if="anchorBounds"
                :x="scaleX(anchorBounds.minX)"
                :y="scaleY(anchorBounds.maxY)"
                :width="scaleX(anchorBounds.maxX) - scaleX(anchorBounds.minX)"
                :height="scaleY(anchorBounds.minY) - scaleY(anchorBounds.maxY)"
                fill="rgba(64, 158, 255, 0.1)"
                stroke="rgba(64, 158, 255, 0.5)"
                stroke-width="2"
                stroke-dasharray="5,5"
              />

              <!-- 路径线 -->
              <polyline
                v-if="editablePoints.length > 1"
                :points="pathLinePoints"
                fill="none"
                stroke="#67c23a"
                stroke-width="2"
              />

              <!-- 路径点 -->
              <g v-for="(point, index) in editablePoints" :key="index">
                <circle
                  :cx="scaleX(point.x)"
                  :cy="scaleY(point.y)"
                  :r="6"
                  :fill="isPointInBounds(point) ? '#67c23a' : '#f56c6c'"
                  stroke="white"
                  stroke-width="2"
                />
                <text
                  :x="scaleX(point.x)"
                  :y="scaleY(point.y) - 10"
                  text-anchor="middle"
                  font-size="12"
                  fill="#333"
                >
                  {{ index + 1 }}
                </text>
              </g>

              <!-- 机器狗当前位置 -->
              <g v-if="currentPosition && (currentPosition.x !== 0 || currentPosition.y !== 0)">
                <!-- 机器狗位置圆圈 -->
                <circle
                  :cx="scaleX(currentPosition.x)"
                  :cy="scaleY(currentPosition.y)"
                  :r="8"
                  fill="#409eff"
                  stroke="white"
                  stroke-width="3"
                >
                  <!-- 添加脉冲动画效果 -->
                  <animate
                    v-if="isActive"
                    attributeName="r"
                    values="8;12;8"
                    dur="2s"
                    repeatCount="indefinite"
                  />
                </circle>

                <!-- 机器狗图标 -->
                <text
                  :x="scaleX(currentPosition.x)"
                  :y="scaleY(currentPosition.y) + 4"
                  text-anchor="middle"
                  font-size="10"
                  fill="white"
                  font-weight="bold"
                >
                  🐕
                </text>

                <!-- 机器狗标签 -->
                <text
                  :x="scaleX(currentPosition.x)"
                  :y="scaleY(currentPosition.y) - 15"
                  text-anchor="middle"
                  font-size="11"
                  fill="#409eff"
                  font-weight="bold"
                >
                  机器狗
                </text>

                <!-- 位置坐标显示 -->
                <text
                  :x="scaleX(currentPosition.x)"
                  :y="scaleY(currentPosition.y) + 25"
                  text-anchor="middle"
                  font-size="9"
                  fill="#666"
                >
                  ({{ currentPosition.x.toFixed(1) }}, {{ currentPosition.y.toFixed(1) }})
                </text>
              </g>
            </svg>
          </div>

          <div class="preview-legend">
            <div class="legend-item">
              <div class="legend-color" style="background: rgba(64, 158, 255, 0.3);"></div>
              <span>基站安全边界</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #67c23a;"></div>
              <span>安全路径点</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #f56c6c;"></div>
              <span>超出边界点</span>
            </div>
            <div class="legend-item" v-if="currentPosition && (currentPosition.x !== 0 || currentPosition.y !== 0)">
              <div class="legend-color robot-dog-legend">🐕</div>
              <span>机器狗当前位置</span>
              <span v-if="isActive" class="legend-status">(巡航中)</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-info">
          <el-tag v-if="hasChanges" type="warning">已修改</el-tag>
          <el-tag v-if="hasErrors" type="danger">存在错误</el-tag>
          <span class="point-count">{{ validPointsCount }}/{{ editablePoints.length }} 个有效路径点</span>
        </div>
        <div class="footer-actions">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSave" :disabled="!canSave">
            保存路径
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  RefreshLeft,
  ArrowUp,
  ArrowDown,
  Delete
} from '@element-plus/icons-vue'
import type { PathPoint } from '../composables/useAutoCruise'

// Props
interface Props {
  visible: boolean
  pathPoints: PathPoint[]
  anchors?: Array<{x: number, y: number}>
  currentPosition?: PathPoint
  isActive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  anchors: () => [],
  currentPosition: () => ({ x: 0, y: 0, name: '机器狗' }),
  isActive: false
})

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'save', points: PathPoint[]): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const editablePoints = ref<PathPoint[]>([])
const originalPoints = ref<PathPoint[]>([])
const canvasWidth = ref(400)
const canvasHeight = ref(300)

// 计算属性
const anchorBounds = computed(() => {
  if (!props.anchors || props.anchors.length < 4) return null

  return {
    minX: Math.min(...props.anchors.map(a => a.x)),
    maxX: Math.max(...props.anchors.map(a => a.x)),
    minY: Math.min(...props.anchors.map(a => a.y)),
    maxY: Math.max(...props.anchors.map(a => a.y))
  }
})

const totalDistance = computed(() => {
  if (editablePoints.value.length < 2) return 0

  let distance = 0
  for (let i = 1; i < editablePoints.value.length; i++) {
    const prev = editablePoints.value[i - 1]
    const curr = editablePoints.value[i]
    distance += Math.sqrt(Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2))
  }
  return distance
})

const hasChanges = computed(() => {
  if (editablePoints.value.length !== originalPoints.value.length) return true

  return editablePoints.value.some((point, index) => {
    const original = originalPoints.value[index]
    return !original ||
           point.x !== original.x ||
           point.y !== original.y ||
           point.name !== original.name
  })
})

const hasErrors = computed(() => {
  return editablePoints.value.some(point => !isPointValid(point))
})

const validPointsCount = computed(() => {
  return editablePoints.value.filter(point => isPointValid(point)).length
})

const canSave = computed(() => {
  return editablePoints.value.length >= 2 && !hasErrors.value
})

const pathLinePoints = computed(() => {
  return editablePoints.value
    .map(point => `${scaleX(point.x)},${scaleY(point.y)}`)
    .join(' ')
})

const nearestPathPoint = computed(() => {
  if (!props.currentPosition || editablePoints.value.length === 0) return null

  let nearest = editablePoints.value[0]
  let minDistance = calculateDistance(props.currentPosition, nearest)

  for (const point of editablePoints.value) {
    const distance = calculateDistance(props.currentPosition, point)
    if (distance < minDistance) {
      minDistance = distance
      nearest = point
    }
  }

  return nearest
})

// 计算两点间距离的辅助函数
const calculateDistance = (p1: PathPoint, p2: PathPoint): number => {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2))
}

// 监听props变化
watch(() => props.pathPoints, (newPoints) => {
  if (newPoints && newPoints.length > 0) {
    editablePoints.value = newPoints.map((point, index) => ({
      ...point,
      id: point.id || `point_${index}_${Date.now()}`
    }))
    originalPoints.value = JSON.parse(JSON.stringify(newPoints))
  }
}, { immediate: true, deep: true })

// 方法
const isPointInBounds = (point: PathPoint): boolean => {
  if (!anchorBounds.value) return true

  // 直接使用基站边界，不添加安全边距
  return point.x >= anchorBounds.value.minX &&
         point.x <= anchorBounds.value.maxX &&
         point.y >= anchorBounds.value.minY &&
         point.y <= anchorBounds.value.maxY
}

const isPointValid = (point: PathPoint): boolean => {
  return point.name &&
         point.name.trim().length > 0 &&
         typeof point.x === 'number' &&
         typeof point.y === 'number' &&
         !isNaN(point.x) &&
         !isNaN(point.y) &&
         isPointInBounds(point)
}

const getPointErrors = (point: PathPoint): string[] => {
  const errors: string[] = []

  if (!point.name || point.name.trim().length === 0) {
    errors.push('路径点名称不能为空')
  }

  if (typeof point.x !== 'number' || isNaN(point.x)) {
    errors.push('X坐标必须是有效数字')
  }

  if (typeof point.y !== 'number' || isNaN(point.y)) {
    errors.push('Y坐标必须是有效数字')
  }

  if (!isPointInBounds(point)) {
    errors.push('路径点超出基站安全边界')
  }

  return errors
}

const getDistanceToLastPoint = (index: number): number => {
  if (index === 0) return 0

  const prev = editablePoints.value[index - 1]
  const curr = editablePoints.value[index]
  return Math.sqrt(Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2))
}

const validatePoint = (point: PathPoint, index: number) => {
  // 实时验证点的有效性
  nextTick(() => {
    if (!isPointValid(point)) {
      console.warn(`路径点 ${index + 1} 验证失败:`, getPointErrors(point))
    }
  })
}

const addNewPoint = () => {
  const lastPoint = editablePoints.value[editablePoints.value.length - 1]
  const newPoint: PathPoint = {
    x: lastPoint ? lastPoint.x + 1 : 0,
    y: lastPoint ? lastPoint.y : 0,
    name: `新路径点${editablePoints.value.length + 1}`,
    id: `point_new_${Date.now()}`
  }

  editablePoints.value.push(newPoint)
  ElMessage.success('已添加新路径点')
}

const removePoint = (index: number) => {
  if (editablePoints.value.length <= 2) {
    ElMessage.warning('至少需要保留2个路径点')
    return
  }

  editablePoints.value.splice(index, 1)
  ElMessage.success('已删除路径点')
}

const movePointUp = (index: number) => {
  if (index === 0) return

  const temp = editablePoints.value[index]
  editablePoints.value[index] = editablePoints.value[index - 1]
  editablePoints.value[index - 1] = temp
}

const movePointDown = (index: number) => {
  if (index === editablePoints.value.length - 1) return

  const temp = editablePoints.value[index]
  editablePoints.value[index] = editablePoints.value[index + 1]
  editablePoints.value[index + 1] = temp
}

const resetToOriginal = () => {
  editablePoints.value = JSON.parse(JSON.stringify(originalPoints.value))
  ElMessage.success('已重置为原始路径')
}

const scaleX = (x: number): number => {
  if (!anchorBounds.value) return x * 10

  const bounds = anchorBounds.value
  const padding = 20
  const width = canvasWidth.value - padding * 2
  return padding + ((x - bounds.minX) / (bounds.maxX - bounds.minX)) * width
}

const scaleY = (y: number): number => {
  if (!anchorBounds.value) return y * 10

  const bounds = anchorBounds.value
  const padding = 20
  const height = canvasHeight.value - padding * 2
  return padding + ((bounds.maxY - y) / (bounds.maxY - bounds.minY)) * height
}

const handleVisibleChange = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSave = () => {
  if (!canSave.value) {
    ElMessage.error('存在无效的路径点，请检查后再保存')
    return
  }

  const validPoints = editablePoints.value.filter(point => isPointValid(point))
  emit('save', validPoints)
  emit('update:visible', false)
  ElMessage.success(`已保存 ${validPoints.length} 个路径点`)
}
</script>

<style scoped lang="scss">
.path-edit-container {
  max-height: 70vh;
  overflow-y: auto;
}

.path-overview {
  margin-bottom: 20px;

  .overview-stats {
    display: flex;
    gap: 40px;
    margin-top: 12px;
  }
}

.path-points-section {
  margin-bottom: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.points-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.point-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &.point-error {
    background-color: #fef0f0;
    border-left: 4px solid #f56c6c;
  }

  .point-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .point-index {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      background: #409eff;
      color: white;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
    }

    .point-actions {
      display: flex;
      gap: 4px;
    }
  }

  .point-content {
    .point-field {
      margin-bottom: 12px;

      label {
        display: block;
        margin-bottom: 4px;
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }
    }

    .point-coordinates {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 12px;
    }

    .point-info {
      .point-status {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .distance-info {
          font-size: 12px;
          color: #666;

          &.robot-distance {
            color: #409eff;
            font-weight: 500;
          }
        }
      }

      .point-errors {
        .el-alert {
          margin-bottom: 4px;
        }
      }
    }
  }
}

.path-preview-section {
  h4 {
    margin: 0 0 16px 0;
    color: #333;
  }

  .preview-container {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    background: #fafafa;

    .preview-canvas {
      margin-bottom: 12px;

      .path-svg {
        width: 100%;
        height: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
      }
    }

    .preview-legend {
      display: flex;
      gap: 20px;
      font-size: 12px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
          border: 1px solid #ddd;

          &.robot-dog-legend {
            background: #409eff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            border-radius: 50%;
          }
        }

        .legend-status {
          font-size: 10px;
          color: #67c23a;
          font-weight: 500;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .point-count {
      font-size: 12px;
      color: #666;
    }
  }

  .footer-actions {
    display: flex;
    gap: 8px;
  }
}
</style>
