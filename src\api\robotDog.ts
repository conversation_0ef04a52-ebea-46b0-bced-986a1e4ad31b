/**
 * 机器狗API接口
 * 提供机器狗连接、控制、数据获取等功能
 */

import request from '@/utils/request'

// 机器狗连接响应接口
export interface ConnectResponse {
  success: boolean
  message: string
  timestamp: string
  connected: boolean
  robot_ip: string
}

// 机器狗断开连接响应接口
export interface DisconnectResponse {
  success: boolean
  message: string
  timestamp: string
}

// 机器狗移动响应接口
export interface MoveResponse {
  success: boolean
  message: string
  timestamp: string
  command_sent: boolean
  command_name: string
  command_id: number
}

// 机器狗移动参数接口
export interface MoveParams {
  x: number
  y: number
  z: number
}

// 机器狗连接状态接口
export interface ConnectionStatus {
  connected: boolean
  lastHeartbeat: string
  connectionInfo: Record<string, any>
  cachedData: Record<string, any>
}

// 机器狗系统信息接口
export interface SystemInfo {
  apiVersion: string
  serverTime: string
  uptime: number
  connections: number
}

// 机器狗IMU数据接口
export interface IMUData {
  imu_state?: {
    rpy: number[] // [roll, pitch, yaw] 弧度值
  }
  bms_state?: {
    soc: number
    current: number
    cycle: number
    bq_ntc: number[]
    mcu_ntc: number[]
  }
  foot_force?: number[]
  motor_state?: Array<{
    temperature: number
  }>
  temperature_ntc1?: number
  power_v?: number
}

// 机器狗命令响应接口
export interface CommandResponse {
  success: boolean
  message: string
  timestamp: string
  commandSent: boolean
  commandName: string
  commandId: number
}

/**
 * 机器狗API服务
 */
export const robotDogApi = {
  /**
   * 连接机器狗
   */
  connect(): Promise<ConnectResponse> {
    return request.post<ConnectResponse>('/dog/connect')
      .then(response => response.data)
  },

  /**
   * 断开机器狗连接
   */
  disconnect(): Promise<DisconnectResponse> {
    return request.post<DisconnectResponse>('/dog/disconnect')
      .then(response => response.data)
  },

  /**
   * 重新连接机器狗
   */
  reconnect(): Promise<ConnectResponse> {
    return request.post<ConnectResponse>('/dog/reconnect')
      .then(response => response.data)
  },

  /**
   * 获取连接状态
   */
  getConnectionStatus(): Promise<ConnectionStatus> {
    return request.get<ConnectionStatus>('/dog/status')
      .then(response => response.data)
  },

  /**
   * 获取系统信息
   */
  getSystemInfo(): Promise<SystemInfo> {
    return request.get<SystemInfo>('/dog/info')
      .then(response => response.data)
  },

  /**
   * 控制机器狗移动
   */
  movement(params: MoveParams): Promise<MoveResponse> {
    return request.post<MoveResponse>('/dog/movement', params)
      .then(response => response.data)
  },

  /**
   * 停止机器狗移动
   */
  stopMovement(): Promise<CommandResponse> {
    return request.post<CommandResponse>('/dog/stop')
      .then(response => response.data)
  },

  /**
   * 紧急停止
   */
  emergencyStop(): Promise<CommandResponse> {
    return request.post<CommandResponse>('/dog/emergency-stop')
      .then(response => response.data)
  },

  /**
   * 获取IMU数据
   */
  getIMUData(): Promise<IMUData> {
    return request.get<IMUData>('/dog/imu')
      .then(response => response.data)
  },

  /**
   * 获取IMU姿态角 (Roll, Pitch, Yaw)
   */
  getIMURPY(): Promise<Record<string, any>> {
    return request.get<Record<string, any>>('/dog/imu/rpy')
      .then(response => response.data)
  },

  /**
   * 获取电机状态
   */
  getMotorStatus(): Promise<Record<string, any>> {
    return request.get<Record<string, any>>('/dog/imu/motors')
      .then(response => response.data)
  },

  /**
   * 获取电池状态
   */
  getBatteryStatus(): Promise<Record<string, any>> {
    return request.get<Record<string, any>>('/dog/imu/battery')
      .then(response => response.data)
  }
}

// 导出默认API实例
export default robotDogApi
