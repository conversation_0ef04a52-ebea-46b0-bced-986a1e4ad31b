<!-- 
  DroneMedicineMonitor.vue
  无人机药箱余量监测模块
  实时监控无人机药箱剩余药量，确保农田喷洒作业的顺利进行
-->
<template>
  <div class="drone-medicine-monitor">
    <!-- 页面标题 -->
    <PageHeader
      title="无人机药箱余量监测"
      description="实时监控无人机药箱剩余药量，确保农田喷洒作业的顺利进行"
      icon="Box"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ getWorkingDronesCount() }}</span>
            <span class="summary-label">作业中无人机</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getLowMedicineCount() }}</span>
            <span class="summary-label">药量不足</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 无人机药箱监测卡片 -->
    <div class="monitor-cards">
      <div v-for="(drone, index) in drones" :key="index" 
           class="monitor-card" 
           :class="{ 
             warning: drone.medicineLevel < warningThreshold,
             'status-working': drone.status === '作业中',
             'status-standby': drone.status === '待机',
             'status-maintenance': drone.status === '维护中'
           }">
        <div class="status-indicator" :class="getMedicineLevelClass(drone.medicineLevel)"></div>
        <div class="card-content">
          <div class="card-header">
            <div class="drone-info">
              <h3 class="drone-name">{{ drone.name }}</h3>
              <div class="drone-id">#{{ drone.id }}</div>
            </div>
            <div class="drone-status" :class="drone.status.toLowerCase()">
              {{ drone.status }}
            </div>
          </div>
          
          <div class="gauge-container">
            <div class="gauge-wrapper">
              <div class="gauge" :class="getMedicineLevelClass(drone.medicineLevel)">
                <div class="gauge-value-container">
                  <div class="gauge-value" :class="getMedicineLevelClass(drone.medicineLevel)">{{ drone.medicineLevel }}%</div>
                  <div class="gauge-label">药箱余量</div>
                </div>
                <svg viewBox="0 0 200 100" class="gauge-svg">
                  <!-- 仪表盘背景 -->
                  <path d="M20,90 A80,80 0 0,1 180,90" class="gauge-bg" />
                  
                  <!-- 仪表盘刻度 -->
                  <path v-for="i in 11" :key="`tick-${i}`" 
                    :d="getTickPath(i-1)" 
                    class="gauge-tick" />
                  
                  <!-- 仪表盘进度 -->
                  <path 
                    :d="getGaugePath(drone.medicineLevel)" 
                    class="gauge-fill" 
                    :class="getMedicineLevelClass(drone.medicineLevel)" />
                  
                  <!-- 仪表盘指针 -->
                  <path 
                    :d="getPointerPath(drone.medicineLevel)" 
                    class="gauge-pointer" 
                    :class="getMedicineLevelClass(drone.medicineLevel)" />
                  
                  <!-- 指针末端的发光点 -->
                  <circle 
                    :cx="getPointerEndPoint(drone.medicineLevel).x" 
                    :cy="getPointerEndPoint(drone.medicineLevel).y" 
                    r="3" 
                    class="gauge-pointer-dot"
                    :class="getMedicineLevelClass(drone.medicineLevel)" />
                </svg>
              </div>
            </div>
          </div>
          
          <div class="medicine-info">
            <div class="medicine-tag">
              <div class="medicine-icon" :class="getMedicineLevelClass(drone.medicineLevel)"></div>
              <div class="medicine-details">
                <div class="medicine-name">{{ drone.medicine.name }}</div>
                <div class="medicine-concentration">浓度: {{ drone.medicine.concentration }}%</div>
              </div>
            </div>
            <div class="medicine-metrics">
              <div class="metric-item">
                <span class="metric-label">推荐用量</span>
                <span class="metric-value">{{ getRecommendedDosage(drone.medicine.concentration) }}ml/亩</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">适用作物</span>
                <span class="metric-value">{{ getCropTypes(drone.medicine.name) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="drone.medicineLevel < warningThreshold" class="warning-bubble">
          <div class="warning-content">
            <i class="warning-icon">!</i>
            <span class="warning-text">药液余量低，请及时补充</span>
          </div>
          <el-button type="danger" size="small" class="refill-button" @click="showRefillGuide(drone)">
            药液补充
          </el-button>
        </div>
        
        <div class="drone-micrograph">
          <div class="drone-icon" :class="drone.status.toLowerCase()"></div>
        </div>
      </div>
    </div>
    
    <!-- 药液补充指南对话框 -->
    <el-dialog
      v-model="refillDialogVisible"
      title="药液补充指南"
      width="500px"
      class="refill-dialog"
    >
      <div v-if="selectedDrone" class="refill-guide">
        <div class="guide-header">
          <div class="drone-name">{{ selectedDrone.name }}</div>
          <div class="drone-id">#{{ selectedDrone.id }}</div>
        </div>
        
        <div class="guide-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              确保无人机处于停机状态并关闭电源
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              打开无人机顶部的药箱盖
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              使用标准接口将药液管道连接至药箱入口
            </div>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
              启动药液泵，将药液注入药箱至刻度线处
            </div>
          </div>
          <div class="step">
            <div class="step-number">5</div>
            <div class="step-content">
              盖紧药箱盖，确保密封良好
            </div>
          </div>
        </div>
        
        <div class="medicine-recommendation">
          <div class="recommendation-title">推荐药液</div>
          <div class="recommendation-content">
            <div class="medicine-item">
              <div class="medicine-name">{{ selectedDrone.medicine.name }}</div>
              <div class="medicine-specs">
                <span class="medicine-concentration">浓度: {{ selectedDrone.medicine.concentration }}%</span>
                <span class="medicine-volume">容量: 5L</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="refillDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRefill">确认已补充</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="药量充足" />
        <StatusIndicator type="normal" label="农田喷洒" />
        <StatusIndicator type="warning" label="智能配比" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Box,
  Refresh,
  View,
  HomeFilled,
  Setting
} from '@element-plus/icons-vue';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import StatusIndicator from './components/StatusIndicator.vue';

// 警告阈值
const warningThreshold = 20;

// 无人机数据
const drones = reactive([
  {
    id: 'DRN001',
    name: '农药无人机 Alpha',
    status: '待机',
    medicineLevel: 72,
    medicine: {
      name: '生物除虫剂 BioShield-X',
      concentration: 5
    }
  },
  {
    id: 'DRN002',
    name: '农药无人机 Beta',
    status: '作业中',
    medicineLevel: 45,
    medicine: {
      name: '有机杀虫剂 EcoDefend',
      concentration: 3
    }
  },
  {
    id: 'DRN003',
    name: '农药无人机 Gamma',
    status: '待机',
    medicineLevel: 18,
    medicine: {
      name: '生物除虫剂 BioShield-X',
      concentration: 5
    }
  },
  {
    id: 'DRN004',
    name: '农药无人机 Delta',
    status: '维护中',
    medicineLevel: 95,
    medicine: {
      name: '植物助长剂 GrowPlus',
      concentration: 8
    }
  }
]);

// 药液补充对话框
const refillDialogVisible = ref(false);
const selectedDrone = ref<any>(null);

// 最后更新时间
const lastUpdateTime = ref(new Date());

// 获取作业中的无人机数量
const getWorkingDronesCount = () => {
  return drones.filter(drone => drone.status === '作业中').length;
};

// 获取药量不足的无人机数量
const getLowMedicineCount = () => {
  return drones.filter(drone => drone.medicineLevel < warningThreshold).length;
};

// 根据浓度获取推荐用量
const getRecommendedDosage = (concentration: number) => {
  // 浓度越高，建议用量越少
  return Math.round(500 / concentration);
};

// 根据药剂名称获取适用作物
const getCropTypes = (medicineName: string) => {
  const cropMap: Record<string, string> = {
    'BioShield-X': '水稻、小麦',
    'EcoDefend': '蔬菜、水果',
    'GrowPlus': '玉米、大豆'
  };
  
  // 匹配药剂名称关键词
  for (const [key, value] of Object.entries(cropMap)) {
    if (medicineName.includes(key)) {
      return value;
    }
  }
  
  return '通用农作物';
};

// 根据药液余量获取颜色样式类
const getMedicineLevelClass = (level: number) => {
  if (level < warningThreshold) {
    return 'level-low';
  } else if (level < 50) {
    return 'level-medium';
  } else {
    return 'level-high';
  }
};

// 获取刻度线路径
const getTickPath = (index: number) => {
  const angleRange = 180; // 仪表盘总角度范围
  const startAngle = 180; // 起始角度
  const angle = startAngle - (index * (angleRange / 10));
  const radian = (angle * Math.PI) / 180;
  
  const outerRadius = 80;
  const innerRadius = index % 5 === 0 ? 70 : 75; // 主刻度和次刻度长度不同
  
  const outerX = 100 + outerRadius * Math.cos(radian);
  const outerY = 90 + outerRadius * Math.sin(radian);
  const innerX = 100 + innerRadius * Math.cos(radian);
  const innerY = 90 + innerRadius * Math.sin(radian);
  
  return `M${outerX},${outerY} L${innerX},${innerY}`;
};

// 获取仪表盘进度路径
const getGaugePath = (level: number) => {
  const angleRange = 180; // 仪表盘总角度范围
  const startAngle = 180; // 起始角度
  const endAngle = startAngle - (level / 100 * angleRange);
  const radius = 80;
  
  const startRadian = (startAngle * Math.PI) / 180;
  const endRadian = (endAngle * Math.PI) / 180;
  
  const startX = 100 + radius * Math.cos(startRadian);
  const startY = 90 + radius * Math.sin(startRadian);
  const endX = 100 + radius * Math.cos(endRadian);
  const endY = 90 + radius * Math.sin(endRadian);
  
  const largeArcFlag = level > 50 ? 1 : 0;
  
  return `M${startX},${startY} A${radius},${radius} 0 ${largeArcFlag} 1 ${endX},${endY}`;
};

// 获取指针路径
const getPointerPath = (level: number) => {
  const angleRange = 180; // 仪表盘总角度范围
  const startAngle = 180; // 起始角度
  const angle = startAngle - (level / 100 * angleRange);
  const radian = (angle * Math.PI) / 180;
  
  const pointerLength = 65; // 指针长度
  const pointerX = 100 + pointerLength * Math.cos(radian);
  const pointerY = 90 + pointerLength * Math.sin(radian);
  
  return `M100,90 L${pointerX},${pointerY}`;
};

// 获取指针末端坐标
const getPointerEndPoint = (level: number) => {
  const angleRange = 180; // 仪表盘总角度范围
  const startAngle = 180; // 起始角度
  const angle = startAngle - (level / 100 * angleRange);
  const radian = (angle * Math.PI) / 180;
  
  const pointerLength = 65; // 指针长度
  const pointerX = 100 + pointerLength * Math.cos(radian);
  const pointerY = 90 + pointerLength * Math.sin(radian);
  
  return { x: pointerX, y: pointerY };
};

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 显示药液补充指南
const showRefillGuide = (drone: any) => {
  selectedDrone.value = drone;
  refillDialogVisible.value = true;
};

// 确认药液已补充
const confirmRefill = () => {
  if (selectedDrone.value) {
    const index = drones.findIndex(d => d.id === selectedDrone.value.id);
    if (index !== -1) {
      drones[index].medicineLevel = 100;
      ElMessage({
        type: 'success',
        message: `${drones[index].name} 药液已成功补充至100%`
      });
    }
    refillDialogVisible.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  // 模拟数据更新
  drones.forEach(drone => {
    if (drone.status !== '维护中') {
      drone.medicineLevel = Math.max(0, Math.min(100, drone.medicineLevel + (Math.random() * 10 - 5)));
    }
  });
  
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

// 模拟药液消耗
let consumptionInterval: number | null = null;

const simulateMedicineConsumption = () => {
  drones.forEach(drone => {
    if (drone.status === '作业中') {
      // 作业中的无人机消耗药液更快
      drone.medicineLevel = Math.max(0, drone.medicineLevel - (Math.random() * 0.8 + 0.2));
    } else if (drone.status === '待机') {
      // 待机状态也有轻微漏损
      drone.medicineLevel = Math.max(0, drone.medicineLevel - (Math.random() * 0.1));
    }
    
    // 随机变更无人机状态
    if (Math.random() < 0.05) {
      const statusOptions = ['待机', '作业中', '维护中'];
      drone.status = statusOptions[Math.floor(Math.random() * statusOptions.length)];
    }
  });
};

onMounted(() => {
  // 启动药液消耗模拟
  consumptionInterval = window.setInterval(simulateMedicineConsumption, 3000);
});

onUnmounted(() => {
  // 清除定时器
  if (consumptionInterval) {
    clearInterval(consumptionInterval);
  }
});
</script>

<style scoped>
.drone-medicine-monitor {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 监控卡片网格 */
.monitor-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

.monitor-card {
  position: relative;
  padding: 20px;
  border-radius: 12px;
  background: linear-gradient(145deg, #1a2234, #2a3349);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #3b4863;
}

.monitor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.monitor-card.status-working {
  border-color: #10b981;
}

.monitor-card.status-standby {
  border-color: #60a5fa;
}

.monitor-card.status-maintenance {
  border-color: #f59e0b;
}

.monitor-card.warning {
  border-color: #ef4444;
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
}

.status-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.level-high {
  background-color: #10b981;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-indicator.level-medium {
  background-color: #f59e0b;
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.status-indicator.level-low {
  background-color: #ef4444;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

/* 卡片内容 */
.card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.drone-info .drone-name {
  font-size: 18px;
  font-weight: 600;
  color: #e5e7eb;
  margin: 0 0 5px 0;
}

.drone-info .drone-id {
  font-size: 12px;
  color: #9ca3af;
  font-family: monospace;
}

.drone-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.drone-status.待机 {
  background-color: rgba(96, 165, 250, 0.2);
  color: #60a5fa;
  border: 1px solid rgba(96, 165, 250, 0.3);
}

.drone-status.作业中 {
  background-color: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.drone-status.维护中 {
  background-color: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

/* 仪表盘容器 */
.gauge-container {
  margin: 15px 0;
  display: flex;
  justify-content: center;
}

.gauge-wrapper {
  width: 200px;
  height: 150px;
  position: relative;
}

.gauge {
  width: 100%;
  height: 100%;
  position: relative;
}

.gauge-value-container {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2;
}

.gauge-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 5px;
}

.gauge-value.level-high {
  color: #10b981;
}

.gauge-value.level-medium {
  color: #f59e0b;
}

.gauge-value.level-low {
  color: #ef4444;
}

.gauge-label {
  font-size: 12px;
  color: #9ca3af;
}

.gauge-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.gauge-bg {
  fill: none;
  stroke: #4b5563;
  stroke-width: 10;
  stroke-linecap: round;
}

.gauge-tick {
  fill: none;
  stroke: #6b7280;
  stroke-width: 1;
}

.gauge-fill {
  fill: none;
  stroke-width: 10;
  stroke-linecap: round;
}

.gauge-fill.level-high {
  stroke: #10b981;
}

.gauge-fill.level-medium {
  stroke: #f59e0b;
}

.gauge-fill.level-low {
  stroke: #ef4444;
}

.gauge-pointer {
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
}

.gauge-pointer.level-high {
  stroke: #10b981;
}

.gauge-pointer.level-medium {
  stroke: #f59e0b;
}

.gauge-pointer.level-low {
  stroke: #ef4444;
}

.gauge-pointer-dot.level-high {
  fill: #10b981;
}

.gauge-pointer-dot.level-medium {
  fill: #f59e0b;
}

.gauge-pointer-dot.level-low {
  fill: #ef4444;
}

/* 药液信息 */
.medicine-info {
  margin-top: 15px;
}

.medicine-tag {
  display: flex;
  align-items: center;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
}

.medicine-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.medicine-icon.level-high {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M19 3v12l-7 5-7-5V3'/%3E%3Cpath d='M12 20v-9'/%3E%3Cpath d='M10 7H8'/%3E%3Cpath d='M16 7h-2'/%3E%3Cpath d='M12 7V3'/%3E%3C/svg%3E");
}

.medicine-icon.level-medium {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f59e0b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M19 3v12l-7 5-7-5V3'/%3E%3Cpath d='M12 20v-9'/%3E%3Cpath d='M10 7H8'/%3E%3Cpath d='M16 7h-2'/%3E%3Cpath d='M12 7V3'/%3E%3C/svg%3E");
}

.medicine-icon.level-low {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ef4444' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M19 3v12l-7 5-7-5V3'/%3E%3Cpath d='M12 20v-9'/%3E%3Cpath d='M10 7H8'/%3E%3Cpath d='M16 7h-2'/%3E%3Cpath d='M12 7V3'/%3E%3C/svg%3E");
}

.medicine-details {
  flex: 1;
}

.medicine-name {
  font-size: 14px;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 3px;
}

.medicine-concentration {
  font-size: 12px;
  color: #9ca3af;
}

.medicine-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.metric-item {
  background: rgba(31, 41, 55, 0.5);
  padding: 8px 10px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
  color: #e5e7eb;
}

/* 警告气泡 */
.warning-bubble {
  margin-top: 15px;
  background: rgba(239, 68, 68, 0.2);
  padding: 10px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.warning-content {
  display: flex;
  align-items: center;
}

.warning-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #ef4444;
  color: white;
  font-weight: bold;
  font-style: normal;
  margin-right: 10px;
}

.warning-text {
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

/* 无人机图标 */
.drone-micrograph {
  position: absolute;
  bottom: 15px;
  right: 15px;
  width: 40px;
  height: 40px;
  opacity: 0.2;
}

.drone-icon {
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23e5e7eb' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='3'/%3E%3Cpath d='M3 9l3 3M9 3l3 3M15 3l-3 3M21 9l-3 3M3 15l3-3M9 21l3-3M15 21l-3-3M21 15l-3-3'/%3E%3C/svg%3E");
}

.drone-icon.作业中 {
  animation: rotate 10s infinite linear;
  opacity: 0.5;
}

/* 底部状态指示器 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 药液补充指南对话框 */
.refill-guide {
  padding: 10px;
}

.guide-header {
  margin-bottom: 20px;
}

.guide-header .drone-name {
  font-size: 18px;
  font-weight: 600;
  color: #10b981;
}

.guide-header .drone-id {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

.guide-steps .step {
  display: flex;
  margin-bottom: 15px;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 14px;
}

.step-content {
  flex: 1;
  padding-top: 2px;
}

.medicine-recommendation {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(16, 185, 129, 0.1);
  border-radius: 8px;
  border-left: 3px solid #10b981;
}

.recommendation-title {
  font-size: 16px;
  font-weight: 600;
  color: #10b981;
  margin-bottom: 10px;
}

.medicine-item {
  padding: 10px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.medicine-item .medicine-name {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
  margin-bottom: 5px;
}

.medicine-specs {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  gap: 15px;
}

/* 动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .monitor-cards {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .medicine-metrics {
    grid-template-columns: 1fr;
  }
}
</style>

<!--
注意: 此组件需要以下SVG图标文件:
- @/assets/icons/drone.svg

如果这些文件不存在，请创建相应的SVG图标文件。
--> 