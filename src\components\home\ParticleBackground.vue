<template>
  <div class="particle-background" ref="particleContainer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'

// 声明全局particlesJS
declare global {
  interface Window {
    particlesJS: any
  }
}

interface Props {
  /** 粒子数量，默认为 80 */
  particleCount?: number
  /** 粒子颜色，默认为 #00ffaa */
  particleColor?: string
  /** 连接线颜色，默认为 #00ffaa */
  lineColor?: string
  /** 粒子连接线最大距离，默认为 150 */
  linkDistance?: number
  /** 粒子移动速度，默认为 1 */
  moveSpeed?: number
  /** 是否自动响应鼠标交互，默认为 true */
  interactWithMouse?: boolean
  /** 是否为低性能模式（减少粒子数量和特效），默认为 false */
  lowPerformance?: boolean
  /** 粒子形状，默认为 circle */
  particleShape?: 'circle' | 'edge' | 'triangle' | 'polygon' | 'star'
  /** 是否启用粒子闪烁效果，默认为 true */
  enableTwinkle?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  particleCount: 80,
  particleColor: '#00ffaa',
  lineColor: '#00ffaa',
  linkDistance: 150,
  moveSpeed: 1,
  interactWithMouse: true,
  lowPerformance: false,
  particleShape: 'circle',
  enableTwinkle: true
})

const particleContainer = ref<HTMLElement | null>(null)
let particlesInitialized = false

// 初始化粒子系统
const initParticles = () => {
  if (!particlesInitialized && particleContainer.value && window.particlesJS) {
    const actualParticleCount = props.lowPerformance ? Math.floor(props.particleCount / 2) : props.particleCount
    const actualLinkDistance = props.lowPerformance ? Math.floor(props.linkDistance * 0.7) : props.linkDistance
    
    // 配置粒子系统
    window.particlesJS('particle-background', {
      particles: {
        number: {
          value: actualParticleCount,
          density: {
            enable: true,
            value_area: 800
          }
        },
        color: {
          value: props.particleColor
        },
        shape: {
          type: props.particleShape,
          stroke: {
            width: 0,
            color: props.particleColor
          },
          polygon: {
            nb_sides: 6
          },
          image: {
            src: 'img/github.svg',
            width: 100,
            height: 100
          }
        },
        opacity: {
          value: 0.4,
          random: true,
          anim: {
            enable: true,
            speed: 0.5,
            opacity_min: 0.1,
            sync: false
          }
        },
        size: {
          value: 3,
          random: true,
          anim: {
            enable: props.lowPerformance ? false : true,
            speed: 1,
            size_min: 0.5,
            sync: false
          }
        },
        line_linked: {
          enable: true,
          distance: actualLinkDistance,
          color: props.lineColor,
          opacity: 0.2,
          width: 1
        },
        move: {
          enable: true,
          speed: props.moveSpeed,
          direction: 'none',
          random: true,
          straight: false,
          out_mode: 'out',
          bounce: false,
          attract: {
            enable: props.lowPerformance ? false : true,
            rotateX: 600,
            rotateY: 1200
          }
        },
        twinkle: {
          particles: {
            enable: props.enableTwinkle,
            frequency: 0.05,
            opacity: 1
          }
        }
      },
      interactivity: {
        detect_on: 'canvas',
        events: {
          onhover: {
            enable: props.interactWithMouse,
            mode: props.lowPerformance ? 'bubble' : 'repulse'
          },
          onclick: {
            enable: props.interactWithMouse && !props.lowPerformance,
            mode: 'push'
          },
          resize: true
        },
        modes: {
          grab: {
            distance: 140,
            line_linked: {
              opacity: 0.8
            }
          },
          bubble: {
            distance: 200,
            size: 4,
            duration: 2,
            opacity: 0.5,
            speed: 3
          },
          repulse: {
            distance: 150,
            duration: 0.4
          },
          push: {
            particles_nb: 4
          },
          remove: {
            particles_nb: 2
          }
        }
      },
      retina_detect: !props.lowPerformance,
      fps_limit: 60
    })
    
    particlesInitialized = true
  }
}

// 监听属性变化
watch([
  () => props.particleCount,
  () => props.particleColor,
  () => props.lineColor,
  () => props.linkDistance,
  () => props.moveSpeed,
  () => props.interactWithMouse,
  () => props.lowPerformance,
  () => props.particleShape,
  () => props.enableTwinkle
], () => {
  // 如果属性变化，重新初始化粒子系统
  if (particlesInitialized) {
    particlesInitialized = false
    // 添加短暂延迟确保DOM更新
    setTimeout(() => {
      initParticles()
    }, 50)
  }
})

// 检测性能
const checkPerformance = () => {
  // 在移动设备上降低性能
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  return isMobile
}

onMounted(() => {
  // 如果粒子JS库可用，使用短暂延迟确保DOM已准备好
  if (window.particlesJS) {
    if (particleContainer.value) {
      particleContainer.value.id = 'particle-background'
      setTimeout(() => {
        initParticles()
      }, 100)
    }
  } else {
    console.error('particles.js库未加载，粒子背景将不可用')
    
    // 尝试动态加载particles.js
    const script = document.createElement('script')
    script.src = 'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js'
    script.onload = () => {
      if (particleContainer.value) {
        particleContainer.value.id = 'particle-background'
        setTimeout(() => {
          initParticles()
        }, 100)
      }
    }
    document.head.appendChild(script)
  }
})

onBeforeUnmount(() => {
  // 清理粒子系统（防止内存泄漏）
  particlesInitialized = false
})
</script>

<style lang="scss" scoped>
.particle-background {
  position: absolute;
  inset: 0;
  z-index: 1;
  pointer-events: none;
  
  // 确保粒子系统不会干扰用户交互
  & ~ * {
    position: relative;
    z-index: 2;
  }
  
  // 添加科幻感的渐变叠加
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(
      circle at 50% 50%,
      rgba(0, 40, 100, 0.1) 0%,
      rgba(0, 20, 60, 0.2) 50%,
      rgba(0, 10, 30, 0.4) 100%
    );
    pointer-events: none;
    z-index: 2;
  }
}

// 使粒子容器全宽高
:deep(canvas) {
  display: block;
  vertical-align: bottom;
  transform: scale(1.2); // 稍微放大以覆盖边缘
  filter: blur(0.5px); // 轻微模糊增加科幻感
}

// 确保全屏粒子
:global(#particle-background) {
  position: fixed;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a1628 0%, #0a1e46 100%);
}

// 添加扫描线动画效果
@keyframes scanline {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

.particle-background::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 255, 170, 0.1) 50%,
    transparent 100%
  );
  background-size: 100% 10px;
  pointer-events: none;
  z-index: 3;
  opacity: 0.3;
  animation: scanline 10s linear infinite;
}
</style> 