# Canvas地图实时朝向显示功能

## 功能概述

在canvas地图上实时显示机器狗的朝向角度，并在页面加载时就开始获取IMU数据，为后续的自动巡航功能提供数据支持。

## 技术实现

### 1. 全局IMU数据服务

创建了`GlobalIMUService`，提供全局的IMU数据访问：

#### 核心特性
- **全局数据共享**：多个组件可以共享同一份IMU数据
- **实时数据更新**：500ms间隔的高频数据获取
- **WebSocket连接管理**：自动连接、重连、错误处理
- **数据统计**：更新频率、连接时长、错误计数

#### 服务接口
```typescript
export class GlobalIMUService {
  // 启动/停止服务
  static async start(): Promise<boolean>
  static stop(): void
  
  // 数据访问
  static getCurrentData(): IMUData | null
  static getCurrentYaw(): number | null
  static getBatteryLevel(): number | null
  static getConnectionStatus(): ConnectionStatus
  static getStats(): IMUStats
}

// 响应式Hook
export const useGlobalIMU = () => {
  return {
    currentData: computed(() => globalIMUData.value),
    currentYaw: computed(() => globalIMUData.value?.attitude?.yaw),
    isConnected: computed(() => isIMUConnected.value),
    isReceiving: computed(() => isIMUReceiving.value),
    // ... 更多响应式数据和方法
  }
}
```

### 2. 设备跟踪页面集成

#### 页面加载时自动启动
```typescript
onMounted(async () => {
  // 立即开始获取IMU数据，为地图朝向显示和自动巡航做准备
  console.log('🚀 启动全局IMU数据服务...')
  try {
    const success = await startGlobalIMU()
    if (success) {
      ElNotification({
        title: '实时数据已启动',
        message: '机器狗朝向和IMU数据正在实时更新',
        type: 'success'
      })
    }
  } catch (error) {
    console.error('❌ 全局IMU数据服务启动异常:', error)
  }
})
```

#### 实时朝向更新
```typescript
// 监听全局IMU数据变化，实时更新朝向角度
watch(globalYaw, (newYaw) => {
  if (newYaw !== undefined && !isNaN(newYaw)) {
    robotYaw.value = newYaw
    console.log('🧭 机器狗朝向更新:', robotYaw.value.toFixed(1) + '°')
  }
}, { immediate: true })
```

#### 状态显示
```vue
<!-- IMU数据状态指示 -->
<div class="imu-status">
  <div class="status-dot" :class="{ connected: imuReceiving }"></div>
  <span class="status-text">{{ imuStatusText }}</span>
  <span v-if="hasValidOrientation" class="orientation-info">
    朝向: {{ robotYaw?.toFixed(1) }}°
  </span>
</div>
```

### 3. Canvas地图朝向显示

#### PlaneMapCanvas组件支持
地图组件已经完美支持朝向显示：

```typescript
// Props接口
interface Props {
  robotYaw?: number;  // 机器狗朝向角度 (-180 到 180 度)
}

// 朝向绘制逻辑
if (typeof props.robotYaw === 'number') {
  // 将角度转换为弧度
  const angleRad = (-props.robotYaw * Math.PI) / 180;
  
  // 绘制箭头指示器
  const arrowLength = robotSize * 1.5;
  // ... 箭头绘制代码
  
  // 显示角度文本
  ctx.fillText(`${props.robotYaw.toFixed(0)}°`, canvasPoint.x, canvasPoint.y - robotSize - 5);
}
```

#### 视觉效果
- **箭头指示器**：显示机器狗当前朝向
- **角度文本**：实时显示朝向角度数值
- **颜色区分**：不同状态使用不同颜色
- **平滑更新**：500ms间隔的流畅更新

## 数据流程

### 1. 数据获取流程
```
WebSocket连接 → IMU数据请求 → 后端响应 → 数据解析 → 全局状态更新
```

### 2. 朝向更新流程
```
IMU数据更新 → Yaw角度提取 → robotYaw状态更新 → Canvas重绘 → 朝向显示更新
```

### 3. 多组件共享
```
全局IMU服务 → 设备跟踪页面（地图显示）
              → 设备详情页（详细数据）
              → 自动巡航功能（导航数据）
              → 其他需要IMU数据的组件
```

## 功能特性

### 1. 实时性
- **高频更新**：500ms间隔，2Hz更新频率
- **低延迟**：WebSocket实时通信，50-100ms延迟
- **流畅显示**：平滑的朝向角度变化

### 2. 可靠性
- **自动重连**：网络中断时自动重连
- **错误处理**：完整的错误处理和恢复机制
- **状态监控**：实时连接状态和数据状态显示

### 3. 易用性
- **自动启动**：页面加载时自动开始数据获取
- **状态反馈**：清晰的连接状态和朝向信息显示
- **用户友好**：错误时有明确的提示信息

### 4. 扩展性
- **全局服务**：其他功能可以轻松访问IMU数据
- **模块化设计**：服务和组件解耦，便于维护
- **类型安全**：完整的TypeScript类型定义

## 使用效果

### 地图显示
- ✅ **实时朝向箭头**：机器狗图标上显示朝向箭头
- ✅ **角度数值**：实时显示朝向角度（如：45°）
- ✅ **平滑更新**：朝向变化流畅自然
- ✅ **状态指示**：有效朝向时显示，无效时隐藏

### 状态监控
- ✅ **IMU连接状态**：实时显示连接状态
- ✅ **数据接收状态**：显示是否正在接收数据
- ✅ **朝向信息**：头部显示当前朝向角度
- ✅ **错误提示**：连接失败时有明确提示

### 自动巡航支持
- ✅ **数据准备**：页面加载时就开始获取数据
- ✅ **全局访问**：自动巡航功能可以直接访问IMU数据
- ✅ **实时导航**：基于实时朝向数据进行路径规划
- ✅ **状态同步**：多个功能共享同一份数据状态

## 配置参数

### 数据获取配置
```typescript
const CONFIG = {
  DATA_REQUEST_INTERVAL: 500,    // 数据请求间隔（毫秒）
  STATS_UPDATE_INTERVAL: 1000,   // 统计更新间隔（毫秒）
  CONNECTION_TIMEOUT: 3000,      // 数据超时时间（毫秒）
  MAX_ERROR_COUNT: 20,           // 最大错误次数
  AUTO_START: true               // 页面加载时自动启动
}
```

### 显示配置
```typescript
const DISPLAY_CONFIG = {
  ARROW_LENGTH_RATIO: 1.5,       // 箭头长度比例
  ARROW_WIDTH: 4,                // 箭头宽度
  ANGLE_PRECISION: 0,            // 角度显示精度（小数位）
  TEXT_OFFSET: 5,                // 文本偏移距离
  UPDATE_THRESHOLD: 1            // 最小更新角度阈值
}
```

## 测试验证

### 测试场景
1. **页面加载**：验证自动启动IMU数据获取
2. **朝向显示**：验证地图上的朝向箭头和角度显示
3. **实时更新**：验证朝向变化的实时性
4. **网络中断**：验证自动重连和错误处理
5. **多组件访问**：验证其他组件能正确访问IMU数据

### 预期结果
- 页面加载后立即开始显示朝向信息
- 朝向箭头和角度数值实时更新
- 网络问题时有适当的错误提示和恢复
- 自动巡航等功能能正确获取IMU数据
- 长时间运行稳定无内存泄漏

## 后续优化建议

### 1. 性能优化
- **智能更新**：只在朝向变化超过阈值时更新显示
- **数据缓存**：缓存最近的IMU数据，减少重复计算
- **渲染优化**：使用requestAnimationFrame优化Canvas绘制

### 2. 功能增强
- **朝向历史**：记录朝向变化历史，支持轨迹分析
- **预测算法**：基于朝向变化趋势预测未来朝向
- **多设备支持**：支持多个机器狗的朝向显示

### 3. 用户体验
- **朝向校准**：提供朝向校准功能
- **显示选项**：允许用户自定义朝向显示样式
- **数据导出**：支持导出朝向数据用于分析

## 总结

通过全局IMU数据服务和实时朝向显示功能，成功实现了：

1. **页面加载时自动获取IMU数据**，为所有功能提供数据基础
2. **Canvas地图上实时显示机器狗朝向**，提供直观的方向信息
3. **全局数据共享**，让自动巡航等功能能够访问实时IMU数据
4. **高频实时更新**，提供流畅的用户体验
5. **健壮的错误处理**，确保系统稳定性

这为后续的自动巡航功能奠定了坚实的数据基础，同时大大提升了地图显示的信息丰富度和用户体验。
