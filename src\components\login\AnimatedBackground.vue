<template>
  <div class="animated-background">
    <div class="background-overlay"></div>
    <div class="particles-container" ref="particlesContainer"></div>
    <div class="decoration-container">
      <img src="@/assets/images/plant-decoration.svg" class="plant-left" alt="Plant decoration" />
      <img src="@/assets/images/plant-decoration.svg" class="plant-right" alt="Plant decoration" />
    </div>
    <div class="grid-overlay"></div>
    <div class="glow-circle circle-1"></div>
    <div class="glow-circle circle-2"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 粒子容器引用
const particlesContainer = ref(null);
let particlesInstance = null;

// 创建粒子效果
const createParticles = () => {
  if (!particlesContainer.value) return;
  
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const container = particlesContainer.value;
  
  // 设置画布尺寸
  const setCanvasSize = () => {
    canvas.width = container.offsetWidth;
    canvas.height = container.offsetHeight;
  };
  
  setCanvasSize();
  container.appendChild(canvas);
  
  // 响应窗口大小变化
  window.addEventListener('resize', setCanvasSize);
  
  // 粒子配置
  const particles = [];
  const particleCount = 50;
  const colors = ['rgba(0, 255, 170, 0.7)', 'rgba(0, 200, 150, 0.5)', 'rgba(0, 150, 255, 0.6)'];
  
  // 创建粒子
  for (let i = 0; i < particleCount; i++) {
    particles.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      radius: Math.random() * 3 + 1,
      color: colors[Math.floor(Math.random() * colors.length)],
      speedX: Math.random() * 0.5 - 0.25,
      speedY: Math.random() * 0.5 - 0.25,
      connectDistance: 150
    });
  }
  
  // 绘制粒子和连接线
  const draw = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制粒子
    particles.forEach(particle => {
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
      ctx.fillStyle = particle.color;
      ctx.fill();
      
      // 更新位置
      particle.x += particle.speedX;
      particle.y += particle.speedY;
      
      // 边界检查
      if (particle.x < 0 || particle.x > canvas.width) particle.speedX *= -1;
      if (particle.y < 0 || particle.y > canvas.height) particle.speedY *= -1;
      
      // 绘制连接线
      particles.forEach(otherParticle => {
        const dx = particle.x - otherParticle.x;
        const dy = particle.y - otherParticle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < particle.connectDistance) {
          ctx.beginPath();
          ctx.strokeStyle = `rgba(0, 255, 170, ${0.2 * (1 - distance / particle.connectDistance)})`;
          ctx.lineWidth = 0.5;
          ctx.moveTo(particle.x, particle.y);
          ctx.lineTo(otherParticle.x, otherParticle.y);
          ctx.stroke();
        }
      });
    });
    
    requestAnimationFrame(draw);
  };
  
  // 开始动画
  draw();
  
  // 返回清理函数
  return () => {
    window.removeEventListener('resize', setCanvasSize);
    if (canvas.parentNode) {
      canvas.parentNode.removeChild(canvas);
    }
  };
};

// 组件挂载时创建粒子效果
onMounted(() => {
  particlesInstance = createParticles();
});

// 组件卸载时清理
onUnmounted(() => {
  if (particlesInstance) {
    particlesInstance();
  }
});
</script>

<style lang="scss" scoped>
@use '@/styles/login.scss' as login;

.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  overflow: hidden;
  
  // 背景渐变
  background: linear-gradient(135deg, login.$bg-dark 0%, login.$bg-darker 100%);
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/images/login-bg.svg');
  background-size: cover;
  opacity: 0.6;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.decoration-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  
  .plant-left {
    position: absolute;
    bottom: 0;
    left: 5%;
    height: 40%;
    opacity: 0.7;
    animation: sway 8s ease-in-out infinite alternate;
  }
  
  .plant-right {
    position: absolute;
    bottom: 0;
    right: 5%;
    height: 45%;
    opacity: 0.7;
    transform: scaleX(-1);
    animation: sway 10s ease-in-out infinite alternate-reverse;
  }
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 255, 170, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 170, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
  z-index: 1;
}

.glow-circle {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  z-index: 0;
}

.circle-1 {
  width: 300px;
  height: 300px;
  background: rgba(0, 255, 170, 0.15);
  top: -100px;
  right: 10%;
  animation: pulse 15s infinite alternate;
}

.circle-2 {
  width: 400px;
  height: 400px;
  background: rgba(0, 100, 255, 0.1);
  bottom: -150px;
  left: 5%;
  animation: pulse 20s infinite alternate-reverse;
}

@keyframes sway {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(5deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}
</style> 