<template>
  <div 
    class="module-card" 
    :class="{ 'is-active': isActive }"
    @click="handleClick" 
    @mouseenter="startHoverAnimation" 
    @mouseleave="resetAnimation"
    ref="cardRef"
  >
    <!-- 主要内容 -->
    <div class="module-content">
      <div class="module-icon-wrapper">
        <el-icon class="module-icon">
          <component :is="module.icon" />
        </el-icon>
        <div class="icon-glow"></div>
        <div class="icon-rings">
          <div class="icon-ring ring-1"></div>
          <div class="icon-ring ring-2"></div>
        </div>
      </div>
      <div class="module-text">
        <h3 class="module-title">{{ module.title }}</h3>
        <p class="module-description">{{ module.description }}</p>
      </div>
      
      <!-- 状态指示器 -->
      <div class="module-status">
        <div class="status-dot"></div>
        <div class="status-label">{{ isActive ? '已激活' : '待激活' }}</div>
      </div>
    </div>
    
    <!-- 交互效果层 -->
    <div class="module-effects">
      <div class="glow-border"></div>
      <div class="hover-effect"></div>
      <div class="corner-decoration top-left"></div>
      <div class="corner-decoration top-right"></div>
      <div class="corner-decoration bottom-left"></div>
      <div class="corner-decoration bottom-right"></div>
    </div>
    
    <!-- 数据线条效果 -->
    <div class="data-lines">
      <div class="data-line line-1"></div>
      <div class="data-line line-2"></div>
      <div class="data-line line-3"></div>
    </div>
    
    <!-- 科技装饰元素 -->
    <div class="tech-elements">
      <div class="circuit-lines">
        <svg viewBox="0 0 100 100" preserveAspectRatio="none" class="circuit-svg">
          <path class="circuit-path path-1" d="M0,20 L30,20 L40,30 L60,30 L70,20 L100,20" />
          <path class="circuit-path path-2" d="M0,50 L20,50 L30,40 L70,40 L80,50 L100,50" />
          <path class="circuit-path path-3" d="M0,80 L40,80 L50,70 L60,70 L70,80 L100,80" />
        </svg>
      </div>
      <div class="tech-dots">
        <div class="tech-dot dot-1"></div>
        <div class="tech-dot dot-2"></div>
        <div class="tech-dot dot-3"></div>
        <div class="tech-dot dot-4"></div>
      </div>
    </div>
    
    <!-- 激活时的扫描效果 -->
    <div class="scan-overlay" v-if="isActive"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import gsap from 'gsap'

interface Module {
  id: number
  title: string
  description: string
  icon: any
}

interface Props {
  module: Module
  isActive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false
})

const emit = defineEmits<{
  (e: 'click', module: Module): void
}>()

const cardRef = ref<HTMLElement | null>(null)
let animation: gsap.core.Timeline | null = null
let circuitAnimation: gsap.core.Timeline | null = null

// 点击处理
const handleClick = () => {
  emit('click', props.module)
  
  // 点击动画效果
  if (cardRef.value) {
    // 创建点击波纹效果
    createRippleEffect(cardRef.value)
    
    // 卡片缩放动画
    gsap.to(cardRef.value, {
      scale: 0.95,
      duration: 0.1,
      onComplete: () => {
        gsap.to(cardRef.value, {
          scale: 1,
          duration: 0.4,
          ease: "elastic.out(1, 0.3)"
        })
      }
    })
  }
}

// 创建点击波纹效果
const createRippleEffect = (element: HTMLElement) => {
  // 创建波纹元素
  const ripple = document.createElement('div')
  ripple.className = 'ripple-effect'
  
  // 添加到卡片
  element.appendChild(ripple)
  
  // 设置波纹位置（居中）
  const rect = element.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  ripple.style.width = ripple.style.height = `${size * 2}px`
  ripple.style.left = `${rect.width / 2 - size}px`
  ripple.style.top = `${rect.height / 2 - size}px`
  
  // 动画
  gsap.to(ripple, {
    opacity: 0,
    scale: 1,
    duration: 0.6,
    ease: "power1.out",
    onComplete: () => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple)
      }
    }
  })
}

// 悬停动画
const startHoverAnimation = () => {
  if (!cardRef.value) return
  
  // 清除之前的动画
  if (animation) {
    animation.kill()
  }
  
  // 创建新的动画时间线
  animation = gsap.timeline()
  
  // 卡片悬停效果
  animation
    .to(cardRef.value, {
      y: -8,
      boxShadow: '0 15px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 255, 170, 0.5)',
      duration: 0.3
    })
    .to(cardRef.value.querySelector('.module-icon'), {
      y: -5,
      rotateY: '30deg',
      scale: 1.2,
      color: '#00ffaa',
      duration: 0.4,
      ease: 'power2.out'
    }, '-=0.2')
    .to(cardRef.value.querySelector('.glow-border'), {
      opacity: 1,
      duration: 0.5
    }, '-=0.4')
    .to(cardRef.value.querySelectorAll('.data-line'), {
      width: '100%',
      opacity: 0.8,
      duration: 0.5,
      stagger: 0.1,
      ease: 'power1.out'
    }, '-=0.3')
    .to(cardRef.value.querySelectorAll('.corner-decoration'), {
      scale: 1,
      opacity: 1,
      duration: 0.4,
      stagger: 0.05,
      ease: 'back.out(1.7)'
    }, '-=0.5')
    .to(cardRef.value.querySelectorAll('.tech-dot'), {
      opacity: 0.8,
      scale: 1.2,
      duration: 0.4,
      stagger: 0.1,
      ease: 'power1.out'
    }, '-=0.4')
    .to(cardRef.value.querySelector('.module-title'), {
      backgroundPosition: '100% 0%',
      duration: 1
    }, '-=0.3')
  
  // 启动电路动画
  startCircuitAnimation()
}

// 电路动画
const startCircuitAnimation = () => {
  if (!cardRef.value) return
  
  const paths = cardRef.value.querySelectorAll('.circuit-path')
  
  if (circuitAnimation) {
    circuitAnimation.kill()
  }
  
  circuitAnimation = gsap.timeline()
  
  paths.forEach((path, index) => {
    circuitAnimation.fromTo(path, 
      { strokeDashoffset: 100 },
      { 
        strokeDashoffset: 0, 
        duration: 1.5, 
        ease: 'power1.inOut',
        delay: index * 0.2
      },
      index === 0 ? 0 : '-=1'
    )
  })
}

// 重置动画
const resetAnimation = () => {
  if (!cardRef.value) return
  
  // 清除之前的动画
  if (animation) {
    animation.kill()
  }
  
  // 创建重置动画
  animation = gsap.timeline()
  animation
    .to(cardRef.value, {
      y: 0,
      boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
      duration: 0.3
    })
    .to(cardRef.value.querySelector('.module-icon'), {
      y: 0,
      rotateY: '0deg',
      scale: 1,
      color: props.isActive ? '#00ffaa' : '#41b883',
      duration: 0.3,
    }, '-=0.3')
    .to(cardRef.value.querySelector('.glow-border'), {
      opacity: props.isActive ? 0.7 : 0,
      duration: 0.3
    }, '-=0.3')
    .to(cardRef.value.querySelectorAll('.data-line'), {
      width: '30%',
      opacity: 0.3,
      duration: 0.3,
      stagger: 0.05,
      ease: 'power1.in'
    }, '-=0.3')
    .to(cardRef.value.querySelectorAll('.corner-decoration'), {
      scale: 0.8,
      opacity: props.isActive ? 0.6 : 0.3,
      duration: 0.3,
      stagger: 0.03,
      ease: 'power1.in'
    }, '-=0.3')
    .to(cardRef.value.querySelectorAll('.tech-dot'), {
      opacity: props.isActive ? 0.6 : 0.3,
      scale: 1,
      duration: 0.3,
      stagger: 0.05,
      ease: 'power1.in'
    }, '-=0.3')
    .to(cardRef.value.querySelector('.module-title'), {
      backgroundPosition: '0% 0%',
      duration: 0.5
    }, '-=0.3')
  
  // 重置电路动画
  if (circuitAnimation) {
    circuitAnimation.kill()
    
    const paths = cardRef.value.querySelectorAll('.circuit-path')
    paths.forEach(path => {
      gsap.to(path, { strokeDashoffset: 100, duration: 0.5 })
    })
  }
}

// 初始化动画
onMounted(() => {
  if (cardRef.value) {
    // 初始化数据线条
    gsap.set(cardRef.value.querySelectorAll('.data-line'), {
      width: '30%',
      opacity: 0.3
    })
    
    // 初始化边角装饰
    gsap.set(cardRef.value.querySelectorAll('.corner-decoration'), {
      scale: 0.8,
      opacity: props.isActive ? 0.6 : 0.3
    })
    
    // 初始化边框发光效果
    gsap.set(cardRef.value.querySelector('.glow-border'), {
      opacity: props.isActive ? 0.7 : 0
    })
    
    // 初始化科技点
    gsap.set(cardRef.value.querySelectorAll('.tech-dot'), {
      opacity: props.isActive ? 0.6 : 0.3,
      scale: 1
    })
    
    // 初始化电路路径
    gsap.set(cardRef.value.querySelectorAll('.circuit-path'), {
      strokeDasharray: 100,
      strokeDashoffset: 100
    })
    
    // 如果是激活状态，启动电路动画
    if (props.isActive) {
      startCircuitAnimation()
    }
  }
})

// 清理
onUnmounted(() => {
  if (animation) {
    animation.kill()
    animation = null
  }
  
  if (circuitAnimation) {
    circuitAnimation.kill()
    circuitAnimation = null
  }
})

// 监听 active 状态变化
watch(() => props.isActive, (newValue) => {
  if (!cardRef.value) return
  
  if (newValue) {
    // 激活状态的特殊动画
    gsap.to(cardRef.value.querySelector('.glow-border'), {
      opacity: 0.7,
      duration: 0.5
    })
    
    gsap.to(cardRef.value.querySelectorAll('.corner-decoration'), {
      scale: 1,
      opacity: 0.6,
      duration: 0.4,
      stagger: 0.05
    })
    
    gsap.to(cardRef.value.querySelector('.module-icon'), {
      color: '#00ffaa',
      duration: 0.3
    })
    
    gsap.to(cardRef.value.querySelector('.status-dot'), {
      backgroundColor: '#00ffaa',
      boxShadow: '0 0 10px rgba(0, 255, 170, 0.7)',
      duration: 0.3
    })
    
    // 启动电路动画
    startCircuitAnimation()
  } else {
    // 恢复初始状态
    resetAnimation()
  }
})
</script>

<style lang="scss" scoped>
.module-card {
  background: rgba(0, 21, 65, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 25px;
  cursor: pointer;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 255, 170, 0.15);
  position: relative;
  overflow: hidden;
  height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  transform-style: preserve-3d;
  perspective: 1000px;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, 
      rgba(0, 21, 65, 0.7) 0%,
      rgba(0, 10, 30, 0.9) 100%
    );
    z-index: 0;
  }
  
  &:hover {
    .module-status {
      opacity: 1;
      transform: translateY(0);
    }
    
    .tech-elements {
      opacity: 1;
    }
  }
  
  &.is-active {
    border-color: rgba(0, 255, 170, 0.5);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 255, 170, 0.3);
    
    .module-icon {
      color: #00ffaa;
    }
    
    .module-title {
      background-size: 200% auto;
      background-image: linear-gradient(90deg, #fff 0%, #00ffaa 50%, #1890ff 100%);
      -webkit-background-clip: text;
      color: transparent;
      animation: textShine 3s infinite linear;
    }
    
    .scan-overlay {
      animation: scanAnimation 3s infinite linear;
    }
    
    .status-dot {
      background-color: #00ffaa;
      box-shadow: 0 0 10px rgba(0, 255, 170, 0.7);
      
      &::after {
        animation: statusPulse 1.5s infinite;
      }
    }
  }
  
  .module-content {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .module-icon-wrapper {
    position: relative;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 70px;
    
    .icon-glow {
      position: absolute;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: radial-gradient(circle, 
                 rgba(0, 255, 170, 0.2) 0%, 
                 rgba(0, 255, 170, 0.1) 40%, 
                 transparent 70%);
      z-index: 1;
      animation: slowPulse 3s infinite alternate;
    }
    
    .icon-rings {
      position: absolute;
      width: 100%;
      height: 100%;
      
      .icon-ring {
        position: absolute;
        border-radius: 50%;
        border: 1px dashed rgba(0, 255, 170, 0.3);
        
        &.ring-1 {
          width: 70px;
          height: 70px;
          left: calc(50% - 35px);
          top: calc(50% - 35px);
          animation: rotateRing 10s linear infinite;
        }
        
        &.ring-2 {
          width: 90px;
          height: 90px;
          left: calc(50% - 45px);
          top: calc(50% - 45px);
          animation: rotateRing 15s linear infinite reverse;
        }
      }
    }
  }
  
  .module-icon {
    font-size: 48px;
    color: #41b883;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 0 8px rgba(0, 255, 170, 0.3));
  }
  
  .module-text {
    flex: 1;
  }
  
  .module-title {
    font-size: 20px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    font-weight: 600;
    color: #fff;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    
    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 40px;
      height: 2px;
      background: linear-gradient(90deg, #00ffaa, transparent);
      transition: width 0.3s ease;
    }
  }
  
  .module-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    line-height: 1.5;
    position: relative;
    z-index: 2;
  }
  
  // 模块状态
  .module-status {
    display: flex;
    align-items: center;
    gap: 8px;
    position: absolute;
    top: 20px;
    right: 20px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    
    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #888;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        inset: -2px;
        border-radius: 50%;
        border: 1px solid rgba(0, 255, 170, 0.5);
        opacity: 0.7;
      }
    }
    
    .status-label {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
    }
  }
  
  // 特效层
  .module-effects {
    position: absolute;
    inset: 0;
    pointer-events: none;
    z-index: 1;
    
    .glow-border {
      position: absolute;
      inset: 0;
      border: 2px solid transparent;
      border-radius: 16px;
      background: linear-gradient(135deg, 
                 rgba(0, 255, 170, 0.8), 
                 rgba(24, 144, 255, 0.8)) border-box;
      -webkit-mask: 
        linear-gradient(#fff 0 0) padding-box, 
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      opacity: 0;
      box-shadow: 0 0 15px rgba(0, 255, 170, 0.5);
    }
    
    .hover-effect {
      position: absolute;
      inset: 0;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 16px;
      transform: scale(0);
      transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }
  }
  
  &:hover .hover-effect {
    transform: scale(1);
  }
  
  // 边角装饰
  .corner-decoration {
    position: absolute;
    width: 15px;
    height: 15px;
    border-style: solid;
    border-color: rgba(0, 255, 170, 0.7);
    transition: all 0.3s ease;
    
    &.top-left {
      top: 10px;
      left: 10px;
      border-width: 2px 0 0 2px;
      border-radius: 5px 0 0 0;
    }
    
    &.top-right {
      top: 10px;
      right: 10px;
      border-width: 2px 2px 0 0;
      border-radius: 0 5px 0 0;
    }
    
    &.bottom-left {
      bottom: 10px;
      left: 10px;
      border-width: 0 0 2px 2px;
      border-radius: 0 0 0 5px;
    }
    
    &.bottom-right {
      bottom: 10px;
      right: 10px;
      border-width: 0 2px 2px 0;
      border-radius: 0 0 5px 0;
    }
  }
  
  // 数据线条效果
  .data-lines {
    position: absolute;
    bottom: 15px;
    left: 0;
    width: 100%;
    height: 20px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    opacity: 0.7;
    z-index: 1;
    
    .data-line {
      height: 1px;
      background: linear-gradient(90deg,
                 transparent 0%,
                 rgba(0, 255, 170, 0.5) 20%,
                 rgba(0, 255, 170, 0.8) 50%,
                 rgba(0, 255, 170, 0.5) 80%,
                 transparent 100%);
      width: 30%;
      margin: 0 auto;
      opacity: 0.3;
      transition: all 0.3s ease;
      
      &.line-1 {
        width: 50%;
      }
      
      &.line-2 {
        width: 40%;
      }
      
      &.line-3 {
        width: 30%;
      }
    }
  }
  
  // 科技装饰元素
  .tech-elements {
    position: absolute;
    inset: 0;
    pointer-events: none;
    z-index: 1;
    opacity: 0.5;
    transition: opacity 0.3s ease;
    
    .circuit-lines {
      position: absolute;
      inset: 0;
      
      .circuit-svg {
        width: 100%;
        height: 100%;
        
        .circuit-path {
          fill: none;
          stroke: rgba(0, 255, 170, 0.5);
          stroke-width: 1;
          stroke-dasharray: 100;
          stroke-dashoffset: 100;
        }
      }
    }
    
    .tech-dots {
      position: absolute;
      inset: 0;
      
      .tech-dot {
        position: absolute;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: rgba(0, 255, 170, 0.5);
        box-shadow: 0 0 5px rgba(0, 255, 170, 0.5);
        
        &.dot-1 {
          top: 30%;
          left: 10%;
        }
        
        &.dot-2 {
          top: 20%;
          right: 15%;
        }
        
        &.dot-3 {
          bottom: 25%;
          left: 20%;
        }
        
        &.dot-4 {
          bottom: 15%;
          right: 10%;
        }
      }
    }
  }
  
  // 扫描效果
  .scan-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(0, 255, 170, 0.1) 48%,
      rgba(0, 255, 170, 0.3) 50%,
      rgba(0, 255, 170, 0.1) 52%,
      transparent 100%
    );
    background-size: 100% 300%;
    background-position: 0 0;
    z-index: 3;
    pointer-events: none;
    opacity: 0.5;
  }
  
  // 点击波纹效果
  .ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(
      circle,
      rgba(0, 255, 170, 0.4) 0%,
      rgba(0, 255, 170, 0.2) 50%,
      transparent 70%
    );
    transform: scale(0);
    opacity: 1;
    z-index: 4;
  }
}

// 动画定义
@keyframes slowPulse {
  0% {
    transform: scale(0.9);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.2);
    opacity: 0.6;
  }
}

@keyframes rotateRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes textShine {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

@keyframes scanAnimation {
  0% {
    background-position: 0 0%;
  }
  100% {
    background-position: 0 100%;
  }
}

@keyframes statusPulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  70% {
    transform: scale(1.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .module-card {
    height: 260px;
    padding: 20px;
    
    .module-icon {
      font-size: 42px;
    }
    
    .module-title {
      font-size: 18px;
    }
    
    .module-description {
      font-size: 13px;
    }
  }
}

@media (max-width: 768px) {
  .module-card {
    height: 220px;
    
    .module-icon {
      font-size: 36px;
    }
    
    .corner-decoration {
      width: 12px;
      height: 12px;
    }
    
    .module-status {
      top: 15px;
      right: 15px;
    }
  }
}
</style> 