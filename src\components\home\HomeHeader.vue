<template>
  <header class="home-header">
    <!-- 顶部导航栏左侧 -->
    <div class="header-left">
      <div class="system-logo">
        <div class="logo-hexagon"></div>
      </div>
      <h1 class="system-title">
        <span class="title-text">智慧农业病虫防治系统</span>
        <span class="title-version">v2.0</span>
      </h1>
      <div class="system-status">
        <span class="status-indicator online"></span>
        <span class="status-text">系统在线</span>
      </div>
    </div>

    <!-- 顶部导航栏中间部分已移除 -->

    <!-- 顶部导航栏右侧 -->
    <div class="header-right">
      <!-- 系统时间显示 -->
      <div class="system-time">
        <div class="time">{{ currentTime }}</div>
        <div class="date">{{ currentDate }}</div>
      </div>

      <!-- 通知图标 -->
      <el-badge :value="notificationCount" class="notification">
        <div class="notification-icon">
          <Bell />
          <div class="notification-pulse"></div>
        </div>
      </el-badge>

      <!-- 用户信息下拉菜单 -->
      <el-dropdown trigger="click">
        <div class="user-info">
          <div class="avatar-container">
            <el-avatar :size="36" :src="userInfo.avatar" />
            <div class="avatar-status"></div>
          </div>
          <div class="user-details">
            <span class="username">{{ userInfo.username }}</span>
            <span class="user-role">{{ userInfo.role || '系统管理员' }}</span>
          </div>
          <div class="dropdown-icon">
            <el-icon><CaretBottom /></el-icon>
          </div>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="sci-fi-dropdown">
            <el-dropdown-item @click="handleProfile">
              <el-icon><User /></el-icon>个人信息
            </el-dropdown-item>
            <el-dropdown-item @click="handleSettings">
              <el-icon><Setting /></el-icon>系统设置
            </el-dropdown-item>
            <el-dropdown-item divided @click="handleLogout">
              <el-icon><SwitchButton /></el-icon>退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { Bell, Setting, SwitchButton, User, CaretBottom } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useAuth } from '@/composables/useAuth'
import { useRouter } from 'vue-router'

interface Props {
  notificationCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  notificationCount: 3
})

const emit = defineEmits<{
  (e: 'settings'): void
  (e: 'logout'): void
  (e: 'profile'): void
}>()

const router = useRouter()
const { user, logout } = useAuth()

// 计算用户信息，如果没有登录用户则使用默认值
const userInfo = computed(() => {
  if (user.value) {
    return {
      username: user.value.nickName || user.value.username,
      avatar: '/src/assets/logo.svg', // 使用默认头像
      role: '农业用户'
    }
  }
  return {
    username: '系统管理员',
    avatar: '',
    role: '系统管理员'
  }
})

// 系统时间和日期
const currentTime = ref('00:00:00')
const currentDate = ref('0000-00-00')
let timeInterval: number | null = null

// 更新系统时间
const updateSystemTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
  currentDate.value = now.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '-')
}



// 处理个人信息
const handleProfile = () => {
  emit('profile')
}

// 处理系统设置
const handleSettings = () => {
  emit('settings')
}

// 处理退出登录
const handleLogout = async () => {
  await logout()
}

onMounted(() => {
  updateSystemTime()
  timeInterval = window.setInterval(updateSystemTime, 1000)
})

onBeforeUnmount(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.home-header {
  height: 70px;
  width: 100%;
  background: var(--background-header, linear-gradient(90deg,
    rgba(6, 15, 45, 0.95),
    rgba(10, 25, 65, 0.9),
    rgba(0, 30, 80, 0.85)));
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 100;

  // 顶部导航栏下边框发光效果
  &::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
      rgba(0, 255, 170, 0.7) 0%,
      rgba(0, 180, 255, 0.7) 50%,
      rgba(0, 255, 170, 0.7) 100%);
    animation: dataFlow 10s linear infinite;
    background-size: 200% 100%;
    box-shadow: 0 0 10px rgba(0, 255, 170, 0.5);
  }

  // 顶部导航栏上边框细线
  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    background: rgba(0, 255, 170, 0.3);
  }

  // 左侧区域
  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    // 系统Logo
    .system-logo {
      width: 40px;
      height: 40px;
      position: relative;

      .logo-hexagon {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #00ffaa, #1890ff);
        clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
        animation: pulsate 3s ease-in-out infinite;
        box-shadow: 0 0 15px rgba(0, 255, 170, 0.5);

        &::before {
          content: '';
          position: absolute;
          top: 5px;
          left: 5px;
          right: 5px;
          bottom: 5px;
          background: rgba(10, 25, 65, 0.8);
          clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
          z-index: 1;
        }

        &::after {
          content: '';
          position: absolute;
          top: 10px;
          left: 10px;
          right: 10px;
          bottom: 10px;
          background: linear-gradient(135deg, rgba(0, 255, 170, 0.8), rgba(24, 144, 255, 0.8));
          clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
          z-index: 2;
          animation: rotate 10s linear infinite;
        }
      }
    }

    // 系统标题
    .system-title {
      font-size: 22px;
      font-weight: 600;
      background: linear-gradient(90deg, #00ffaa, #1890ff);
      -webkit-background-clip: text;
      color: transparent;
      text-shadow: 0 0 15px rgba(0, 255, 170, 0.3);
      letter-spacing: 1px;
      position: relative;
      display: flex;
      align-items: center;
      gap: 10px;

      .title-version {
        font-size: 12px;
        background: linear-gradient(90deg, #00ffaa, #1890ff);
        -webkit-background-clip: text;
        color: transparent;
        opacity: 0.8;
      }
    }

    // 系统状态
    .system-status {
      display: flex;
      align-items: center;
      gap: 5px;
      margin-left: 10px;

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.online {
          background: #00ffaa;
          box-shadow: 0 0 10px #00ffaa;
          animation: blink 2s infinite;
        }
      }

      .status-text {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }

  // 中间区域样式已移除

  // 右侧区域
  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;

    // 系统时间
    .system-time {
      text-align: right;
      margin-right: 10px;

      .time {
        font-size: 16px;
        font-weight: 500;
        color: #00ffaa;
        font-family: 'Courier New', monospace;
        letter-spacing: 1px;
      }

      .date {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        font-family: 'Courier New', monospace;
      }
    }

    // 通知图标
    .notification {
      margin-right: 10px;

      .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(0, 255, 170, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background: rgba(0, 255, 170, 0.2);
        }

        .el-icon {
          font-size: 20px;
          color: rgba(255, 255, 255, 0.8);
        }

        .notification-pulse {
          position: absolute;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 2px solid rgba(0, 255, 170, 0.5);
          animation: pulse 2s infinite;
          opacity: 0;
        }
      }
    }

    // 用户信息
    .user-info {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 5px 10px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s;
      border: 1px solid transparent;

      &:hover {
        background: rgba(0, 255, 170, 0.1);
        border: 1px solid rgba(0, 255, 170, 0.2);
      }

      .avatar-container {
        position: relative;

        .avatar-status {
          position: absolute;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background: #00ffaa;
          border: 2px solid #0a1941;
          bottom: 0;
          right: 0;
        }
      }

      .user-details {
        display: flex;
        flex-direction: column;

        .username {
          font-size: 14px;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
        }

        .user-role {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
        }
      }

      .dropdown-icon {
        display: flex;
        align-items: center;
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
}

// 科幻风格下拉菜单样式
:deep(.sci-fi-dropdown) {
  background: rgba(10, 25, 65, 0.95);
  border: 1px solid rgba(0, 255, 170, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);

  .el-dropdown-menu__item {
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      background: rgba(0, 255, 170, 0.15);
    }

    .el-icon {
      margin-right: 8px;
      color: #00ffaa;
    }
  }
}

// 动画
@keyframes dataFlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes pulsate {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .home-header {
    // 中间区域媒体查询已移除，因为中间区域已被移除
  }
}

@media (max-width: 768px) {
  .home-header {
    padding: 0 15px;

    .header-left {
      .system-title {
        font-size: 18px;

        .title-version {
          display: none;
        }
      }

      .system-status {
        display: none;
      }
    }

    .header-right {
      .system-time {
        display: none;
      }

      .user-info {
        .user-details {
          display: none;
        }
      }
    }
  }
}
</style>
