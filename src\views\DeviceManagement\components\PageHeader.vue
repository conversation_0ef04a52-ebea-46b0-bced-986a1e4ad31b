<!-- 
  PageHeader.vue
  页面标题组件
  用于显示页面标题、描述和操作按钮
-->
<template>
  <div class="page-header">
    <div class="header-content">
      <div class="header-icon" v-if="icon">
        <el-icon :size="24"><component :is="icon" /></el-icon>
      </div>
      <div class="header-text">
        <h1 class="header-title">{{ title }}</h1>
        <p class="header-description" v-if="description">{{ description }}</p>
      </div>
    </div>
    <div class="header-actions" v-if="$slots.actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  /** 页面标题 */
  title: {
    type: String,
    required: true
  },
  /** 页面描述 */
  description: {
    type: String,
    default: ''
  },
  /** 图标名称 (Element Plus图标组件名) */
  icon: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  color: #3b82f6;
}

.header-text {
  display: flex;
  flex-direction: column;
}

.header-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #e5e7eb;
}

.header-description {
  margin: 4px 0 0 0;
  font-size: 0.875rem;
  color: #9ca3af;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style> 