/**
 * 聊天服务模块
 * 用于处理消息发送、接收等操作
 */

import { ref } from 'vue';
import type { ChatMessage } from '@/types/chat';
import { useChatSessionManager } from '@/utils/chatSessionManager';
import { SIMULATION_CONFIG } from '@/config/business';

// 消息类型枚举
export type MessageRole = 'user' | 'assistant' | 'system';

// 消息角色常量
export const MessageRole = {
  User: 'user' as MessageRole,
  Assistant: 'assistant' as MessageRole,
  System: 'system' as MessageRole
};

// 模拟的响应延迟（毫秒）
const SIMULATED_DELAY = SIMULATION_CONFIG.API_DELAY;

// 服务状态
export const useChatService = () => {
  // 加载状态
  const isLoading = ref(false);
  // 错误信息
  const error = ref<string | null>(null);

  // 会话管理器
  const sessionManager = useChatSessionManager();

  // 创建新消息对象
  const createMessage = (content: string, role: MessageRole): ChatMessage => {
    // 当role为system时，强制转换为assistant，因为ChatMessage类型只支持user和assistant
    const messageRole = role === MessageRole.System ? MessageRole.Assistant : role;
    return {
      id: Date.now().toString(),
      content,
      role: messageRole as 'user' | 'assistant',
      timestamp: Date.now()
    };
  };

  // 发送消息（并获取回复）
  const sendMessage = async (content: string): Promise<void> => {
    if (!content.trim()) return;

    try {
      // 设置加载状态
      isLoading.value = true;
      error.value = null;

      // 创建用户消息
      const userMessage = createMessage(content, MessageRole.User);

      // 添加到会话
      sessionManager.addMessageToActiveSession(userMessage);

      // 模拟API调用
      const response = await simulateApiCall(content);

      // 创建助手回复消息
      const assistantMessage = createMessage(response, MessageRole.Assistant);

      // 添加到会话
      sessionManager.addMessageToActiveSession(assistantMessage);

    } catch (err) {
      // 处理错误
      console.error('发送消息失败：', err);
      error.value = err instanceof Error ? err.message : '发送消息失败';

      // 添加系统错误消息
      const errorMessage = createMessage(
        '抱歉，处理您的消息时发生了错误。请稍后再试。',
        MessageRole.System
      );
      sessionManager.addMessageToActiveSession(errorMessage);

    } finally {
      // 重置加载状态
      isLoading.value = false;
    }
  };

  // 模拟API调用（实际项目中将替换为真实API调用）
  const simulateApiCall = async (message: string): Promise<string> => {
    return new Promise((resolve) => {
      // 模拟网络延迟
      setTimeout(() => {
        // 简单的回复生成逻辑（实际项目会替换为后端API调用）
        const responses = [
          `您好！感谢您的消息："${message}"。我是一个AI助手，目前只能提供简单的回复。在实际项目中，这里会连接到真实的后端API。`,
          `收到您的问题："${message}"。在完整实现中，我将连接到智慧农业知识库，为您提供专业的解答。`,
          `您询问的是："${message}"。这个问题很有趣！在正式版本中，我将分析农业大数据，给您最准确的回答。`,
          `关于"${message}"，我理解您的需求。实际部署后，我将能够访问气象数据、土壤监测结果和农作物生长数据，为您提供全面分析。`
        ];

        // 随机选择一个回复
        const randomIndex = Math.floor(Math.random() * responses.length);
        resolve(responses[randomIndex]);
      }, SIMULATED_DELAY);
    });
  };

  return {
    isLoading,
    error,
    sendMessage,
    createMessage,
    MessageRole,
    ...sessionManager
  };
};
