<template>
  <div class="typing-text" :class="{ 'blinking-cursor': showCursor }">
    <span class="typed-text" v-html="displayText"></span>
    <span class="cursor" v-if="showCursor">|</span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface Props {
  /** 要显示的文本 */
  text: string
  /** 打字速度，单位为毫秒/字符，默认为 50 */
  typeSpeed?: number
  /** 删除速度，单位为毫秒/字符，默认为 30 */
  deleteSpeed?: number
  /** 文本完成后的延迟，单位为毫秒，默认为 2000 */
  delayAfterText?: number
  /** 是否循环播放，默认为 false */
  loop?: boolean
  /** 是否在完成后显示光标，默认为 true */
  showCursor?: boolean
  /** 是否启用闪烁效果，默认为 true */
  enableGlitch?: boolean
  /** 是否自动开始，默认为 true */
  autoStart?: boolean
  /** 是否在循环时删除文本，默认为 false */
  deleteOnLoop?: boolean
  /** 循环的文本数组，如果提供则会循环显示这些文本 */
  loopTexts?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  typeSpeed: 50,
  deleteSpeed: 30,
  delayAfterText: 2000,
  loop: false,
  showCursor: true,
  enableGlitch: true,
  autoStart: true,
  deleteOnLoop: false,
  loopTexts: () => []
})

// 当前显示的文本
const currentText = ref('')
// 当前处理的文本索引（用于循环文本数组）
const currentTextIndex = ref(0)
// 是否正在删除
const isDeleting = ref(false)
// 是否暂停
const isPaused = ref(false)
// 计时器ID
let typingTimer: number | null = null

// 计算当前要显示的文本
const textToType = computed(() => {
  if (props.loopTexts && props.loopTexts.length > 0) {
    return props.loopTexts[currentTextIndex.value]
  }
  return props.text
})

// 计算要显示的文本（包括HTML标签）
const displayText = computed(() => {
  return currentText.value
    .replace(/\n/g, '<br>')
    .replace(/\s/g, '&nbsp;')
})

// 添加故障效果
const addGlitchEffect = () => {
  if (!props.enableGlitch) return
  
  // 随机决定是否添加故障效果
  if (Math.random() > 0.95) {
    // 随机选择一个字符位置
    const position = Math.floor(Math.random() * currentText.value.length)
    if (position >= 0 && position < currentText.value.length) {
      // 临时替换为随机字符
      const originalChar = currentText.value.charAt(position)
      const randomChar = String.fromCharCode(Math.floor(Math.random() * 94) + 33) // ASCII 可见字符
      
      // 替换字符
      const textArray = currentText.value.split('')
      textArray[position] = randomChar
      currentText.value = textArray.join('')
      
      // 短暂延迟后恢复
      setTimeout(() => {
        const textArray = currentText.value.split('')
        textArray[position] = originalChar
        currentText.value = textArray.join('')
      }, 100)
    }
  }
}

// 打字效果函数
const typeText = () => {
  // 如果暂停，不执行任何操作
  if (isPaused.value) return
  
  // 确定当前速度
  const speed = isDeleting.value ? props.deleteSpeed : props.typeSpeed
  
  // 添加随机的故障效果
  addGlitchEffect()
  
  if (!isDeleting.value && currentText.value.length < textToType.value.length) {
    // 添加下一个字符
    currentText.value = textToType.value.substring(0, currentText.value.length + 1)
    
    // 继续打字
    typingTimer = window.setTimeout(typeText, speed)
  } else if (isDeleting.value && currentText.value.length > 0) {
    // 删除一个字符
    currentText.value = currentText.value.substring(0, currentText.value.length - 1)
    
    // 继续删除
    typingTimer = window.setTimeout(typeText, speed)
  } else if (!isDeleting.value && currentText.value.length === textToType.value.length) {
    // 文本完成，等待指定时间后继续
    if (props.loop || (props.loopTexts && props.loopTexts.length > 0)) {
      if (props.deleteOnLoop) {
        // 设置为删除模式
        isDeleting.value = true
        typingTimer = window.setTimeout(typeText, props.delayAfterText)
      } else {
        // 直接切换到下一个文本
        if (props.loopTexts && props.loopTexts.length > 0) {
          currentTextIndex.value = (currentTextIndex.value + 1) % props.loopTexts.length
        }
        currentText.value = ''
        typingTimer = window.setTimeout(typeText, props.delayAfterText)
      }
    }
  } else if (isDeleting.value && currentText.value.length === 0) {
    // 删除完成，切换到下一个文本
    isDeleting.value = false
    
    if (props.loopTexts && props.loopTexts.length > 0) {
      currentTextIndex.value = (currentTextIndex.value + 1) % props.loopTexts.length
    }
    
    // 开始打字
    typingTimer = window.setTimeout(typeText, speed)
  }
}

// 开始打字
const startTyping = () => {
  isPaused.value = false
  
  // 如果已经有计时器，先清除
  if (typingTimer !== null) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
  
  // 重置状态
  currentText.value = ''
  isDeleting.value = false
  
  // 开始打字
  typeText()
}

// 暂停打字
const pauseTyping = () => {
  isPaused.value = true
  
  if (typingTimer !== null) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
}

// 重置打字
const resetTyping = () => {
  pauseTyping()
  currentText.value = ''
  isDeleting.value = false
  currentTextIndex.value = 0
}

// 监听文本变化
watch(() => props.text, () => {
  // 如果文本变化，重新开始打字
  if (props.autoStart) {
    resetTyping()
    startTyping()
  }
})

// 监听循环文本数组变化
watch(() => props.loopTexts, () => {
  // 如果循环文本数组变化，重新开始打字
  if (props.autoStart) {
    resetTyping()
    startTyping()
  }
}, { deep: true })

// 组件挂载时
onMounted(() => {
  if (props.autoStart) {
    startTyping()
  }
})

// 组件卸载时
onUnmounted(() => {
  if (typingTimer !== null) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
})

// 暴露方法
defineExpose({
  startTyping,
  pauseTyping,
  resetTyping
})
</script>

<style lang="scss" scoped>
.typing-text {
  display: inline-block;
  font-family: 'Courier New', monospace;
  position: relative;
  
  .typed-text {
    display: inline-block;
    color: inherit;
    text-shadow: 0 0 5px rgba(0, 255, 170, 0.7);
  }
  
  .cursor {
    display: inline-block;
    color: #00ffaa;
    font-weight: bold;
    margin-left: 2px;
    animation: blink 1s infinite;
  }
}

// 光标闪烁动画
@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

// 科幻风格的闪烁光标
.typing-text.blinking-cursor .typed-text::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%,
    rgba(0, 255, 170, 0.3) 10%,
    rgba(0, 255, 170, 0.7) 50%,
    rgba(0, 255, 170, 0.3) 90%,
    transparent 100%
  );
  animation: scanline 2s linear infinite;
}

@keyframes scanline {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style> 