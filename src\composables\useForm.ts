import { reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

/**
 * 登录表单处理的组合式函数
 */
export function useLoginForm() {
  const loginFormRef = ref<FormInstance>()

  // 表单数据
  const loginForm = reactive({
    username: '',
    password: '',
    remember: false
  })

  // 手机号验证规则
  const validatePhone = (rule: any, value: string, callback: any) => {
    const phoneRegex = /^1[3-9]\d{9}$/
    if (value && !phoneRegex.test(value)) {
      callback(new Error('请输入正确的手机号码'))
    } else {
      callback()
    }
  }

  // 用户名或手机号验证规则
  const validateUsernameOrPhone = (rule: any, value: string, callback: any) => {
    if (!value) {
      callback(new Error('请输入用户名或手机号'))
      return
    }

    // 检查是否为手机号
    const phoneRegex = /^1[3-9]\d{9}$/
    if (phoneRegex.test(value)) {
      callback() // 是有效手机号
      return
    }

    // 检查用户名格式
    if (value.length < 2 || value.length > 20) {
      callback(new Error('用户名长度应在2-20个字符之间'))
      return
    }

    callback()
  }

  // 表单验证规则
  const rules: FormRules = {
    username: [
      { validator: validateUsernameOrPhone, trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度应在6-20个字符之间', trigger: 'blur' }
    ]
  }

  // 从本地存储加载记住的用户信息
  const loadRememberedUser = () => {
    const rememberedUser = localStorage.getItem('rememberedUser')
    if (rememberedUser) {
      try {
        const user = JSON.parse(rememberedUser)
        loginForm.username = user.username
        loginForm.password = user.password
        loginForm.remember = true
      } catch (error) {
        console.error('解析记住的用户信息失败:', error)
        localStorage.removeItem('rememberedUser')
      }
    }
  }

  // 重置表单
  const resetForm = () => {
    if (loginFormRef.value) {
      loginFormRef.value.resetFields()
    }
  }

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    if (!loginFormRef.value) return false

    return await loginFormRef.value.validate()
      .then(() => true)
      .catch(() => false)
  }

  return {
    loginFormRef,
    loginForm,
    rules,
    loadRememberedUser,
    resetForm,
    validateForm
  }
}
