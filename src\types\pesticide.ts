// 农药台账接口
export interface PesticideLedger {
  id: string;
  name: string;
  registrationNumber: string;
  activeIngredients: string;
  formulation: string;
  manufacturer: string;
  approvalDate: string;
  validUntil: string;
  toxicityLevel: string;
  usageTargets: string[];
  dosageRange: string;
  safetyInterval: number;
  storageRequirements: string;
  notes: string;
  currentStock: number;
  stockUnit: string;
  lowStockThreshold: number;
  createdAt: string;
  updatedAt: string;
  specification?: string;
  stockQuantity?: number;
  unit?: string;
  expiryDate?: string;
  batchNumber?: string;
  supplier?: string;
  inboundTime?: string;
  purchaseBatch?: string;
  lowStockWarning?: number;
}

// 农药采购记录接口
export interface PesticidePurchase {
  id: string;
  pesticideId: string;
  pesticide?: PesticideLedger;
  purchaseDate: string;
  quantity: number;
  unit: string;
  price: number;
  supplierName: string;
  supplierContact: string;
  invoiceNumber: string;
  batchNumber: string;
  expiryDate: string;
  purchasedBy: string;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

// 农药使用记录接口
export interface PesticideUsage {
  id: string;
  pesticideId: string;
  pesticide?: PesticideLedger;
  usageDate: string;
  quantity: number;
  unit: string;
  targetCrop: string;
  targetPest: string;
  fieldLocation: string;
  fieldArea: number;
  areaUnit: string;
  applicationMethod: string;
  weatherConditions: string;
  appliedBy: string;
  supervisedBy: string;
  notes: string;
  createdAt: string;
  updatedAt: string;
}

// 配比计算参数接口
export interface RatioCalculationParams {
  cropType: string;
  pestType: string;
  cropGrowthStage: string;
  fieldArea: number;
  areaUnit: string;
  infestationLevel: 'light' | 'medium' | 'severe';
  applicationMethod: string;
  temperature: number;
  humidity: number;
  windSpeed: number;
}

// 配比推荐结果接口
export interface RatioRecommendation {
  id: string;
  pesticideId: string;
  pesticide: PesticideLedger;
  dilutionRatio: string;
  quantity: number;
  unit: string;
  waterVolume: number;
  applicationRate: string;
  precautions: string[];
  effectiveness: number;
  safetyScore: number;
  environmentalImpact: number;
  costEstimate: number;
  isFavorite: boolean;
  createdAt: string;
}

/**
 * 无人机喷洒参数类型定义
 */

/**
 * 无人机喷洒参数
 */
export interface UAVSprayingParameters {
  /** 配置ID */
  id?: string;
  /** 无人机型号 */
  uavModel: string;
  /** 飞行模式 */
  flightMode: 'manual' | 'semi_auto' | 'full_auto';
  /** 农田区域ID */
  farmAreaId: string;
  /** 农田区域名称 */
  farmAreaName: string;
  /** 飞行高度(米) */
  flightHeight: number;
  /** 飞行速度(米/秒) */
  flightSpeed: number;
  /** 喷洒量(升/亩) */
  sprayingQuantity: number;
  /** 喷雾粒径 */
  dropletSize: 'fine' | 'medium' | 'coarse';
  /** 风速(米/秒) */
  windSpeed: number;
  /** 风向 */
  windDirection: string;
  /** 与障碍物安全距离(米) */
  safetyDistanceToObstacles: number;
  /** 与非目标区域安全距离(米) */
  safetyDistanceToNonTargetAreas: number;
  /** 紧急制动配置 */
  emergencyBraking: boolean;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 创建人 */
  createdBy?: string;
}

/**
 * 农田区域
 */
export interface FarmArea {
  /** 区域ID */
  id: string;
  /** 区域名称 */
  name: string;
  /** 区域面积(亩) */
  area: number;
  /** 区域位置 */
  location?: string;
  /** 种植作物 */
  crops?: string;
}

// 电子围栏接口
export interface ElectronicFence {
  id: string;
  name: string;
  description: string;
  coordinates: { lat: number; lng: number }[];
  fenceType: 'no_entry' | 'no_spray' | 'limited_spray';
  color: string;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 监测点接口
export interface MonitoringPoint {
  id: string;
  name: string;
  location: { lat: number; lng: number };
  type: 'soil' | 'water' | 'air';
  lastSampledAt: string;
  currentValue: number;
  unit: string;
  standardValue: number;
  status: 'normal' | 'warning' | 'danger';
  trend: 'stable' | 'rising' | 'falling';
  historicalData: { date: string; value: number }[];
  notes: string;
  createdAt: string;
  updatedAt: string;
}

// Environmental Compliance Types
export type ComplianceCheckItem = {
  id: string;
  checkTitle: string;
  category: string;
  requirementLevel: 'mandatory' | 'recommended';
  status: 'passed' | 'failed' | 'warning' | 'not_checked';
  lastCheckedAt: string;
  referenceStandard: string;
  verificationMethod: string;
  description: string;
}

export type ComplianceViolation = {
  id: string;
  checkItemId: string;
  severity: 'minor' | 'moderate' | 'major' | 'critical';
  violationDate: string;
  description: string;
  correctiveAction: string;
  actionStatus: 'pending' | 'in_progress' | 'completed';
  actionCompletedAt?: string;
} 