<template>
  <div class="map-container">
    <div ref="mapContainer" class="trajectory-map"></div>
    
    <div class="map-controls" v-if="devicePaths.length > 0">
      <div class="device-list">
        <div v-for="(device, index) in deviceList" :key="device.id" class="device-item">
          <el-checkbox 
            v-model="deviceVisibility[index]" 
            @change="toggleDeviceVisibility(index)"
          >
            <span :style="{ color: deviceColors[index % deviceColors.length] }">{{ device.name }}</span>
          </el-checkbox>
        </div>
      </div>
      
      <div class="map-legend">
        <div class="legend-item">
          <div class="legend-color" style="background-color: #67c23a;"></div>
          <span class="legend-label">起始点</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: #f56c6c;"></div>
          <span class="legend-label">结束点</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: #e6a23c;"></div>
          <span class="legend-label">关键点</span>
        </div>
      </div>
    </div>
    
    <div class="empty-map" v-if="devicePaths.length === 0">
      <el-empty description="暂无设备路径数据" :image-size="80" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import type { DeviceInfo } from '@/types/taskScheduling';

// 定义传入的属性
const props = defineProps<{
  deviceList: DeviceInfo[];
  devicePaths: Array<{
    deviceId: string;
    path: Array<{
      x: number;
      y: number;
      timestamp: string;
      type?: 'normal' | 'key' | 'warning';
    }>;
  }>;
}>();

// 地图容器引用
const mapContainer = ref<HTMLDivElement | null>(null);

// 设备轨迹颜色
const deviceColors = [
  '#3b82f6', // 蓝色
  '#10b981', // 绿色
  '#f59e0b', // 橙色
  '#8b5cf6', // 紫色
  '#ec4899', // 粉色
  '#14b8a6', // 青色
  '#f43f5e', // 红色
  '#6366f1'  // 靛蓝
];

// 设备可见性控制
const deviceVisibility = ref<boolean[]>([]);

// 初始化设备可见性
watch(() => props.deviceList, () => {
  deviceVisibility.value = Array(props.deviceList.length).fill(true);
}, { immediate: true });

// 切换设备可见性
const toggleDeviceVisibility = (index: number) => {
  // 实际应用中，这里会调用地图API来显示/隐藏对应设备的轨迹
  console.log(`切换设备 ${props.deviceList[index].name} 的可见性为: ${deviceVisibility.value[index]}`);
};

// 渲染地图
const renderMap = () => {
  if (!mapContainer.value) return;
  
  // 这里是地图渲染的示意代码
  // 实际项目中，你需要使用具体的地图API来实现
  // 例如：Leaflet、OpenLayers、百度地图API等
  
  // 示例：使用HTML5 Canvas绘制简单的轨迹图
  const canvas = document.createElement('canvas');
  canvas.width = mapContainer.value.clientWidth;
  canvas.height = mapContainer.value.clientHeight;
  mapContainer.value.innerHTML = '';
  mapContainer.value.appendChild(canvas);
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // 设置背景
  ctx.fillStyle = 'rgba(31, 41, 55, 0.5)';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制网格
  ctx.strokeStyle = 'rgba(75, 85, 99, 0.2)';
  ctx.lineWidth = 1;
  
  const gridSize = 30;
  for (let x = 0; x < canvas.width; x += gridSize) {
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, canvas.height);
    ctx.stroke();
  }
  
  for (let y = 0; y < canvas.height; y += gridSize) {
    ctx.beginPath();
    ctx.moveTo(0, y);
    ctx.lineTo(canvas.width, y);
    ctx.stroke();
  }
  
  // 绘制设备轨迹
  props.devicePaths.forEach((devicePath, deviceIndex) => {
    if (!deviceVisibility.value[deviceIndex]) return;
    
    const deviceColor = deviceColors[deviceIndex % deviceColors.length];
    const path = devicePath.path;
    
    if (path.length === 0) return;
    
    // 归一化坐标
    const allX = path.map(p => p.x);
    const allY = path.map(p => p.y);
    const minX = Math.min(...allX);
    const maxX = Math.max(...allX);
    const minY = Math.min(...allY);
    const maxY = Math.max(...allY);
    
    const padding = 40;
    const normalizeX = (x: number) => (
      padding + (x - minX) / (maxX - minX || 1) * (canvas.width - padding * 2)
    );
    const normalizeY = (y: number) => (
      padding + (y - minY) / (maxY - minY || 1) * (canvas.height - padding * 2)
    );
    
    // 绘制轨迹线
    ctx.strokeStyle = deviceColor;
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(normalizeX(path[0].x), normalizeY(path[0].y));
    
    for (let i = 1; i < path.length; i++) {
      ctx.lineTo(normalizeX(path[i].x), normalizeY(path[i].y));
    }
    
    ctx.stroke();
    
    // 绘制起点
    ctx.fillStyle = '#67c23a';
    ctx.beginPath();
    ctx.arc(normalizeX(path[0].x), normalizeY(path[0].y), 8, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制终点
    ctx.fillStyle = '#f56c6c';
    ctx.beginPath();
    ctx.arc(
      normalizeX(path[path.length - 1].x), 
      normalizeY(path[path.length - 1].y), 
      8, 0, Math.PI * 2
    );
    ctx.fill();
    
    // 绘制关键点
    path.forEach((point, i) => {
      if (i === 0 || i === path.length - 1) return; // 跳过起点和终点
      
      if (point.type === 'key' || point.type === 'warning') {
        ctx.fillStyle = point.type === 'key' ? '#e6a23c' : '#f56c6c';
        ctx.beginPath();
        ctx.arc(normalizeX(point.x), normalizeY(point.y), 6, 0, Math.PI * 2);
        ctx.fill();
      } else {
        // 普通轨迹点
        ctx.fillStyle = deviceColor;
        ctx.beginPath();
        ctx.arc(normalizeX(point.x), normalizeY(point.y), 3, 0, Math.PI * 2);
        ctx.fill();
      }
    });
  });
};

// 在组件挂载后初始化地图
onMounted(() => {
  renderMap();
  
  // 监听窗口大小变化，调整地图大小
  window.addEventListener('resize', renderMap);
});

// 监听属性变化，更新地图
watch(() => [props.devicePaths, deviceVisibility.value], () => {
  renderMap();
}, { deep: true });
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(31, 41, 55, 0.3);
}

.trajectory-map {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(31, 41, 55, 0.8);
  border-radius: 8px;
  padding: 10px;
  color: #e5e7eb;
  z-index: 10;
  max-width: 200px;
}

.device-list {
  margin-bottom: 15px;
}

.device-item {
  margin-bottom: 5px;
}

.map-legend {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-label {
  font-size: 12px;
  color: #e5e7eb;
}

.empty-map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(31, 41, 55, 0.3);
}
</style> 