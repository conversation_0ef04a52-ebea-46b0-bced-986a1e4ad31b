<template>
  <div class="auto-cruise-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <div class="panel-title">
        <el-icon class="title-icon">
          <Location />
        </el-icon>
        <span>自动巡航</span>
        <el-badge
          :value="statusText"
          :type="statusBadgeType"
          class="status-badge"
        />
      </div>
      <div class="panel-actions">
        <el-button
          size="small"
          type="primary"
          @click="showSettings = !showSettings"
          circle
        >
          <el-icon><Setting /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content">
      <!-- 状态信息 -->
      <div class="status-section">
        <div class="status-info">
          <div class="info-item">
            <span class="label">状态:</span>
            <span class="value" :class="`status-${status}`">{{ statusText }}</span>
          </div>
          <div class="info-item" v-if="isActive">
            <span class="label">进度:</span>
            <span class="value">{{ progress }}%</span>
          </div>
          <div class="info-item" v-if="runningTime !== '00:00:00'">
            <span class="label">运行时间:</span>
            <span class="value">{{ runningTime }}</span>
          </div>
        </div>

        <!-- 进度条 -->
        <el-progress
          v-if="isActive && pathPoints.length > 0"
          :percentage="progress"
          :status="status === 'error' ? 'exception' : 'success'"
          :stroke-width="6"
          class="progress-bar"
        />
      </div>

      <!-- 路径信息 -->
      <div class="path-section">
        <div class="section-title">
          <span>巡航路径</span>
          <span class="path-count">({{ pathPoints.length }} 个点)</span>
          <el-button
            v-if="pathPoints.length > 0"
            size="small"
            type="primary"
            text
            @click="showEditDialog"
            :disabled="isActive"
          >
            <el-icon><Edit /></el-icon>
            编辑路径
          </el-button>
        </div>

        <div class="path-actions">
          <div class="path-info">
            <el-alert
              title="所有路径都使用基站完整覆盖范围，充分利用定位区域"
              type="info"
              :closable="false"
              show-icon
              style="margin-bottom: 12px;"
            />
          </div>

          <el-button-group size="small">
            <el-button
              @click="generateRectanglePath"
              :disabled="isActive || !hasAnchors"
            >
              <el-icon><Grid /></el-icon>
              矩形路径
            </el-button>
            <el-button
              @click="generateCirclePath"
              :disabled="isActive || !hasAnchors"
            >
              <el-icon><Compass /></el-icon>
              圆形路径
            </el-button>
            <el-button
              @click="generateSShapePath"
              :disabled="isActive || !hasAnchors"
            >
              <el-icon><Switch /></el-icon>
              S形路径
            </el-button>
          </el-button-group>

          <el-button-group size="small" style="margin-top: 8px;">
            <el-button
              @click="generateSpiralPath"
              :disabled="isActive || !hasAnchors"
            >
              <el-icon><Refresh /></el-icon>
              螺旋路径
            </el-button>
            <el-button
              @click="generateAnchorPath"
              :disabled="isActive || !hasAnchors"
              type="primary"
            >
              <el-icon><Position /></el-icon>
              基站路径 (推荐)
            </el-button>

            <el-button
              @click="openCustomPathDrawer"
              :disabled="isActive || !hasAnchors"
              type="success"
            >
              <el-icon><Edit /></el-icon>
              自定义路径
            </el-button>
            <el-button
              @click="optimizeCurrentPath"
              :disabled="isActive || pathPoints.length < 3"
            >
              <el-icon><Tools /></el-icon>
              优化路径
            </el-button>
          </el-button-group>

          <div class="path-warning" v-if="!hasAnchors">
            <el-alert
              title="需要至少4个基站才能生成安全路径"
              type="warning"
              :closable="false"
              show-icon
              style="margin-top: 8px;"
            />
          </div>
        </div>

        <!-- 路径点列表 -->
        <div class="path-points" v-if="pathPoints.length > 0">
          <div
            v-for="(point, index) in pathPoints"
            :key="index"
            class="path-point"
            :class="{ active: index === currentPointIndex && isActive }"
          >
            <div class="point-info">
              <span class="point-name">{{ point.name || `点${index + 1}` }}</span>
              <span class="point-coords">({{ point.x.toFixed(1) }}, {{ point.y.toFixed(1) }})</span>
            </div>
            <el-icon v-if="index === currentPointIndex && isActive" class="current-icon">
              <LocationFilled />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="control-section">
        <div class="main-controls">
          <el-button
            v-if="status === 'idle' || status === 'error'"
            type="primary"
            size="large"
            @click="startCruise"
            :disabled="pathPoints.length === 0 || !rtcConnected"
            class="control-btn"
          >
            <el-icon><VideoPlay /></el-icon>
            开始巡航
          </el-button>

          <el-button
            v-else-if="status === 'paused'"
            type="success"
            size="large"
            @click="resumeCruise"
            class="control-btn"
          >
            <el-icon><VideoPlay /></el-icon>
            继续巡航
          </el-button>

          <template v-else-if="isActive">
            <el-button
              type="warning"
              size="large"
              @click="pauseCruise"
              class="control-btn"
            >
              <el-icon><VideoPause /></el-icon>
              暂停巡航
            </el-button>
            <el-button
              type="danger"
              size="large"
              @click="stopCruise"
              class="control-btn"
            >
              <el-icon><VideoCamera /></el-icon>
              停止巡航
            </el-button>
          </template>
        </div>
      </div>

      <!-- 设置面板 -->
      <el-collapse-transition>
        <div v-show="showSettings" class="settings-section">
          <div class="section-title">巡航设置</div>

          <div class="setting-item">
            <label>移动速度</label>
            <el-slider
              v-model="localConfig.speed"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              :disabled="isActive"
              @change="updateCruiseConfig"
            />
            <span class="setting-value">{{ localConfig.speed }}</span>
          </div>

          <div class="setting-item">
            <label>停留时间 (秒)</label>
            <el-slider
              v-model="localConfig.pauseTime"
              :min="0"
              :max="10"
              :step="1"
              :disabled="isActive"
              @change="updateCruiseConfig"
            />
            <span class="setting-value">{{ localConfig.pauseTime }}s</span>
          </div>



          <div class="setting-switches">
            <el-switch
              v-model="localConfig.loopMode"
              :disabled="isActive"
              @change="updateCruiseConfig"
            />
            <label>循环巡航</label>
          </div>

          <div class="setting-switches">
            <el-switch
              v-model="localConfig.returnToStart"
              :disabled="isActive || localConfig.loopMode"
              @change="updateCruiseConfig"
            />
            <label>返回起点</label>
          </div>

          <div class="setting-item">
            <label>最大移动距离 (米)</label>
            <el-slider
              v-model="localConfig.maxDistance"
              :min="1"
              :max="20"
              :step="1"
              :disabled="isActive"
              @change="updateCruiseConfig"
            />
            <span class="setting-value">{{ localConfig.maxDistance }}m</span>
          </div>

          <div class="setting-item">
            <label>超时时间 (秒)</label>
            <el-slider
              v-model="localConfig.timeoutSeconds"
              :min="10"
              :max="120"
              :step="5"
              :disabled="isActive"
              @change="updateCruiseConfig"
            />
            <span class="setting-value">{{ localConfig.timeoutSeconds }}s</span>
          </div>

          <div class="setting-switches">
            <el-switch
              v-model="localConfig.enableBoundaryCheck"
              :disabled="isActive"
              @change="updateCruiseConfig"
            />
            <label>启用边界检查</label>
          </div>

          <!-- 角度控制设置 -->
          <div class="section-divider"></div>
          <div class="section-subtitle">角度控制</div>

          <div class="setting-switches">
            <el-switch
              v-model="localConfig.enableAngleControl"
              :disabled="isActive"
              @change="updateCruiseConfig"
            />
            <label>启用角度控制</label>
          </div>

          <div v-if="localConfig.enableAngleControl" class="angle-control-settings">
            <div class="setting-item">
              <label>转向速度 (度/秒)</label>
              <el-slider
                v-model="localConfig.rotationSpeed"
                :min="10"
                :max="90"
                :step="5"
                :disabled="isActive"
                @change="updateCruiseConfig"
              />
              <span class="setting-value">{{ localConfig.rotationSpeed }}°/s</span>
            </div>

            <div class="setting-item">
              <label>角度容差 (度)</label>
              <el-slider
                v-model="localConfig.angleTolerance"
                :min="5"
                :max="30"
                :step="1"
                :disabled="isActive"
                @change="updateCruiseConfig"
              />
              <span class="setting-value">{{ localConfig.angleTolerance }}°</span>
            </div>

            <div class="setting-item">
              <label>转向超时 (秒)</label>
              <el-slider
                v-model="localConfig.rotationTimeout"
                :min="5"
                :max="30"
                :step="1"
                :disabled="isActive"
                @change="updateCruiseConfig"
              />
              <span class="setting-value">{{ localConfig.rotationTimeout }}s</span>
            </div>
          </div>
        </div>
      </el-collapse-transition>

      <!-- 错误信息 -->
      <div v-if="lastError" class="error-section">
        <el-alert
          :title="lastError"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 统计信息 -->
      <div v-if="totalDistance > 0 || completedLoops > 0" class="stats-section">
        <div class="section-title">统计信息</div>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">总距离</span>
            <span class="stat-value">{{ totalDistance.toFixed(1) }}m</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">完成轮次</span>
            <span class="stat-value">{{ completedLoops }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 路径编辑对话框 -->
    <PathEditDialog
      v-model:visible="editDialogVisible"
      :path-points="pathPoints"
      :anchors="anchors"
      :current-position="currentPosition"
      :is-active="isActive"
      @save="handlePathEdit"
    />

    <!-- 自定义路径绘制器 -->
    <CustomPathDrawer
      v-model:visible="customPathDrawerVisible"
      :anchors="anchors"
      :current-position="currentPosition"
      @save="handleCustomPath"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Location,
  LocationFilled,
  Setting,
  VideoPlay,
  VideoPause,
  VideoCamera,
  Grid,
  Compass,
  Switch,
  Refresh,
  Position,
  Tools,
  Edit
} from '@element-plus/icons-vue'
import type { PathPoint, CruiseConfig, CruiseStatus } from '../composables/useAutoCruise'
import PathEditDialog from './PathEditDialog.vue'
import CustomPathDrawer from './CustomPathDrawer.vue'

// Props
interface Props {
  status: CruiseStatus
  config: CruiseConfig
  pathPoints: PathPoint[]
  currentPointIndex: number
  currentPosition: PathPoint
  isActive: boolean
  progress: number
  runningTime: string
  totalDistance: number
  completedLoops: number
  lastError: string
  rtcConnected: boolean
  anchors?: Array<{x: number, y: number}>
}

const props = withDefaults(defineProps<Props>(), {
  anchors: () => []
})

// Emits
interface Emits {
  (e: 'start'): void
  (e: 'stop'): void
  (e: 'pause'): void
  (e: 'resume'): void
  (e: 'update-config', config: Partial<CruiseConfig>): void
  (e: 'set-path', points: PathPoint[]): void
  (e: 'generate-default-path', center: PathPoint): void
  (e: 'generate-circular-path', center: PathPoint): void
  (e: 'generate-s-shape-path', center: PathPoint): void
  (e: 'generate-spiral-path', center: PathPoint): void
  (e: 'generate-anchor-path'): void
  (e: 'generate-custom-path', points: PathPoint[]): void
  (e: 'optimize-path'): void
  (e: 'edit-path', points: PathPoint[]): void
}

const emit = defineEmits<Emits>()

// 本地状态
const showSettings = ref(false)
const localConfig = ref<CruiseConfig>({ ...props.config })
const editDialogVisible = ref(false)
const customPathDrawerVisible = ref(false)

// 计算属性
const statusText = computed(() => {
  switch (props.status) {
    case 'idle': return '空闲'
    case 'planning': return '规划中'
    case 'cruising': return '巡航中'
    case 'paused': return '已暂停'
    case 'returning': return '返回中'
    case 'error': return '错误'
    default: return '未知'
  }
})

const statusBadgeType = computed(() => {
  switch (props.status) {
    case 'cruising': return 'success'
    case 'paused': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
})

const hasAnchors = computed(() => props.anchors && props.anchors.length >= 4)

// 监听配置变化
watch(() => props.config, (newConfig) => {
  localConfig.value = { ...newConfig }
}, { deep: true })

// 方法
const startCruise = () => {
  if (props.pathPoints.length === 0) {
    ElMessage.warning('请先设置巡航路径')
    return
  }
  if (!props.rtcConnected) {
    ElMessage.warning('机器狗未连接')
    return
  }
  emit('start')
}

const stopCruise = () => {
  emit('stop')
}

const pauseCruise = () => {
  emit('pause')
}

const resumeCruise = () => {
  emit('resume')
}

const updateCruiseConfig = () => {
  emit('update-config', localConfig.value)
}

const generateRectanglePath = () => {
  emit('generate-default-path', props.currentPosition)
}

const generateCirclePath = () => {
  emit('generate-circular-path', props.currentPosition)
}

const generateSShapePath = () => {
  emit('generate-s-shape-path', props.currentPosition)
}

const generateSpiralPath = () => {
  emit('generate-spiral-path', props.currentPosition)
}

const generateAnchorPath = () => {
  if (!hasAnchors.value) {
    ElMessage.warning('基站数量不足，无法生成路径')
    return
  }
  emit('generate-anchor-path')
}

const optimizeCurrentPath = () => {
  if (props.pathPoints.length < 3) {
    ElMessage.warning('路径点数量不足，无法优化')
    return
  }
  emit('optimize-path')
}

const showEditDialog = () => {
  if (props.pathPoints.length === 0) {
    ElMessage.warning('请先生成巡航路径')
    return
  }
  editDialogVisible.value = true
}

const handlePathEdit = (editedPoints: PathPoint[]) => {
  emit('edit-path', editedPoints)
  ElMessage.success(`路径编辑完成，共 ${editedPoints.length} 个路径点`)
}

const openCustomPathDrawer = () => {
  if (!hasAnchors.value) {
    ElMessage.warning('请先确保基站数据完整')
    return
  }
  customPathDrawerVisible.value = true
}

const handleCustomPath = (customPoints: PathPoint[]) => {
  emit('generate-custom-path', customPoints)
  ElMessage.success(`自定义路径生成完成，共 ${customPoints.length} 个路径点`)
}
</script>

<style scoped lang="scss">
.auto-cruise-panel {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(235, 238, 245, 0.8);

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .panel-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;

      .title-icon {
        font-size: 18px;
      }

      .status-badge {
        margin-left: 8px;
      }
    }
  }

  .panel-content {
    padding: 20px;
  }

  .status-section {
    margin-bottom: 20px;

    .status-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 12px;
      margin-bottom: 12px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;

        .label {
          font-size: 12px;
          color: #666;
        }

        .value {
          font-weight: 600;
          font-size: 14px;

          &.status-cruising { color: #67c23a; }
          &.status-paused { color: #e6a23c; }
          &.status-error { color: #f56c6c; }
          &.status-idle { color: #909399; }
        }
      }
    }

    .progress-bar {
      margin-top: 8px;
    }
  }

  .path-section {
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #333;

      .path-count {
        font-size: 12px;
        color: #666;
        font-weight: normal;
      }
    }

    .path-actions {
      margin-bottom: 12px;
    }

    .path-points {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .path-point {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &.active {
          background: #e8f4fd;
          border-left: 3px solid #409eff;
        }

        .point-info {
          .point-name {
            font-weight: 500;
            margin-right: 8px;
          }

          .point-coords {
            font-size: 12px;
            color: #666;
          }
        }

        .current-icon {
          color: #409eff;
          animation: pulse 2s infinite;
        }
      }
    }
  }

  .control-section {
    margin-bottom: 20px;

    .main-controls {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      .control-btn {
        flex: 1;
        min-width: 120px;
      }
    }
  }

  .settings-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .section-title {
      margin-bottom: 16px;
      font-weight: 600;
      color: #333;
    }

    .setting-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      gap: 12px;

      label {
        min-width: 80px;
        font-size: 14px;
        color: #666;
      }

      .el-slider {
        flex: 1;
      }

      .setting-value {
        min-width: 40px;
        text-align: right;
        font-weight: 600;
        color: #333;
      }
    }

    .setting-switches {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      label {
        font-size: 14px;
        color: #666;
      }
    }

    .section-divider {
      height: 1px;
      background: #e5e7eb;
      margin: 20px 0 16px 0;
    }

    .section-subtitle {
      font-size: 14px;
      font-weight: 600;
      color: #6b7280;
      margin-bottom: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .angle-control-settings {
      margin-left: 16px;
      padding-left: 16px;
      border-left: 2px solid #e5e7eb;

      .setting-item {
        margin-bottom: 12px;
      }
    }
  }

  .error-section {
    margin-bottom: 20px;
  }

  .stats-section {
    .section-title {
      margin-bottom: 12px;
      font-weight: 600;
      color: #333;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;

        .stat-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .stat-value {
          font-weight: 600;
          font-size: 16px;
          color: #333;
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
