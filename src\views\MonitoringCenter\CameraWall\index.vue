<template>
  <div class="camera-wall-container">
    <!--
      注意：当前实现已替换为机器狗实时监控
      原虚拟摄像头代码已移除
    -->

    <!-- 页面标题 -->
    <PageHeader title="智慧农场智能设备监控" description="实时监控各智能设备摄像头视频流" icon="Monitor">
      <template #actions>
        <div class="connection-actions">
          <el-button
            type="primary"
            size="small"
            @click="handleConnect"
            icon="Refresh"
          >
            刷新摄像头
          </el-button>
          <el-button
            type="danger"
            size="small"
            :disabled="cameras.length === 0"
            @click="handleDisconnect"
            icon="CircleClose"
          >
            断开所有连接
          </el-button>
        </div>
      </template>
    </PageHeader>

    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="bg-grid"></div>
      <div class="bg-circle circle-1"></div>
      <div class="bg-circle circle-2"></div>
      <div class="bg-circle circle-3"></div>
    </div>

    <!-- 主内容区域：摄像头网格布局 -->
    <div class="main-content">
      <div class="camera-wall-layout">
        <!-- 控制面板 -->
        <div class="control-section">
          <ControlPanel
            :cameras="cameras"
            :current-layout="currentLayout"
            :carousel-settings="carouselSettings"
            @update:layout="handleLayoutChange"
            @update:carousel="handleCarouselChange"
            @update:status-filter="handleStatusFilterChange"
            @refresh-cameras="refreshCameras"
          />
        </div>

        <!-- 摄像头网格 -->
        <div class="camera-section">
          <CameraGrid
            :cameras="cameras"
            :layout="currentLayout"
            :selected-statuses="selectedStatuses"
            @camera-selected="handleCameraSelected"
            @camera-error="handleCameraError"
          />
        </div>
      </div>
    </div>

    <!-- 状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator
          type="success"
          :active="connected"
          label="已连接"
        />
        <StatusIndicator
          type="warning"
          :active="connecting"
          label="连接中"
        />
        <StatusIndicator
          type="error"
          :active="connectionError"
          label="连接错误"
        />
        <StatusIndicator
          type="offline"
          :active="!connected && !connecting && !connectionError"
          label="未连接"
        />
      </div>
      <div class="refresh-info">
        <span>最后更新时间: {{ formatTime(currentDateTime) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Monitor, Connection, Link, CircleClose } from '@element-plus/icons-vue';
import PageHeader from '../components/PageHeader.vue';
import StatusIndicator from '../components/StatusIndicator.vue';
import CameraGrid from './components/CameraGrid.vue';
import ControlPanel from './components/ControlPanel.vue';
import { getMockCameras } from './api/camera';
import type { Camera } from './types';
import envConfig from '@/config/env';

// 状态变量
const cameras = ref<Camera[]>([]);
const currentLayout = ref('2x2');
const selectedStatuses = ref(['normal', 'lowBattery']);
const carouselSettings = ref({
  enabled: false,
  interval: 10,
  order: 'sequential'
});
const selectedCamera = ref<Camera | null>(null);

// 时间相关状态
const currentDateTime = ref(new Date());
let timeUpdateTimer: number | null = null;

// 时间格式化函数
const formatTime = (date: Date): string => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 更新当前时间
const updateCurrentTime = () => {
  currentDateTime.value = new Date();
};

// 启动时间更新定时器
const startTimeUpdate = () => {
  updateCurrentTime();
  timeUpdateTimer = window.setInterval(updateCurrentTime, 1000);
};

// 停止时间更新定时器
const stopTimeUpdate = () => {
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer);
    timeUpdateTimer = null;
  }
};

// 摄像头管理方法
const refreshCameras = async () => {
  try {
    // 这里可以调用真实的API获取摄像头列表
    // const response = await getCameras();
    // cameras.value = response;

    // 暂时使用模拟数据
    cameras.value = getMockCameras();
    ElMessage.success('摄像头列表已刷新');
  } catch (error) {
    console.error('刷新摄像头列表失败:', error);
    ElMessage.error('刷新摄像头列表失败');
  }
};

// 控制面板事件处理
const handleLayoutChange = (layout: string) => {
  currentLayout.value = layout;
  ElMessage.success(`布局已切换为 ${layout}`);
};

const handleCarouselChange = (settings: any) => {
  carouselSettings.value = { ...settings };
  if (settings.enabled) {
    ElMessage.success('轮播模式已启用');
  } else {
    ElMessage.info('轮播模式已关闭');
  }
};

const handleStatusFilterChange = (statuses: string[]) => {
  selectedStatuses.value = [...statuses];
};

const handleCameraSelected = (camera: Camera) => {
  selectedCamera.value = camera;
  console.log('选中摄像头:', camera);
};

const handleCameraError = (cameraId: string, error: string) => {
  console.error(`摄像头 ${cameraId} 错误:`, error);
  ElMessage.error(`摄像头 ${cameraId}: ${error}`);
};

// 连接处理函数（用于批量连接所有摄像头）
const handleConnect = async () => {
  try {
    await refreshCameras();
    ElMessage.success('摄像头系统已初始化');
  } catch (error) {
    console.error('初始化摄像头系统失败:', error);
    ElMessage.error('初始化摄像头系统失败');
  }
};

// 断开连接（清理所有摄像头连接）
const handleDisconnect = () => {
  ElMessageBox.confirm('确定要断开所有摄像头连接吗?', '断开连接', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    cameras.value = [];
    selectedCamera.value = null;
    ElMessage.info('已断开所有摄像头连接');
  }).catch(() => {
    // 用户取消
  });
};

// 组件挂载时
onMounted(() => {
  // 启动时间更新
  startTimeUpdate();
  // 初始化摄像头列表
  refreshCameras();
});

// 组件卸载时清理
onBeforeUnmount(() => {
  // 停止时间更新定时器
  stopTimeUpdate();
  // 清理工作由CameraGrid组件处理
});
</script>

<style scoped lang="scss">
@use '../components/styles/common.scss' as common;

// 全局动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(common.$primary-color, 0.4);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(common.$primary-color, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(common.$primary-color, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-5px);
  }

  100% {
    transform: translateY(0px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(common.$primary-color, 0.5);
  }

  50% {
    box-shadow: 0 0 15px rgba(common.$primary-color, 0.8);
  }

  100% {
    box-shadow: 0 0 5px rgba(common.$primary-color, 0.5);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.camera-wall-container {
  padding: common.$spacing-md;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  background-color: common.$bg-dark;
  color: common.$text-light;
  animation: fadeIn 0.6s ease-out;

  // 添加微妙的背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 10% 10%, rgba(common.$primary-color, 0.05) 0%, transparent 70%);
    z-index: -1;
  }

  // 确保内容区域适当填充
  &>* {
    width: 100%;
  }

  // 优化页面标题在小屏幕上的显示
  :deep(.page-header) {
    margin-bottom: common.$spacing-md;

    @include common.responsive-md {
      margin-bottom: common.$spacing-sm;

      .header-title {
        font-size: 1.5rem;
      }

      .header-description {
        font-size: 0.9rem;
      }
    }

    @include common.responsive-sm {
      flex-direction: column;
      align-items: flex-start;
      gap: common.$spacing-xs;

      .header-actions {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}

// 主内容区域布局 - 摄像头直播墙
.main-content {
  flex: 1;
  min-height: 0; // 修复 flex 布局中的溢出问题
  margin-bottom: common.$spacing-md;
}

.camera-wall-layout {
  display: flex;
  height: 100%;
  gap: common.$spacing-md;

  @include common.responsive-md {
    flex-direction: column;
    gap: common.$spacing-sm;
  }
}

.control-section {
  width: 300px;
  flex-shrink: 0;

  @include common.responsive-md {
    width: 100%;
    height: auto;
  }
}

.camera-section {
  flex: 1;
  min-height: 400px;
  border-radius: common.$border-radius-md;
  overflow: hidden;
  box-shadow: common.$shadow-lg;

  @include common.responsive-md {
    min-height: 300px;
  }

  @include common.responsive-sm {
    min-height: 250px;
  }
}

// 状态指示器区域
.status-indicators {
  @include common.flex-between;
  padding: common.$spacing-md;
  background: common.$bg-panel;
  border-radius: common.$border-radius-md;
  margin-top: auto;
  box-shadow: common.$shadow-md;
  backdrop-filter: blur(5px);
  border-top: 1px solid rgba(common.$text-light, 0.05);
  animation: fadeIn 0.8s ease-out;

  &:hover {
    box-shadow: common.$shadow-lg;
  }

  .indicator-group {
    display: flex;
    gap: common.$spacing-lg;
    flex-wrap: wrap;

    @include common.responsive-md {
      gap: common.$spacing-md;
      justify-content: center;
    }

    @include common.responsive-sm {
      gap: common.$spacing-sm;
      width: 100%;
      justify-content: space-between;

      :deep(.status-indicator) {
        font-size: 12px; // 减小字体大小

        &::before {
          width: 8px; // 减小状态点大小
          height: 8px;
        }
      }
    }
  }

  .refresh-info {
    display: flex;
    align-items: center;
    gap: common.$spacing-md;
    color: common.$text-secondary;
    font-size: 14px;

    @include common.responsive-md {
      width: 100%;
      justify-content: space-between;
      margin-top: common.$spacing-sm;
    }

    @include common.responsive-sm {
      flex-direction: column;
      align-items: flex-start;
      gap: common.$spacing-xs;
        font-size: 12px;
    }
  }

  // 响应式调整
  @include common.responsive-md {
    flex-direction: column;
    gap: common.$spacing-md;
    padding: common.$spacing-sm;
  }

  @include common.responsive-sm {
    border-radius: common.$border-radius-sm;
    padding: common.$spacing-xs common.$spacing-sm;
  }
}

// 连接操作按钮样式
.connection-actions {
  display: flex;
  gap: 10px;

  @include common.responsive-sm {
    flex-direction: column;
    gap: 5px;
  }
}

// 背景装饰
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  pointer-events: none;
  opacity: 0.2;

  .bg-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  .bg-circle {
    position: absolute;
    border-radius: 50%;

    &.circle-1 {
      width: 300px;
      height: 300px;
      top: -100px;
      right: -100px;
      background: radial-gradient(circle, rgba(common.$primary-color, 0.2) 0%, transparent 70%);
    }

    &.circle-2 {
      width: 500px;
      height: 500px;
      bottom: -200px;
      left: -200px;
      background: radial-gradient(circle, rgba(common.$primary-color, 0.15) 0%, transparent 70%);
    }

    &.circle-3 {
      width: 200px;
      height: 200px;
      top: 30%;
      left: 20%;
      background: radial-gradient(circle, rgba(common.$primary-color, 0.1) 0%, transparent 70%);
    }
  }
}

// 响应式布局
@include common.responsive-sm {
  .camera-wall-container {
    padding: common.$spacing-sm;
  }
}
</style>
