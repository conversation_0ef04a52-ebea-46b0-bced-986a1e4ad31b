// 主题颜色
$primary-color: #00ffaa;
$primary-bg: #001041;
$secondary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$danger-color: #ff4d4f;
$primary-color-light: rgba($primary-color, 0.3);

// 文字颜色
$text-primary: #ffffff;
$text-secondary: rgba(255, 255, 255, 0.8);
$text-hint: rgba(255, 255, 255, 0.5);
$text-light: #ffffff;
$text-tertiary: rgba(255, 255, 255, 0.65);

// 背景颜色
$bg-dark: #000820;
$bg-card: rgba(0, 21, 65, 0.7);
$bg-header: linear-gradient(90deg, rgba(10, 22, 70, 0.9), rgba(0, 33, 91, 0.7));
$background-dark: #001528;
$background-header: linear-gradient(90deg, rgba(10, 22, 70, 0.95), rgba(0, 40, 100, 0.85));
$background-panel: rgba(0, 25, 75, 0.8);

// 边框颜色
$border-color: rgba(0, 255, 170, 0.2);
$border-light: rgba(255, 255, 255, 0.1);

// 阴影
$box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
$glow-shadow: 0 0 10px rgba(0, 255, 170, 0.5);
$shadow-color: rgba(0, 10, 30, 0.6);

// 字体大小
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 24px;

// 圆角
$border-radius-sm: 4px;
$border-radius-base: 8px;
$border-radius-md: 6px;
$border-radius-lg: 10px;
$border-radius-xl: 20px;
$border-radius: 12px;

// 间距
$spacing-xs: 5px;
$spacing-sm: 10px;
$spacing-md: 15px;
$spacing-lg: 20px;
$spacing-xl: 30px;

// 尺寸变量
$header-height: 70px;
$panel-width: 320px;
$card-padding: 15px;

// 动画
@mixin transition-base {
  transition: all 0.3s ease;
}
$transition-normal: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
$transition-fast: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);

// 弹性布局 - 两端对齐
@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 模糊效果
@mixin backdrop-blur {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

// 卡片样式
@mixin card-style {
  background: $bg-card;
  border: 1px solid $border-color;
  border-radius: $border-radius-lg;
  overflow: hidden;
  @include backdrop-blur;
  box-shadow: $box-shadow;
}

// 渐变背景
@mixin gradient-bg {
  background: linear-gradient(135deg, rgba(0, 32, 82, 0.8), rgba(0, 48, 130, 0.8));
}

// 响应式断点
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1440px;
$breakpoint-xl: 1920px;

// 状态颜色
$status-online: #2bff96;
$status-standby: #ffcc00;
$status-offline: #ff3b5c; 