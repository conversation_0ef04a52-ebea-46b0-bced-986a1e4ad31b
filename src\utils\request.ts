import axios from 'axios'
import type { AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'
import envConfig from '@/config/env'

// 定义接口响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 获取API基础URL
const API_BASE_URL = envConfig.apiBaseUrl

// 创建axios实例
const service = axios.create({
  baseURL: API_BASE_URL, // 从环境配置获取API基础URL
  timeout: envConfig.apiTimeout // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      // 添加token到请求头
      config.headers['Authorization'] = `Bearer ${token}`
    }
    console.log('请求URL:', `${API_BASE_URL}${config.url}`)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data as ApiResponse

    // 如果返回的状态码不是200，说明接口请求有问题
    if (res.code !== 200) {
      ElMessage({
        message: res.message || '系统错误',
        type: 'error',
        duration: 5 * 1000
      })

      // 401: 未授权（token失效）
      if (res.code === 401) {
        // 清除本地token和用户信息
        localStorage.removeItem('token')
        localStorage.removeItem('user')

        // 跳转到登录页
        router.push('/login')
      }

      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res as any
    }
  },
  error => {
    console.error('响应错误:', error)

    // 处理网络错误或服务器错误
    if (error.response) {
      const status = error.response.status

      // 401: 未授权（token失效）
      if (status === 401) {
        ElMessage({
          message: '登录已过期，请重新登录',
          type: 'error',
          duration: 5 * 1000
        })

        // 清除本地token和用户信息
        localStorage.removeItem('token')
        localStorage.removeItem('user')

        // 跳转到登录页
        router.push('/login')
      } else {
        ElMessage({
          message: error.response.data?.message || '系统错误',
          type: 'error',
          duration: 5 * 1000
        })
      }
    } else {
      ElMessage({
        message: '网络连接异常，请检查您的网络',
        type: 'error',
        duration: 5 * 1000
      })
    }

    return Promise.reject(error)
  }
)

// 扩展axios，添加类型化的请求方法
const request = {
  get<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    return service.get(url, { params })
  },
  post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return service.post(url, data)
  },
  put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return service.put(url, data)
  },
  delete<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    return service.delete(url, { params })
  }
}

export default request
