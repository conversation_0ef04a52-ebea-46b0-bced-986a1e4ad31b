<!-- 
  PageHeader.vue
  页面标题组件
  用于在监控中心各页面顶部显示统一的标题、描述和图标
-->
<template>
  <div class="page-header">
    <div class="header-content">
      <div class="header-icon">
        <el-icon v-if="icon"><component :is="icon" /></el-icon>
      </div>
      <div class="header-text">
        <h1 class="header-title">{{ title }}</h1>
        <p class="header-description">{{ description }}</p>
      </div>
    </div>
    <div class="header-actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  color: #3b82f6;
  font-size: 24px;
}

.header-text {
  display: flex;
  flex-direction: column;
}

.header-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.header-description {
  margin: 5px 0 0 0;
  font-size: 14px;
  color: #9ca3af;
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style> 