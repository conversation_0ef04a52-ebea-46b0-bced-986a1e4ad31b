/**
 * 预警数据的可复用逻辑
 */
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { alertService } from '../services/alertService';
import type { AlertInfo } from '../types';

export function useAlertData() {
  // 预警信息列表
  const alerts = ref<AlertInfo[]>([]);
  
  // 定时器ID
  let alertsTimer: number | null = null;
  
  // 模拟预警数据
  const mockAlerts: AlertInfo[] = [
    {
      id: '1',
      level: 'high',
      title: '土壤湿度过低',
      message: '区域A的土壤湿度低于阈值，可能需要灌溉。',
      deviceId: 'device-001',
      timestamp: Date.now() - 1000 * 60 * 5, // 5分钟前
      resolved: false
    },
    {
      id: '2',
      level: 'medium',
      title: '设备电量不足',
      message: '设备ID: device-003 电量低于 20%，请及时充电。',
      deviceId: 'device-003',
      timestamp: Date.now() - 1000 * 60 * 30, // 30分钟前
      resolved: false
    },
    {
      id: '3',
      level: 'low',
      title: '数据传输延迟',
      message: '部分设备数据传输延迟超过预期，请检查网络连接。',
      deviceId: 'device-007',
      timestamp: Date.now() - 1000 * 60 * 60, // 1小时前
      resolved: false
    }
  ];
  
  // 初始化预警数据
  const initAlertData = () => {
    // 获取预警信息列表
    alerts.value = [...mockAlerts];
    
    // 开始预警模拟
    startAlertSimulation();
  };
  
  // 更新预警数据
  const updateAlertData = () => {
    // 更新预警信息
    alertService.updateAlerts();
    alerts.value = alertService.getAlerts();
  };
  
  // 开始预警模拟
  const startAlertSimulation = () => {
    // 设置预警信息更新定时器
    alertsTimer = window.setInterval(() => {
      updateAlertData();
    }, 20000);
    
    // 初始更新一次
    updateAlertData();
  };
  
  // 停止预警模拟
  const stopAlertSimulation = () => {
    if (alertsTimer !== null) {
      window.clearInterval(alertsTimer);
      alertsTimer = null;
    }
  };
  
  // 清除所有预警
  const clearAlerts = () => {
    alertService.clearAlerts();
    alerts.value = [];
  };
  
  // 移除指定预警
  const removeAlert = (id: string) => {
    const index = alerts.value.findIndex(alert => alert.id === id);
    if (index !== -1) {
      alertService.removeAlert(index);
      alerts.value = alertService.getAlerts();
    }
  };
  
  // 组件卸载时清理资源
  onUnmounted(() => {
    stopAlertSimulation();
  });
  
  // 添加随机预警
  const addRandomAlert = () => {
    const levels = ['high', 'medium', 'low'] as const;
    const devices = ['device-001', 'device-002', 'device-003', 'device-004', 'device-007'];
    const titles = [
      '温度异常',
      '湿度异常',
      '光照不足',
      '设备连接中断',
      '数据异常'
    ];
    const messages = [
      '请检查设备状态并及时处理。',
      '建议立即检查相关设备。',
      '可能影响农作物生长，请关注。',
      '需要技术人员现场检查。',
      '请查看详细数据分析报告。'
    ];
    
    const level = levels[Math.floor(Math.random() * levels.length)];
    const deviceId = devices[Math.floor(Math.random() * devices.length)];
    const title = titles[Math.floor(Math.random() * titles.length)];
    const message = title + '，' + messages[Math.floor(Math.random() * messages.length)];
    
    const newAlert: AlertInfo = {
      id: Date.now().toString(),
      level,
      title,
      message,
      deviceId,
      timestamp: Date.now(),
      resolved: false
    };
    
    alerts.value = [newAlert, ...alerts.value];
  };
  
  // 添加预警
  const addAlert = (alert: AlertInfo) => {
    alerts.value = [alert, ...alerts.value];
  };
  
  // 标记预警为已解决
  const markAlertAsResolved = (id: string) => {
    const index = alerts.value.findIndex(alert => alert.id === id);
    if (index !== -1) {
      alerts.value[index].resolved = true;
      // 模拟延迟后移除已解决的预警
      setTimeout(() => {
        removeAlert(id);
      }, 5000);
    }
  };
  
  // 获取未解决的预警数量
  const activeAlertsCount = computed(() => {
    return alerts.value.filter(alert => !alert.resolved).length;
  });
  
  // 按严重程度获取预警
  const alertsByLevel = computed(() => {
    return {
      high: alerts.value.filter(alert => alert.level === 'high' && !alert.resolved),
      medium: alerts.value.filter(alert => alert.level === 'medium' && !alert.resolved),
      low: alerts.value.filter(alert => alert.level === 'low' && !alert.resolved)
    };
  });
  
  return {
    alerts,
    initAlertData,
    startAlertSimulation,
    stopAlertSimulation,
    clearAlerts,
    removeAlert,
    addRandomAlert,
    addAlert,
    markAlertAsResolved,
    activeAlertsCount,
    alertsByLevel
  };
} 