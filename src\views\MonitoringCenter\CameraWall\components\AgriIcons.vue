<template>
  <div class="agri-icon" :class="type">
    <svg v-if="type === 'plant'" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M12,3.9c0,0-4,3.1-4,8.1c0,4.1,2.7,7,4,8c1.3-1,4-3.9,4-8C16,7,12,3.9,12,3.9z"/>
      <path d="M12,13c0,0-1-1-1-2c0-1.7,1-3,1-3s1,1.3,1,3C13,12,12,13,12,13z"/>
      <path d="M18,21H6c-0.6,0-1-0.4-1-1s0.4-1,1-1h12c0.6,0,1,0.4,1,1S18.6,21,18,21z"/>
    </svg>
    
    <svg v-else-if="type === 'greenhouse'" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M3,13h18v8H3V13z"/>
      <path d="M12,3L2,13h20L12,3z"/>
      <path d="M7,13v8 M12,13v8 M17,13v8"/>
    </svg>
    
    <svg v-else-if="type === 'field'" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M3,21V3h18v18H3z"/>
      <path d="M3,9h18 M3,15h18 M9,3v18 M15,3v18"/>
    </svg>
    
    <svg v-else-if="type === 'warehouse'" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M3,21V11L12,5l9,6v10H3z"/>
      <path d="M9,21V15h6v6"/>
    </svg>
    
    <svg v-else-if="type === 'tractor'" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M7,15c-1.7,0-3,1.3-3,3s1.3,3,3,3s3-1.3,3-3S8.7,15,7,15z"/>
      <path d="M17,15c-1.7,0-3,1.3-3,3s1.3,3,3,3s3-1.3,3-3S18.7,15,17,15z"/>
      <path d="M5,15v-3h6l3-6h4v6 M3,9h8"/>
    </svg>
    
    <svg v-else-if="type === 'weather'" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M6,19A5,5,0,1,1,6,9a1,1,0,0,1,0,2A3,3,0,1,0,6,17a1,1,0,0,1,0,2Z"/>
      <path d="M12,19a3,3,0,1,1,0-6h5a3,3,0,0,1,0,6Z"/>
      <path d="M12,5a1,1,0,0,1,1,1v2a1,1,0,0,1-2,0V6A1,1,0,0,1,12,5Z"/>
      <path d="M17.66,7.34a1,1,0,0,1,0,1.42l-1.42,1.41a1,1,0,0,1-1.41-1.41l1.41-1.42A1,1,0,0,1,17.66,7.34Z"/>
      <path d="M6.34,7.34a1,1,0,0,1,1.42,0l1.41,1.42a1,1,0,0,1-1.41,1.41L6.34,8.76A1,1,0,0,1,6.34,7.34Z"/>
    </svg>
    
    <svg v-else viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,17h-2v-6h2V17z M13,9h-2V7h2V9z"/>
    </svg>
  </div>
</template>

<script setup lang="ts">
defineProps({
  type: {
    type: String,
    default: 'default',
    validator: (value: string) => {
      return ['plant', 'greenhouse', 'field', 'warehouse', 'tractor', 'weather', 'default'].includes(value);
    }
  }
});
</script>

<style scoped>
.agri-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.agri-icon svg {
  width: 100%;
  height: 100%;
  fill: currentColor;
  stroke: currentColor;
  stroke-width: 0.5;
}

.plant svg {
  fill: var(--status-normal, #2ecc71);
}

.greenhouse svg {
  fill: var(--primary-green, #1e8449);
}

.field svg {
  fill: var(--wheat-yellow, #f39c12);
}

.warehouse svg {
  fill: var(--soil-brown, #795548);
}

.tractor svg, .weather svg {
  fill: var(--tech-blue, #3498db);
}
</style> 