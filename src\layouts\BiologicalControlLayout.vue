<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  HomeFilled,
  ElementPlus,
  Position,
  Monitor,
  Aim,
  Briefcase,
  CircleCheck,
  Download
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'
import { getModuleTheme } from './components/layoutConfig'

const route = useRoute()
const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 下载生态报告
const downloadReport = () => {
  ElMessage({
    message: '生态报告：正在生成生态系统平衡报告，请稍候...',
    type: 'info',
    duration: 3000
  })

  // 这里可以添加实际的报告下载逻辑
  setTimeout(() => {
    ElMessage({
      message: '报告生成完成：生态系统平衡报告已生成，可在下载中心查看',
      type: 'success',
      duration: 3000
    })
  }, 1500)
}

// 组件挂载
onMounted(() => {
  // 监听BaseLayout事件
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<template>
  <BaseLayout
    title="生物防治拓展"
    theme="biologicalControl"
    themeColor="#10b981"
    moduleIcon="Briefcase"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="天敌昆虫投放管理"
        icon="ElementPlus"
        route="/biological-control/predatory-insects"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="信息素防控记录"
        icon="Position"
        route="/biological-control/pheromone-control"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="生物农药使用追踪"
        icon="Monitor"
        route="/biological-control/biopesticide-tracking"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="生态平衡指数评估"
        icon="Aim"
        route="/biological-control/ecological-balance"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
    </template>

    <template #header-actions>
      <div class="eco-status">
        <div class="status-item positive">
          <el-icon><CircleCheck /></el-icon>
          <span>生态系统健康度: </span>
          <span class="status-value">92%</span>
        </div>
      </div>

      <TechActionButton
        type="success"
        size="small"
        icon="Download"
        text="生态报告"
        @click="downloadReport"
      />
    </template>

    <router-view />
  </BaseLayout>
</template>

<style scoped>
.eco-status {
  margin-right: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(16, 185, 129, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  color: #d1d5db;
  font-size: 14px;
  transition: all 0.3s ease;
}

.status-item:hover {
  background: rgba(16, 185, 129, 0.2);
  transform: translateY(-2px);
}

.status-item.positive {
  border-color: rgba(16, 185, 129, 0.3);
}

.status-value {
  color: #10b981;
  font-weight: 700;
}

@media (max-width: 768px) {
  .eco-status {
    display: none;
  }
}
</style>
