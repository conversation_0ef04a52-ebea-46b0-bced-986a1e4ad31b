<template>
  <div 
    class="base-layout" 
    :class="[`theme-${theme}`, { 'fullscreen-mode': isFullscreen }]"
    ref="layoutRef"
  >
    <div class="tech-bg-overlay"></div>
    
    <!-- 背景视觉元素 -->
    <div class="tech-decorations">
      <div class="tech-grid"></div>
      <div class="tech-particles"></div>
      <div class="corner-decoration top-left"></div>
      <div class="corner-decoration top-right"></div>
      <div class="corner-decoration bottom-left"></div>
      <div class="corner-decoration bottom-right"></div>
    </div>
    
    <el-container class="container">
      <el-aside width="240px" class="aside" :class="{ 'aside-collapsed': isAsideCollapsed }">
        <!-- 侧边栏标题区域 -->
        <div class="logo-container">
          <div class="title-area">
            <div class="icon-wrapper" v-if="moduleIcon">
              <el-icon class="module-icon"><component :is="moduleIcon" /></el-icon>
            </div>
            <h2 class="system-title" :style="{ color: themeColor }">{{ title }}</h2>
          </div>
          <el-button
            class="home-button"
            type="primary"
            size="small"
            plain
            @click="goToHome"
          >
            <el-icon>
              <HomeFilled />
            </el-icon>
            <span v-if="!isAsideCollapsed">返回首页</span>
          </el-button>
        </div>
        
        <!-- 菜单区域 -->
        <div class="menu-container">
          <slot name="menu"></slot>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="action-buttons" v-if="$slots.actions">
          <slot name="actions"></slot>
        </div>
        
        <!-- 侧边栏收起/展开按钮 -->
        <div class="collapse-btn" @click="toggleAside">
          <el-icon>
            <ArrowLeft v-if="!isAsideCollapsed" />
            <ArrowRight v-else />
          </el-icon>
        </div>
      </el-aside>
      
      <el-container class="content-container">
        <!-- 主内容区域 -->
        <el-main class="main-content">
          <slot></slot>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, useSlots, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { HomeFilled, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { useMotion } from '@vueuse/motion'
import { gsap } from 'gsap'

// 组件属性定义
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  theme: {
    type: String,
    default: 'default'
  },
  themeColor: {
    type: String,
    default: '#3b82f6'
  },
  moduleIcon: {
    type: String,
    default: ''
  }
})

// 定义事件
const emit = defineEmits(['fullscreen-change', 'collapse-change'])

// 响应式状态
const router = useRouter()
const isFullscreen = ref(false)
const isAsideCollapsed = ref(false)
const layoutRef = ref(null)
const slots = useSlots()

// 事件系统，模拟Vue 2的$on, $off, $emit
const eventBus = reactive({
  events: new Map(),
  $on(event: string, callback: Function) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(callback);
  },
  $off(event: string, callback?: Function) {
    if (!this.events.has(event)) return;
    
    if (!callback) {
      this.events.delete(event);
      return;
    }
    
    const callbacks = this.events.get(event)!;
    const index = callbacks.indexOf(callback);
    if (index > -1) {
      callbacks.splice(index, 1);
    }
    
    if (callbacks.length === 0) {
      this.events.delete(event);
    }
  },
  $emit(event: string, ...args: any[]) {
    if (!this.events.has(event)) return;
    
    const callbacks = this.events.get(event)!;
    callbacks.forEach(callback => {
      callback(...args);
    });
  }
});

// 切换全屏模式
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      console.error(`错误：${err.message}`)
    })
    isFullscreen.value = true
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }
  emit('fullscreen-change', isFullscreen.value)
  // 使用自定义事件系统触发事件
  eventBus.$emit('fullscreen-change', isFullscreen.value)
}

// 切换侧边栏收起/展开状态
const toggleAside = () => {
  isAsideCollapsed.value = !isAsideCollapsed.value
  emit('collapse-change', isAsideCollapsed.value)
  // 使用自定义事件系统触发事件
  eventBus.$emit('collapse-change', isAsideCollapsed.value)
  
  // 添加动画效果
  const aside = document.querySelector('.aside')
  if (aside) {
    gsap.to(aside, {
      width: isAsideCollapsed.value ? '60px' : '240px',
      duration: 0.3,
      ease: 'power2.out'
    })
  }
}

// 为组件添加$on等方法
defineExpose({
  toggleFullscreen,
  $on: eventBus.$on.bind(eventBus),
  $off: eventBus.$off.bind(eventBus),
  $emit: eventBus.$emit.bind(eventBus)
});

// 监听全屏变化事件
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
  emit('fullscreen-change', isFullscreen.value)
}

// 返回首页
const goToHome = () => {
  router.push('/home')
}

// 组件挂载与卸载生命周期钩子
onMounted(() => {
  // 添加全屏事件监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  
  // 初始化粒子效果
  if (window.particlesJS && layoutRef.value) {
    try {
      window.particlesJS('tech-particles', {
        particles: {
          number: { value: 80, density: { enable: true, value_area: 800 } },
          color: { value: props.themeColor },
          opacity: { value: 0.2, random: true },
          size: { value: 3, random: true },
          line_linked: {
            enable: true,
            distance: 150,
            color: props.themeColor,
            opacity: 0.2,
            width: 1
          },
          move: { 
            enable: true, 
            speed: 1,
            random: true, 
            direction: "none" 
          }
        }
      });
    } catch (e) {
      console.error('粒子效果初始化失败', e)
    }
  }
  
  // 应用运动效果
  if (layoutRef.value) {
    useMotion(layoutRef, {
      initial: { opacity: 0, y: 20 },
      enter: { 
        opacity: 1, 
        y: 0,
        transition: { duration: 600 }
      }
    })
  }
})

onUnmounted(() => {
  // 移除全屏事件监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
/* 基础布局样式 */
.base-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  color: #d1d5db;
  --primary-dark: #1f2937;
  --secondary-dark: #374151;
  --border-dark: #4b5563;
}

/* 主题样式 */
.base-layout.theme-default {
  --theme-color: v-bind('props.themeColor');
  --theme-text-color: #d1d5db;
  --theme-primary: #1f2937;
  --theme-secondary: #374151;
  --theme-border: #4b5563;
}

/* 科技风背景和装饰 */
.tech-bg-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(31, 41, 55, 0.9) 0%, rgba(17, 24, 39, 0.95) 50%, rgba(10, 15, 25, 1) 100%);
  z-index: -10;
  pointer-events: none;
}

.tech-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -5;
  pointer-events: none;
}

.tech-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(to right, rgba(75, 85, 99, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(75, 85, 99, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.corner-decoration {
  position: absolute;
  width: 150px;
  height: 150px;
  pointer-events: none;
}

.corner-decoration.top-left {
  top: 0;
  left: 0;
  border-top: 2px solid rgba(59, 130, 246, 0.5);
  border-left: 2px solid rgba(59, 130, 246, 0.5);
  border-top-left-radius: 8px;
}

.corner-decoration.top-right {
  top: 0;
  right: 0;
  border-top: 2px solid rgba(59, 130, 246, 0.5);
  border-right: 2px solid rgba(59, 130, 246, 0.5);
  border-top-right-radius: 8px;
}

.corner-decoration.bottom-left {
  bottom: 0;
  left: 0;
  border-bottom: 2px solid rgba(59, 130, 246, 0.5);
  border-left: 2px solid rgba(59, 130, 246, 0.5);
  border-bottom-left-radius: 8px;
}

.corner-decoration.bottom-right {
  bottom: 0;
  right: 0;
  border-bottom: 2px solid rgba(59, 130, 246, 0.5);
  border-right: 2px solid rgba(59, 130, 246, 0.5);
  border-bottom-right-radius: 8px;
}

/* 容器样式 */
.container {
  height: 100%;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* 侧边栏样式 */
.aside {
  background-color: rgba(31, 41, 55, 0.7);
  border-right: 1px solid var(--theme-border);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  backdrop-filter: blur(10px);
  transition: width 0.3s ease;
  overflow: hidden;
}

.aside-collapsed {
  width: 60px !important;
}

.logo-container {
  padding: 20px 16px;
  border-bottom: 1px solid var(--theme-border);
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: rgba(17, 24, 39, 0.4);
}

.title-area {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.2);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.module-icon {
  font-size: 18px;
  color: var(--theme-color);
}

.system-title {
  margin: 0;
  font-size: 18px;
  text-align: center;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  letter-spacing: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.home-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin-top: 8px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.home-button:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.menu-container {
  padding: 20px 0;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-buttons {
  padding: 20px 16px;
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-top: 1px solid var(--theme-border);
  background: rgba(17, 24, 39, 0.4);
}

/* 收起/展开按钮 */
.collapse-btn {
  position: absolute;
  top: 50%;
  right: -12px;
  width: 24px;
  height: 40px;
  background-color: var(--theme-color);
  border-radius: 0 6px 6px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  z-index: 100;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.collapse-btn:hover {
  background-color: rgba(59, 130, 246, 0.8);
  transform: scale(1.1);
}

/* 内容区域容器 */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 主内容区域 */
.main-content {
  background-color: rgba(31, 41, 55, 0.4);
  height: 100%;
  overflow-y: auto;
  flex: 1;
  position: relative;
}

/* 全屏模式样式 */
.fullscreen-mode {
  background-color: #000;
}

.fullscreen-mode .tech-bg-overlay {
  background: radial-gradient(circle at 50% 50%, rgba(17, 24, 39, 0.9) 0%, rgba(10, 15, 25, 0.95) 80%, rgba(0, 0, 0, 1) 100%);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .system-title {
    font-size: 16px;
  }
  
  .aside {
    width: 200px !important;
  }
  
  .aside-collapsed {
    width: 60px !important;
  }
}

@media (max-width: 480px) {
  .aside {
    width: 60px !important;
  }
  
  .system-title {
    display: none;
  }
  
  .home-button span {
    display: none;
  }
}
</style> 