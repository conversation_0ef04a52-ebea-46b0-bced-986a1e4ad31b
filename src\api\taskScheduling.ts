import request from '@/utils/request'
import type { PathPlanningParams, PathPlanningResult } from '@/types/taskScheduling'
import type { PeriodicTask, EmergencyTask, TaskProgress, DeviceInfo, TaskExecutionRecord } from '@/types/taskScheduling'

/**
 * 智能路径规划相关API
 */
export const pathPlanningApi = {
  // 获取路径规划结果
  getPathPlanning: (params: PathPlanningParams) => {
    return request.post<PathPlanningResult>('/task-scheduling/path-planning', params)
  },

  // 获取农田障碍物数据
  getObstacleData: (fieldId: string) => {
    return request.get('/task-scheduling/obstacles', { params: { fieldId } })
  },

  // 保存路径规划结果
  savePlannedPath: (pathData: PathPlanningResult) => {
    return request.post('/task-scheduling/save-path', pathData)
  }
}

/**
 * 任务调度相关API接口
 */
// const api = request.create({
//   baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
//   timeout: 10000
// })

// 直接使用request对象
const api = request

// 查询参数类型
interface QueryParams {
  page?: number;
  pageSize?: number;
  status?: string;
  type?: string;
  deviceId?: string;
  startDate?: string;
  endDate?: string;
  keyword?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应类型
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 周期性巡航任务相关API
 */
export const periodicTaskApi = {
  // 获取任务列表
  getTaskList(params: QueryParams = {}): Promise<PaginatedResponse<PeriodicTask>> {
    return api.get('/tasks/periodic', { params }).then(response => response.data);
  },
  
  // 获取单个任务详情
  getTaskById(id: string): Promise<PeriodicTask> {
    return api.get(`/tasks/periodic/${id}`).then(response => response.data);
  },
  
  // 创建新任务
  createTask(task: Omit<PeriodicTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<PeriodicTask> {
    return api.post('/tasks/periodic', task).then(response => response.data);
  },
  
  // 更新任务
  updateTask(id: string, task: Partial<PeriodicTask>): Promise<PeriodicTask> {
    return api.put(`/tasks/periodic/${id}`, task).then(response => response.data);
  },
  
  // 删除任务
  deleteTask(id: string): Promise<void> {
    return api.delete(`/tasks/periodic/${id}`).then(response => response.data);
  },
  
  // 批量删除任务
  batchDeleteTasks(ids: string[]): Promise<void> {
    return api.delete('/tasks/periodic/batch', { data: { ids } }).then(response => response.data);
  },
  
  // 切换任务启用状态
  toggleTaskStatus(id: string, enabled: boolean): Promise<PeriodicTask> {
    return api.patch(`/tasks/periodic/${id}/status`, { enabled }).then(response => response.data);
  },
  
  // 批量切换任务启用状态
  batchToggleTaskStatus(ids: string[], enabled: boolean): Promise<void> {
    return api.patch('/tasks/periodic/batch/status', { ids, enabled }).then(response => response.data);
  },
  
  // 获取任务执行记录
  getTaskExecutionRecords(taskId: string, params: QueryParams = {}): Promise<PaginatedResponse<TaskExecutionRecord>> {
    return api.get(`/tasks/periodic/${taskId}/executions`, { params }).then(response => response.data);
  }
}

/**
 * 应急消杀任务相关API
 */
export const emergencyTaskApi = {
  // 创建应急任务
  createEmergencyTask: (task: EmergencyTask) => {
    return api.post('/task-scheduling/emergency-tasks', task)
  },

  // 获取应急任务列表
  getEmergencyTasks: () => {
    return api.get('/task-scheduling/emergency-tasks')
  },

  // 获取应急任务详情
  getEmergencyTaskDetail: (taskId: string) => {
    return api.get<EmergencyTask>(`/task-scheduling/emergency-tasks/${taskId}`)
  }
}

/**
 * 多机协同工作模式相关API
 */
export const collaborationApi = {
  // 获取设备列表
  getDeviceList: () => {
    return api.get<DeviceInfo[]>('/task-scheduling/devices')
  },

  // 创建协同任务
  createCollaborationTask: (data: any) => {
    return api.post('/task-scheduling/collaboration-tasks', data)
  },

  // 获取协同任务列表
  getCollaborationTasks: () => {
    return api.get('/task-scheduling/collaboration-tasks')
  },

  // 获取设备实时数据
  getDeviceRealTimeData: (deviceId: string) => {
    return api.get(`/task-scheduling/devices/${deviceId}/real-time`)
  },

  // 获取协同事件流
  getCollaborationEvents: (taskId: string) => {
    return api.get(`/task-scheduling/collaboration-tasks/${taskId}/events`)
  }
}

/**
 * 任务执行进度相关API
 */
export const taskProgressApi = {
  // 获取任务进度
  getTaskProgress: (taskId: string) => {
    return api.get<TaskProgress>(`/task-scheduling/tasks/${taskId}/progress`)
  },

  // 获取任务进度历史
  getTaskProgressHistory: (taskId: string, timeRange: { start: string; end: string }) => {
    return api.get(`/task-scheduling/tasks/${taskId}/progress-history`, {
      params: timeRange
    })
  },

  // 获取设备状态
  getDeviceStatus: (taskId: string) => {
    return api.get(`/task-scheduling/tasks/${taskId}/device-status`)
  },

  // 获取任务事件日志
  getTaskEventLogs: (taskId: string, params: { page: number; pageSize: number }) => {
    return api.get(`/task-scheduling/tasks/${taskId}/event-logs`, { params })
  }
}

/**
 * 历史轨迹回放相关API
 */
export const trajectoryApi = {
  // 获取设备历史轨迹
  getDeviceTrajectory: (deviceId: string, timeRange: { start: string; end: string }) => {
    return api.get(`/task-scheduling/devices/${deviceId}/trajectory`, {
      params: timeRange
    })
  },

  // 获取轨迹关键点信息
  getTrajectoryKeyPoints: (trajectoryId: string) => {
    return api.get(`/task-scheduling/trajectory/${trajectoryId}/key-points`)
  },

  // 获取设备历史任务列表
  getDeviceHistoryTasks: (deviceId: string) => {
    return api.get(`/task-scheduling/devices/${deviceId}/history-tasks`)
  }
}

/**
 * 设备相关API
 */
export const deviceApi = {
  // 获取可用设备列表
  getAvailableDevices(): Promise<{ id: string; name: string; type: string }[]> {
    return api.get('/devices/available').then(response => response.data);
  }
}

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加认证信息等
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    // 统一处理错误
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

export default api 