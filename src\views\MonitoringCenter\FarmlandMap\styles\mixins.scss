/**
 * 农田地图组件混合器
 */
@use 'variables.scss' as vars;

// 卡片混合器
@mixin card {
  background-color: rgba(0, 21, 65, 0.8);
  border: 1px solid vars.$panel-border-color;
  border-radius: vars.$border-radius;
  box-shadow: vars.$shadow-medium;
  padding: 15px;
  
  @include vars.glass-effect;
}

// 带标题的卡片
@mixin card-with-header {
  @include card;
  padding: 0;
  
  .card-header {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    @include vars.flex-between;
  }
  
  .card-body {
    padding: 15px;
  }
  
  .card-footer {
    padding: 12px 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.08);
  }
}

// 状态标签
@mixin status-label($color) {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  background-color: rgba($color, 0.15);
  color: $color;
}

// 按钮动效
@mixin button-hover-effect {
  transition: vars.$transition-normal;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: vars.$shadow-small;
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 响应式网格
@mixin responsive-grid($columns-desktop: 3, $columns-tablet: 2, $columns-mobile: 1, $gap: 15px) {
  display: grid;
  grid-template-columns: repeat($columns-desktop, 1fr);
  gap: $gap;
  
  @media (max-width: vars.$breakpoint-md) {
    grid-template-columns: repeat($columns-tablet, 1fr);
  }
  
  @media (max-width: vars.$breakpoint-xs) {
    grid-template-columns: repeat($columns-mobile, 1fr);
  }
}

// 截断文本
@mixin truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 多行文本截断
@mixin multi-line-truncate($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
} 