<!-- 
  DeviceStatusBoard.vue
  设备状态看板模块
  实时监控机器狗、无人机等农业智能设备的电量、信号强度及工作模式
-->
<template>
  <div class="device-status-board">
    <!-- 页面标题 -->
    <PageHeader
      title="智慧农场设备状态看板"
      description="实时监控机器狗、无人机等农业智能设备的电量、信号强度及工作模式"
      icon="Monitor"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ getOnlineDevicesCount() }}</span>
            <span class="summary-label">在线设备</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getWorkingDevicesCount() }}</span>
            <span class="summary-label">作业中</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 设备状态卡片网格 -->
    <div class="status-cards">
      <!-- 机器狗状态卡片 -->
      <DeviceCard
        deviceName="机器狗 Alpha-X"
        :deviceId="robotDogData.deviceId"
        deviceFunction="农田巡检与监测"
        type="robot-dog"
        :batteryLevel="robotDogData.batteryLevel"
        :signalStrength="robotDogData.signalStrength"
        :workMode="robotDogData.workMode"
      >
        <template #device-icon>
          <div class="robot-dog-icon" :class="robotDogData.workMode.toLowerCase()"></div>
        </template>
        <template #footer>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="openDeviceDetail('robot-dog')">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            <el-button size="small" @click="sendCommand('robot-dog', 'return')">
              <el-icon><HomeFilled /></el-icon>
              召回
            </el-button>
          </div>
        </template>
      </DeviceCard>
      
      <!-- 无人机状态卡片 -->
      <DeviceCard
        deviceName="无人机 Sky-7"
        :deviceId="droneData.deviceId"
        deviceFunction="农田测绘与喷洒"
        type="drone"
        :batteryLevel="droneData.batteryLevel"
        :signalStrength="droneData.signalStrength"
        :workMode="droneData.workMode"
      >
        <template #device-icon>
          <div class="drone-icon" :class="droneData.workMode.toLowerCase()"></div>
        </template>
        <template #footer>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="openDeviceDetail('drone')">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            <el-button size="small" @click="sendCommand('drone', 'return')">
              <el-icon><HomeFilled /></el-icon>
              召回
            </el-button>
          </div>
        </template>
      </DeviceCard>
      
      <!-- 捕虫灯状态卡片 -->
      <DeviceCard
        deviceName="捕虫灯 LT-200"
        :deviceId="insectTrapData.deviceId"
        deviceFunction="害虫监测与诱捕"
        type="insect-trap"
        :batteryLevel="insectTrapData.batteryLevel"
        :signalStrength="insectTrapData.signalStrength"
        :workMode="insectTrapData.workMode"
      >
        <template #device-icon>
          <div class="insect-trap-icon" :class="insectTrapData.workMode.toLowerCase()"></div>
        </template>
        <template #footer>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="openDeviceDetail('insect-trap')">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            <el-button size="small" @click="toggleDeviceMode('insect-trap')">
              <el-icon><Switch /></el-icon>
              切换模式
            </el-button>
          </div>
        </template>
      </DeviceCard>
      
      <!-- 超声波设备状态卡片 -->
      <DeviceCard
        deviceName="超声波装置 US-50"
        :deviceId="ultrasonicData.deviceId"
        deviceFunction="驱鸟驱兽保护"
        type="ultrasonic"
        :batteryLevel="ultrasonicData.batteryLevel"
        :signalStrength="ultrasonicData.signalStrength"
        :workMode="ultrasonicData.workMode"
      >
        <template #device-icon>
          <div class="ultrasonic-icon" :class="ultrasonicData.workMode.toLowerCase()"></div>
        </template>
        <template #footer>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="openDeviceDetail('ultrasonic')">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            <el-button size="small" @click="adjustIntensity('ultrasonic')">
              <el-icon><Setting /></el-icon>
              调节强度
            </el-button>
          </div>
        </template>
      </DeviceCard>
    </div>
    
    <!-- 设备状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="设备健康" />
        <StatusIndicator type="warning" label="需要维护" />
        <StatusIndicator type="error" label="故障设备" />
        <StatusIndicator type="offline" label="离线设备" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  View, 
  HomeFilled, 
  Switch, 
  Setting, 
  Refresh, 
  Monitor 
} from '@element-plus/icons-vue';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import DeviceCard from './components/DeviceCard.vue';
import StatusIndicator from './components/StatusIndicator.vue';

// 模拟设备数据
const robotDogData = ref({
  deviceId: 'RD-2023-001',
  batteryLevel: 78,
  signalStrength: 92,
  workMode: 'Patrol',
  status: 'normal'
});

const droneData = ref({
  deviceId: 'DR-2023-005',
  batteryLevel: 45,
  signalStrength: 87,
  workMode: 'Standby',
  status: 'warning'
});

const insectTrapData = ref({
  deviceId: 'IT-2023-012',
  batteryLevel: 92,
  signalStrength: 95,
  workMode: 'Active',
  status: 'normal'
});

const ultrasonicData = ref({
  deviceId: 'US-2023-008',
  batteryLevel: 65,
  signalStrength: 78,
  workMode: 'Active',
  status: 'normal'
});

// 最后更新时间
const lastUpdateTime = ref(new Date());

// 获取在线设备数量
const getOnlineDevicesCount = () => {
  return 4; // 假设所有设备都在线
};

// 获取工作中设备数量
const getWorkingDevicesCount = () => {
  let count = 0;
  if (robotDogData.value.workMode === 'Patrol') count++;
  if (droneData.value.workMode === 'Active') count++;
  if (insectTrapData.value.workMode === 'Active') count++;
  if (ultrasonicData.value.workMode === 'Active') count++;
  return count;
};

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 打开设备详情
const openDeviceDetail = (deviceType: string) => {
  ElMessage.info(`查看${deviceType}详情`);
};

// 发送命令
const sendCommand = (deviceType: string, command: string) => {
  ElMessage.success(`向${deviceType}发送${command}命令`);
};

// 切换设备模式
const toggleDeviceMode = (deviceType: string) => {
  if (deviceType === 'insect-trap') {
    insectTrapData.value.workMode = insectTrapData.value.workMode === 'Active' ? 'Standby' : 'Active';
    ElMessage.success(`已切换捕虫灯模式为: ${insectTrapData.value.workMode}`);
  }
};

// 调节强度
const adjustIntensity = (deviceType: string) => {
  ElMessage.info(`调节${deviceType}强度`);
};

// 刷新数据
const refreshData = () => {
  // 模拟数据更新
  robotDogData.value.batteryLevel = Math.floor(Math.random() * 40) + 60;
  robotDogData.value.signalStrength = Math.floor(Math.random() * 20) + 80;
  
  droneData.value.batteryLevel = Math.floor(Math.random() * 40) + 30;
  droneData.value.signalStrength = Math.floor(Math.random() * 30) + 70;
  
  insectTrapData.value.batteryLevel = Math.floor(Math.random() * 20) + 80;
  insectTrapData.value.signalStrength = Math.floor(Math.random() * 10) + 90;
  
  ultrasonicData.value.batteryLevel = Math.floor(Math.random() * 30) + 60;
  ultrasonicData.value.signalStrength = Math.floor(Math.random() * 20) + 70;
  
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

// 模拟数据自动更新
let dataUpdateInterval: number | null = null;

onMounted(() => {
  dataUpdateInterval = window.setInterval(() => {
    // 小幅度随机波动数据
    robotDogData.value.batteryLevel = Math.max(0, Math.min(100, robotDogData.value.batteryLevel + (Math.random() * 6 - 3)));
    robotDogData.value.signalStrength = Math.max(0, Math.min(100, robotDogData.value.signalStrength + (Math.random() * 6 - 3)));
    
    droneData.value.batteryLevel = Math.max(0, Math.min(100, droneData.value.batteryLevel + (Math.random() * 6 - 3)));
    droneData.value.signalStrength = Math.max(0, Math.min(100, droneData.value.signalStrength + (Math.random() * 6 - 3)));
    
    insectTrapData.value.batteryLevel = Math.max(0, Math.min(100, insectTrapData.value.batteryLevel + (Math.random() * 4 - 2)));
    insectTrapData.value.signalStrength = Math.max(0, Math.min(100, insectTrapData.value.signalStrength + (Math.random() * 4 - 2)));
    
    ultrasonicData.value.batteryLevel = Math.max(0, Math.min(100, ultrasonicData.value.batteryLevel + (Math.random() * 4 - 2)));
    ultrasonicData.value.signalStrength = Math.max(0, Math.min(100, ultrasonicData.value.signalStrength + (Math.random() * 4 - 2)));
    
    lastUpdateTime.value = new Date();
  }, 30000); // 每30秒更新一次
});

onUnmounted(() => {
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
});
</script>

<style scoped>
.device-status-board {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态卡片网格 */
.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 卡片操作按钮 */
.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 设备图标样式 */
.robot-dog-icon,
.drone-icon,
.insect-trap-icon,
.ultrasonic-icon {
  width: 60px;
  height: 60px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.robot-dog-icon {
  background-image: url('@/assets/icons/robot-dog.svg');
}

.drone-icon {
  background-image: url('@/assets/icons/drone.svg');
}

.insect-trap-icon {
  background-image: url('@/assets/icons/insect-trap.svg');
}

.ultrasonic-icon {
  background-image: url('@/assets/icons/ultrasonic.svg');
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-cards {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>

<!--
注意: 此组件需要以下SVG图标文件:
- @/assets/icons/robot-dog.svg
- @/assets/icons/drone.svg
- @/assets/icons/insect-trap.svg
- @/assets/icons/ultrasonic.svg

如果这些文件不存在，请创建相应的SVG图标文件。
--> 