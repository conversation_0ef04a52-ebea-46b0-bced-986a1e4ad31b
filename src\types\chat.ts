/**
 * 聊天相关类型定义
 */

/**
 * 聊天消息
 */
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  type?: 'text' | 'code' | 'image';
  status?: 'sending' | 'sent' | 'error';
  metadata?: Record<string, any>;
}

/**
 * 聊天会话
 */
export interface ChatSession {
  id: string;
  title: string;
  createdAt: number;
  updatedAt: number;
  messageCount: number;
  preview?: string;
  backendSessionId?: string; // 后端会话ID
  conversationId?: string;   // Dify对话ID
} 