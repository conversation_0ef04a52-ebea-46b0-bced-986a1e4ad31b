<template>
  <div class="data-panel" :class="[position + '-panel', { 'show': show, 'collapsed': collapsed }]">
    <!-- 收起/展开按钮 -->
    <div class="collapse-toggle" @click="toggleCollapse">
      <el-icon :class="{ 'rotate-icon': collapsed }">
        <component :is="position === 'left' ? 'ArrowLeft' : 'ArrowRight'" />
      </el-icon>
    </div>
    
    <!-- 面板标题 -->
    <div class="panel-header">
      <h3>{{ title }}</h3>
      <el-button type="text" @click="togglePanel" class="close-btn">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <!-- 面板内容插槽 -->
    <div class="panel-content" v-show="!collapsed">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { Close, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import type { PanelProps } from '../types';

// 定义组件属性
const props = defineProps<{
  title: string;
  position: 'left' | 'right';
  show: boolean;
}>();

// 定义组件事件
const emit = defineEmits<{
  (e: 'toggle'): void;
  (e: 'collapse-change', collapsed: boolean): void;
}>();

// 面板收起状态
const collapsed = ref(false);

// 监控显示状态变化
watch(() => props.show, (newShow) => {
  console.log(`DataPanel [${props.title}]: 显示状态变更为`, newShow);
}, { immediate: true });

// 组件挂载时
onMounted(() => {
  console.log(`DataPanel [${props.title}] 已挂载, 显示状态:`, props.show);
});

// 切换面板显示/隐藏
const togglePanel = () => {
  console.log(`DataPanel [${props.title}]: 切换显示状态`);
  emit('toggle');
};

// 切换面板收起/展开状态
const toggleCollapse = () => {
  collapsed.value = !collapsed.value;
  console.log(`DataPanel [${props.title}]: 切换收起状态为`, collapsed.value);
  emit('collapse-change', collapsed.value);
};
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as vars;
@use '../styles/panels.scss' as panels;
@use '../styles/responsive.scss' as responsive;

.data-panel {
  background-color: rgba(0, 21, 65, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 255, 170, 0.15);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  transition: width 0.3s ease;
  
  // 显示状态
  &.show {
    display: flex;
  }
  
  // 隐藏状态
  &:not(.show) {
    display: none;
  }
  
  // 收起状态样式
  &.collapsed {
    width: 40px !important;
    padding: 0;
    
    .panel-header {
      padding: 15px 0;
      writing-mode: vertical-rl;
      text-orientation: mixed;
      height: auto;
      border-bottom: none;
      justify-content: center;
      
      h3 {
        padding: 0;
        margin: 0 auto;
        
        &::before {
          display: none;
        }
      }
      
      .close-btn {
        display: none;
      }
    }
  }
  
  // 左侧面板特殊样式
  &.left-panel {
    .collapse-toggle {
      right: -15px;
    }
    
    &.collapsed .collapse-toggle {
      right: 10px;
    }
  }
  
  // 右侧面板特殊样式
  &.right-panel {
    .collapse-toggle {
      left: -15px;
    }
    
    &.collapsed .collapse-toggle {
      left: 10px;
    }
  }
              
  .panel-header {
    padding: 15px 15px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 255, 170, 0.15);
    margin-bottom: 5px;
    transition: all 0.3s ease;
    
    h3 {
      color: vars.$text-light;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      position: relative;
      padding-left: 15px;
      transition: all 0.3s ease;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: vars.$primary-color;
        border-radius: 2px;
      }
    }
    
    .close-btn {
      color: vars.$text-secondary;
      
      &:hover {
        color: vars.$text-light;
      }
      
      .el-icon {
        font-size: 20px;
      }
    }
  }
  
  // 收起/展开按钮样式
  .collapse-toggle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background-color: rgba(0, 21, 65, 0.9);
    border: 1px solid rgba(0, 255, 170, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    
    &:hover {
      background-color: rgba(0, 255, 170, 0.2);
    }
    
    .el-icon {
      color: vars.$primary-color;
      font-size: 16px;
      transition: transform 0.3s ease;
      
      &.rotate-icon {
        transform: rotate(180deg);
      }
    }
  }
  
  // 面板内容区域
  .panel-content {
    flex: 1;
    overflow-y: auto;
    transition: opacity 0.3s ease;
    
    // 隐藏滚动条但保留滚动功能
    @include vars.hide-scrollbar;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .data-panel {
    &.collapsed {
      width: 30px !important;
      
      .panel-header {
        padding: 10px 0;
      }
    }
    
    .panel-header {
      padding: 12px;
      
      h3 {
        font-size: 16px;
      }
    }
    
    .collapse-toggle {
      width: 24px;
      height: 24px;
    }
  }
}
</style> 