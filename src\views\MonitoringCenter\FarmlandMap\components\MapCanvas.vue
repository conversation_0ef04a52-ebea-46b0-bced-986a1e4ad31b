<template>
  <div class="map-canvas-wrapper">
    <!-- 使用Three.js渲染农田地图 -->
    <ThreeMapCanvas ref="threeMapCanvas" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useDeviceData } from '../composables/useDeviceData';
import ThreeMapCanvas from './ThreeMapCanvas.vue';

// 获取设备数据
const { 
  devices, 
  initDeviceData, 
  startDeviceSimulation, 
  selectDevice,
  selectedDevice
} = useDeviceData();

// Three.js地图画布引用
const threeMapCanvas = ref<InstanceType<typeof ThreeMapCanvas> | null>(null);

// 监听选中设备变化，聚焦到对应设备
watch(selectedDevice, (newDevice) => {
  if (newDevice && threeMapCanvas.value) {
    threeMapCanvas.value.focusOnDevice(newDevice.id);
  }
});

// 组件挂载时的操作
onMounted(async () => {
  try {
    console.log('MapCanvas组件已挂载，初始化设备数据');
    
    // 初始化设备数据
    initDeviceData();
    console.log('设备数据已初始化，数量:', devices.value.length);
    
    // 开始设备模拟
    startDeviceSimulation();
    console.log('设备模拟已启动');
  } catch (error) {
    console.error('地图初始化失败:', error);
  }
});
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as vars;

.map-canvas-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  border-radius: vars.$border-radius;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}
</style> 