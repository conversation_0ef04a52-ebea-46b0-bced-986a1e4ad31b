/**
 * 性能监控工具
 * 用于监控响应式布局和Canvas渲染性能
 */

// 性能指标接口
export interface PerformanceMetrics {
  renderTime: number;
  resizeTime: number;
  memoryUsage: number;
  fps: number;
  timestamp: number;
}

// 性能监控器类
export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private maxMetrics = 100; // 最多保存100个指标
  private frameCount = 0;
  private lastFrameTime = 0;
  private fpsHistory: number[] = [];

  /**
   * 开始性能测量
   */
  startMeasure(name: string): void {
    performance.mark(`${name}-start`);
  }

  /**
   * 结束性能测量并返回耗时
   */
  endMeasure(name: string): number {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);

    const measure = performance.getEntriesByName(name, 'measure')[0];
    const duration = measure ? measure.duration : 0;

    // 清理性能标记
    performance.clearMarks(`${name}-start`);
    performance.clearMarks(`${name}-end`);
    performance.clearMeasures(name);

    return duration;
  }

  /**
   * 记录渲染性能
   */
  recordRenderTime(duration: number): void {
    this.addMetric({
      renderTime: duration,
      resizeTime: 0,
      memoryUsage: this.getMemoryUsage(),
      fps: this.getCurrentFPS(),
      timestamp: Date.now()
    });
  }

  /**
   * 记录resize性能
   */
  recordResizeTime(duration: number): void {
    this.addMetric({
      renderTime: 0,
      resizeTime: duration,
      memoryUsage: this.getMemoryUsage(),
      fps: this.getCurrentFPS(),
      timestamp: Date.now()
    });
  }

  /**
   * 更新FPS计算
   */
  updateFPS(): void {
    const now = performance.now();

    if (this.lastFrameTime > 0) {
      const delta = now - this.lastFrameTime;
      const fps = 1000 / delta;

      this.fpsHistory.push(fps);
      if (this.fpsHistory.length > 60) { // 保持最近60帧的记录
        this.fpsHistory.shift();
      }
    }

    this.lastFrameTime = now;
    this.frameCount++;
  }

  /**
   * 获取当前FPS
   */
  private getCurrentFPS(): number {
    if (this.fpsHistory.length === 0) return 0;

    const sum = this.fpsHistory.reduce((a, b) => a + b, 0);
    return Math.round(sum / this.fpsHistory.length);
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / 1024 / 1024; // 转换为MB
    }
    return 0;
  }

  /**
   * 添加性能指标
   */
  private addMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);

    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }
  }

  /**
   * 获取性能统计
   */
  getStats(): {
    avgRenderTime: number;
    avgResizeTime: number;
    avgFPS: number;
    avgMemoryUsage: number;
    totalFrames: number;
  } {
    if (this.metrics.length === 0) {
      return {
        avgRenderTime: 0,
        avgResizeTime: 0,
        avgFPS: 0,
        avgMemoryUsage: 0,
        totalFrames: 0
      };
    }

    const renderTimes = this.metrics.filter(m => m.renderTime > 0).map(m => m.renderTime);
    const resizeTimes = this.metrics.filter(m => m.resizeTime > 0).map(m => m.resizeTime);
    const fpsList = this.metrics.map(m => m.fps).filter(fps => fps > 0);
    const memoryList = this.metrics.map(m => m.memoryUsage).filter(mem => mem > 0);

    return {
      avgRenderTime: renderTimes.length > 0 ? renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length : 0,
      avgResizeTime: resizeTimes.length > 0 ? resizeTimes.reduce((a, b) => a + b, 0) / resizeTimes.length : 0,
      avgFPS: fpsList.length > 0 ? fpsList.reduce((a, b) => a + b, 0) / fpsList.length : 0,
      avgMemoryUsage: memoryList.length > 0 ? memoryList.reduce((a, b) => a + b, 0) / memoryList.length : 0,
      totalFrames: this.frameCount
    };
  }

  /**
   * 检查性能是否良好
   */
  isPerformanceGood(): {
    overall: boolean;
    renderTime: boolean;
    resizeTime: boolean;
    fps: boolean;
    memory: boolean;
  } {
    const stats = this.getStats();

    return {
      overall: stats.avgRenderTime < 16 && stats.avgResizeTime < 100 && stats.avgFPS > 30,
      renderTime: stats.avgRenderTime < 16, // 小于16ms保证60fps
      resizeTime: stats.avgResizeTime < 100, // resize操作小于100ms
      fps: stats.avgFPS > 30, // FPS大于30
      memory: stats.avgMemoryUsage < 100 // 内存使用小于100MB
    };
  }

  /**
   * 获取性能报告
   */
  getReport(): string {
    const stats = this.getStats();
    const performance = this.isPerformanceGood();

    return `
性能监控报告:
- 平均渲染时间: ${stats.avgRenderTime.toFixed(2)}ms ${performance.renderTime ? '✅' : '❌'}
- 平均Resize时间: ${stats.avgResizeTime.toFixed(2)}ms ${performance.resizeTime ? '✅' : '❌'}
- 平均FPS: ${stats.avgFPS.toFixed(0)} ${performance.fps ? '✅' : '❌'}
- 平均内存使用: ${stats.avgMemoryUsage.toFixed(2)}MB ${performance.memory ? '✅' : '❌'}
- 总帧数: ${stats.totalFrames}
- 整体性能: ${performance.overall ? '良好 ✅' : '需要优化 ❌'}
    `.trim();
  }

  /**
   * 清理性能数据
   */
  clear(): void {
    this.metrics = [];
    this.frameCount = 0;
    this.lastFrameTime = 0;
    this.fpsHistory = [];
  }

  /**
   * 导出性能数据
   */
  exportData(): PerformanceMetrics[] {
    return [...this.metrics];
  }
}

// 全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor();

// 性能监控装饰器
export function measurePerformance(name: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      performanceMonitor.startMeasure(`${name}-${propertyKey}`);
      const result = originalMethod.apply(this, args);
      const duration = performanceMonitor.endMeasure(`${name}-${propertyKey}`);

      if (name === 'render') {
        performanceMonitor.recordRenderTime(duration);
      } else if (name === 'resize') {
        performanceMonitor.recordResizeTime(duration);
      }

      return result;
    };

    return descriptor;
  };
}

// 开发环境性能监控
export function enableDevPerformanceMonitoring(): void {
  // 每5秒输出性能报告
  setInterval(() => {
    console.log(performanceMonitor.getReport());
  }, 5000);

  // 在控制台暴露性能监控器
  (window as any).performanceMonitor = performanceMonitor;
}
