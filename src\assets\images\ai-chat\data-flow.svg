<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00e676" />
      <stop offset="100%" stop-color="#00c853" />
    </linearGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="2" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- 数据点动画 -->
    <circle id="dataPoint" cx="0" cy="0" r="1.5" fill="#00e676" />
    
    <!-- 数据流动画 -->
    <path id="flowPath1" d="M10,30 Q30,10 50,30 Q70,50 90,30" stroke-dasharray="2,3" />
    <path id="flowPath2" d="M10,50 Q30,70 50,50 Q70,30 90,50" stroke-dasharray="3,4" />
    <path id="flowPath3" d="M10,70 Q30,90 50,70 Q70,50 90,70" stroke-dasharray="4,5" />
  </defs>
  
  <!-- 背景网格 -->
  <rect x="5" y="5" width="90" height="90" fill="none" stroke="url(#dataGradient)" stroke-width="0.5" stroke-dasharray="1,1" />
  
  <!-- 数据流线 -->
  <path d="M10,30 Q30,10 50,30 Q70,50 90,30" fill="none" stroke="url(#dataGradient)" stroke-width="1.5" />
  <path d="M10,50 Q30,70 50,50 Q70,30 90,50" fill="none" stroke="url(#dataGradient)" stroke-width="1.5" />
  <path d="M10,70 Q30,90 50,70 Q70,50 90,70" fill="none" stroke="url(#dataGradient)" stroke-width="1.5" />
  
  <!-- 数据节点 -->
  <circle cx="10" cy="30" r="2" fill="#00e676" filter="url(#glow)" />
  <circle cx="50" cy="30" r="2" fill="#00e676" filter="url(#glow)" />
  <circle cx="90" cy="30" r="2" fill="#00e676" filter="url(#glow)" />
  
  <circle cx="10" cy="50" r="2" fill="#00e676" filter="url(#glow)" />
  <circle cx="50" cy="50" r="3" fill="#00e676" filter="url(#glow)" />
  <circle cx="90" cy="50" r="2" fill="#00e676" filter="url(#glow)" />
  
  <circle cx="10" cy="70" r="2" fill="#00e676" filter="url(#glow)" />
  <circle cx="50" cy="70" r="2" fill="#00e676" filter="url(#glow)" />
  <circle cx="90" cy="70" r="2" fill="#00e676" filter="url(#glow)" />
  
  <!-- 数据连接线 -->
  <line x1="10" y1="30" x2="10" y2="70" stroke="#00e676" stroke-width="0.5" stroke-dasharray="2,2" />
  <line x1="50" y1="30" x2="50" y2="70" stroke="#00e676" stroke-width="0.5" stroke-dasharray="2,2" />
  <line x1="90" y1="30" x2="90" y2="70" stroke="#00e676" stroke-width="0.5" stroke-dasharray="2,2" />
  
  <!-- 中心数据处理单元 -->
  <rect x="45" y="45" width="10" height="10" rx="2" fill="none" stroke="#00e676" stroke-width="1" />
  <circle cx="50" cy="50" r="3" fill="none" stroke="#00e676" stroke-width="0.5" />
  
  <!-- 小数据点 -->
  <circle cx="30" cy="20" r="1" fill="#00e676" />
  <circle cx="70" cy="40" r="1" fill="#00e676" />
  <circle cx="30" cy="80" r="1" fill="#00e676" />
  <circle cx="70" cy="60" r="1" fill="#00e676" />
</svg> 