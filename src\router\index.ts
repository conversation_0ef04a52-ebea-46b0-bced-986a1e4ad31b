import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login',
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/home',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/biological-control',
      name: 'biological-control',
      component: () => import('../layouts/BiologicalControlLayout.vue'),
      redirect: '/biological-control/predatory-insects',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'predatory-insects',
          name: 'predatory-insects',
          component: () => import('../views/BiologicalControl/PredatoryInsectsDeployment.vue'),
          meta: { title: '天敌昆虫投放管理' },
        },
        {
          path: 'pheromone-control',
          name: 'pheromone-control',
          component: () => import('../views/BiologicalControl/PheromoneControlRecord.vue'),
          meta: { title: '信息素防控记录' },
        },
        {
          path: 'biopesticide-tracking',
          name: 'biopesticide-tracking',
          component: () => import('../views/BiologicalControl/BiopesticideTracking.vue'),
          meta: { title: '生物农药使用追踪' },
        },
        {
          path: 'ecological-balance',
          name: 'ecological-balance',
          component: () => import('../views/BiologicalControl/EcologicalBalanceAssessment.vue'),
          meta: { title: '生态平衡指数评估' },
        },
      ],
    },
    {
      path: '/pest-analysis',
      name: 'pest-analysis',
      component: () => import('../layouts/PestAnalysisLayout.vue'),
      redirect: '/pest-analysis/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'pest-dashboard',
          component: () => import('../views/PestAnalysis/PestDashboard.vue'),
          meta: { title: '虫害分析平台' },
        },
        {
          path: 'pest-database',
          name: 'pest-database',
          component: () => import('../views/PestAnalysis/PestDatabase.vue'),
          meta: { title: '多维度虫害数据库' },
        },
        {
          path: 'pest-detail/:id',
          name: 'pest-detail',
          component: () => import('../views/PestAnalysis/PestDetail.vue'),
          meta: { title: '虫害详情' },
        },
        {
          path: 'ai-recognition-log',
          name: 'ai-recognition-log',
          component: () => import('../views/PestAnalysis/AIRecognitionLog.vue'),
          meta: { title: 'AI图像识别日志追溯' },
        },
        {
          path: 'outbreak-prediction',
          name: 'outbreak-prediction',
          component: () => import('../views/PestAnalysis/OutbreakPrediction.vue'),
          meta: { title: '虫害爆发趋势预测' },
        },
        {
          path: 'control-comparison',
          name: 'control-comparison',
          component: () => import('../views/PestAnalysis/ControlComparison.vue'),
          meta: { title: '消杀效果对比分析' },
        },
        {
          path: 'smart-recommendation',
          name: 'smart-recommendation',
          component: () => import('../views/PestAnalysis/SmartRecommendation.vue'),
          meta: { title: '智能用药推荐系统' },
        },
        {
          path: 'report-generation',
          name: 'report-generation',
          component: () => import('../views/PestAnalysis/ReportGeneration.vue'),
          meta: { title: '数据报告自动生成' },
        },
      ],
    },
    {
      path: '/pesticide-management',
      name: 'pesticide-management',
      component: () => import('../layouts/PesticideManagementLayout.vue'),
      redirect: '/pesticide-management/ledger',
      children: [
        {
          path: 'ledger',
          name: 'pesticide-ledger',
          component: () => import('../views/PesticideManagement/PesticideLedger.vue'),
          meta: { title: '农药电子台账' },
        },
        {
          path: 'smart-ratio',
          name: 'smart-ratio',
          component: () => import('../views/PesticideManagement/SmartRatioCalculator.vue'),
          meta: { title: '智能配比计算器' },
        },
        {
          path: 'uav-config',
          name: 'uav-config',
          component: () => import('../views/PesticideManagement/UAVSprayingConfig.vue'),
          meta: { title: '无人机喷洒参数配置' },
        },
        {
          path: 'electronic-fence',
          name: 'electronic-fence',
          component: () => import('../views/PesticideManagement/ElectronicFenceManagement.vue'),
          meta: { title: '施药区域电子围栏' },
        },
        {
          path: 'residue-monitoring',
          name: 'residue-monitoring',
          component: () => import('../views/PesticideManagement/ResidueMonitoring.vue'),
          meta: { title: '农药残留监测接口' },
        },
        {
          path: 'environmental-compliance',
          name: 'environmental-compliance',
          component: () => import('../views/PesticideManagement/EnvironmentalCompliance.vue'),
          meta: { title: '环保合规性检查' },
        },
      ],
    },
    {
      path: '/environment',
      name: 'environment',
      component: () => import('../layouts/EnvironmentLayout.vue'),
      children: [
        {
          path: 'microclimate',
          name: 'microclimate',
          component: () => import('../views/environment/MicroclimateMonitoring.vue'),
        },
        {
          path: 'soil-moisture',
          name: 'soil-moisture',
          component: () => import('../views/environment/SoilMoistureMonitoring.vue'),
        },
        {
          path: 'crop-assessment',
          name: 'crop-assessment',
          component: () => import('../views/environment/CropGrowthAssessment.vue'),
        },
        {
          path: 'pest-analysis',
          name: 'environment-pest-analysis',
          component: () => import('../views/environment/PestAnalysis.vue'),
        },
        {
          path: 'smart-control',
          name: 'smart-control',
          component: () => import('../views/environment/SmartControl.vue'),
        },
        {
          path: '',
          redirect: '/environment/microclimate',
        },
      ],
    },
    {
      path: '/decision-support',
      name: 'decision-support',
      component: () => import('../layouts/DecisionSupportLayout.vue'),
      redirect: '/decision-support/expert-knowledge',
      children: [
        {
          path: 'expert-knowledge',
          name: 'expert-knowledge',
          component: () => import('../views/DecisionSupport/ExpertKnowledge.vue'),
          meta: { title: '专家知识库' },
        },
        {
          path: 'decision-tree',
          name: 'decision-tree',
          component: () => import('../views/DecisionSupport/DecisionTree.vue'),
          meta: { title: '智能决策树推荐' },
        },
        {
          path: 'emergency-plans',
          name: 'emergency-plans',
          component: () => import('../views/DecisionSupport/EmergencyPlans.vue'),
          meta: { title: '应急预案库管理' },
        },
        {
          path: 'cost-benefit',
          name: 'cost-benefit',
          component: () => import('../views/DecisionSupport/CostBenefit.vue'),
          meta: { title: '成本效益分析仪表盘' },
        },
      ],
    },
    {
      path: '/device-management',
      name: 'device-management',
      component: () => import('../layouts/DeviceManagementLayout.vue'),
      redirect: '/device-management/device-status',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'device-status',
          name: 'device-status',
          component: () => import('../views/DeviceManagement/DeviceStatusBoard.vue'),
          meta: { title: '设备状态看板' },
        },

        {
          path: 'drone-medicine',
          name: 'drone-medicine',
          component: () => import('../views/DeviceManagement/DroneMedicineMonitor.vue'),
          meta: { title: '无人机药箱余量监测' },
        },
        {
          path: 'ultrasonic-control',
          name: 'ultrasonic-control',
          component: () => import('../views/DeviceManagement/UltrasonicControl.vue'),
          meta: { title: '超声波强度实时调节面板' },
        },
        {
          path: 'insect-trap-mode',
          name: 'insect-trap-mode',
          component: () => import('../views/DeviceManagement/InsectTrapMode.vue'),
          meta: { title: '捕虫灯工作模式切换' },
        },
        {
          path: 'firmware-upgrade',
          name: 'firmware-upgrade',
          component: () => import('../views/DeviceManagement/FirmwareUpgrade.vue'),
          meta: { title: '固件远程升级系统' },
        },
      ],
    },
    {
      path: '/task-scheduling',
      name: 'task-scheduling',
      component: () => import('../layouts/TaskSchedulingLayout.vue'),
      redirect: '/task-scheduling/path-planning',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'path-planning',
          name: 'path-planning',
          component: () => import('../views/TaskScheduling/PathPlanningEngine.vue'),
          meta: { title: '智能路径规划引擎' },
        },
        {
          path: 'periodic-tasks',
          name: 'periodic-tasks',
          component: () => import('../views/TaskScheduling/PeriodicTaskArrangement.vue'),
          meta: { title: '周期性巡航任务编排' },
        },
        {
          path: 'emergency-tasks',
          name: 'emergency-tasks',
          component: () => import('../views/TaskScheduling/EmergencyTaskQueue.vue'),
          meta: { title: '应急消杀任务插队机制' },
        },
        {
          path: 'multi-device-collaboration',
          name: 'multi-device-collaboration',
          component: () => import('../views/TaskScheduling/MultiDeviceCollaboration.vue'),
          meta: { title: '多机协同工作模式' },
        },
        {
          path: 'task-progress',
          name: 'task-progress',
          component: () => import('../views/TaskScheduling/TaskProgressVisualization.vue'),
          meta: { title: '任务执行进度可视化' },
        },
        {
          path: 'history-trajectory',
          name: 'history-trajectory',
          component: () => import('../views/TaskScheduling/HistoryTrajectoryPlayback.vue'),
          meta: { title: '历史轨迹回放功能' },
        },
      ],
    },
    {
      path: '/monitoring-center',
      name: 'monitoring-center',
      component: () => import('../layouts/MonitoringCenterLayout.vue'),
      redirect: '/monitoring-center/3d-map',
      meta: { requiresAuth: true },
      children: [
        {
          path: '3d-map',
          name: 'monitoring-3d-map',
          component: () => import('../views/MonitoringCenter/FarmlandMap/FarmlandMap.vue'),
          meta: { title: '农田地图' },
        },
        {
          path: 'device-tracking',
          name: 'device-tracking',
          component: () => import('../views/MonitoringCenter/DeviceTracking/index.vue'),
          meta: { title: '智慧农场设备追踪' },
        },
        {
          path: 'camera-wall',
          name: 'CameraWall',
          component: () => import("../views/MonitoringCenter/CameraWall/index.vue"),
          meta: {
            title: '摄像头直播墙',
            icon: 'VideoCamera',
          },
        },

      ],
    },
    {
      path: '/test',
      name: 'test',
      component: () => import('../layouts/TestLayout.vue'),
      redirect: '/test/camera',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'camera',
          name: 'camera',
          component: () => import('../views/Test/Camera.vue'),
          meta: { title: '获取摄像头' },
        },
        {
          path: 'api',
          name: 'api-test',
          component: () => import('../views/Test/ApiTest.vue'),
          meta: { title: 'API测试工具' },
        },
      ],
    },
    {
      path: '/ai-assistant',
      name: 'ai-assistant',
      component: () => import('../views/AIAssistant/index.vue'),
      meta: { requiresAuth: true, title: 'AI智能问答' }
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  // 如果需要认证但没有token
  if (requiresAuth && !token) {
    ElMessage.warning('请先登录')
    next('/login')
  }
  // 如果已登录且要去登录页
  else if (token && to.path === '/login') {
    next('/home')
  }
  // 其他情况正常放行
  else {
    next()
  }
})

export default router
