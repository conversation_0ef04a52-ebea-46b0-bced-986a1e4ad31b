<template>
  <div class="carousel-controls" v-if="showControls">
    <div class="carousel-progress">
      <div class="progress-bar">
        <div class="progress-indicator" :style="progressStyle"></div>
      </div>
      <div class="carousel-indicators">
        <span>{{ currentPage }} / {{ totalPages }}</span>
      </div>
    </div>
    <div class="carousel-buttons">
      <button class="carousel-button" @click="prevPage" title="上一页">
        <i class="el-icon-arrow-left"></i>
      </button>
      <button class="carousel-button play-pause" @click="togglePause" title="暂停/播放">
        <i :class="isPaused ? 'el-icon-video-play' : 'el-icon-video-pause'"></i>
      </button>
      <button class="carousel-button" @click="nextPage" title="下一页">
        <i class="el-icon-arrow-right"></i>
      </button>
    </div>
    
    <!-- 缩略图预览 -->
    <div class="carousel-thumbnails" v-if="showThumbnails">
      <div 
        v-for="(_, index) in totalPages" 
        :key="index"
        class="thumbnail-indicator"
        :class="{ 'active': index + 1 === currentPage }"
        @click="goToPage(index + 1)"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps<{
  currentIndex: number;
  totalItems: number;
  itemsPerPage: number;
  isPaused: boolean;
  showControls: boolean;
}>();

const emit = defineEmits<{
  (e: 'prev'): void;
  (e: 'next'): void;
  (e: 'togglePause'): void;
  (e: 'goToPage', page: number): void;
}>();

// 是否显示缩略图指示器
const showThumbnails = ref(true);

// 计算当前页和总页数
const currentPage = computed(() => {
  return Math.floor(props.currentIndex / props.itemsPerPage) + 1;
});

const totalPages = computed(() => {
  return Math.ceil(props.totalItems / props.itemsPerPage);
});

// 计算进度条样式
const progressStyle = computed(() => {
  const progress = (currentPage.value - 1) / (totalPages.value - 1) * 100;
  return {
    width: totalPages.value > 1 ? `${progress}%` : '0%'
  };
});

// 控制函数
const prevPage = () => {
  emit('prev');
};

const nextPage = () => {
  emit('next');
};

const togglePause = () => {
  emit('togglePause');
};

const goToPage = (page: number) => {
  // 计算对应的索引
  const index = (page - 1) * props.itemsPerPage;
  emit('goToPage', index);
};
</script>

<style scoped>
.carousel-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  z-index: 100;
}

.carousel-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 200px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-indicator {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green, #1e8449), var(--tech-blue, #3498db));
  border-radius: 2px;
  transition: width 0.3s ease;
}

.carousel-indicators {
  color: white;
  font-size: 12px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2px 8px;
  border-radius: 10px;
}

.carousel-buttons {
  display: flex;
  gap: 16px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px 20px;
  border-radius: 30px;
  backdrop-filter: blur(5px);
}

.carousel-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.carousel-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.carousel-button.play-pause {
  width: 44px;
  height: 44px;
  background-color: var(--primary-green, #1e8449);
}

.carousel-button.play-pause:hover {
  background-color: var(--tech-blue, #3498db);
}

.carousel-button i {
  font-size: 16px;
}

.carousel-thumbnails {
  display: flex;
  gap: 8px;
  margin-top: 5px;
}

.thumbnail-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.thumbnail-indicator:hover {
  background-color: rgba(255, 255, 255, 0.5);
}

.thumbnail-indicator.active {
  background-color: white;
  transform: scale(1.2);
}
</style> 