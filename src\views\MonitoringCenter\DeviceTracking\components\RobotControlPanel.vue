<!--
机器狗控制面板
功能特性：
1. 键盘控制映射 (WASDQE)
2. 持续按键控制逻辑
3. 控制状态可视化
4. 紧急停止功能
5. 移动参数调节
-->

<template>
  <div class="robot-control-panel" :class="{ 'keyboard-active': keyboardEnabled }">
    <!-- 面板标题 -->
    <div class="panel-header">
      <div class="panel-title">
        <el-icon class="title-icon"><Operation /></el-icon>
        <span>机器狗控制</span>
        <el-badge
          :value="isControlling ? '控制中' : '待机'"
          :type="isControlling ? 'success' : 'info'"
          class="status-badge"
        />
      </div>
      <div class="panel-actions">
        <el-popconfirm
          title="确定要执行紧急停止吗？这会让机器狗立即停止所有动作并可能倒地！"
          confirm-button-text="确定执行"
          cancel-button-text="取消"
          confirm-button-type="danger"
          @confirm="emergencyStop"
        >
          <template #reference>
            <el-button
              type="danger"
              size="small"
              :loading="isEmergencyStoping"
            >
              紧急停止
            </el-button>
          </template>
        </el-popconfirm>
      </div>
    </div>

    <!-- 控制区域 -->
    <div class="control-content">
      <!-- 键盘控制开关 -->
      <div class="control-section">
        <div class="section-title">
          <el-icon><Key /></el-icon>
          <span>键盘控制</span>
        </div>
        <div class="keyboard-toggle">
          <el-switch
            v-model="keyboardEnabled"
            :disabled="!isConnected"
            @change="handleKeyboardToggle"
          />
          <span class="toggle-label">
            {{ keyboardEnabled ? '已启用' : '已禁用' }}
          </span>
        </div>
      </div>

      <!-- 移动参数设置 -->
      <div class="control-section">
        <div class="section-title">
          <el-icon><Setting /></el-icon>
          <span>移动参数</span>
        </div>
        <div class="parameter-controls">
          <div class="parameter-item">
            <label>移动速度</label>
            <el-slider
              v-model="moveSpeed"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              :disabled="!isConnected"
              show-input
              :show-input-controls="false"
              input-size="small"
            />
          </div>
          <div class="parameter-item">
            <label>旋转速度</label>
            <el-slider
              v-model="rotateSpeed"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              :disabled="!isConnected"
              show-input
              :show-input-controls="false"
              input-size="small"
            />
          </div>
        </div>
      </div>

      <!-- 键盘映射显示 -->
      <div class="control-section" v-if="keyboardEnabled">
        <div class="section-title">
          <el-icon><Guide /></el-icon>
          <span>按键映射</span>
        </div>
        <div class="keyboard-mapping">
          <div class="key-row">
            <div class="key-item" :class="{ active: keyStates.q }">
              <span class="key">Q</span>
              <span class="action">左转</span>
            </div>
            <div class="key-item" :class="{ active: keyStates.w }">
              <span class="key">W</span>
              <span class="action">前进</span>
            </div>
            <div class="key-item" :class="{ active: keyStates.e }">
              <span class="key">E</span>
              <span class="action">右转</span>
            </div>
          </div>
          <div class="key-row">
            <div class="key-item" :class="{ active: keyStates.a }">
              <span class="key">A</span>
              <span class="action">左移</span>
            </div>
            <div class="key-item" :class="{ active: keyStates.s }">
              <span class="key">S</span>
              <span class="action">后退</span>
            </div>
            <div class="key-item" :class="{ active: keyStates.d }">
              <span class="key">D</span>
              <span class="action">右移</span>
            </div>
          </div>
          <div class="key-row">
            <div class="key-item special">
              <span class="key">空格</span>
              <span class="action">停止移动</span>
            </div>
          </div>
        </div>
        <div class="keyboard-tips">
          <p class="tip-text">
            <el-icon><InfoFilled /></el-icon>
            按住按键持续移动，松开立即停止。空格键为普通停止，不会让机器狗倒地。
          </p>
        </div>
      </div>

      <!-- 手动控制按钮 -->
      <div class="control-section">
        <div class="section-title">
          <el-icon><Position /></el-icon>
          <span>手动控制</span>
        </div>
        <div class="manual-controls">
          <div class="direction-pad">
            <!-- 前进 -->
            <el-button
              class="direction-btn up"
              :disabled="!isConnected"
              @mousedown="startManualMove(moveSpeed, 0, 0)"
              @mouseup="stopManualMove"
              @mouseleave="stopManualMove"
            >
              <el-icon><ArrowUp /></el-icon>
            </el-button>

            <!-- 左移 -->
            <el-button
              class="direction-btn left"
              :disabled="!isConnected"
              @mousedown="startManualMove(0, moveSpeed, 0)"
              @mouseup="stopManualMove"
              @mouseleave="stopManualMove"
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>

            <!-- 停止 -->
            <el-button
              class="direction-btn center stop-btn"
              type="danger"
              :disabled="!isConnected"
              @click="stopMovement"
            >
              <el-icon><VideoPause /></el-icon>
            </el-button>

            <!-- 右移 -->
            <el-button
              class="direction-btn right"
              :disabled="!isConnected"
              @mousedown="startManualMove(0, -moveSpeed, 0)"
              @mouseup="stopManualMove"
              @mouseleave="stopManualMove"
            >
              <el-icon><ArrowRight /></el-icon>
            </el-button>

            <!-- 后退 -->
            <el-button
              class="direction-btn down"
              :disabled="!isConnected"
              @mousedown="startManualMove(-moveSpeed, 0, 0)"
              @mouseup="stopManualMove"
              @mouseleave="stopManualMove"
            >
              <el-icon><ArrowDown /></el-icon>
            </el-button>
          </div>

          <!-- 旋转控制 -->
          <div class="rotation-controls">
            <el-button
              class="rotate-btn"
              :disabled="!isConnected"
              @mousedown="startManualMove(0, 0, rotateSpeed)"
              @mouseup="stopManualMove"
              @mouseleave="stopManualMove"
            >
              <el-icon><RefreshLeft /></el-icon>
              左转
            </el-button>
            <el-button
              class="rotate-btn"
              :disabled="!isConnected"
              @mousedown="startManualMove(0, 0, -rotateSpeed)"
              @mouseup="stopManualMove"
              @mouseleave="stopManualMove"
            >
              <el-icon><RefreshRight /></el-icon>
              右转
            </el-button>
          </div>
        </div>
      </div>

      <!-- 控制状态显示 -->
      <div class="control-section">
        <div class="section-title">
          <el-icon><Monitor /></el-icon>
          <span>控制状态</span>
        </div>
        <div class="status-display">
          <div class="status-item">
            <span class="status-label">连接状态：</span>
            <span class="status-value" :class="connectionStatusClass">
              {{ connectionStatusText }}
            </span>
          </div>
          <div class="status-item">
            <span class="status-label">当前动作：</span>
            <span class="status-value">{{ currentActionText }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">移动参数：</span>
            <span class="status-value">
              X: {{ currentMoveParams.x.toFixed(1) }},
              Y: {{ currentMoveParams.y.toFixed(1) }},
              Z: {{ currentMoveParams.z.toFixed(1) }}
            </span>
          </div>
          <div class="status-item" v-if="lastCommandTime">
            <span class="status-label">最后命令：</span>
            <span class="status-value">{{ formatTime(lastCommandTime) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Operation, Key, Setting, Guide, Position, Monitor,
  ArrowUp, ArrowDown, ArrowLeft, ArrowRight, VideoPause,
  RefreshLeft, RefreshRight, InfoFilled
} from '@element-plus/icons-vue'
import { robotWebSocketService, ConnectionStatus, type MoveParams } from '@/services/robotWebSocketService'
import envConfig from '@/config/env'

// 组件属性
interface Props {
  autoEnable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoEnable: true
})

// 组件事件
const emit = defineEmits<{
  'control-change': [controlling: boolean]
  'move-command': [params: MoveParams]
  'error': [error: Error]
}>()

// 响应式数据
const keyboardEnabled = ref(false)
const isControlling = ref(false)
const isEmergencyStoping = ref(false)
const moveSpeed = ref(envConfig.robotControlMoveSpeed)
const rotateSpeed = ref(envConfig.robotControlRotateSpeed)
const lastCommandTime = ref<Date | null>(null)

// 键盘状态
const keyStates = reactive({
  w: false, // 前进
  a: false, // 左移
  s: false, // 后退
  d: false, // 右移
  q: false, // 左转
  e: false  // 右转
})

// 当前移动参数
const currentMoveParams = reactive<MoveParams>({
  x: 0,
  y: 0,
  z: 0
})

// 控制定时器
let manualControlTimer: number | null = null
let keyboardControlTimer: number | null = null

// 计算属性
const isConnected = computed(() => robotWebSocketService.connectionState.connected)

const connectionStatusText = computed(() => {
  switch (robotWebSocketService.connectionState.status) {
    case ConnectionStatus.CONNECTED: return '已连接'
    case ConnectionStatus.CONNECTING: return '连接中'
    case ConnectionStatus.RECONNECTING: return '重连中'
    case ConnectionStatus.DISCONNECTED: return '已断开'
    case ConnectionStatus.ERROR: return '连接错误'
    default: return '未知状态'
  }
})

const connectionStatusClass = computed(() => ({
  'status-success': robotWebSocketService.connectionState.status === ConnectionStatus.CONNECTED,
  'status-warning': [ConnectionStatus.CONNECTING, ConnectionStatus.RECONNECTING].includes(robotWebSocketService.connectionState.status),
  'status-error': robotWebSocketService.connectionState.status === ConnectionStatus.ERROR,
  'status-offline': robotWebSocketService.connectionState.status === ConnectionStatus.DISCONNECTED
}))

const currentActionText = computed(() => {
  if (!isControlling.value) return '待机'

  const { x, y, z } = currentMoveParams
  const actions = []

  if (x > 0) actions.push('前进')
  else if (x < 0) actions.push('后退')

  if (y > 0) actions.push('左移')
  else if (y < 0) actions.push('右移')

  if (z > 0) actions.push('左转')
  else if (z < 0) actions.push('右转')

  return actions.length > 0 ? actions.join(' + ') : '停止'
})

// 键盘控制相关方法
const handleKeyboardToggle = (enabled: boolean) => {
  if (enabled && !isConnected.value) {
    keyboardEnabled.value = false
    ElMessage.warning('请先连接机器狗')
    return
  }

  if (enabled) {
    enableKeyboardControl()
    ElMessage.success('键盘控制已启用')
  } else {
    disableKeyboardControl()
    ElMessage.info('键盘控制已禁用')
  }
}

const enableKeyboardControl = () => {
  document.addEventListener('keydown', handleKeyDown)
  document.addEventListener('keyup', handleKeyUp)
  window.addEventListener('blur', handleWindowBlur)
}

const disableKeyboardControl = () => {
  document.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('keyup', handleKeyUp)
  window.removeEventListener('blur', handleWindowBlur)

  // 停止键盘控制定时器
  stopKeyboardControlTimer()

  // 重置所有按键状态
  Object.keys(keyStates).forEach(key => {
    keyStates[key as keyof typeof keyStates] = false
  })

  // 发送停止命令
  if (isControlling.value) {
    stopMovement()
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (!keyboardEnabled.value || !isConnected.value) return

  const key = event.key.toLowerCase()
  if (key in keyStates && !keyStates[key as keyof typeof keyStates]) {
    keyStates[key as keyof typeof keyStates] = true

    // 立即发送命令
    updateMovementFromKeyboard()

    // 开始持续控制
    startKeyboardControlTimer()

    event.preventDefault()
  }

  // 空格键普通停止（不是紧急停止）
  if (event.code === 'Space') {
    stopMovement()
    event.preventDefault()
  }
}

const handleKeyUp = (event: KeyboardEvent) => {
  if (!keyboardEnabled.value) return

  const key = event.key.toLowerCase()
  if (key in keyStates && keyStates[key as keyof typeof keyStates]) {
    keyStates[key as keyof typeof keyStates] = false

    // 检查是否还有其他按键按下
    const hasActiveKeys = Object.values(keyStates).some(state => state)

    if (hasActiveKeys) {
      // 还有其他按键，更新移动参数
      updateMovementFromKeyboard()
    } else {
      // 没有按键了，停止移动
      stopKeyboardControlTimer()
      stopMovement()
    }

    event.preventDefault()
  }
}

const handleWindowBlur = () => {
  // 窗口失焦时停止所有移动（普通停止，不是紧急停止）
  stopKeyboardControlTimer()
  Object.keys(keyStates).forEach(key => {
    keyStates[key as keyof typeof keyStates] = false
  })
  stopMovement()
  console.log('窗口失焦，已停止机器狗移动')
}

const updateMovementFromKeyboard = () => {
  let x = 0, y = 0, z = 0

  // 计算移动参数
  if (keyStates.w) x += moveSpeed.value  // 前进
  if (keyStates.s) x -= moveSpeed.value  // 后退
  if (keyStates.a) y += moveSpeed.value  // 左移
  if (keyStates.d) y -= moveSpeed.value  // 右移
  if (keyStates.q) z += rotateSpeed.value  // 左转
  if (keyStates.e) z -= rotateSpeed.value  // 右转

  // 发送移动命令
  sendMoveCommand(x, y, z)
}

// 开始键盘控制定时器
const startKeyboardControlTimer = () => {
  // 如果定时器已存在，不重复创建
  if (keyboardControlTimer) return

  // 持续发送移动命令，确保机器狗持续移动
  keyboardControlTimer = window.setInterval(() => {
    if (keyboardEnabled.value && isConnected.value) {
      const hasActiveKeys = Object.values(keyStates).some(state => state)
      if (hasActiveKeys) {
        updateMovementFromKeyboard()
      } else {
        // 没有按键时停止定时器
        stopKeyboardControlTimer()
        stopMovement()
      }
    }
  }, 100) // 每100ms发送一次命令，确保持续控制
}

// 停止键盘控制定时器
const stopKeyboardControlTimer = () => {
  if (keyboardControlTimer) {
    clearInterval(keyboardControlTimer)
    keyboardControlTimer = null
  }
}

// 手动控制相关方法
const startManualMove = (x: number, y: number, z: number) => {
  if (!isConnected.value) return

  // 清除之前的定时器
  if (manualControlTimer) {
    clearInterval(manualControlTimer)
  }

  // 立即发送命令
  sendMoveCommand(x, y, z)

  // 持续发送命令（防止网络延迟导致的停止）
  manualControlTimer = window.setInterval(() => {
    sendMoveCommand(x, y, z)
  }, 200)
}

const stopManualMove = () => {
  if (manualControlTimer) {
    clearInterval(manualControlTimer)
    manualControlTimer = null
  }

  stopMovement()
}

// 移动控制核心方法
const sendMoveCommand = async (x: number, y: number, z: number) => {
  if (!isConnected.value) return

  try {
    // 更新当前移动参数
    currentMoveParams.x = x
    currentMoveParams.y = y
    currentMoveParams.z = z

    // 更新控制状态
    isControlling.value = x !== 0 || y !== 0 || z !== 0

    // 发送WebSocket命令
    await robotWebSocketService.sendMoveCommand(x, y, z)

    lastCommandTime.value = new Date()
    emit('control-change', isControlling.value)
    emit('move-command', { x, y, z })

  } catch (error) {
    console.error('发送移动命令失败:', error)
    ElMessage.error('移动命令发送失败')
    emit('error', error instanceof Error ? error : new Error('移动命令发送失败'))
  }
}

const stopMovement = async () => {
  try {
    await robotWebSocketService.sendStopCommand()

    // 重置状态
    currentMoveParams.x = 0
    currentMoveParams.y = 0
    currentMoveParams.z = 0
    isControlling.value = false

    lastCommandTime.value = new Date()
    emit('control-change', false)
    emit('move-command', { x: 0, y: 0, z: 0 })

  } catch (error) {
    console.error('发送停止命令失败:', error)
    ElMessage.error('停止命令发送失败')
  }
}

const emergencyStop = async () => {
  try {
    isEmergencyStoping.value = true

    console.warn('用户主动执行紧急停止 - 机器狗将立即停止所有动作')

    // 停止所有定时器
    stopKeyboardControlTimer()
    if (manualControlTimer) {
      clearInterval(manualControlTimer)
      manualControlTimer = null
    }

    // 禁用键盘控制
    if (keyboardEnabled.value) {
      keyboardEnabled.value = false
      disableKeyboardControl()
    }

    // 发送紧急停止命令
    await robotWebSocketService.sendEmergencyStopCommand()

    // 重置所有状态
    currentMoveParams.x = 0
    currentMoveParams.y = 0
    currentMoveParams.z = 0
    isControlling.value = false

    lastCommandTime.value = new Date()

    ElMessage({
      message: '⚠️ 紧急停止已执行！机器狗已停止所有动作',
      type: 'warning',
      duration: 5000,
      showClose: true
    })

    emit('control-change', false)
    emit('move-command', { x: 0, y: 0, z: 0 })

  } catch (error) {
    console.error('紧急停止失败:', error)
    ElMessage.error('紧急停止执行失败，请检查连接状态')
    emit('error', error instanceof Error ? error : new Error('紧急停止失败'))
  } finally {
    isEmergencyStoping.value = false
  }
}

// 工具函数
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 监听连接状态变化
watch(() => robotWebSocketService.connectionState.connected, (connected) => {
  if (!connected && keyboardEnabled.value) {
    // 连接断开时停止移动并禁用键盘控制（不使用紧急停止）
    stopKeyboardControlTimer()
    if (manualControlTimer) {
      clearInterval(manualControlTimer)
      manualControlTimer = null
    }

    keyboardEnabled.value = false
    disableKeyboardControl()

    // 发送普通停止命令
    if (isControlling.value) {
      stopMovement()
    }

    ElMessage.warning('连接断开，已停止移动并禁用键盘控制')
  }
})

// 生命周期
onMounted(() => {
  console.log('机器狗控制面板已加载')

  // 自动启用键盘控制
  if (props.autoEnable && isConnected.value) {
    keyboardEnabled.value = true
    enableKeyboardControl()
  }
})

onUnmounted(() => {
  // 清理资源（组件卸载时只做普通停止，不执行紧急停止）
  console.log('机器狗控制面板卸载，清理资源并停止移动')

  disableKeyboardControl()
  stopKeyboardControlTimer()

  if (manualControlTimer) {
    clearInterval(manualControlTimer)
    manualControlTimer = null
  }

  // 发送普通停止命令确保安全（不是紧急停止）
  if (isControlling.value) {
    stopMovement()
  }
})
</script>

<style lang="scss" scoped>
.robot-control-panel {
  background: rgba(31, 41, 55, 0.9);
  border-radius: 16px;
  border: 1px solid rgba(75, 85, 99, 0.3);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  &.keyboard-active {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: rgba(31, 41, 55, 0.8);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);

    .panel-title {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 16px;
      font-weight: 600;
      color: #f3f4f6;

      .title-icon {
        color: #3b82f6;
        font-size: 18px;
      }

      .status-badge {
        margin-left: 8px;
      }
    }

    .panel-actions {
      .el-button {
        background: rgba(239, 68, 68, 0.2);
        border-color: rgba(239, 68, 68, 0.3);
        color: #ef4444;

        &:hover {
          background: rgba(239, 68, 68, 0.3);
        }
      }
    }
  }

  .control-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .control-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 600;
        color: #f3f4f6;
        margin-bottom: 12px;

        .el-icon {
          color: #3b82f6;
          font-size: 16px;
        }
      }
    }

    .keyboard-toggle {
      display: flex;
      align-items: center;
      gap: 12px;

      .toggle-label {
        font-size: 12px;
        color: #9ca3af;
      }
    }

    .parameter-controls {
      .parameter-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          display: block;
          font-size: 12px;
          color: #9ca3af;
          margin-bottom: 8px;
        }

        :deep(.el-slider) {
          .el-slider__runway {
            background-color: rgba(75, 85, 99, 0.5);
          }

          .el-slider__bar {
            background-color: #3b82f6;
          }

          .el-slider__button {
            border-color: #3b82f6;
          }
        }

        :deep(.el-input) {
          .el-input__inner {
            background: rgba(31, 41, 55, 0.8);
            border-color: rgba(75, 85, 99, 0.3);
            color: #f3f4f6;
          }
        }
      }
    }

    .keyboard-mapping {
      .key-row {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .key-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 8px 12px;
          background: rgba(75, 85, 99, 0.3);
          border-radius: 8px;
          border: 1px solid rgba(75, 85, 99, 0.5);
          transition: all 0.2s ease;
          min-width: 60px;

          &.active {
            background: rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.5);
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
          }

          &.special {
            background: rgba(34, 197, 94, 0.2);
            border-color: rgba(34, 197, 94, 0.3);
          }

          .key {
            font-size: 16px;
            font-weight: 600;
            color: #f3f4f6;
            margin-bottom: 4px;
          }

          .action {
            font-size: 10px;
            color: #9ca3af;
          }
        }
      }
    }

    .keyboard-tips {
      margin-top: 12px;
      padding: 8px 12px;
      background: rgba(59, 130, 246, 0.1);
      border-radius: 6px;
      border: 1px solid rgba(59, 130, 246, 0.2);

      .tip-text {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #9ca3af;
        margin: 0;

        .el-icon {
          color: #3b82f6;
          font-size: 14px;
        }
      }
    }

    .manual-controls {
      .direction-pad {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: 1fr 1fr 1fr;
        gap: 8px;
        margin-bottom: 16px;
        max-width: 200px;
        margin-left: auto;
        margin-right: auto;

        .direction-btn {
          width: 50px;
          height: 50px;
          border-radius: 8px;
          background: rgba(75, 85, 99, 0.3);
          border: 1px solid rgba(75, 85, 99, 0.5);
          color: #f3f4f6;

          &:hover:not(:disabled) {
            background: rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.5);
          }

          &:active:not(:disabled) {
            transform: scale(0.95);
          }

          &.up {
            grid-column: 2;
            grid-row: 1;
          }

          &.left {
            grid-column: 1;
            grid-row: 2;
          }

          &.center {
            grid-column: 2;
            grid-row: 2;
          }

          &.right {
            grid-column: 3;
            grid-row: 2;
          }

          &.down {
            grid-column: 2;
            grid-row: 3;
          }

          &.stop-btn {
            background: rgba(239, 68, 68, 0.3);
            border-color: rgba(239, 68, 68, 0.5);

            &:hover:not(:disabled) {
              background: rgba(239, 68, 68, 0.4);
            }
          }
        }
      }

      .rotation-controls {
        display: flex;
        justify-content: center;
        gap: 12px;

        .rotate-btn {
          background: rgba(75, 85, 99, 0.3);
          border: 1px solid rgba(75, 85, 99, 0.5);
          color: #f3f4f6;

          &:hover:not(:disabled) {
            background: rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.5);
          }

          &:active:not(:disabled) {
            transform: scale(0.95);
          }
        }
      }
    }

    .status-display {
      .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .status-label {
          color: #9ca3af;
        }

        .status-value {
          color: #f3f4f6;
          font-family: monospace;

          &.status-success {
            color: #10b981;
          }

          &.status-warning {
            color: #f59e0b;
          }

          &.status-error {
            color: #ef4444;
          }

          &.status-offline {
            color: #6b7280;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .robot-control-panel {
    .control-content {
      padding: 12px;

      .control-section {
        margin-bottom: 20px;
      }

      .keyboard-mapping {
        .key-row {
          .key-item {
            min-width: 50px;
            padding: 6px 8px;

            .key {
              font-size: 14px;
            }

            .action {
              font-size: 9px;
            }
          }
        }
      }

      .manual-controls {
        .direction-pad {
          max-width: 180px;

          .direction-btn {
            width: 45px;
            height: 45px;
          }
        }
      }
    }
  }
}
</style>
