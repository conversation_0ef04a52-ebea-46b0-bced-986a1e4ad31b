// 智慧农业系统 - 混合器
// 这个文件包含了系统中使用的所有SCSS混合器

// 玻璃态效果
@mixin glassmorphism($bg-color: rgba(0, 16, 65, 0.5), $blur: 16px, $border-color: rgba(0, 255, 170, 0.1)) {
  background: $bg-color;
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
  border: 1px solid $border-color;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              0 0 20px rgba(0, 255, 170, 0.05);
}

// 浅色玻璃态效果
@mixin light-glassmorphism {
  @include glassmorphism(rgba(255, 255, 255, 0.1), 12px, rgba(255, 255, 255, 0.2));
}

// 卡片悬浮效果
@mixin card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 36px rgba(0, 0, 0, 0.2),
                0 0 16px rgba(0, 255, 170, 0.15);
  }
}

// 轻微悬浮效果
@mixin hover-lift {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 255, 170, 0.2);
  }
}

// 霓虹文字效果
@mixin neon-text($color: #00ffaa, $glow-size: 2px) {
  color: $color;
  text-shadow: 0 0 $glow-size rgba($color, 0.8);
}

// 霓虹边框效果
@mixin neon-border($color: #00ffaa, $glow-size: 2px) {
  border: 1px solid $color;
  box-shadow: 0 0 $glow-size $color,
              inset 0 0 $glow-size $color;
}

// 主题色渐变
@mixin primary-gradient($direction: 90deg) {
  background: linear-gradient($direction, #00ffaa, #00aa83);
}

// 布局居中
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 布局两端对齐
@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 网格背景
@mixin grid-background($color: rgba(0, 255, 170, 0.05), $size: 30px) {
  background-image: 
    linear-gradient($color 1px, transparent 1px),
    linear-gradient(90deg, $color 1px, transparent 1px);
  background-size: $size $size;
}

// 标准边距
@mixin standard-padding {
  padding: 24px;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

// 标准圆角
@mixin rounded($radius: 12px) {
  border-radius: $radius;
}

// 截断文本
@mixin truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 3D卡片效果
@mixin card-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  &:hover {
    transform: rotateX(10deg) rotateY(-10deg) translateZ(10px);
  }
}

// 响应式布局断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == "sm" {
    @media (max-width: 576px) { @content; }
  }
  @else if $breakpoint == "md" {
    @media (max-width: 768px) { @content; }
  }
  @else if $breakpoint == "lg" {
    @media (max-width: 992px) { @content; }
  }
  @else if $breakpoint == "xl" {
    @media (max-width: 1200px) { @content; }
  }
  @else if $breakpoint == "xxl" {
    @media (max-width: 1400px) { @content; }
  }
} 