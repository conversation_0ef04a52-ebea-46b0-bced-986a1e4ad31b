<template>
  <div class="heatmap-type-selector">
    <div class="selector-label">{{ label }}</div>
    <el-radio-group v-model="selectedType" @change="handleChange" size="small">
      <el-radio-button v-for="option in options" :key="option.value" :label="option.value">
        {{ option.label }}
      </el-radio-button>
    </el-radio-group>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

export interface TypeOption {
  label: string
  value: string
}

const props = defineProps<{
  modelValue: string
  options: TypeOption[]
  label?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

const selectedType = ref(props.modelValue)

watch(() => props.modelValue, (newValue) => {
  selectedType.value = newValue
})

const handleChange = (value: string) => {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped>
.heatmap-type-selector {
  display: flex;
  align-items: center;
}

.selector-label {
  margin-right: 10px;
  color: #d1d5db;
  font-size: 14px;
}
</style> 