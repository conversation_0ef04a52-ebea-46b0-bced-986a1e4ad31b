<template>
  <div class="alert-panel">
    <div class="alert-header">
      <div class="alert-stats">
        <div class="stat-item" :class="{ 'active': activeFilter === 'all' }" @click="setFilter('all')">
          <div class="stat-count">{{ totalAlerts }}</div>
          <div class="stat-label">全部</div>
        </div>
        <div class="stat-item" :class="{ 'active': activeFilter === 'critical' }" @click="setFilter('critical')">
          <div class="stat-count">{{ criticalCount }}</div>
          <div class="stat-label">严重</div>
        </div>
        <div class="stat-item" :class="{ 'active': activeFilter === 'warning' }" @click="setFilter('warning')">
          <div class="stat-count">{{ warningCount }}</div>
          <div class="stat-label">警告</div>
        </div>
        <div class="stat-item" :class="{ 'active': activeFilter === 'info' }" @click="setFilter('info')">
          <div class="stat-count">{{ infoCount }}</div>
          <div class="stat-label">信息</div>
        </div>
      </div>
      <div class="alert-actions">
        <el-button size="small" icon="Refresh" @click="refreshAlerts">刷新</el-button>
        <el-button size="small" icon="Delete" @click="clearAllAlerts">清空</el-button>
      </div>
    </div>
    
    <div class="alert-content">
      <transition-group name="alert-list" tag="div" class="alerts-container">
        <div v-if="filteredAlerts.length === 0" class="no-alerts" key="no-alerts">
          <svg-icon name="no-alerts" size="64" />
          <p>暂无预警信息</p>
        </div>
        <div 
          v-for="alert in filteredAlerts" 
          :key="alert.id" 
          class="alert-item"
          :class="[alert.level]"
          @click="selectAlert(alert)"
        >
          <div class="alert-icon">
            <svg-icon :name="getAlertIcon(alert.level)" size="24" />
          </div>
          <div class="alert-content">
            <div class="alert-title">
              <span class="alert-level-tag">{{ getAlertLevelText(alert.level) }}</span>
              {{ alert.title }}
            </div>
            <div class="alert-desc">{{ alert.description }}</div>
            <div class="alert-meta">
              <span class="alert-time">{{ formatTime(alert.time) }}</span>
              <span class="alert-location">{{ alert.location }}</span>
            </div>
          </div>
          <div class="alert-actions">
            <el-button 
              circle 
              size="small" 
              icon="View" 
              title="查看详情"
              @click.stop="viewAlertDetail(alert)"
            />
            <el-button 
              circle 
              size="small" 
              icon="Check" 
              title="标记为已处理"
              @click.stop="markAsHandled(alert)"
            />
          </div>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import SvgIcon from '@/components/SvgIcon/index.vue';
import { formatDate } from '@/utils/date';

// 模拟预警数据，实际项目中可能从API获取
const mockAlerts = [
  {
    id: 'a1',
    title: '温度异常警报',
    description: '大棚A区温度超过设定阈值，当前温度38°C，阈值35°C',
    level: 'critical',
    time: new Date(Date.now() - 1000 * 60 * 5), // 5分钟前
    location: '大棚A区 - 温度传感器',
    device: 'sensor-temp-a1',
    handled: false
  },
  {
    id: 'a2',
    title: '湿度过低警告',
    description: '大棚B区湿度低于推荐值，当前湿度32%，推荐值40%',
    level: 'warning',
    time: new Date(Date.now() - 1000 * 60 * 15), // 15分钟前
    location: '大棚B区 - 湿度传感器',
    device: 'sensor-hum-b1',
    handled: false
  },
  {
    id: 'a3',
    title: '摄像头连接中断',
    description: '西北角监控摄像头连接中断，请检查设备',
    level: 'warning',
    time: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
    location: '西北角 - 监控摄像头',
    device: 'camera-nw1',
    handled: false
  },
  {
    id: 'a4',
    title: '网关设备电量低',
    description: '中心区域网关设备电量低于20%，请及时充电',
    level: 'info',
    time: new Date(Date.now() - 1000 * 60 * 45), // 45分钟前
    location: '中心区域 - 网关设备',
    device: 'gateway-center',
    handled: false
  },
  {
    id: 'a5',
    title: '系统更新可用',
    description: '系统有新版本可用，建议在维护时间更新',
    level: 'info',
    time: new Date(Date.now() - 1000 * 60 * 60), // 1小时前
    location: '系统',
    device: 'system',
    handled: false
  }
];

const alerts = ref([...mockAlerts]);
const isOpen = ref(false);
const activeFilter = ref('all');

// 处理面板收起/展开状态变化
const handleCollapse = (collapsed: boolean) => {
  console.log('面板收起状态:', collapsed);
};

// 根据筛选条件过滤预警
const filteredAlerts = computed(() => {
  if (activeFilter.value === 'all') {
    return alerts.value;
  }
  return alerts.value.filter(alert => alert.level === activeFilter.value);
});

// 计算各类型预警数量
const totalAlerts = computed(() => alerts.value.length);
const criticalCount = computed(() => alerts.value.filter(a => a.level === 'critical').length);
const warningCount = computed(() => alerts.value.filter(a => a.level === 'warning').length);
const infoCount = computed(() => alerts.value.filter(a => a.level === 'info').length);

// 设置筛选条件
const setFilter = (filter) => {
  activeFilter.value = filter;
};

// 获取预警图标
const getAlertIcon = (level) => {
  const iconMap = {
    'critical': 'alert-critical',
    'warning': 'alert-warning',
    'info': 'alert-info'
  };
  return iconMap[level] || 'alert-info';
};

// 获取预警等级文本
const getAlertLevelText = (level) => {
  const textMap = {
    'critical': '严重',
    'warning': '警告',
    'info': '信息'
  };
  return textMap[level] || '未知';
};

// 格式化时间
const formatTime = (date) => {
  return formatDate(date);
};

// 刷新预警
const refreshAlerts = () => {
  console.log('刷新预警');
  // 实际项目中可能需要调用API
};

// 清空所有预警
const clearAllAlerts = () => {
  alerts.value = [];
};

// 选择预警
const selectAlert = (alert) => {
  console.log('选择预警:', alert);
  // 实际项目中可能需要执行其他操作
};

// 查看预警详情
const viewAlertDetail = (alert) => {
  console.log('查看预警详情:', alert);
  // 实际项目中可能需要显示详情对话框
};

// 标记为已处理
const markAsHandled = (alert) => {
  console.log('标记预警为已处理:', alert);
  const index = alerts.value.findIndex(a => a.id === alert.id);
  if (index !== -1) {
    alerts.value.splice(index, 1);
  }
};

// 自动打开面板
watch(() => alerts.value.length, (newVal, oldVal) => {
  // 当有新预警时自动打开面板
  if (newVal > oldVal) {
    isOpen.value = true;
  }
});
</script>

<style scoped lang="scss">
@use "../styles/variables.scss" as vars;

// 预警面板样式
.alert-panel {
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: vars.$panel-background;
  backdrop-filter: blur(vars.$panel-blur);
  -webkit-backdrop-filter: blur(vars.$panel-blur);
}

// 预警统计和筛选头部
.alert-header {
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(0, 21, 41, 0.3);
  
  .alert-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    
    .stat-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px 5px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(43, 255, 150, 0.08);
      }
      
      &.active {
        background: rgba(43, 255, 150, 0.15);
        
        .stat-count {
          color: vars.$primary-color;
        }
        
        .stat-label {
          color: vars.$text-light;
        }
      }
      
      .stat-count {
        font-size: 22px;
        font-weight: 600;
        color: vars.$text-light;
      }
      
      .stat-label {
        font-size: 12px;
        color: vars.$text-secondary;
        margin-top: 4px;
      }
    }
  }
  
  .alert-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

// 警报内容区域
.alert-content {
  flex: 1;
  overflow-y: auto;
  
  // 自定义滚动条
  @include vars.custom-scrollbar();
}

// 预警列表容器
.alerts-container {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 预警项
.alert-item {
  display: flex;
  background: rgba(0, 21, 41, 0.3);
  border-radius: 8px;
  padding: 12px;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  
  // 背景光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, transparent 60%);
    z-index: 0;
    pointer-events: none;
  }
  
  // 不同级别的样式
  &.critical {
    border-left-color: #f5222d;
    
    .alert-icon {
      color: #f5222d;
      background: rgba(245, 34, 45, 0.1);
    }
    
    .alert-level-tag {
      background: rgba(245, 34, 45, 0.15);
      color: #f5222d;
    }
  }
  
  &.warning {
    border-left-color: #faad14;
    
    .alert-icon {
      color: #faad14;
      background: rgba(250, 173, 20, 0.1);
    }
    
    .alert-level-tag {
      background: rgba(250, 173, 20, 0.15);
      color: #faad14;
    }
  }
  
  &.info {
    border-left-color: #1890ff;
    
    .alert-icon {
      color: #1890ff;
      background: rgba(24, 144, 255, 0.1);
    }
    
    .alert-level-tag {
      background: rgba(24, 144, 255, 0.15);
      color: #1890ff;
    }
  }
  
  // 悬停效果
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    
    &::before {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, transparent 70%);
    }
    
    .alert-actions {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  .alert-icon {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    z-index: 1;
  }
  
  .alert-content {
    flex: 1;
    min-width: 0; // 防止文本溢出
    position: relative;
    z-index: 1;
    overflow: visible;
    
    .alert-title {
      font-size: 15px;
      font-weight: 600;
      color: vars.$text-light;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      
      .alert-level-tag {
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 10px;
        margin-right: 8px;
        font-weight: normal;
      }
    }
    
    .alert-desc {
      font-size: 13px;
      color: vars.$text-secondary;
      margin-bottom: 6px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.4;
    }
    
    .alert-meta {
      display: flex;
      font-size: 12px;
      color: vars.$text-tertiary;
      
      .alert-time {
        margin-right: 15px;
      }
    }
  }
  
  .alert-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-left: 10px;
    opacity: 0.7;
    transform: translateX(5px);
    transition: all 0.3s ease;
    z-index: 1;
  }
}

// 无预警状态
.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: vars.$text-secondary;
  
  svg {
    margin-bottom: 15px;
    opacity: 0.5;
  }
  
  p {
    font-size: 16px;
    margin: 0;
  }
}

// 预警列表动画
.alert-list-enter-active,
.alert-list-leave-active {
  transition: all 0.3s ease;
}

.alert-list-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.alert-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// 响应式调整
@media (max-width: 768px) {
  .alert-stats {
    .stat-item {
      .stat-count {
        font-size: 18px;
      }
      
      .stat-label {
        font-size: 11px;
      }
    }
  }
}
</style> 