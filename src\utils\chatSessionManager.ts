/**
 * 会话管理工具类
 * 用于处理会话的增删改查、持久化等
 */

import { ref, computed } from 'vue';
import { useStorage } from '@vueuse/core';
import type { ChatMessage, ChatSession } from '@/types/chat';

// 生成唯一ID的函数
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// 生成预览文本
const generatePreview = (content: string, maxLength: number = 30): string => {
  if (content.length <= maxLength) return content;
  return content.substring(0, maxLength) + '...';
};

// 会话管理器
export const useChatSessionManager = () => {
  // 会话列表
  const sessions = useStorage<ChatSession[]>('ai-chat-sessions', []);
  // 当前活动会话ID
  const activeSessionId = useStorage<string>('ai-chat-active-session-id', '');
  
  // 获取会话消息
  const getSessionMessages = (sessionId: string): ChatMessage[] => {
    const key = `ai-chat-messages-${sessionId}`;
    const savedMessages = localStorage.getItem(key);
    return savedMessages ? JSON.parse(savedMessages) : [];
  };
  
  // 保存会话消息
  const saveSessionMessages = (sessionId: string, messages: ChatMessage[]): void => {
    const key = `ai-chat-messages-${sessionId}`;
    
    try {
      // 尝试保存消息
      localStorage.setItem(key, JSON.stringify(messages));
    } catch (error) {
      console.warn('💾 localStorage空间不足，尝试清理旧数据...', error);
      
      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        // 存储空间不足，执行清理策略
        try {
          cleanupOldStorageData();
          // 再次尝试保存
          localStorage.setItem(key, JSON.stringify(messages));
          console.log('✅ 清理后保存成功');
        } catch (secondError) {
          console.error('❌ 清理后仍然无法保存，尝试压缩数据...', secondError);
          
          // 如果还是失败，尝试保存压缩版本（移除音频数据）
          try {
            const compressedMessages = messages.map(msg => ({
              ...msg,
              metadata: msg.metadata ? {
                ...msg.metadata,
                audioData: undefined // 移除音频数据以节省空间
              } : undefined
            }));
            localStorage.setItem(key, JSON.stringify(compressedMessages));
            console.log('✅ 压缩后保存成功（已移除音频数据）');
          } catch (finalError) {
            console.error('❌ 最终保存失败，数据可能丢失:', finalError);
            // 可以在这里触发用户通知
            if (typeof window !== 'undefined' && window.ElMessage) {
              window.ElMessage.warning('聊天记录存储空间不足，部分数据可能无法保存');
            }
          }
        }
      } else {
        console.error('❌ localStorage保存失败:', error);
      }
    }
  };
  
  // 清理旧的存储数据
  const cleanupOldStorageData = (): void => {
    console.log('🧹 开始清理旧的存储数据...');
    
    try {
      // 获取所有localStorage中的聊天消息键
      const chatMessageKeys: string[] = [];
      const sessionIds = new Set(sessions.value.map(s => s.id));
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('ai-chat-messages-')) {
          const sessionId = key.replace('ai-chat-messages-', '');
          
          // 如果这个会话已经不存在，删除其消息
          if (!sessionIds.has(sessionId)) {
            chatMessageKeys.push(key);
          }
        }
      }
      
      // 删除无效会话的消息
      chatMessageKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log(`🗑️ 已删除无效会话数据: ${key}`);
      });
      
      // 如果还需要更多空间，删除最旧的会话（保留最新的3个）
      if (sessions.value.length > 3) {
        const sortedSessions = [...sessions.value].sort((a, b) => b.updatedAt - a.updatedAt);
        const sessionsToKeep = sortedSessions.slice(0, 3);
        const sessionsToDelete = sortedSessions.slice(3);
        
        sessionsToDelete.forEach(session => {
          localStorage.removeItem(`ai-chat-messages-${session.id}`);
          console.log(`🗑️ 已删除旧会话数据: ${session.title}`);
        });
        
        // 更新会话列表
        sessions.value = sessionsToKeep;
      }
      
      console.log('✅ 存储清理完成');
    } catch (error) {
      console.error('❌ 清理存储数据时出错:', error);
    }
  };
  
  // 检查存储空间使用情况
  const checkStorageUsage = (): { used: number; available: number; percentage: number } => {
    let used = 0;
    
    try {
      // 计算当前使用的存储空间
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }
      
      // 大概的可用空间（不同浏览器限制不同，这里假设5MB）
      const available = 5 * 1024 * 1024; // 5MB in bytes
      const percentage = (used / available) * 100;
      
      return { used, available, percentage };
    } catch (error) {
      console.error('检查存储使用情况失败:', error);
      return { used: 0, available: 0, percentage: 0 };
    }
  };
  
  // 创建新会话
  const createSession = (title: string = '新会话'): string => {
    const id = generateId();
    const newSession: ChatSession = {
      id,
      title,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      messageCount: 0,
    };
    
    sessions.value = [newSession, ...sessions.value];
    saveSessionMessages(id, []);
    activeSessionId.value = id;
    
    return id;
  };
  
  // 获取当前活动会话
  const activeSession = computed(() => {
    if (!activeSessionId.value && sessions.value.length === 0) {
      // 如果没有会话，创建一个默认会话
      createSession();
    } else if (!activeSessionId.value && sessions.value.length > 0) {
      // 如果有会话但没有设置活动会话，使用第一个
      activeSessionId.value = sessions.value[0].id;
    }
    
    return sessions.value.find(s => s.id === activeSessionId.value);
  });
  
  // 获取当前会话的消息
  const activeSessionMessages = computed(() => {
    if (!activeSession.value) return [];
    return getSessionMessages(activeSession.value.id);
  });
  
  // 切换会话
  const switchSession = (sessionId: string): void => {
    if (sessions.value.some(s => s.id === sessionId)) {
      activeSessionId.value = sessionId;
    }
  };
  
  // 更新会话
  const updateSession = (sessionId: string, updates: Partial<ChatSession>): void => {
    sessions.value = sessions.value.map(session => {
      if (session.id === sessionId) {
        return { ...session, ...updates, updatedAt: Date.now() };
      }
      return session;
    });
  };
  
  // 删除会话
  const deleteSession = (sessionId: string): void => {
    sessions.value = sessions.value.filter(session => session.id !== sessionId);
    
    // 删除会话消息
    localStorage.removeItem(`ai-chat-messages-${sessionId}`);
    
    // 如果删除的是当前活动会话，切换到第一个会话或创建新会话
    if (activeSessionId.value === sessionId) {
      if (sessions.value.length > 0) {
        activeSessionId.value = sessions.value[0].id;
      } else {
        createSession();
      }
    }
  };
  
  // 添加消息到当前会话
  const addMessageToActiveSession = (message: ChatMessage): void => {
    if (!activeSession.value) return;
    
    const sessionId = activeSession.value.id;
    const messages = getSessionMessages(sessionId);
    messages.push(message);
    
    // 检查存储使用情况
    const storageInfo = checkStorageUsage();
    if (storageInfo.percentage > 80) {
      console.warn(`⚠️ 存储空间使用率已达${storageInfo.percentage.toFixed(1)}%，建议清理`);
    }
    
    try {
      saveSessionMessages(sessionId, messages);
      
      // 更新会话信息
      updateSession(sessionId, {
        preview: generatePreview(message.content),
        messageCount: messages.length,
        updatedAt: message.timestamp
      });
    } catch (error) {
      console.error('❌ 添加消息到会话失败:', error);
      // 即使保存失败，也要尝试更新会话信息
      try {
        updateSession(sessionId, {
          preview: generatePreview(message.content),
          messageCount: messages.length,
          updatedAt: message.timestamp
        });
      } catch (updateError) {
        console.error('❌ 更新会话信息也失败:', updateError);
      }
    }
  };
  
  // 清空当前会话消息
  const clearActiveSessionMessages = (): void => {
    if (!activeSession.value) return;
    
    const sessionId = activeSession.value.id;
    saveSessionMessages(sessionId, []);
    
    // 更新会话信息
    updateSession(sessionId, {
      preview: undefined,
      messageCount: 0
    });
  };
  
  // 重命名会话
  const renameSession = (sessionId: string, newTitle: string): void => {
    updateSession(sessionId, { title: newTitle });
  };
  
  return {
    sessions,
    activeSessionId,
    activeSession,
    activeSessionMessages,
    createSession,
    switchSession,
    updateSession,
    deleteSession,
    addMessageToActiveSession,
    clearActiveSessionMessages,
    renameSession,
    checkStorageUsage,
    cleanupOldStorageData
  };
};
