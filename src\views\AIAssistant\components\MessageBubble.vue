<template>
  <div class="message-bubble" :class="bubbleClass">
    <div class="message-avatar">
      <el-avatar v-if="message.role === 'assistant'" :size="36" src="/ai-avatar.svg" />
      <el-avatar v-else :size="36" icon="el-icon-user" />
    </div>
    
    <div class="message-content-wrapper">
      <div class="message-header">
        <div class="message-author">{{ authorName }}</div>
        <div class="message-time">{{ formattedTime }}</div>
      </div>
      
      <div class="message-content" :class="{ 'typing': isTyping && isLastMessage && message.role === 'assistant' }">
        <div v-if="message.type === 'code'" class="code-block">
          <pre><code>{{ message.content }}</code></pre>
        </div>
        <div v-else-if="isTyping && isLastMessage && message.role === 'assistant'" ref="typingContent">
          {{ displayContent }}
        </div>
        <div v-else v-html="formattedContent"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue';
import type { ChatMessage } from '@/types/chat';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const props = defineProps<{
  message: ChatMessage;
  isLastMessage?: boolean;
}>();

// 是否正在执行打字效果
const isTyping = ref(false);
// 当前显示内容
const displayContent = ref('');
// 打字定时器引用
let typingTimer: number | null = null;
// 打字下标
let charIndex = 0;
// 打字内容引用
const typingContent = ref<HTMLElement | null>(null);

// 格式化消息内容（处理换行和链接）
const formattedContent = computed(() => {
  if (!props.message.content) return '';
  
  // 处理换行
  let content = props.message.content.replace(/\n/g, '<br>');
  
  // 处理链接
  content = content.replace(
    /(https?:\/\/[^\s]+)/g, 
    '<a href="$1" target="_blank" class="message-link">$1</a>'
  );
  
  return content;
});

// 格式化时间
const formattedTime = computed(() => {
  return formatDistanceToNow(new Date(props.message.timestamp), {
    addSuffix: true,
    locale: zhCN
  });
});

// 作者名称
const authorName = computed(() => {
  return props.message.role === 'assistant' ? '智慧农业AI助手' : '您';
});

// 气泡样式类
const bubbleClass = computed(() => {
  return {
    'ai-message': props.message.role === 'assistant',
    'user-message': props.message.role === 'user',
    'with-typing': isTyping.value && props.isLastMessage
  };
});

// 模拟打字效果
const simulateTyping = () => {
  if (props.message.role !== 'assistant' || !props.isLastMessage) return;
  
  const content = props.message.content;
  if (!content) return;
  
  isTyping.value = true;
  displayContent.value = '';
  charIndex = 0;
  
  // 清除之前的定时器
  if (typingTimer) {
    window.clearInterval(typingTimer);
  }
  
  // 打字效果
  typingTimer = window.setInterval(() => {
    if (charIndex < content.length) {
      displayContent.value += content[charIndex];
      charIndex++;
    } else {
      isTyping.value = false;
      if (typingTimer) {
        window.clearInterval(typingTimer);
        typingTimer = null;
      }
    }
  }, 20);
};

// 组件挂载后，如果是最后一条AI消息，启动打字效果
onMounted(() => {
  if (props.message.role === 'assistant' && props.isLastMessage) {
    simulateTyping();
  }
});

// 监听 isLastMessage 变化，当变为 true 时可能需要启动打字效果
watch(() => props.isLastMessage, (newValue) => {
  if (newValue && props.message.role === 'assistant') {
    simulateTyping();
  }
});

// 监听消息内容变化，重置打字效果
watch(() => props.message.content, () => {
  if (props.message.role === 'assistant' && props.isLastMessage) {
    simulateTyping();
  }
});

// 组件销毁时清除定时器
onUnmounted(() => {
  if (typingTimer) {
    window.clearInterval(typingTimer);
    typingTimer = null;
  }
});
</script>

<style lang="scss" scoped>
.message-bubble {
  display: flex;
  gap: 12px;
  max-width: 85%;
  animation: message-appear 0.3s ease-out;
  
  &.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
    margin-left: auto;
    
    .message-content-wrapper {
      background-color: rgba(24, 144, 255, 0.15);
      border: 1px solid rgba(24, 144, 255, 0.3);
      border-radius: 12px 2px 12px 12px;
    }
    
    .message-header {
      flex-direction: row-reverse;
    }
  }
  
  &.ai-message {
    align-self: flex-start;
    
    .message-content-wrapper {
      background-color: rgba(0, 255, 170, 0.1);
      border: 1px solid rgba(0, 255, 170, 0.3);
      border-radius: 2px 12px 12px 12px;
    }
  }
  
  .message-avatar {
    align-self: flex-start;
  }
  
  .message-content-wrapper {
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: calc(100% - 60px);
    
    .message-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;
      
      .message-author {
        font-size: 13px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
      }
      
      .message-time {
        font-size: 11px;
        color: rgba(255, 255, 255, 0.5);
      }
    }
    
    .message-content {
      font-size: 14px;
      line-height: 1.5;
      color: rgba(255, 255, 255, 0.8);
      word-break: break-word;
      
      &.typing::after {
        content: '|';
        animation: cursor-blink 0.8s infinite;
        font-weight: bold;
        color: #00ffaa;
      }
      
      .code-block {
        background-color: rgba(0, 0, 0, 0.3);
        padding: 10px;
        border-radius: 5px;
        overflow-x: auto;
        
        pre {
          margin: 0;
          white-space: pre-wrap;
          font-family: 'Courier New', Courier, monospace;
        }
      }
    }
  }
}

:deep(.message-link) {
  color: #00ffaa;
  text-decoration: underline;
  
  &:hover {
    text-decoration: none;
  }
}

@keyframes message-appear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cursor-blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
</style> 