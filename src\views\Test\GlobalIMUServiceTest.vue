<template>
  <div class="global-imu-test">
    <div class="test-header">
      <h2>全局IMU数据服务测试</h2>
      <p>测试全局IMU数据服务的功能和性能</p>
    </div>

    <!-- 服务控制 -->
    <div class="service-control">
      <el-button 
        type="primary" 
        @click="startService"
        :disabled="isConnected"
        :loading="isStarting"
      >
        启动服务
      </el-button>
      <el-button 
        type="danger" 
        @click="stopService"
        :disabled="!isConnected"
      >
        停止服务
      </el-button>
      <el-button @click="refreshData">刷新数据</el-button>
    </div>

    <!-- 连接状态 -->
    <div class="status-section">
      <h3>连接状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">服务状态:</span>
          <span class="value" :class="getStatusClass()">{{ getStatusText() }}</span>
        </div>
        <div class="status-item">
          <span class="label">数据接收:</span>
          <span class="value" :class="{ active: isReceiving }">
            {{ isReceiving ? '正在接收' : '未接收' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">最后更新:</span>
          <span class="value">{{ formatLastUpdate() }}</span>
        </div>
        <div class="status-item">
          <span class="label">错误次数:</span>
          <span class="value error">{{ errorCount }}</span>
        </div>
      </div>
    </div>

    <!-- 实时数据显示 -->
    <div class="data-section">
      <h3>实时IMU数据</h3>
      <div v-if="currentData" class="data-grid">
        <!-- 朝向数据 -->
        <div class="data-card">
          <h4>朝向角度</h4>
          <div class="orientation-display">
            <div class="angle-item">
              <span class="label">Yaw (朝向):</span>
              <span class="value primary">{{ currentData.attitude.yaw.toFixed(1) }}°</span>
            </div>
            <div class="angle-item">
              <span class="label">Roll (横滚):</span>
              <span class="value">{{ currentData.attitude.roll.toFixed(1) }}°</span>
            </div>
            <div class="angle-item">
              <span class="label">Pitch (俯仰):</span>
              <span class="value">{{ currentData.attitude.pitch.toFixed(1) }}°</span>
            </div>
          </div>
          
          <!-- 朝向可视化 -->
          <div class="orientation-visual">
            <div class="compass">
              <div 
                class="compass-arrow" 
                :style="{ transform: `rotate(${currentData.attitude.yaw}deg)` }"
              ></div>
              <div class="compass-text">{{ currentData.attitude.yaw.toFixed(0) }}°</div>
            </div>
          </div>
        </div>

        <!-- 电池数据 -->
        <div class="data-card">
          <h4>电池状态</h4>
          <div class="battery-display">
            <div class="battery-item">
              <span class="label">电量:</span>
              <span class="value" :class="getBatteryClass()">{{ currentData.battery.soc }}%</span>
            </div>
            <div class="battery-item">
              <span class="label">电流:</span>
              <span class="value">{{ currentData.battery.current.toFixed(2) }}A</span>
            </div>
            <div class="battery-item">
              <span class="label">循环次数:</span>
              <span class="value">{{ currentData.battery.cycle }}</span>
            </div>
          </div>
          
          <!-- 电池可视化 -->
          <div class="battery-visual">
            <el-progress 
              :percentage="currentData.battery.soc" 
              :color="getBatteryColor()"
              :stroke-width="12"
            />
          </div>
        </div>

        <!-- 传感器数据 -->
        <div class="data-card">
          <h4>传感器数据</h4>
          <div class="sensor-display">
            <div class="sensor-item">
              <span class="label">温度:</span>
              <span class="value">{{ currentData.sensors.temperatureNtc1.toFixed(1) }}°C</span>
            </div>
            <div class="sensor-item">
              <span class="label">电压:</span>
              <span class="value">{{ currentData.sensors.powerVoltage.toFixed(2) }}V</span>
            </div>
            <div class="sensor-item">
              <span class="label">电机最高温:</span>
              <span class="value" :class="getMotorTempClass()">
                {{ currentData.sensors.motorMaxTemp.toFixed(1) }}°C
              </span>
            </div>
            <div class="sensor-item">
              <span class="label">电机平均温:</span>
              <span class="value">{{ currentData.sensors.motorAvgTemp.toFixed(1) }}°C</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-data">
        <p>暂无IMU数据</p>
        <p>请启动服务开始获取数据</p>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <h3>统计信息</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="label">总更新次数:</span>
          <span class="value">{{ stats.totalUpdates }}</span>
        </div>
        <div class="stat-item">
          <span class="label">更新频率:</span>
          <span class="value">{{ stats.updateRate.toFixed(1) }} Hz</span>
        </div>
        <div class="stat-item">
          <span class="label">连接时长:</span>
          <span class="value">{{ formatDuration(stats.connectionDuration) }}</span>
        </div>
      </div>
    </div>

    <!-- API测试 -->
    <div class="api-test-section">
      <h3>API测试</h3>
      <div class="api-buttons">
        <el-button @click="testGetCurrentData">获取当前数据</el-button>
        <el-button @click="testGetCurrentYaw">获取当前朝向</el-button>
        <el-button @click="testGetBatteryLevel">获取电池电量</el-button>
        <el-button @click="testGetConnectionStatus">获取连接状态</el-button>
        <el-button @click="testGetStats">获取统计信息</el-button>
      </div>
      
      <div class="api-result">
        <h4>API调用结果:</h4>
        <pre>{{ apiResult }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useGlobalIMU, GlobalIMUService } from '@/services/globalIMUService'

// 使用全局IMU服务
const {
  currentData,
  isConnected,
  isReceiving,
  lastUpdate,
  errorCount,
  stats,
  currentYaw,
  batteryLevel,
  start,
  stop
} = useGlobalIMU()

// 本地状态
const isStarting = ref(false)
const apiResult = ref('')

// 获取状态样式类
const getStatusClass = () => {
  if (isReceiving.value) return 'active'
  if (isConnected.value) return 'connected'
  return 'disconnected'
}

// 获取状态文本
const getStatusText = () => {
  if (isReceiving.value) return '数据接收中'
  if (isConnected.value) return '已连接'
  return '未连接'
}

// 获取电池样式类
const getBatteryClass = () => {
  if (!currentData.value) return ''
  const soc = currentData.value.battery.soc
  if (soc < 20) return 'danger'
  if (soc < 50) return 'warning'
  return 'good'
}

// 获取电池颜色
const getBatteryColor = () => {
  if (!currentData.value) return '#909399'
  const soc = currentData.value.battery.soc
  if (soc < 20) return '#f56c6c'
  if (soc < 50) return '#e6a23c'
  return '#67c23a'
}

// 获取电机温度样式类
const getMotorTempClass = () => {
  if (!currentData.value) return ''
  const temp = currentData.value.sensors.motorMaxTemp
  if (temp > 80) return 'danger'
  if (temp > 60) return 'warning'
  return 'good'
}

// 格式化最后更新时间
const formatLastUpdate = () => {
  if (!lastUpdate.value) return '无'
  return new Date(lastUpdate.value).toLocaleTimeString()
}

// 格式化持续时间
const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds.toFixed(0)}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds.toFixed(0)}秒`
}

// 启动服务
const startService = async () => {
  isStarting.value = true
  try {
    const success = await start()
    if (success) {
      ElMessage.success('全局IMU服务启动成功')
    } else {
      ElMessage.error('全局IMU服务启动失败')
    }
  } catch (error) {
    ElMessage.error('启动服务时发生错误')
  } finally {
    isStarting.value = false
  }
}

// 停止服务
const stopService = () => {
  stop()
  ElMessage.info('全局IMU服务已停止')
}

// 刷新数据
const refreshData = () => {
  apiResult.value = ''
  ElMessage.info('数据已刷新')
}

// API测试方法
const testGetCurrentData = () => {
  const data = GlobalIMUService.getCurrentData()
  apiResult.value = JSON.stringify(data, null, 2)
  ElMessage.success('获取当前数据成功')
}

const testGetCurrentYaw = () => {
  const yaw = GlobalIMUService.getCurrentYaw()
  apiResult.value = `当前朝向: ${yaw?.toFixed(1) || 'null'}°`
  ElMessage.success('获取当前朝向成功')
}

const testGetBatteryLevel = () => {
  const battery = GlobalIMUService.getBatteryLevel()
  apiResult.value = `电池电量: ${battery || 'null'}%`
  ElMessage.success('获取电池电量成功')
}

const testGetConnectionStatus = () => {
  const status = GlobalIMUService.getConnectionStatus()
  apiResult.value = JSON.stringify(status, null, 2)
  ElMessage.success('获取连接状态成功')
}

const testGetStats = () => {
  const statsData = GlobalIMUService.getStats()
  apiResult.value = JSON.stringify(statsData, null, 2)
  ElMessage.success('获取统计信息成功')
}

// 生命周期
onMounted(() => {
  console.log('全局IMU服务测试页面已加载')
})

onUnmounted(() => {
  // 页面卸载时不停止服务，因为其他页面可能还在使用
  console.log('全局IMU服务测试页面已卸载')
})
</script>

<style lang="scss" scoped>
.global-imu-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  .test-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      color: #2c3e50;
      margin-bottom: 10px;
    }

    p {
      color: #7f8c8d;
      font-size: 14px;
    }
  }

  .service-control {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    justify-content: center;
  }

  .status-section,
  .data-section,
  .stats-section,
  .api-test-section {
    background: #fff;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;

    h3 {
      margin: 0 0 15px 0;
      color: #2c3e50;
    }
  }

  .status-grid,
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;

    .status-item,
    .stat-item {
      display: flex;
      justify-content: space-between;
      padding: 10px 15px;
      background: #f8f9fa;
      border-radius: 6px;

      .label {
        color: #6c757d;
        font-weight: 500;
      }

      .value {
        font-weight: 600;
        font-family: monospace;

        &.active {
          color: #28a745;
        }

        &.connected {
          color: #17a2b8;
        }

        &.disconnected {
          color: #dc3545;
        }

        &.primary {
          color: #007bff;
          font-size: 16px;
        }

        &.error {
          color: #dc3545;
        }

        &.good {
          color: #28a745;
        }

        &.warning {
          color: #ffc107;
        }

        &.danger {
          color: #dc3545;
        }
      }
    }
  }

  .data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;

    .data-card {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 15px;
      background: #f8f9fa;

      h4 {
        margin: 0 0 15px 0;
        color: #495057;
        font-size: 16px;
      }

      .orientation-display,
      .battery-display,
      .sensor-display {
        display: grid;
        gap: 10px;
        margin-bottom: 15px;

        .angle-item,
        .battery-item,
        .sensor-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 12px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #e9ecef;

          .label {
            color: #6c757d;
            font-size: 13px;
          }

          .value {
            font-weight: 600;
            font-family: monospace;
          }
        }
      }

      .orientation-visual {
        display: flex;
        justify-content: center;
        margin-top: 15px;

        .compass {
          position: relative;
          width: 80px;
          height: 80px;
          border: 2px solid #007bff;
          border-radius: 50%;
          background: #f8f9fa;

          .compass-arrow {
            position: absolute;
            top: 10px;
            left: 50%;
            width: 2px;
            height: 30px;
            background: #dc3545;
            transform-origin: bottom center;
            transition: transform 0.3s ease;
          }

          .compass-text {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            font-weight: 600;
            color: #007bff;
          }
        }
      }

      .battery-visual {
        margin-top: 10px;
      }
    }
  }

  .no-data {
    text-align: center;
    color: #6c757d;
    padding: 40px 0;

    p {
      margin: 5px 0;
    }
  }

  .api-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
  }

  .api-result {
    h4 {
      margin: 0 0 10px 0;
      color: #495057;
    }

    pre {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      font-size: 12px;
      line-height: 1.4;
      max-height: 300px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}
</style>
