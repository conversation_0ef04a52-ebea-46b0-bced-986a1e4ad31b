/**
 * 设备详情弹窗样式文件
 * 提供统一的弹窗样式和响应式设计
 */

// 变量定义
$dialog-bg: rgba(31, 41, 55, 0.95);
$dialog-border: rgba(75, 85, 99, 0.3);
$card-bg: rgba(55, 65, 81, 0.8);
$text-primary: #f3f4f6;
$text-secondary: #e5e7eb;
$text-muted: #9ca3af;
$accent-color: #3b82f6;
$success-color: #10b981;
$warning-color: #f59e0b;
$danger-color: #ef4444;

// 弹窗基础样式
.device-detail-dialog {
  .el-dialog {
    background: $dialog-bg;
    border: 1px solid $dialog-border;
    border-radius: 16px;
    backdrop-filter: blur(12px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);

    // 弹窗头部
    .el-dialog__header {
      background: $card-bg;
      border-bottom: 1px solid $dialog-border;
      border-radius: 16px 16px 0 0;
      padding: 16px 20px;

      .el-dialog__title {
        color: $text-primary;
        font-weight: 600;
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          color: $text-muted;
          font-size: 18px;

          &:hover {
            color: $text-primary;
          }
        }
      }
    }

    // 弹窗内容
    .el-dialog__body {
      padding: 0;
      color: $text-primary;
      max-height: 70vh;
      overflow-y: auto;

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(55, 65, 81, 0.3);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(156, 163, 175, 0.5);
        border-radius: 3px;

        &:hover {
          background: rgba(156, 163, 175, 0.7);
        }
      }
    }

    // 弹窗底部
    .el-dialog__footer {
      background: $card-bg;
      border-top: 1px solid $dialog-border;
      border-radius: 0 0 16px 16px;
      padding: 16px 20px;
    }
  }

  // 弹窗遮罩层
  .el-overlay {
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
  }
}

// 弹窗头部样式
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: $text-primary;

  .device-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .device-icon {
      font-size: 24px;
      color: $accent-color;
      padding: 8px;
      background: rgba(59, 130, 246, 0.1);
      border-radius: 8px;
    }

    .device-details {
      .device-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: $text-secondary;
        line-height: 1.2;
      }

      .device-id {
        margin: 4px 0 0 0;
        font-size: 12px;
        color: $text-muted;
        font-family: 'Courier New', monospace;
      }
    }
  }

  .connection-status {
    display: flex;
    align-items: center;
    gap: 16px;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      background: rgba(55, 65, 81, 0.6);
      border-radius: 20px;
      border: 1px solid $dialog-border;

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6b7280;
        transition: all 0.3s ease;

        &.connected { 
          background: $success-color;
          box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
        }
        
        &.connecting { 
          background: $warning-color;
          animation: pulse 2s infinite;
        }
        
        &.error { 
          background: $danger-color;
          box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
        }
      }

      .status-text {
        font-size: 14px;
        color: $text-secondary;
        font-weight: 500;
      }
    }

    .action-buttons {
      .el-button {
        background: rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.3);
        color: $accent-color;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(59, 130, 246, 0.3);
          border-color: rgba(59, 130, 246, 0.5);
          transform: translateY(-1px);
        }

        &.el-button--warning {
          background: rgba(245, 158, 11, 0.2);
          border-color: rgba(245, 158, 11, 0.3);
          color: $warning-color;

          &:hover {
            background: rgba(245, 158, 11, 0.3);
            border-color: rgba(245, 158, 11, 0.5);
          }
        }
      }
    }
  }
}

// 弹窗内容样式
.dialog-content {
  .connection-prompt,
  .error-prompt {
    padding: 20px;

    .el-alert {
      background: rgba(55, 65, 81, 0.8);
      border: 1px solid $dialog-border;
      border-radius: 8px;

      .el-alert__title {
        color: $text-primary;
      }

      .el-alert__description {
        color: $text-muted;
      }
    }
  }

  .imu-data-container {
    .data-status-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      background: rgba(55, 65, 81, 0.6);
      border-bottom: 1px solid $dialog-border;
      backdrop-filter: blur(8px);

      .status-info {
        display: flex;
        gap: 20px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: $text-muted;
          padding: 4px 8px;
          background: rgba(75, 85, 99, 0.3);
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(75, 85, 99, 0.5);
            color: $text-secondary;
          }

          .el-icon {
            font-size: 14px;
            color: $accent-color;
          }
        }
      }

      .status-actions {
        display: flex;
        gap: 8px;

        .el-button {
          background: rgba(75, 85, 99, 0.3);
          border-color: rgba(75, 85, 99, 0.5);
          color: $text-muted;
          padding: 6px;
          min-width: auto;

          &:hover {
            background: rgba(75, 85, 99, 0.5);
            color: $text-primary;
            transform: scale(1.05);
          }
        }
      }
    }

    .data-quality-section {
      margin: 20px;
      padding: 16px;
      background: $card-bg;
      border-radius: 12px;
      border: 1px solid $dialog-border;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid $dialog-border;

        .section-icon {
          color: $accent-color;
          font-size: 18px;
        }

        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: $text-secondary;
        }
      }

      .quality-content {
        .quality-score {
          margin-bottom: 16px;

          .score-label {
            display: block;
            font-size: 12px;
            color: $text-muted;
            margin-bottom: 8px;
            font-weight: 500;
          }

          .el-progress {
            .el-progress__text {
              color: $text-primary !important;
              font-weight: 600;
            }
          }
        }

        .quality-details {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;

          .quality-issues,
          .quality-recommendations {
            padding: 12px;
            border-radius: 8px;
            background: rgba(75, 85, 99, 0.3);

            h4 {
              margin: 0 0 8px 0;
              font-size: 13px;
              color: $text-secondary;
              font-weight: 600;
            }

            ul {
              margin: 0;
              padding-left: 16px;
              font-size: 12px;
              line-height: 1.4;

              li {
                margin-bottom: 4px;
                position: relative;

                &::marker {
                  color: $accent-color;
                }
              }
            }
          }

          .quality-issues {
            border-left: 3px solid $danger-color;

            ul {
              color: #fca5a5;
            }
          }

          .quality-recommendations {
            border-left: 3px solid $success-color;

            ul {
              color: #93c5fd;
            }
          }
        }
      }
    }
  }
}

// 弹窗底部样式
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-info {
    .recording-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: $danger-color;
      font-weight: 500;
      padding: 6px 12px;
      background: rgba(239, 68, 68, 0.1);
      border-radius: 16px;
      border: 1px solid rgba(239, 68, 68, 0.3);

      .recording-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: $danger-color;
        animation: pulse 2s infinite;
      }
    }
  }

  .footer-actions {
    display: flex;
    gap: 12px;

    .el-button {
      background: rgba(75, 85, 99, 0.3);
      border-color: rgba(75, 85, 99, 0.5);
      color: $text-muted;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(75, 85, 99, 0.5);
        color: $text-primary;
        transform: translateY(-1px);
      }

      &.el-button--primary {
        background: rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.3);
        color: $accent-color;

        &:hover {
          background: rgba(59, 130, 246, 0.3);
          border-color: rgba(59, 130, 246, 0.5);
        }
      }
    }
  }
}

// 动画定义
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .device-detail-dialog .el-dialog {
    width: 95% !important;
    margin: 20px auto;
  }
}

@media (max-width: 768px) {
  .device-detail-dialog .el-dialog {
    width: 98% !important;
    margin: 10px auto;
    border-radius: 12px;

    .el-dialog__header {
      padding: 12px 16px;
      border-radius: 12px 12px 0 0;
    }

    .el-dialog__body {
      max-height: 60vh;
    }

    .el-dialog__footer {
      padding: 12px 16px;
      border-radius: 0 0 12px 12px;
    }
  }

  .dialog-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;

    .device-info {
      width: 100%;

      .device-icon {
        font-size: 20px;
        padding: 6px;
      }

      .device-details {
        .device-name {
          font-size: 16px;
        }
      }
    }

    .connection-status {
      width: 100%;
      justify-content: space-between;

      .status-indicator {
        padding: 4px 8px;
        
        .status-text {
          font-size: 12px;
        }
      }

      .action-buttons {
        .el-button {
          font-size: 12px;
          padding: 4px 8px;
        }
      }
    }
  }

  .dialog-content {
    .imu-data-container {
      .data-status-bar {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start !important;
        padding: 12px 16px;

        .status-info {
          flex-wrap: wrap;
          gap: 8px !important;
          width: 100%;

          .info-item {
            font-size: 11px;
            padding: 3px 6px;
          }
        }

        .status-actions {
          width: 100%;
          justify-content: center;
        }
      }

      .data-quality-section {
        margin: 16px;
        padding: 12px;

        .quality-content {
          .quality-details {
            grid-template-columns: 1fr !important;
            gap: 12px;

            .quality-issues,
            .quality-recommendations {
              padding: 8px;

              h4 {
                font-size: 12px;
              }

              ul {
                font-size: 11px;
                padding-left: 12px;
              }
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .footer-info {
      text-align: center;

      .recording-indicator {
        justify-content: center;
        font-size: 11px;
        padding: 4px 8px;
      }
    }

    .footer-actions {
      justify-content: center;

      .el-button {
        flex: 1;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .device-detail-dialog .el-dialog {
    width: 100% !important;
    margin: 0;
    border-radius: 0;
    height: 100vh;

    .el-dialog__header {
      border-radius: 0;
    }

    .el-dialog__body {
      max-height: calc(100vh - 120px);
    }

    .el-dialog__footer {
      border-radius: 0;
    }
  }

  .dialog-content {
    .connection-prompt,
    .error-prompt {
      padding: 16px;
    }

    .imu-data-container {
      .data-quality-section {
        margin: 12px;
        padding: 10px;
      }
    }
  }
}
