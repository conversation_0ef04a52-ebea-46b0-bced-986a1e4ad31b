<!-- 
  PestDistributionMap.vue
  虫害分布地图组件，用于可视化展示虫害在不同农田区域的分布情况
  支持热力图、标记点等多种展示方式
-->
<template>
  <div class="pest-distribution-map">
    <div class="map-container" ref="mapContainer"></div>
    
    <div class="map-controls">
      <div class="control-item">
        <span>显示方式:</span>
        <el-radio-group v-model="displayMode" size="small">
          <el-radio-button label="heatmap">热力图</el-radio-button>
          <el-radio-button label="marker">标记点</el-radio-button>
        </el-radio-group>
      </div>
      
      <div class="control-item">
        <span>数据类型:</span>
        <el-select v-model="dataType" size="small" style="width: 120px">
          <el-option label="虫害数量" value="count" />
          <el-option label="危害程度" value="damage" />
          <el-option label="发现频率" value="frequency" />
        </el-select>
      </div>
    </div>
    
    <div class="map-legend">
      <div class="legend-title">图例</div>
      <div class="legend-items">
        <div class="legend-item" v-for="item in legendItems" :key="item.value">
          <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
          <div class="legend-label">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  mapData: {
    type: Array,
    default: () => []
  },
  defaultMode: {
    type: String,
    default: 'heatmap'
  }
});

const mapContainer = ref(null);
const displayMode = ref(props.defaultMode);
const dataType = ref('count');
let mapChart = null;

// 图例项目
const legendItems = reactive([
  { value: 'high', label: '高密度区域', color: '#ef4444' },
  { value: 'medium', label: '中密度区域', color: '#f59e0b' },
  { value: 'low', label: '低密度区域', color: '#10b981' }
]);

// 模拟的GeoJSON数据
const geoData = {
  "type": "FeatureCollection",
  "features": [
    // 这里应该是真实的农田GeoJSON数据，目前使用占位符
    { "type": "Feature", "properties": { "name": "北部农田" }, "geometry": { "type": "Polygon", "coordinates": [[[0, 0], [100, 0], [100, 100], [0, 100], [0, 0]]] } },
    { "type": "Feature", "properties": { "name": "南部农田" }, "geometry": { "type": "Polygon", "coordinates": [[[0, -100], [100, -100], [100, 0], [0, 0], [0, -100]]] } },
    { "type": "Feature", "properties": { "name": "东部农田" }, "geometry": { "type": "Polygon", "coordinates": [[[100, -100], [200, -100], [200, 100], [100, 100], [100, -100]]] } },
    { "type": "Feature", "properties": { "name": "西部农田" }, "geometry": { "type": "Polygon", "coordinates": [[[-100, -100], [0, -100], [0, 100], [-100, 100], [-100, -100]]] } },
    { "type": "Feature", "properties": { "name": "中央农田" }, "geometry": { "type": "Polygon", "coordinates": [[[-50, -50], [50, -50], [50, 50], [-50, 50], [-50, -50]]] } }
  ]
};

// 模拟的热力图数据
const getHeatmapData = () => {
  const data = [];
  for (let i = 0; i < 50; i++) {
    data.push([
      Math.random() * 300 - 100,
      Math.random() * 200 - 100,
      Math.random() * 100
    ]);
  }
  return data;
};

// 模拟的标记点数据
const getMarkerData = () => {
  const data = [];
  const pestTypes = ['稻飞虱', '玉米螟', '棉铃虫', '稻纵卷叶螟', '小麦蚜虫'];
  
  for (let i = 0; i < 30; i++) {
    data.push({
      name: pestTypes[Math.floor(Math.random() * pestTypes.length)],
      value: [
        Math.random() * 300 - 100,
        Math.random() * 200 - 100,
        Math.floor(Math.random() * 1000)
      ],
      symbolSize: 10 + Math.random() * 20,
      itemStyle: {
        color: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'][Math.floor(Math.random() * 5)]
      }
    });
  }
  
  return data;
};

// 初始化地图
const initMap = () => {
  if (mapContainer.value) {
    mapChart = echarts.init(mapContainer.value);
    
    // 基础配置
    const option = {
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      title: {
        text: '虫害分布地图',
        left: 'center',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          if (displayMode.value === 'marker') {
            return `${params.name}<br/>数量: ${params.value[2]}`;
          } else {
            return `位置: (${params.value[0].toFixed(0)}, ${params.value[1].toFixed(0)})<br/>值: ${params.value[2].toFixed(0)}`;
          }
        }
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        inRange: {
          color: ['#10b981', '#f59e0b', '#ef4444']
        },
        textStyle: {
          color: '#9ca3af'
        }
      },
      geo: {
        map: 'farmland',
        roam: true,
        zoom: 1.2,
        itemStyle: {
          areaColor: '#1f2937',
          borderColor: '#3b4863',
          borderWidth: 1
        },
        emphasis: {
          itemStyle: {
            areaColor: '#374151'
          },
          label: {
            color: '#e5e7eb'
          }
        }
      }
    };
    
    // 注册自定义地图
    echarts.registerMap('farmland', geoData);
    
    // 更新配置
    updateMapConfig();
  }
};

// 更新地图配置
const updateMapConfig = () => {
  if (!mapChart) return;
  
  let option = {};
  
  if (displayMode.value === 'heatmap') {
    option = {
      series: [
        {
          name: '虫害密度',
          type: 'heatmap',
          coordinateSystem: 'geo',
          data: getHeatmapData(),
          pointSize: 8,
          blurSize: 12
        }
      ]
    };
  } else {
    option = {
      series: [
        {
          name: '虫害分布',
          type: 'scatter',
          coordinateSystem: 'geo',
          data: getMarkerData(),
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: true,
              position: 'top',
              formatter: '{b}'
            }
          }
        }
      ]
    };
  }
  
  mapChart.setOption(option);
};

// 监听显示模式变化
watch(displayMode, () => {
  updateMapConfig();
});

// 监听数据类型变化
watch(dataType, () => {
  updateMapConfig();
});

onMounted(() => {
  initMap();
  
  window.addEventListener('resize', () => {
    mapChart?.resize();
  });
});

onUnmounted(() => {
  mapChart?.dispose();
  window.removeEventListener('resize', () => {});
});
</script>

<style scoped>
.pest-distribution-map {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.map-container {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(31, 41, 55, 0.8);
  border-radius: 8px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #e5e7eb;
  font-size: 14px;
}

.map-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(31, 41, 55, 0.8);
  border-radius: 8px;
  padding: 10px;
}

.legend-title {
  color: #e5e7eb;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
}

.legend-label {
  color: #d1d5db;
  font-size: 12px;
}
</style> 