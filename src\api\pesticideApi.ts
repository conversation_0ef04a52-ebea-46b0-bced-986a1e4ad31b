import axios from 'axios'
import type {
  PesticideLedger,
  PesticidePurchase,
  PesticideUsage,
  RatioCalculationParams,
  RatioRecommendation,
  UAVSprayingParameters,
  ElectronicFence,
  MonitoringPoint,
  ComplianceCheckItem,
  ComplianceViolation,
  FarmArea
} from '@/types/pesticide'
import { SIMULATION_CONFIG } from '@/config/business'

const API_URL = import.meta.env.VITE_API_BASE_URL || '/api'

/**
 * 农药台账相关接口
 */
export const pesticideLedgerApi = {
  // 获取所有农药列表
  getPesticides: async () => {
    try {
      const response = await axios.get(`${API_URL}/pesticides`)
      return response.data
    } catch (error) {
      console.error('获取农药列表失败:', error)
      return []
    }
  },

  // 获取单个农药详情
  getPesticideById: async (id: string) => {
    try {
      const response = await axios.get(`${API_URL}/pesticides/${id}`)
      return response.data
    } catch (error) {
      console.error(`获取农药详情失败 [ID: ${id}]:`, error)
      return null
    }
  },

  // 添加新农药
  addPesticide: async (pesticide: Omit<PesticideLedger, 'id'>) => {
    try {
      const response = await axios.post(`${API_URL}/pesticides`, pesticide)
      return response.data
    } catch (error) {
      console.error('添加农药失败:', error)
      throw error
    }
  },

  // 更新农药信息
  updatePesticide: async (id: string, pesticide: Partial<PesticideLedger>) => {
    try {
      const response = await axios.put(`${API_URL}/pesticides/${id}`, pesticide)
      return response.data
    } catch (error) {
      console.error(`更新农药失败 [ID: ${id}]:`, error)
      throw error
    }
  },

  // 删除农药
  deletePesticide: async (id: string) => {
    try {
      await axios.delete(`${API_URL}/pesticides/${id}`)
      return true
    } catch (error) {
      console.error(`删除农药失败 [ID: ${id}]:`, error)
      throw error
    }
  },

  // 获取农药采购记录
  getPurchaseRecords: async (pesticideId?: string) => {
    try {
      const url = pesticideId
        ? `${API_URL}/pesticides/purchases?pesticideId=${pesticideId}`
        : `${API_URL}/pesticides/purchases`
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('获取采购记录失败:', error)
      return []
    }
  },

  // 添加采购记录
  addPurchaseRecord: async (purchase: Omit<PesticidePurchase, 'id'>) => {
    try {
      const response = await axios.post(`${API_URL}/pesticides/purchases`, purchase)
      return response.data
    } catch (error) {
      console.error('添加采购记录失败:', error)
      throw error
    }
  },

  // 获取农药使用记录
  getUsageRecords: async (pesticideId?: string) => {
    try {
      const url = pesticideId
        ? `${API_URL}/pesticides/usages?pesticideId=${pesticideId}`
        : `${API_URL}/pesticides/usages`
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('获取使用记录失败:', error)
      return []
    }
  },

  // 添加使用记录
  addUsageRecord: async (usage: Omit<PesticideUsage, 'id'>) => {
    try {
      const response = await axios.post(`${API_URL}/pesticides/usages`, usage)
      return response.data
    } catch (error) {
      console.error('添加使用记录失败:', error)
      throw error
    }
  },

  // 获取库存预警
  getLowStockAlerts: async () => {
    try {
      const response = await axios.get(`${API_URL}/pesticides/low-stock-alerts`)
      return response.data
    } catch (error) {
      console.error('获取库存预警失败:', error)
      return []
    }
  }
}

/**
 * 智能配比计算器相关接口
 */
export const ratioCalculatorApi = {
  // 获取作物类型列表
  getCropTypes: async () => {
    try {
      const response = await axios.get(`${API_URL}/ratio-calculator/crop-types`)
      return response.data
    } catch (error) {
      console.error('获取作物类型失败:', error)
      return []
    }
  },

  // 获取病虫害类型列表
  getPestTypes: async (cropType?: string) => {
    try {
      const url = cropType
        ? `${API_URL}/ratio-calculator/pest-types?cropType=${cropType}`
        : `${API_URL}/ratio-calculator/pest-types`
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('获取病虫害类型失败:', error)
      return []
    }
  },

  // 计算推荐配比
  calculateRatio: async (params: RatioCalculationParams) => {
    try {
      const response = await axios.post(`${API_URL}/ratio-calculator/calculate`, params)
      return response.data as RatioRecommendation[]
    } catch (error) {
      console.error('计算配比失败:', error)
      return []
    }
  },

  // 获取历史配比推荐
  getHistoricalRecommendations: async () => {
    try {
      const response = await axios.get(`${API_URL}/ratio-calculator/history`)
      return response.data
    } catch (error) {
      console.error('获取历史推荐失败:', error)
      return []
    }
  },

  // 收藏推荐方案
  saveFavoriteRecommendation: async (recommendationId: string) => {
    try {
      const response = await axios.post(`${API_URL}/ratio-calculator/favorites/${recommendationId}`)
      return response.data
    } catch (error) {
      console.error('收藏推荐方案失败:', error)
      throw error
    }
  }
}

/**
 * 农药管理相关API接口
 */

// 模拟API延迟
const delay = (ms: number = SIMULATION_CONFIG.API_DELAY) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 无人机喷洒API
 */
export const uavSprayingApi = {
  /**
   * 获取无人机型号列表
   */
  async getUAVModels() {
    // 模拟API请求
    await delay(500);
    return [
      { label: 'DJI Agras T30', value: 'dji_agras_t30' },
      { label: 'DJI Agras T20P', value: 'dji_agras_t20p' },
      { label: 'DJI Agras T10', value: 'dji_agras_t10' },
      { label: 'XAG P100', value: 'xag_p100' },
      { label: 'XAG P50', value: 'xag_p50' },
      { label: 'XAG P40', value: 'xag_p40' }
    ];
  },

  /**
   * 获取农田区域列表
   */
  async getFarmAreas(): Promise<FarmArea[]> {
    // 模拟API请求
    await delay(800);
    return [
      { id: 'area1', name: '东部水稻田', area: 120, crops: '水稻', location: '东部' },
      { id: 'area2', name: '西部玉米田', area: 85, crops: '玉米', location: '西部' },
      { id: 'area3', name: '南部蔬菜地', area: 45, crops: '蔬菜', location: '南部' },
      { id: 'area4', name: '北部果园', area: 60, crops: '苹果', location: '北部' }
    ];
  },

  /**
   * 保存无人机喷洒配置
   * @param config 喷洒配置
   */
  async saveConfiguration(config: Omit<UAVSprayingParameters, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>): Promise<UAVSprayingParameters> {
    // 模拟API请求
    await delay(1200);

    // 模拟服务器返回的数据
    return {
      ...config,
      id: 'config_' + Date.now(),
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'admin'
    };
  },

  /**
   * 获取喷洒配置详情
   * @param id 配置ID
   */
  async getConfiguration(id: string): Promise<UAVSprayingParameters> {
    // 模拟API请求
    await delay(600);

    // 模拟服务器返回的数据
    return {
      id,
      uavModel: 'dji_agras_t30',
      flightMode: 'semi_auto',
      farmAreaId: 'area1',
      farmAreaName: '东部水稻田',
      flightHeight: 2.0,
      flightSpeed: 3.0,
      sprayingQuantity: 15,
      dropletSize: 'medium',
      windSpeed: 0,
      windDirection: 'north',
      safetyDistanceToObstacles: 5,
      safetyDistanceToNonTargetAreas: 10,
      emergencyBraking: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'admin'
    };
  }
}

/**
 * 电子围栏相关接口
 */
export const fenceApi = {
  // 获取所有电子围栏
  getFences: async () => {
    try {
      const response = await axios.get(`${API_URL}/electronic-fences`)
      // 检查后端返回的数据是否为数组格式
      if (response.data && !Array.isArray(response.data)) {
        console.warn('后端返回的围栏数据不是数组格式:', response.data)
        // 如果不是数组，尝试处理常见的API返回格式
        if (response.data.data && Array.isArray(response.data.data)) {
          return response.data.data
        } else if (response.data.fences && Array.isArray(response.data.fences)) {
          return response.data.fences
        } else if (response.data.items && Array.isArray(response.data.items)) {
          return response.data.items
        } else {
          console.error('无法从API响应中提取围栏数组')
          return []
        }
      }

      return response.data
    } catch (error) {
      console.error('获取电子围栏列表失败:', error)

      // 开发环境下使用模拟数据
      if (import.meta.env.DEV) {
        console.info('使用模拟围栏数据')
        return [
          {
            id: '1',
            name: '东部水稻田禁喷区',
            description: '东部水稻田周边的生态保护区，禁止喷洒农药',
            coordinates: [
              { lat: 39.90923, lng: 116.397428 },
              { lat: 39.90923, lng: 116.407428 },
              { lat: 39.91923, lng: 116.407428 },
              { lat: 39.91923, lng: 116.397428 }
            ],
            fenceType: 'no_spray',
            color: '#ff5500',
            isActive: true,
            startDate: '2023-01-01',
            endDate: '2023-12-31',
            createdBy: 'admin',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z'
          },
          {
            id: '2',
            name: '西北角限制喷洒区',
            description: '西北角靠近村庄区域，限制高浓度农药喷洒',
            coordinates: [
              { lat: 39.92923, lng: 116.387428 },
              { lat: 39.92923, lng: 116.397428 },
              { lat: 39.93923, lng: 116.397428 },
              { lat: 39.93923, lng: 116.387428 }
            ],
            fenceType: 'limited_spray',
            color: '#ffaa00',
            isActive: true,
            startDate: '',
            endDate: '',
            createdBy: 'admin',
            createdAt: '2023-02-15T00:00:00Z',
            updatedAt: '2023-02-15T00:00:00Z'
          },
          {
            id: '3',
            name: '南部禁入区',
            description: '南部高压设备区域，禁止人员和设备进入',
            coordinates: [
              { lat: 39.89923, lng: 116.397428 },
              { lat: 39.89923, lng: 116.407428 },
              { lat: 39.88923, lng: 116.407428 },
              { lat: 39.88923, lng: 116.397428 }
            ],
            fenceType: 'no_entry',
            color: '#ff0000',
            isActive: false,
            startDate: '2023-03-01',
            endDate: '2023-06-30',
            createdBy: 'admin',
            createdAt: '2023-03-01T00:00:00Z',
            updatedAt: '2023-03-01T00:00:00Z'
          }
        ]
      }

      return []
    }
  },

  // 获取单个电子围栏详情
  getFenceById: async (id: string) => {
    try {
      const response = await axios.get(`${API_URL}/electronic-fences/${id}`)
      return response.data
    } catch (error) {
      console.error(`获取电子围栏详情失败 [ID: ${id}]:`, error)

      // 开发环境下使用模拟数据
      if (import.meta.env.DEV) {
        console.info(`使用ID为${id}的模拟围栏数据`)
        const mockFences = [
          {
            id: '1',
            name: '东部水稻田禁喷区',
            description: '东部水稻田周边的生态保护区，禁止喷洒农药',
            coordinates: [
              { lat: 39.90923, lng: 116.397428 },
              { lat: 39.90923, lng: 116.407428 },
              { lat: 39.91923, lng: 116.407428 },
              { lat: 39.91923, lng: 116.397428 }
            ],
            fenceType: 'no_spray',
            color: '#ff5500',
            isActive: true,
            startDate: '2023-01-01',
            endDate: '2023-12-31',
            createdBy: 'admin',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z'
          },
          {
            id: '2',
            name: '西北角限制喷洒区',
            description: '西北角靠近村庄区域，限制高浓度农药喷洒',
            coordinates: [
              { lat: 39.92923, lng: 116.387428 },
              { lat: 39.92923, lng: 116.397428 },
              { lat: 39.93923, lng: 116.397428 },
              { lat: 39.93923, lng: 116.387428 }
            ],
            fenceType: 'limited_spray',
            color: '#ffaa00',
            isActive: true,
            startDate: '',
            endDate: '',
            createdBy: 'admin',
            createdAt: '2023-02-15T00:00:00Z',
            updatedAt: '2023-02-15T00:00:00Z'
          },
          {
            id: '3',
            name: '南部禁入区',
            description: '南部高压设备区域，禁止人员和设备进入',
            coordinates: [
              { lat: 39.89923, lng: 116.397428 },
              { lat: 39.89923, lng: 116.407428 },
              { lat: 39.88923, lng: 116.407428 },
              { lat: 39.88923, lng: 116.397428 }
            ],
            fenceType: 'no_entry',
            color: '#ff0000',
            isActive: false,
            startDate: '2023-03-01',
            endDate: '2023-06-30',
            createdBy: 'admin',
            createdAt: '2023-03-01T00:00:00Z',
            updatedAt: '2023-03-01T00:00:00Z'
          }
        ];

        const fence = mockFences.find(f => f.id === id);
        return fence || null;
      }

      return null
    }
  },

  // 创建电子围栏
  createFence: async (fence: Omit<ElectronicFence, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await axios.post(`${API_URL}/electronic-fences`, fence)
      return response.data
    } catch (error) {
      console.error('创建电子围栏失败:', error)

      // 开发环境下模拟创建成功
      if (import.meta.env.DEV) {
        console.info('模拟创建围栏成功')
        return {
          ...fence,
          id: 'new_' + Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      }

      throw error
    }
  },

  // 更新电子围栏
  updateFence: async (id: string, fence: Partial<ElectronicFence>) => {
    try {
      const response = await axios.put(`${API_URL}/electronic-fences/${id}`, fence)
      return response.data
    } catch (error) {
      console.error(`更新电子围栏失败 [ID: ${id}]:`, error)

      // 开发环境下模拟更新成功
      if (import.meta.env.DEV) {
        console.info(`模拟更新ID为${id}的围栏成功`)
        return {
          id,
          ...fence,
          updatedAt: new Date().toISOString()
        };
      }

      throw error
    }
  },

  // 删除电子围栏
  deleteFence: async (id: string) => {
    try {
      await axios.delete(`${API_URL}/electronic-fences/${id}`)
      return true
    } catch (error) {
      console.error(`删除电子围栏失败 [ID: ${id}]:`, error)

      // 开发环境下模拟删除成功
      if (import.meta.env.DEV) {
        console.info(`模拟删除ID为${id}的围栏成功`)
        return true;
      }

      throw error
    }
  },

  // 检查点位是否在围栏内
  checkPointInFence: async (lat: number, lng: number) => {
    try {
      const response = await axios.get(`${API_URL}/electronic-fences/check-point`, {
        params: { lat, lng }
      })
      return response.data
    } catch (error) {
      console.error('检查点位失败:', error)
      return { inFence: false, fences: [] }
    }
  }
}

/**
 * 农药残留监测接口
 */
export const residueMonitoringApi = {
  // 获取监测总览数据
  getMonitoringOverview: async () => {
    try {
      const response = await axios.get(`${API_URL}/residue-monitoring/overview`)
      return response.data
    } catch (error) {
      console.error('获取监测总览失败:', error)
      return null
    }
  },

  // 获取所有监测点
  getMonitoringPoints: async () => {
    try {
      const response = await axios.get(`${API_URL}/residue-monitoring/points`)
      return response.data
    } catch (error) {
      console.error('获取监测点失败:', error)
      return []
    }
  },

  // 获取实时监测数据
  getRealTimeData: async () => {
    try {
      const response = await axios.get(`${API_URL}/residue-monitoring/real-time`)
      return response.data
    } catch (error) {
      console.error('获取实时监测数据失败:', error)
      return []
    }
  },

  // 获取历史监测数据
  getHistoricalData: async (startDate: string, endDate: string) => {
    try {
      const response = await axios.get(
        `${API_URL}/residue-monitoring/historical?startDate=${startDate}&endDate=${endDate}`
      )
      return response.data
    } catch (error) {
      console.error('获取历史监测数据失败:', error)
      return []
    }
  },

  // 获取异常预警数据
  getAlerts: async (level?: string) => {
    try {
      const url = level
        ? `${API_URL}/residue-monitoring/alerts?level=${level}`
        : `${API_URL}/residue-monitoring/alerts`
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('获取异常预警数据失败:', error)
      return []
    }
  },

  // 标记预警为已处理
  markAlertAsHandled: async (alertId: string, handlingInfo: { person: string, remarks?: string }) => {
    try {
      const response = await axios.put(`${API_URL}/residue-monitoring/alerts/${alertId}/handle`, handlingInfo)
      return response.data
    } catch (error) {
      console.error(`标记预警为已处理失败 [ID: ${alertId}]:`, error)
      throw error
    }
  },

  // 获取数据同步状态
  getSynchronizationStatus: async () => {
    try {
      const response = await axios.get(`${API_URL}/residue-monitoring/sync-status`)
      return response.data
    } catch (error) {
      console.error('获取数据同步状态失败:', error)
      return { status: 'error', lastSync: null, message: '获取同步状态失败' }
    }
  }
}

/**
 * 环保合规性检查相关接口
 */
export const environmentalComplianceApi = {
  // 获取合规性概览数据
  async getComplianceOverview() {
    try {
      const response = await axios.get(`${API_URL}/environmental-compliance/overview`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch compliance overview:', error)
      // 返回模拟数据用于开发
      return {
        complianceRate: 75,
        passedChecks: 18,
        pendingIssues: 4,
        criticalIssues: 1
      }
    }
  },

  // 获取检查结果列表
  async getCheckResults() {
    try {
      const response = await axios.get(`${API_URL}/environmental-compliance/check-results`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch check results:', error)
      // 返回模拟数据用于开发
      return [
        {
          id: '1',
          checkTitle: '农药储存场所通风设施',
          category: '储存安全',
          requirementLevel: 'mandatory',
          status: 'passed',
          lastCheckedAt: '2023-11-15T08:30:00Z',
          referenceStandard: '农药管理条例第25条',
          verificationMethod: '现场检查',
          description: '农药储存场所必须配备通风设施，保持空气流通，防止有害气体积聚。'
        },
        {
          id: '2',
          checkTitle: '农药包装废弃物回收',
          category: '废弃物处理',
          requirementLevel: 'mandatory',
          status: 'warning',
          lastCheckedAt: '2023-11-10T14:20:00Z',
          referenceStandard: '农药包装废弃物回收处理管理办法第8条',
          verificationMethod: '记录审查',
          description: '农药使用者应当将农药包装废弃物交由专门机构或者组织回收处理。'
        },
        {
          id: '3',
          checkTitle: '高毒农药使用记录',
          category: '使用管理',
          requirementLevel: 'mandatory',
          status: 'failed',
          lastCheckedAt: '2023-11-12T10:45:00Z',
          referenceStandard: '农药管理条例第33条',
          verificationMethod: '文档审查',
          description: '使用高毒农药应当详细记录使用时间、地点、对象、面积、用量、施药人员等信息。'
        },
        {
          id: '4',
          checkTitle: '农药喷洒缓冲区设置',
          category: '施用安全',
          requirementLevel: 'recommended',
          status: 'not_checked',
          lastCheckedAt: '2023-10-20T09:15:00Z',
          referenceStandard: '农药使用安全规范',
          verificationMethod: '现场检查',
          description: '在水源地、河流湖泊周边施用农药时，应设置适当的缓冲区，减少农药漂移和径流。'
        }
      ]
    }
  },

  // 获取违规记录列表
  async getViolations() {
    try {
      const response = await axios.get(`${API_URL}/environmental-compliance/violations`)
      return response.data
    } catch (error) {
      console.error('Failed to fetch violations:', error)
      // 返回模拟数据用于开发
      return [
        {
          id: '1',
          checkItemId: '2',
          severity: 'moderate',
          violationDate: '2023-11-10T14:20:00Z',
          description: '发现部分农药包装废弃物未按规定回收，直接丢弃在普通垃圾中。',
          correctiveAction: '建立农药包装废弃物专门回收点，并对相关人员进行培训。',
          actionStatus: 'in_progress'
        },
        {
          id: '2',
          checkItemId: '3',
          severity: 'major',
          violationDate: '2023-11-12T10:45:00Z',
          description: '高毒农药使用记录不完整，缺少使用时间、地点等关键信息。',
          correctiveAction: '完善农药使用记录系统，确保每次使用高毒农药都有完整记录。',
          actionStatus: 'pending'
        }
      ]
    }
  },

  // 获取历史记录
  getHistoricalRecords: async (startDate: string, endDate: string) => {
    try {
      const response = await axios.get(
        `${API_URL}/environmental-compliance/historical?startDate=${startDate}&endDate=${endDate}`
      )
      return response.data
    } catch (error) {
      console.error('获取历史记录失败:', error)
      return []
    }
  },

  // 获取整改进度
  getRemediationProgress: async () => {
    try {
      const response = await axios.get(`${API_URL}/environmental-compliance/remediation-progress`)
      return response.data
    } catch (error) {
      console.error('获取整改进度失败:', error)
      return []
    }
  },

  // 更新整改进度
  async updateRemediationProgress(violationId: string, progress: number) {
    try {
      const response = await axios.put(`${API_URL}/environmental-compliance/violations/${violationId}/progress`, {
        progress
      })
      return response.data
    } catch (error) {
      console.error('Failed to update remediation progress:', error)
      // 模拟成功响应
      return { success: true }
    }
  },

  // 获取法规知识库列表
  getRegulatoryKnowledgeBase: async (query?: string) => {
    try {
      const url = query
        ? `${API_URL}/environmental-compliance/regulations?query=${query}`
        : `${API_URL}/environmental-compliance/regulations`
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('获取法规知识库失败:', error)
      return []
    }
  }
}

/**
 * 农药残留监测相关接口
 */
export const monitoringApi = {
  // 获取所有监测点
  getMonitoringPoints: async () => {
    try {
      const response = await axios.get(`${API_URL}/monitoring/points`)
      return response.data
    } catch (error) {
      console.error('获取监测点列表失败:', error)
      return []
    }
  },

  // 获取单个监测点详情
  getMonitoringPointById: async (id: string) => {
    try {
      const response = await axios.get(`${API_URL}/monitoring/points/${id}`)
      return response.data
    } catch (error) {
      console.error(`获取监测点详情失败 [ID: ${id}]:`, error)
      return null
    }
  },

  // 创建监测点
  createMonitoringPoint: async (point: Omit<MonitoringPoint, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await axios.post(`${API_URL}/monitoring/points`, point)
      return response.data
    } catch (error) {
      console.error('创建监测点失败:', error)
      throw error
    }
  },

  // 更新监测点
  updateMonitoringPoint: async (id: string, point: Partial<MonitoringPoint>) => {
    try {
      const response = await axios.put(`${API_URL}/monitoring/points/${id}`, point)
      return response.data
    } catch (error) {
      console.error(`更新监测点失败 [ID: ${id}]:`, error)
      throw error
    }
  },

  // 删除监测点
  deleteMonitoringPoint: async (id: string) => {
    try {
      await axios.delete(`${API_URL}/monitoring/points/${id}`)
      return true
    } catch (error) {
      console.error(`删除监测点失败 [ID: ${id}]:`, error)
      throw error
    }
  },

  // 记录新样本数据
  recordSample: async (pointId: string, value: number) => {
    try {
      const response = await axios.post(`${API_URL}/monitoring/points/${pointId}/samples`, { value })
      return response.data
    } catch (error) {
      console.error(`记录样本数据失败 [监测点ID: ${pointId}]:`, error)
      throw error
    }
  },

  // 获取监测点的历史数据
  getHistoricalData: async (pointId: string, startDate?: string, endDate?: string) => {
    try {
      const url = `${API_URL}/monitoring/points/${pointId}/history`

      const params: Record<string, string> = {}
      if (startDate) params.startDate = startDate
      if (endDate) params.endDate = endDate

      const response = await axios.get(url, { params })
      return response.data
    } catch (error) {
      console.error(`获取历史数据失败 [监测点ID: ${pointId}]:`, error)
      return []
    }
  }
}
