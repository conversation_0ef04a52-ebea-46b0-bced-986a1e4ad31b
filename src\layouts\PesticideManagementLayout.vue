<template>
  <BaseLayout
    title="精准施药管理系统"
    theme="pesticideManagement"
    themeColor="#10b981"
    moduleIcon="Cpu"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="农药电子台账"
        icon="Notebook"
        route="/pesticide-management/ledger"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="智能配比计算器"
        icon="SetUp"
        route="/pesticide-management/smart-ratio"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="无人机喷洒参数配置"
        icon="Connection"
        route="/pesticide-management/uav-config"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="施药区域电子围栏"
        icon="Location"
        route="/pesticide-management/electronic-fence"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="农药残留监测接口"
        icon="Monitor"
        route="/pesticide-management/residue-monitoring"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="环保合规性检查"
        icon="DocumentChecked"
        route="/pesticide-management/environmental-compliance"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
    </template>

    <template #breadcrumb>
      <div class="breadcrumb">
        <span class="path-item">精准施药管理系统</span>
        <el-icon><ArrowRight /></el-icon>
        <span class="current-path">{{ currentRouteTitle }}</span>
      </div>
    </template>

    <template #header-actions>
      <div class="pesticide-usage">
        <div class="usage-item warning">
          <el-icon><Warning /></el-icon>
          <span>今日农药使用量: </span>
          <span class="usage-value">12.5kg</span>
          <span class="usage-hint">(接近警戒值)</span>
        </div>
      </div>

      <TechActionButton
        type="primary"
        size="small"
        icon="Printer"
        text="导出用药记录"
        @click="exportRecord"
      />
    </template>

    <router-view />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Notebook,
  SetUp,
  Connection,
  Location,
  Monitor,
  DocumentChecked,
  HomeFilled,
  Cpu,
  ArrowRight,
  Warning,
  Printer
} from '@element-plus/icons-vue'
import { ElMessage, ElNotification } from 'element-plus'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'
import { getModuleTheme } from './components/layoutConfig'

const route = useRoute()
const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 路由标题映射
const currentRouteTitle = computed(() => {
  const routeTitles: Record<string, string> = {
    '/pesticide-management/ledger': '农药电子台账',
    '/pesticide-management/smart-ratio': '智能配比计算器',
    '/pesticide-management/uav-config': '无人机喷洒参数配置',
    '/pesticide-management/electronic-fence': '施药区域电子围栏',
    '/pesticide-management/residue-monitoring': '农药残留监测接口',
    '/pesticide-management/environmental-compliance': '环保合规性检查'
  }
  return routeTitles[route.path] || '精准施药管理系统'
})

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 导出用药记录
const exportRecord = () => {
  ElMessage({
    message: '正在生成用药记录报表...',
    type: 'info',
    duration: 2000
  })

  // 这里可以添加实际的导出逻辑
  setTimeout(() => {
    ElNotification({
      title: '导出成功',
      message: '用药记录报表已生成，可在下载中心查看',
      type: 'success',
      duration: 3000
    })
  }, 1500)
}

// 组件挂载
onMounted(() => {
  // 监听BaseLayout事件
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<style scoped>
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #94a3b8;
}

.path-item {
  opacity: 0.7;
}

.current-path {
  font-weight: 500;
  color: #d1d5db;
}

.pesticide-usage {
  margin-right: 15px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(16, 185, 129, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  color: #d1d5db;
  font-size: 14px;
  transition: all 0.3s ease;
}

.usage-item:hover {
  background: rgba(16, 185, 129, 0.2);
  transform: translateY(-2px);
}

.usage-item.warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.usage-item.warning:hover {
  background: rgba(245, 158, 11, 0.2);
}

.usage-value {
  font-weight: 700;
  color: #fff;
}

.usage-hint {
  color: #f59e0b;
  font-size: 12px;
  font-style: italic;
}

@media (max-width: 1024px) {
  .usage-hint {
    display: none;
  }
}

@media (max-width: 768px) {
  .pesticide-usage {
    display: none;
  }

  .breadcrumb {
    display: none;
  }
}
</style>
