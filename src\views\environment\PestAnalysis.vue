<!-- 
  PestAnalysis.vue
  环境-虫害关联分析模块
  分析环境因素与虫害之间的关联关系，帮助发现环境变化对虫害的影响规律
-->
<template>
  <div class="pest-analysis">
    <!-- 页面标题 -->
    <PageHeader
      title="环境-虫害关联分析"
      description="分析环境因素与虫害之间的关联关系，帮助发现环境变化对虫害的影响规律"
      icon="DataAnalysis"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ selectedFactors.length }}</span>
            <span class="summary-label">已选环境因素</span>
          </div>
          <div class="summary-item" v-if="analysisComplete">
            <span class="summary-value">{{ analysisResults.significantFactorsCount }}</span>
            <span class="summary-label">显著相关因子</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 主要内容区域 -->
    <div class="analysis-container">
      <!-- 左侧控制面板 -->
      <div class="control-panel">
        <DataPanel title="环境因素选择">
          <div class="factors-library">
            <div
              v-for="factor in environmentalFactors"
              :key="factor.key"
              :class="['factor-item', { active: selectedFactors.includes(factor.key) }]"
              @click="toggleFactor(factor.key)"
            >
              <el-icon :size="24">
                <component :is="factor.icon"></component>
              </el-icon>
              <div class="factor-name">{{ factor.name }}</div>
            </div>
          </div>
        </DataPanel>

        <DataPanel title="分析参数设置">
          <div class="analysis-params">
            <div class="param-group">
              <div class="param-label">时间范围</div>
              <el-date-picker
                v-model="timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="default"
                style="width: 100%"
              />
            </div>

            <div class="param-group">
              <div class="param-label">相关性阈值</div>
              <el-slider
                v-model="correlationThreshold"
                :min="0"
                :max="1"
                :step="0.01"
                :format-tooltip="formatThreshold"
              />
            </div>

            <div class="param-group">
              <div class="param-label">数据采样频率</div>
              <el-select v-model="samplingFrequency" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in samplingOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>

          <div class="control-buttons">
            <el-button type="primary" @click="runAnalysis" :disabled="selectedFactors.length === 0">
              <el-icon><VideoPlay /></el-icon>
              运行分析
            </el-button>
            <el-button @click="resetAnalysis">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </DataPanel>
      </div>

      <!-- 右侧分析结果展示区 -->
      <div class="results-area">
        <template v-if="analysisComplete">
          <!-- 分析结果概览卡片 -->
          <DataPanel title="分析结果概览">
            <template #actions>
              <el-tag type="success" effect="dark" size="small">分析完成</el-tag>
            </template>
            <div class="results-metrics">
              <div class="metric-card">
                <div class="metric-title">最大相关性系数</div>
                <div class="metric-value">{{ analysisResults.maxCorrelation.toFixed(2) }}</div>
                <div class="metric-detail">{{ analysisResults.maxCorrelationFactors }}</div>
              </div>
              <div class="metric-card">
                <div class="metric-title">平均相关性系数</div>
                <div class="metric-value">{{ analysisResults.avgCorrelation.toFixed(2) }}</div>
              </div>
              <div class="metric-card">
                <div class="metric-title">显著相关因素数量</div>
                <div class="metric-value">{{ analysisResults.significantFactorsCount }}</div>
              </div>
            </div>
          </DataPanel>

          <!-- 详细图表分析 -->
          <DataPanel title="详细图表分析">
            <template #actions>
              <el-radio-group v-model="activeChartTab" size="small">
                <el-radio-button label="matrix">相关性矩阵</el-radio-button>
                <el-radio-button label="trend">趋势分析</el-radio-button>
                <el-radio-button label="heatmap">热力分布</el-radio-button>
              </el-radio-group>
            </template>
            <div class="chart-wrapper">
              <div v-show="activeChartTab === 'matrix'" class="chart-container" ref="matrixChartContainer"></div>
              <div v-show="activeChartTab === 'trend'" class="chart-container" ref="trendChartContainer"></div>
              <div v-show="activeChartTab === 'heatmap'" class="chart-container" ref="heatmapChartContainer"></div>
            </div>
          </DataPanel>

          <!-- 分析报告生成 -->
          <DataPanel title="分析报告生成">
            <div class="report-options">
              <div class="options-group">
                <div class="options-label">报告格式</div>
                <el-radio-group v-model="reportFormat" size="small">
                  <el-radio label="pdf">PDF</el-radio>
                  <el-radio label="excel">Excel</el-radio>
                </el-radio-group>
              </div>
              <div class="options-group">
                <div class="options-label">报告内容</div>
                <el-checkbox-group v-model="reportSections">
                  <el-checkbox label="summary">分析摘要</el-checkbox>
                  <el-checkbox label="charts">详细图表</el-checkbox>
                  <el-checkbox label="explanations">相关性解释</el-checkbox>
                  <el-checkbox label="recommendations">建议措施</el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
            <div class="report-action">
              <el-button type="primary" @click="generateReport">
                <el-icon><DocumentAdd /></el-icon>
                一键生成分析报告
              </el-button>
            </div>
          </DataPanel>
        </template>

        <!-- 未分析时的提示 -->
        <div v-else class="no-analysis-placeholder">
          <el-icon :size="64"><DataAnalysis /></el-icon>
          <div class="placeholder-text">请选择环境因素并运行分析以查看结果</div>
          <el-button type="primary" @click="runAnalysis" :disabled="selectedFactors.length === 0">
            开始分析
          </el-button>
        </div>
      </div>
    </div>

    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="模型训练完成" />
        <StatusIndicator type="normal" label="可用数据: 180天" />
        <StatusIndicator type="warning" label="AI预测中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { 
  DataAnalysis, InfoFilled, Sunny, Cloudy, Lightning, 
  Stopwatch, Moon, Sunrise, Odometer, VideoPlay, Refresh,
  DocumentAdd, Place, Download
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from '../environment/components/PageHeader.vue'
import StatusIndicator from '../environment/components/StatusIndicator.vue'
import DataPanel from '../environment/components/DataPanel.vue'

// 状态变量
const selectedFactors = ref<string[]>(['temperature', 'humidity', 'rainfall'])
const timeRange = ref<[Date, Date]>([new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()])
const correlationThreshold = ref<number>(0.5)
const samplingFrequency = ref<string>('daily')
const analysisComplete = ref<boolean>(false)
const activeChartTab = ref<string>('matrix')
const reportFormat = ref<string>('pdf')
const reportSections = ref<string[]>(['summary', 'charts', 'explanations', 'recommendations'])
const lastUpdateTime = ref<Date>(new Date())

// 图表容器引用
const matrixChartContainer = ref<HTMLElement | null>(null)
const trendChartContainer = ref<HTMLElement | null>(null)
const heatmapChartContainer = ref<HTMLElement | null>(null)

// 图表实例
let matrixChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null
let heatmapChart: echarts.ECharts | null = null

// 环境因素列表
const environmentalFactors = reactive([
  { key: 'temperature', name: '温度', icon: 'Sunny' },
  { key: 'humidity', name: '湿度', icon: 'Download' },
  { key: 'rainfall', name: '降雨量', icon: 'Lightning' },
  { key: 'illuminance', name: '光照强度', icon: 'Sunrise' },
  { key: 'windSpeed', name: '风速', icon: 'Place' },
  { key: 'airPressure', name: '气压', icon: 'Odometer' },
  { key: 'soilMoisture', name: '土壤湿度', icon: 'Cloudy' },
  { key: 'dayLength', name: '日照时长', icon: 'Stopwatch' },
  { key: 'nightTemp', name: '夜间温度', icon: 'Moon' }
])

// 数据采样频率选项
const samplingOptions = reactive([
  { value: 'hourly', label: '每小时' },
  { value: 'daily', label: '每天' },
  { value: 'weekly', label: '每周' },
  { value: 'monthly', label: '每月' }
])

// 分析结果
const analysisResults = reactive({
  maxCorrelation: 0,
  maxCorrelationFactors: '',
  avgCorrelation: 0,
  significantFactorsCount: 0
})

// 格式化时间
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 格式化相关性阈值
const formatThreshold = (val: number): string => {
  return `${val.toFixed(2)}`
}

// 切换环境因素选择
const toggleFactor = (factorKey: string): void => {
  const index = selectedFactors.value.indexOf(factorKey)
  if (index > -1) {
    selectedFactors.value.splice(index, 1)
  } else {
    selectedFactors.value.push(factorKey)
  }
}

// 运行分析
const runAnalysis = (): void => {
  if (selectedFactors.value.length === 0) {
    ElMessage.warning('请至少选择一个环境因素')
    return
  }
  
  ElMessage.info('正在分析数据，请稍候...')
  
  // 模拟分析过程延迟
  setTimeout(() => {
    analysisComplete.value = true
    
    // 模拟分析结果
    analysisResults.maxCorrelation = 0.78 + Math.random() * 0.1
    analysisResults.maxCorrelationFactors = '温度与蚜虫出现频率'
    analysisResults.avgCorrelation = 0.45 + Math.random() * 0.1
    analysisResults.significantFactorsCount = Math.floor(selectedFactors.value.length * 0.7)
    
    ElMessage.success('分析完成')
    
    // 初始化图表
    nextTick(() => {
      initMatrixChart()
      initTrendChart()
      initHeatmapChart()
    })
  }, 1500)
}

// 重置分析
const resetAnalysis = (): void => {
  selectedFactors.value = ['temperature', 'humidity', 'rainfall']
  timeRange.value = [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()]
  correlationThreshold.value = 0.5
  samplingFrequency.value = 'daily'
  analysisComplete.value = false
  
  // 清除图表
  matrixChart?.dispose()
  trendChart?.dispose()
  heatmapChart?.dispose()
  
  matrixChart = null
  trendChart = null
  heatmapChart = null
  
  ElMessage.info('已重置分析参数')
}

// 刷新数据
const refreshData = (): void => {
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
  
  if (analysisComplete.value) {
    // 模拟数据变化
    analysisResults.maxCorrelation = 0.78 + Math.random() * 0.1
    analysisResults.avgCorrelation = 0.45 + Math.random() * 0.1
    analysisResults.significantFactorsCount = Math.floor(selectedFactors.value.length * 0.7)
    
    // 更新图表
    if (activeChartTab.value === 'matrix' && matrixChart) {
      initMatrixChart()
    } else if (activeChartTab.value === 'trend' && trendChart) {
      initTrendChart()
    } else if (activeChartTab.value === 'heatmap' && heatmapChart) {
      initHeatmapChart()
    }
  }
}

// 生成报告
const generateReport = (): void => {
  const sections = reportSections.value.join(', ')
  ElMessage.success(`正在生成${reportFormat.value === 'pdf' ? 'PDF' : 'Excel'}格式的分析报告，包含: ${sections}`)
}

// 初始化相关性矩阵图表
const initMatrixChart = (): void => {
  if (matrixChart) {
    matrixChart.dispose()
  }
  
  if (matrixChartContainer.value) {
    matrixChart = echarts.init(matrixChartContainer.value)
    
    // 准备图表数据
    const environmentFactors = selectedFactors.value.map(key => {
      const factor = environmentalFactors.find(f => f.key === key)
      return factor ? factor.name : key
    })
    
    const pestTypes = ['蚜虫', '叶螨', '粉虱', '蓟马', '白粉虱']
    
    // 生成相关性矩阵数据
    const data = []
    for (let i = 0; i < environmentFactors.length; i++) {
      for (let j = 0; j < pestTypes.length; j++) {
        // 生成-1到1之间的随机相关系数，但使其更倾向于正相关
        const correlation = Math.pow(Math.random(), 1.5) * (Math.random() > 0.7 ? 1 : -1)
        data.push([i, j, correlation.toFixed(2)])
      }
    }
    
    const option = {
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          const environmentFactor = environmentFactors[params.data[0]]
          const pestType = pestTypes[params.data[1]]
          const correlation = params.data[2]
          return `${environmentFactor} - ${pestType}<br>相关性: ${correlation}`
        },
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      grid: {
        left: '3%',
        right: '7%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: environmentFactors,
        splitArea: {
          show: true
        },
        axisLabel: {
          color: '#d1d5db',
          interval: 0,
          rotate: 45
        },
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: pestTypes,
        splitArea: {
          show: true
        },
        axisLabel: {
          color: '#d1d5db'
        },
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        }
      },
      visualMap: {
        min: -1,
        max: 1,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '0%',
        text: ['强正相关', '强负相关'],
        textStyle: {
          color: '#d1d5db'
        },
        inRange: {
          color: ['#0f172a', '#3b82f6', '#ffffff', '#f87171', '#c62828']
        }
      },
      series: [
        {
          name: '相关性系数',
          type: 'heatmap',
          data: data,
          label: {
            show: true,
            color: '#1f2937'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    
    matrixChart.setOption(option)
  }
}

// 初始化趋势分析图表
const initTrendChart = (): void => {
  if (trendChart) {
    trendChart.dispose()
  }
  
  if (trendChartContainer.value) {
    trendChart = echarts.init(trendChartContainer.value)
    
    // 生成日期序列
    const days = 30
    const dates = []
    for (let i = 0; i < days; i++) {
      const date = new Date()
      date.setDate(date.getDate() - days + i + 1)
      dates.push(`${date.getMonth() + 1}-${date.getDate()}`)
    }
    
    // 生成环境因素数据
    const temperatureData = Array(days).fill(0).map(() => (Math.random() * 10 + 20).toFixed(1))
    const humidityData = Array(days).fill(0).map(() => (Math.random() * 30 + 50).toFixed(1))
    const rainfallData = Array(days).fill(0).map(() => (Math.random() > 0.7 ? Math.random() * 15 : 0).toFixed(1))
    
    // 生成虫害数据
    const aphidData = Array(days).fill(0).map((_, i) => {
      // 使蚜虫出现与温度有一定相关性
      return (parseFloat(temperatureData[i]) > 25 ? Math.random() * 10 + 2 : Math.random() * 2).toFixed(1)
    })
    
    const option = {
      backgroundColor: 'transparent',
      title: {
        text: '环境因素与虫害趋势关系',
        textStyle: {
          color: '#d1d5db',
          fontSize: 16
        },
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      legend: {
        data: ['温度(°C)', '湿度(%)', '降雨量(mm)', '蚜虫数量(个/㎡)'],
        textStyle: {
          color: '#d1d5db'
        },
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: 80,
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            color: '#d1d5db',
            fontSize: 10
          },
          axisLine: {
            lineStyle: {
              color: '#4b5563'
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '温度/湿度',
          position: 'left',
          axisLabel: {
            formatter: '{value}',
            color: '#d1d5db'
          },
          nameTextStyle: {
            color: '#d1d5db'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(75, 85, 99, 0.1)'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#4b5563'
            }
          }
        },
        {
          type: 'value',
          name: '降雨量',
          position: 'right',
          offset: 0,
          axisLabel: {
            formatter: '{value} mm',
            color: '#d1d5db'
          },
          nameTextStyle: {
            color: '#d1d5db'
          },
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#4b5563'
            }
          }
        },
        {
          type: 'value',
          name: '蚜虫数量',
          position: 'right',
          offset: 60,
          axisLabel: {
            formatter: '{value} 个/㎡',
            color: '#d1d5db'
          },
          nameTextStyle: {
            color: '#d1d5db'
          },
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#4b5563'
            }
          }
        }
      ],
      series: [
        {
          name: '温度(°C)',
          type: 'line',
          data: temperatureData,
          color: '#ef4444',
          smooth: true
        },
        {
          name: '湿度(%)',
          type: 'line',
          data: humidityData,
          color: '#3b82f6',
          smooth: true
        },
        {
          name: '降雨量(mm)',
          type: 'bar',
          yAxisIndex: 1,
          data: rainfallData,
          color: '#10b981'
        },
        {
          name: '蚜虫数量(个/㎡)',
          type: 'line',
          yAxisIndex: 2,
          data: aphidData,
          color: '#f59e0b',
          smooth: true,
          lineStyle: {
            width: 3
          },
          symbol: 'diamond',
          symbolSize: 8
        }
      ]
    }
    
    trendChart.setOption(option)
  }
}

// 初始化热力图图表
const initHeatmapChart = (): void => {
  if (heatmapChart) {
    heatmapChart.dispose()
  }
  
  if (heatmapChartContainer.value) {
    heatmapChart = echarts.init(heatmapChartContainer.value)
    
    const option = {
      backgroundColor: 'transparent',
      title: {
        text: '虫害分布热力图',
        textStyle: {
          color: '#d1d5db',
          fontSize: 16
        },
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: 密度 {c}',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      visualMap: {
        min: 0,
        max: 10,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        text: ['高密度', '低密度'],
        textStyle: {
          color: '#d1d5db'
        },
        inRange: {
          color: ['#3b82f6', '#fbbf24', '#ef4444']
        }
      },
      series: [
        {
          name: '蚜虫分布',
          type: 'heatmap',
          coordinateSystem: 'cartesian2d',
          data: generateHeatmapData(),
          pointSize: 5,
          blurSize: 6
        }
      ],
      grid: {
        top: 60,
        bottom: 60,
        left: 60,
        right: 40
      },
      xAxis: {
        type: 'category',
        data: Array(20).fill(0).map((_, i) => `区域${i + 1}`),
        axisLabel: {
          color: '#d1d5db',
          fontSize: 10
        },
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: Array(20).fill(0).map((_, i) => `区域${i + 1}`),
        axisLabel: {
          color: '#d1d5db',
          fontSize: 10
        },
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      }
    }
    
    heatmapChart.setOption(option)
  }
}

// 生成热力图数据
const generateHeatmapData = () => {
  const data = []
  for (let x = 0; x < 20; x++) {
    for (let y = 0; y < 20; y++) {
      const centerX = 10
      const centerY = 10
      // 距离中心点越近，值越大
      const distanceFromCenter = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2))
      let value = 10 - distanceFromCenter * 0.7
      // 添加随机扰动
      value = Math.max(0, Math.min(10, value + (Math.random() - 0.5) * 3))
      data.push([x, y, value.toFixed(1)])
    }
  }
  return data
}

// 窗口大小变化时重新调整图表
const handleResize = (): void => {
  matrixChart?.resize()
  trendChart?.resize()
  heatmapChart?.resize()
}

// 组件挂载
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

// 组件销毁
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  matrixChart?.dispose()
  trendChart?.dispose()
  heatmapChart?.dispose()
})
</script>

<style scoped>
.pest-analysis {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要样式 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 主内容区域 */
.analysis-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
  overflow: hidden;
}

/* 控制面板样式 */
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 环境因素选择 */
.factors-library {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 10px;
}

.factor-item {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 6px;
  padding: 12px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.factor-item:hover {
  background-color: rgba(59, 130, 246, 0.2);
}

.factor-item.active {
  background-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
  border: 1px solid rgba(59, 130, 246, 0.7);
}

.factor-name {
  font-size: 12px;
  margin-top: 8px;
  text-align: center;
  color: #e5e7eb;
}

/* 分析参数设置 */
.analysis-params {
  margin-bottom: 20px;
}

.param-group {
  margin-bottom: 16px;
}

.param-label {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 8px;
}

.control-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

/* 结果区域样式 */
.results-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
}

/* 结果指标卡片 */
.results-metrics {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.metric-card {
  flex: 1;
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.metric-title {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 4px;
}

.metric-detail {
  font-size: 12px;
  color: #9ca3af;
}

/* 图表容器 */
.chart-wrapper {
  height: 400px;
  position: relative;
}

.chart-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
}

/* 报告选项 */
.report-options {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 20px;
}

.options-group {
  flex: 1;
  min-width: 200px;
}

.options-label {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 8px;
}

.report-action {
  display: flex;
  justify-content: center;
}

/* 无分析时的占位符 */
.no-analysis-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 40px;
}

.placeholder-text {
  font-size: 16px;
  color: #9ca3af;
}

/* 底部状态指示器 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-tabs__item) {
  color: #d1d5db;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

:deep(.el-tabs__active-bar) {
  background-color: #3b82f6;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #374151;
}

:deep(.el-checkbox__label),
:deep(.el-radio__label) {
  color: #d1d5db;
}

:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-input__inner),
:deep(.el-range-input) {
  background-color: rgba(31, 41, 55, 0.8);
  border-color: #374151;
  color: #d1d5db;
}

:deep(.el-range-separator) {
  color: #d1d5db;
}

:deep(.el-select .el-input .el-select__caret) {
  color: #d1d5db;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .analysis-container {
    grid-template-columns: 1fr;
  }
  
  .results-metrics {
    flex-direction: column;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>

<!--
注意: 此组件需要安装以下依赖：
1. echarts: npm install echarts --save
2. 确保引入Element Plus图标组件
-->