<!-- 
  EnvironmentalCompliance.vue
  环保合规性检查模块
  监控农药使用的环保合规情况，包括检查结果、违规记录和整改进度
-->
<template>
  <div class="environmental-compliance">
    <!-- 页面标题 -->
    <PageHeader
      title="环保合规性检查"
      description="监控农药使用的环保合规情况，包括检查结果、违规记录和整改进度"
      icon="DocumentChecked"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ complianceRate }}%</span>
            <span class="summary-label">总体合规率</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ passedChecks }}</span>
            <span class="summary-label">通过检查</span>
          </div>
        </div>
        <el-button type="primary" size="small" @click="generateReport">
          <el-icon><Printer /></el-icon>
          生成报告
        </el-button>
      </template>
    </PageHeader>
    
    <!-- 合规概览卡片 -->
    <div class="overview-cards">
      <DataPanel title="总体合规率" dark>
        <div class="compliance-rate-container">
          <el-progress
            type="dashboard"
            :percentage="complianceRate"
            :color="getComplianceRateColor()"
            :stroke-width="10"
          >
            <template #default="{ percentage }">
              <div class="progress-content">
                <span class="progress-value">{{ percentage }}%</span>
                <span class="progress-label">合规率</span>
              </div>
            </template>
          </el-progress>
          <div class="rate-description">
            <p v-if="complianceRate >= 90">优秀，符合环保要求</p>
            <p v-else-if="complianceRate >= 75">良好，但仍有提升空间</p>
            <p v-else-if="complianceRate >= 60">一般，需要改进</p>
            <p v-else>较差，需要立即整改</p>
          </div>
        </div>
      </DataPanel>
      
      <DataPanel title="检查通过项" dark>
        <div class="stat-card success">
          <div class="stat-icon">
            <el-icon><CircleCheckFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ passedChecks }}</div>
            <div class="stat-label">已通过检查项</div>
          </div>
        </div>
      </DataPanel>
      
      <DataPanel title="待处理问题" dark>
        <div class="stat-card warning">
          <div class="stat-icon">
            <el-icon><WarningFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ pendingIssues }}</div>
            <div class="stat-label">待处理问题</div>
          </div>
          <div class="stat-footer" v-if="pendingIssues > 0">
            <span>需要尽快解决</span>
          </div>
        </div>
      </DataPanel>
      
      <DataPanel title="严重违规" dark>
        <div class="stat-card danger">
          <div class="stat-icon">
            <el-icon><CircleCloseFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ criticalIssues }}</div>
            <div class="stat-label">严重违规</div>
          </div>
          <div class="stat-footer" v-if="criticalIssues > 0">
            <span>需要立即整改</span>
          </div>
        </div>
      </DataPanel>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 检查项目列表 -->
      <DataPanel title="合规检查项目" dark>
        <template #actions>
          <div class="filter-actions">
            <el-select 
              v-model="categoryFilter" 
              placeholder="检查类别" 
              clearable 
              size="small"
            >
              <el-option 
                v-for="category in checkCategories" 
                :key="category" 
                :label="category" 
                :value="category" 
              />
            </el-select>
            <el-select 
              v-model="statusFilter" 
              placeholder="检查状态" 
              clearable 
              size="small"
            >
              <el-option label="全部" value="" />
              <el-option label="通过" value="passed" />
              <el-option label="未通过" value="failed" />
              <el-option label="警告" value="warning" />
              <el-option label="未检查" value="not_checked" />
            </el-select>
          </div>
        </template>
        
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="filteredCheckItems"
            style="width: 100%"
            @row-click="selectCheckItem"
            :highlight-current-row="true"
            size="small"
          >
            <el-table-column prop="checkTitle" label="检查项目" min-width="180" />
            <el-table-column prop="category" label="类别" width="120" />
            <el-table-column prop="requirementLevel" label="要求等级" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="row.requirementLevel === 'mandatory' ? 'danger' : 'info'"
                  size="small"
                  effect="dark"
                >
                  {{ row.requirementLevel === 'mandatory' ? '强制' : '建议' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small" effect="dark">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastCheckedAt" label="检查时间" width="120">
              <template #default="{ row }">
                {{ formatDate(row.lastCheckedAt) }}
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 空状态显示 -->
          <div v-if="filteredCheckItems.length === 0 && !loading" class="empty-state">
            <el-empty description="暂无符合条件的检查项目" />
          </div>
        </div>
      </DataPanel>
      
      <!-- 检查项目详情 -->
      <DataPanel title="检查项目详情" dark>
        <template v-if="selectedCheckItem">
          <div class="detail-header">
            <div class="detail-title">
              <h3>{{ selectedCheckItem.checkTitle }}</h3>
              <el-tag :type="getStatusType(selectedCheckItem.status)" effect="dark">
                {{ getStatusLabel(selectedCheckItem.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="detail-content">
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="检查类别">
                {{ selectedCheckItem.category }}
              </el-descriptions-item>
              <el-descriptions-item label="要求等级">
                {{ selectedCheckItem.requirementLevel === 'mandatory' ? '强制要求' : '建议要求' }}
              </el-descriptions-item>
              <el-descriptions-item label="参考标准">
                {{ selectedCheckItem.referenceStandard }}
              </el-descriptions-item>
              <el-descriptions-item label="检查方法">
                {{ selectedCheckItem.verificationMethod }}
              </el-descriptions-item>
              <el-descriptions-item label="最近检查时间">
                {{ formatDate(selectedCheckItem.lastCheckedAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="详细说明">
                {{ selectedCheckItem.description }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 整改进度部分 -->
          <div v-if="selectedCheckItem.status === 'failed' || selectedCheckItem.status === 'warning'" class="remediation-section">
            <div class="section-divider">
              <h4>整改进度</h4>
            </div>
            
            <div v-if="associatedViolation" class="violation-detail">
              <div class="violation-header">
                <span class="violation-severity" :class="associatedViolation.severity">
                  {{ getSeverityLabel(associatedViolation.severity) }}
                </span>
                <span class="violation-date">
                  违规记录日期: {{ formatDate(associatedViolation.violationDate) }}
                </span>
              </div>
              
              <div class="violation-description">
                <p>{{ associatedViolation.description }}</p>
              </div>
              
              <div class="remediation-progress">
                <div class="progress-header">
                  <span>整改状态: {{ getActionStatusLabel(associatedViolation.actionStatus) }}</span>
                  <span v-if="associatedViolation.actionCompletedAt">
                    完成时间: {{ formatDate(associatedViolation.actionCompletedAt) }}
                  </span>
                </div>
                
                <el-steps 
                  :active="getActionStatusStep(associatedViolation.actionStatus)" 
                  finish-status="success"
                  simple
                >
                  <el-step title="待处理" />
                  <el-step title="处理中" />
                  <el-step title="已完成" />
                </el-steps>
                
                <div class="corrective-action">
                  <h5>整改措施</h5>
                  <p>{{ associatedViolation.correctiveAction }}</p>
                </div>
                
                <el-form v-if="associatedViolation.actionStatus !== 'completed'" inline class="update-form">
                  <el-form-item label="更新状态">
                    <el-select v-model="updateStatusValue" placeholder="选择状态" size="small">
                      <el-option 
                        v-for="status in getAvailableStatusOptions(associatedViolation.actionStatus)"
                        :key="status.value"
                        :label="status.label"
                        :value="status.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button 
                      type="primary" 
                      size="small"
                      @click="updateRemediationStatus"
                      :disabled="!updateStatusValue || updateStatusValue === associatedViolation.actionStatus"
                    >
                      更新
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            
            <div v-else class="no-violation-record">
              <el-empty description="未找到相关违规记录" />
            </div>
          </div>
        </template>
        
        <div v-else class="no-selection">
          <el-empty description="请选择一个检查项目查看详情" />
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部：法规知识库快速访问 -->
    <DataPanel title="法规知识库" dark>
      <template #actions>
        <div class="search-box">
          <el-input
            v-model="regulationSearch"
            placeholder="搜索相关法规"
            clearable
            size="small"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </template>
      
      <div class="regulation-cards">
        <div 
          v-for="regulation in filteredRegulations" 
          :key="regulation.id"
          class="regulation-card"
        >
          <div class="regulation-header">
            <div class="regulation-title">{{ regulation.title }}</div>
            <el-tag size="small" effect="dark" type="info">{{ regulation.type }}</el-tag>
          </div>
          <div class="regulation-date">生效日期: {{ regulation.effectiveDate }}</div>
          <div class="regulation-summary">{{ regulation.summary }}</div>
          <div class="regulation-actions">
            <el-button type="primary" size="small" @click="viewRegulationDetails(regulation)">
              <el-icon><DocumentChecked /></el-icon>
              查看详情
            </el-button>
          </div>
        </div>
        
        <!-- 空状态显示 -->
        <div v-if="filteredRegulations.length === 0" class="empty-state">
          <el-empty description="未找到相关法规" />
        </div>
      </div>
    </DataPanel>

    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="合规项目" />
        <StatusIndicator type="warning" label="待整改项目" />
        <StatusIndicator type="error" label="严重违规" />
        <StatusIndicator type="normal" label="法规更新" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  DocumentChecked, 
  Printer, 
  CircleCheckFilled,
  WarningFilled,
  CircleCloseFilled,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'

// 定义类型
interface ComplianceCheckItem {
  id: string;
  checkTitle: string;
  category: string;
  requirementLevel: 'mandatory' | 'recommended';
  status: 'passed' | 'failed' | 'warning' | 'not_checked';
  lastCheckedAt: string;
  referenceStandard: string;
  verificationMethod: string;
  description: string;
}

interface ComplianceViolation {
  id: string;
  checkItemId: string;
  severity: 'minor' | 'moderate' | 'major' | 'critical';
  violationDate: string;
  description: string;
  correctiveAction: string;
  actionStatus: 'pending' | 'in_progress' | 'completed';
  actionCompletedAt?: string | null;
}

// 格式化日期的工具函数
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 模拟API服务
// 实际项目中应该从@/api/pesticideApi导入
const environmentalComplianceApi = {
  async getComplianceOverview() {
    // 模拟数据
    return {
      complianceRate: 85,
      passedChecks: 17,
      pendingIssues: 3,
      criticalIssues: 0
    };
  },
  async getCheckResults() {
    // 模拟检查项目数据
    return [
      {
        id: '1',
        checkTitle: '农药使用记录完整性检查',
        category: '记录管理',
        requirementLevel: 'mandatory',
        status: 'passed',
        lastCheckedAt: '2023-05-15',
        referenceStandard: '《农药管理条例》第二十五条',
        verificationMethod: '文档审核',
        description: '检查农药使用记录是否完整，包括使用时间、地点、农药名称、用量等信息。'
      },
      {
        id: '2',
        checkTitle: '农药储存条件合规性',
        category: '储存管理',
        requirementLevel: 'mandatory',
        status: 'passed',
        lastCheckedAt: '2023-05-15',
        referenceStandard: '《农药管理条例》第二十四条',
        verificationMethod: '现场检查',
        description: '检查农药是否存放在专用仓库或者专柜中，并有明显标志。'
      },
      {
        id: '3',
        checkTitle: '高毒农药使用审批',
        category: '使用管理',
        requirementLevel: 'mandatory',
        status: 'warning',
        lastCheckedAt: '2023-05-14',
        referenceStandard: '《农药管理条例》第二十六条',
        verificationMethod: '文档审核',
        description: '检查使用高毒农药是否履行了审批手续。'
      },
      {
        id: '4',
        checkTitle: '农药包装废弃物回收',
        category: '废弃物管理',
        requirementLevel: 'mandatory',
        status: 'failed',
        lastCheckedAt: '2023-05-13',
        referenceStandard: '《农药包装废弃物回收处理管理办法》第十一条',
        verificationMethod: '现场检查',
        description: '检查农药包装废弃物是否按规定回收处理。'
      },
      {
        id: '5',
        checkTitle: '农药使用安全间隔期遵守情况',
        category: '使用管理',
        requirementLevel: 'mandatory',
        status: 'passed',
        lastCheckedAt: '2023-05-12',
        referenceStandard: '《农药使用安全规范》',
        verificationMethod: '记录审核',
        description: '检查是否遵守农药安全间隔期规定。'
      }
    ] as ComplianceCheckItem[];
  },
  async getViolations() {
    // 模拟违规记录数据
    const violations = [
      {
        id: '1',
        checkItemId: '3',
        severity: 'moderate',
        violationDate: '2023-05-14',
        description: '使用高毒农药时未完全履行审批手续，缺少专家评估报告。',
        correctiveAction: '补充提交专家评估报告，并完善高毒农药使用审批流程。',
        actionStatus: 'in_progress',
        actionCompletedAt: null
      },
      {
        id: '2',
        checkItemId: '4',
        severity: 'major',
        violationDate: '2023-05-13',
        description: '农药包装废弃物未按规定集中回收处理，存在随意丢弃现象。',
        correctiveAction: '设置专门的农药包装废弃物回收点，培训工作人员正确处理农药包装废弃物。',
        actionStatus: 'pending',
        actionCompletedAt: null
      }
    ];
    return violations as ComplianceViolation[];
  },
  async updateRemediationProgress(id: string, progress: number) {
    // 模拟API调用
    console.log(`更新整改进度: ID=${id}, 进度=${progress}%`);
    return true;
  }
};

// 数据加载状态
const loading = ref(false)

// 合规概况数据
const complianceRate = ref(0)
const passedChecks = ref(0)
const pendingIssues = ref(0)
const criticalIssues = ref(0)
const lastUpdateTime = ref(new Date())

// 检查项目数据
const checkItems = ref<ComplianceCheckItem[]>([])
const selectedCheckItem = ref<ComplianceCheckItem | null>(null)
const violations = ref<ComplianceViolation[]>([])
const associatedViolation = ref<ComplianceViolation | null>(null)
const updateStatusValue = ref<'pending' | 'in_progress' | 'completed' | null>(null)

// 筛选条件
const categoryFilter = ref('')
const statusFilter = ref('')
const regulationSearch = ref('')

// 法规知识库数据
const regulations = ref([
  {
    id: '1',
    title: '农药管理条例',
    type: '国家法规',
    effectiveDate: '2017-06-01',
    summary: '规范农药的生产、经营和使用，保证农药质量，防止农药污染环境和农药中毒事故。'
  },
  {
    id: '2',
    title: '高毒农药定点经营管理办法',
    type: '部门规章',
    effectiveDate: '2013-10-01',
    summary: '加强高毒农药管理，规范高毒农药经营行为，防范农药使用风险。'
  },
  {
    id: '3',
    title: '农药包装废弃物回收处理管理办法',
    type: '部门规章',
    effectiveDate: '2020-10-01',
    summary: '规范农药包装废弃物回收处理活动，防治农药包装废弃物污染环境。'
  },
  {
    id: '4',
    title: '农药使用安全规范',
    type: '技术标准',
    effectiveDate: '2019-05-01',
    summary: '规定了农药使用的安全间隔期、混配使用禁忌、施药器械安全和个人防护等要求。'
  }
])

// 计算属性：检查项目类别列表
const checkCategories = computed(() => {
  const categories = new Set<string>()
  checkItems.value.forEach(item => categories.add(item.category))
  return Array.from(categories)
})

// 计算属性：筛选后的检查项目
const filteredCheckItems = computed(() => {
  return checkItems.value.filter(item => {
    const categoryMatch = !categoryFilter.value || item.category === categoryFilter.value
    const statusMatch = !statusFilter.value || item.status === statusFilter.value
    return categoryMatch && statusMatch
  })
})

// 计算属性：筛选后的法规
const filteredRegulations = computed(() => {
  if (!regulationSearch.value) return regulations.value
  
  const searchText = regulationSearch.value.toLowerCase()
  return regulations.value.filter(regulation => 
    regulation.title.toLowerCase().includes(searchText) ||
    regulation.summary.toLowerCase().includes(searchText)
  )
})

// 获取状态标签类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'passed': 'success',
    'failed': 'danger',
    'warning': 'warning',
    'not_checked': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态显示文本
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'passed': '通过',
    'failed': '未通过',
    'warning': '警告',
    'not_checked': '未检查'
  }
  return statusMap[status] || status
}

// 获取严重程度显示文本
const getSeverityLabel = (severity: string) => {
  const severityMap: Record<string, string> = {
    'minor': '轻微',
    'moderate': '中等',
    'major': '严重',
    'critical': '极严重'
  }
  return severityMap[severity] || severity
}

// 获取整改状态显示文本
const getActionStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'in_progress': '处理中',
    'completed': '已完成'
  }
  return statusMap[status] || status
}

// 获取整改状态步骤值
const getActionStatusStep = (status: string) => {
  const stepMap: Record<string, number> = {
    'pending': 0,
    'in_progress': 1,
    'completed': 2
  }
  return stepMap[status] || 0
}

// 获取可用的状态选项
const getAvailableStatusOptions = (currentStatus: string) => {
  const options = []
  
  if (currentStatus === 'pending') {
    options.push({ label: '处理中', value: 'in_progress' })
    options.push({ label: '已完成', value: 'completed' })
  } else if (currentStatus === 'in_progress') {
    options.push({ label: '已完成', value: 'completed' })
  }
  
  return options
}

// 获取合规率颜色
const getComplianceRateColor = () => {
  if (complianceRate.value >= 90) return '#10b981'
  if (complianceRate.value >= 75) return '#3b82f6'
  if (complianceRate.value >= 60) return '#f59e0b'
  return '#ef4444'
}

// 获取检查结果数据
const fetchComplianceData = async () => {
  loading.value = true
  
  try {
    // 获取合规性概览
    const overview = await environmentalComplianceApi.getComplianceOverview()
    if (overview) {
      complianceRate.value = overview.complianceRate || 0
      passedChecks.value = overview.passedChecks || 0
      pendingIssues.value = overview.pendingIssues || 0
      criticalIssues.value = overview.criticalIssues || 0
    }
    
    // 获取检查结果
    const results = await environmentalComplianceApi.getCheckResults()
    checkItems.value = results
    
    // 获取违规详情
    const violationResults = await environmentalComplianceApi.getViolations()
    violations.value = violationResults
    
  } catch (error) {
    console.error('Failed to fetch compliance data:', error)
    ElMessage.error('获取合规数据失败')
  } finally {
    loading.value = false
  }
}

// 选择检查项目
const selectCheckItem = (item: ComplianceCheckItem) => {
  selectedCheckItem.value = item
  
  // 查找关联的违规记录
  const violation = violations.value.find(v => v.checkItemId === item.id)
  associatedViolation.value = violation || null
  updateStatusValue.value = null
}

// 更新整改状态
const updateRemediationStatus = async () => {
  if (!associatedViolation.value || !updateStatusValue.value) return
  
  try {
    // 假设API提供了更新违规记录的方法
    const progress = updateStatusValue.value === 'completed' ? 100 : 50
    await environmentalComplianceApi.updateRemediationProgress(
      associatedViolation.value.id,
      progress
    )
    
    // 更新本地数据
    if (associatedViolation.value) {
      associatedViolation.value.actionStatus = updateStatusValue.value
      if (updateStatusValue.value === 'completed') {
        associatedViolation.value.actionCompletedAt = new Date().toISOString()
      }
    }
    
    ElMessage.success('整改状态已更新')
    updateStatusValue.value = null
  } catch (error) {
    console.error('Failed to update remediation status:', error)
    ElMessage.error('更新整改状态失败')
  }
}

// 生成合规报告
const generateReport = () => {
  ElMessage.info('正在生成合规报告，请稍候...')
  // 实际实现可能包括调用API生成报告并下载
}

// 查看法规详情
const viewRegulationDetails = (regulation: any) => {
  ElMessage.info(`查看法规详情: ${regulation.title}`)
  // 实现查看法规详情的逻辑
}

// 刷新数据
const refreshData = () => {
  fetchComplianceData()
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
}

// 组件挂载时
onMounted(async () => {
  await fetchComplianceData()
})
</script>

<style scoped lang="scss">
.environmental-compliance {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  /* 状态摘要 */
  .status-summary {
    display: flex;
    gap: 20px;
    margin-right: 20px;
    
    .summary-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .summary-value {
        font-size: 24px;
        font-weight: 600;
        color: #3b82f6;
      }
      
      .summary-label {
        font-size: 14px;
        color: #9ca3af;
      }
    }
  }
  
  /* 概览卡片网格 */
  .overview-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 20px;
    
    /* 合规率卡片 */
    .compliance-rate-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 20px 0;
      
      .progress-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .progress-value {
          font-size: 28px;
          font-weight: 700;
          color: #e5e7eb;
        }
        
        .progress-label {
          font-size: 14px;
          color: #9ca3af;
        }
      }
      
      .rate-description {
        margin-top: 15px;
        text-align: center;
        color: #d1d5db;
        font-size: 14px;
      }
    }
    
    /* 统计卡片 */
    .stat-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 20px;
      position: relative;
      
      .stat-icon {
        font-size: 36px;
        margin-bottom: 15px;
      }
      
      .stat-content {
        text-align: center;
        
        .stat-value {
          font-size: 36px;
          font-weight: 700;
          color: #e5e7eb;
        }
        
        .stat-label {
          font-size: 14px;
          color: #9ca3af;
          margin-top: 5px;
        }
      }
      
      .stat-footer {
        margin-top: 15px;
        font-size: 14px;
      }
      
      &.success .stat-icon {
        color: #10b981;
      }
      
      &.warning .stat-icon {
        color: #f59e0b;
      }
      
      &.danger .stat-icon {
        color: #ef4444;
      }
    }
  }
  
  /* 主内容区域 */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: hidden;
    min-height: 0;
    
    /* 筛选操作 */
    .filter-actions {
      display: flex;
      gap: 10px;
    }
    
    /* 表格容器 */
    .table-container {
      height: 100%;
      overflow: auto;
    }
    
    /* 自定义表格样式 */
    :deep(.el-table) {
      --el-table-header-bg-color: rgba(31, 41, 55, 0.8);
      --el-table-border-color: #3b4863;
      --el-table-row-hover-bg-color: rgba(59, 130, 246, 0.1);
      background-color: transparent;
      color: #e5e7eb;
      
      th {
        background-color: rgba(31, 41, 55, 0.8);
        color: #e5e7eb;
        font-weight: 500;
      }
      
      td {
        border-bottom-color: rgba(59, 130, 246, 0.1);
      }
      
      .el-table__row {
        background-color: rgba(31, 41, 55, 0.3);
        
        &:hover > td {
          background-color: rgba(59, 130, 246, 0.1);
        }
      }
      
      .current-row {
        background-color: rgba(59, 130, 246, 0.2) !important;
      }
    }
    
    /* 详情部分 */
    .detail-header {
      margin-bottom: 20px;
      
      .detail-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        h3 {
          margin: 0;
          color: #e5e7eb;
          font-size: 18px;
        }
      }
    }
    
    .detail-content {
      margin-bottom: 20px;
    }
    
    /* 整改部分 */
    .remediation-section {
      .section-divider {
        margin: 20px 0;
        border-bottom: 1px solid rgba(59, 130, 246, 0.2);
        padding-bottom: 10px;
        
        h4 {
          margin: 0;
          color: #e5e7eb;
          font-size: 16px;
        }
      }
      
      .violation-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        
        .violation-severity {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 14px;
          
          &.minor {
            background-color: rgba(16, 185, 129, 0.2);
            color: #10b981;
          }
          
          &.moderate {
            background-color: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
          }
          
          &.major,
          &.critical {
            background-color: rgba(239, 68, 68, 0.2);
            color: #ef4444;
          }
        }
        
        .violation-date {
          color: #9ca3af;
          font-size: 14px;
        }
      }
      
      .violation-description {
        background-color: rgba(31, 41, 55, 0.3);
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 20px;
        
        p {
          margin: 0;
          color: #e5e7eb;
        }
      }
      
      .remediation-progress {
        .progress-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
          color: #d1d5db;
        }
        
        .corrective-action {
          margin-top: 20px;
          
          h5 {
            margin: 0 0 10px 0;
            color: #e5e7eb;
            font-size: 15px;
          }
          
          p {
            background-color: rgba(31, 41, 55, 0.3);
            border-radius: 6px;
            padding: 12px;
            margin: 0;
            color: #d1d5db;
          }
        }
        
        .update-form {
          margin-top: 20px;
          padding-top: 15px;
          border-top: 1px solid rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
  
  /* 法规知识库 */
  .regulation-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 10px;
    
    .regulation-card {
      background-color: rgba(31, 41, 55, 0.3);
      border-radius: 8px;
      padding: 16px;
      border: 1px solid rgba(59, 130, 246, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        border-color: rgba(59, 130, 246, 0.3);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      .regulation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .regulation-title {
          font-weight: 500;
          font-size: 16px;
          color: #e5e7eb;
        }
      }
      
      .regulation-date {
        color: #9ca3af;
        font-size: 14px;
        margin-bottom: 12px;
      }
      
      .regulation-summary {
        color: #d1d5db;
        margin-bottom: 12px;
        font-size: 14px;
        line-height: 1.5;
      }
      
      .regulation-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
      }
    }
  }
  
  /* 空状态 */
  .empty-state,
  .no-selection,
  .no-violation-record {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  /* 状态指示器区域 */
  .status-indicators {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(31, 41, 55, 0.5);
    border-radius: 8px;
    margin-top: 20px;
    
    .indicator-group {
      display: flex;
      gap: 20px;
    }
    
    .refresh-info {
      display: flex;
      align-items: center;
      gap: 15px;
      color: #9ca3af;
      font-size: 14px;
    }
  }
  
  /* 搜索框 */
  .search-box {
    width: 250px;
  }
  
  /* 响应式调整 */
  @media (max-width: 1200px) {
    .overview-cards {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .regulation-cards {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .overview-cards {
      grid-template-columns: 1fr;
    }
    
    .regulation-cards {
      grid-template-columns: 1fr;
    }
    
    .status-indicators {
      flex-direction: column;
      gap: 15px;
    }
    
    .indicator-group {
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .search-box {
      width: 100%;
    }
  }
}
</style> 