# 智慧农场设备追踪系统 - 重构版本

## 概述

本模块是智慧农场设备追踪系统的重构版本，采用模块化、组件化的设计理念，提供了完整的设备监控、数据记录和导出功能。

## 功能特性

### 🚀 核心功能
- **实时设备追踪**: WebSocket实时接收设备位置数据
- **可视化地图**: 基于Canvas的2D地图显示设备位置和轨迹
- **设备状态监控**: 实时显示设备电量、速度、状态等信息
- **轨迹记录**: 自动记录设备移动轨迹，支持历史回放

### 📊 数据记录功能
- **WebSocket数据记录开关**: 可控制是否记录实时数据
- **智能数据管理**: 自动限制记录数量，防止内存溢出
- **实时统计**: 显示记录数量、时长、频率等统计信息
- **自动保存**: 支持定时自动保存功能

### 📁 数据导出功能
- **Excel导出**: 支持导出为.xlsx格式，包含完整的设备数据
- **多格式支持**: 支持CSV、JSON、GPX等多种导出格式
- **灵活筛选**: 支持按时间范围、设备类型筛选导出数据
- **批量导出**: 支持按设备分组批量导出

### 🎨 界面优化
- **响应式设计**: 适配不同屏幕尺寸
- **现代化UI**: 采用深色主题，提供良好的视觉体验
- **动画效果**: 丰富的过渡动画和状态指示
- **模块化布局**: 可调整的面板布局

## 文件结构

```
src/views/MonitoringCenter/DeviceTracking/
├── index.vue                    # 主页面组件
├── components/                  # 组件目录
│   ├── DeviceList.vue          # 设备列表组件
│   ├── DataRecordPanel.vue     # 数据记录控制面板
│   ├── ExportPanel.vue         # 导出功能面板

├── composables/                 # 组合式函数
│   ├── useDataRecording.ts     # 数据记录管理
│   └── useExcelExport.ts       # Excel导出功能
├── services/                    # 服务层
│   └── dataExportService.ts    # 数据导出服务
├── types/                       # 类型定义
│   └── index.ts                # 导出相关类型
├── styles/                      # 样式文件
│   └── index.scss              # 组件样式
└── README.md                   # 说明文档
```

## 技术栈

- **Vue 3**: 使用Composition API
- **TypeScript**: 完整的类型支持
- **Element Plus**: UI组件库
- **XLSX**: Excel文件处理
- **WebSocket**: 实时数据通信
- **SCSS**: 样式预处理器

## 使用说明

### 1. 设备监控
- 页面加载后自动连接WebSocket服务
- 实时显示设备位置、状态、电量等信息
- 点击设备可查看详细信息
- 双击设备可聚焦到该设备

### 2. 数据记录
1. 点击"数据记录"面板中的"开始记录"按钮
2. 系统将自动记录所有WebSocket接收到的数据
3. 可在配置中调整最大记录数、自动保存等选项
4. 点击"停止记录"可停止数据记录

### 3. 数据导出
1. 在"数据导出"面板中选择导出格式
2. 可选择导出范围：全部数据、时间范围、指定设备
3. 点击"快速导出Excel"进行简单导出
4. 使用"高级导出选项"进行更精细的配置
5. 支持批量导出，按设备分组生成多个文件

### 4. 设备控制
- 在设备详情弹窗中可发送控制命令
- 支持启动、暂停、停止、重启、返航等操作
- 所有操作都有确认提示

## 配置选项

### 数据记录配置
```typescript
interface RecordingConfig {
  enabled: boolean      // 是否启用记录
  maxRecords: number    // 最大记录数量
  autoSave: boolean     // 是否自动保存
  saveInterval: number  // 保存间隔（分钟）
}
```

### 导出配置
```typescript
interface ExportConfig {
  filename: string      // 文件名前缀
  sheetName: string     // Excel工作表名称
  includeHeaders: boolean // 是否包含表头
  dateFormat: string    // 日期格式
}
```

## API接口

### WebSocket数据格式
```typescript
interface RobotLocationData {
  tagId: number         // 设备标签ID
  x: number            // X坐标
  y: number            // Y坐标
  timestamp: number    // 时间戳
}
```

### 设备信息格式
```typescript
interface RobotDevice {
  id: string           // 设备ID
  name: string         // 设备名称
  tagId: number        // 标签ID
  status: string       // 设备状态
  battery: number      // 电池电量
  position: Point      // 当前位置
  speed: number        // 当前速度
  runTime: string      // 运行时长
  task?: string        // 工作任务
  lastUpdateTime: Date // 最后更新时间
}
```

## 开发指南

### 添加新的导出格式
1. 在`services/dataExportService.ts`中添加新的导出方法
2. 在`composables/useExcelExport.ts`中添加对应的调用逻辑
3. 在`components/ExportPanel.vue`中添加UI选项

### 扩展设备控制功能
1. 在设备列表或其他控制面板中添加新的控制按钮
2. 在主页面中添加相应的命令处理方法
3. 根据需要扩展WebSocket通信协议

### 自定义样式主题
1. 修改`styles/index.scss`中的CSS变量
2. 调整组件中的样式类名
3. 使用SCSS混入来保持样式一致性

## 注意事项

1. **WebSocket连接**: 确保WebSocket服务正常运行
2. **内存管理**: 长时间记录数据可能占用较多内存，建议定期清理
3. **文件大小**: 导出大量数据时注意文件大小限制
4. **浏览器兼容**: 某些高级功能可能需要现代浏览器支持

## 更新日志

### v2.0.0 (重构版本)
- ✨ 全新的模块化架构
- ✨ 新增WebSocket数据记录功能
- ✨ 新增Excel数据导出功能
- ✨ 优化UI设计和用户体验
- ✨ 完善的TypeScript类型支持
- ✨ 响应式布局设计
- 🐛 修复了原版本的性能问题
- 🐛 改进了错误处理机制

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。
