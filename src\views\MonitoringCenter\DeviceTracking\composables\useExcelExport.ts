/**
 * Excel导出功能 Composable
 * 负责将记录的数据导出为Excel文件
 */

import { ref, computed, readonly } from 'vue'
import { ElNotification, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'
import type { DataRecord, ExportConfig, ExportDataRow } from '../types'

// 默认导出配置
const DEFAULT_EXPORT_CONFIG: ExportConfig = {
  filename: '设备追踪数据',
  sheetName: '追踪记录',
  includeHeaders: true,
  dateFormat: 'YYYY-MM-DD HH:mm:ss'
}

export function useExcelExport() {
  // 导出配置
  const exportConfig = ref<ExportConfig>({ ...DEFAULT_EXPORT_CONFIG })

  // 导出状态
  const isExporting = ref(false)

  // 最后导出时间
  const lastExportTime = ref<Date | null>(null)

  /**
   * 格式化时间戳为可读时间
   */
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  /**
   * 将数据记录转换为Excel行格式
   */
  const convertToExportData = (records: DataRecord[]): ExportDataRow[] => {
    return records.map((record, index) => ({
      序号: index + 1,
      设备名称: record.deviceName,
      标签ID: record.data.tagId,
      X坐标: Number(record.data.x.toFixed(3)),
      Y坐标: Number(record.data.y.toFixed(3)),
      时间戳: record.timestamp,
      记录时间: record.recordTime,
      备注: `数据ID: ${record.id}`
    }))
  }

  /**
   * 生成文件名
   */
  const generateFilename = (customName?: string): string => {
    const name = customName || exportConfig.value.filename
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    return `${name}_${timestamp}.xlsx`
  }

  /**
   * 导出数据到Excel
   */
  const exportToExcel = async (
    records: DataRecord[],
    customConfig?: Partial<ExportConfig>
  ): Promise<boolean> => {
    if (records.length === 0) {
      ElNotification({
        title: '导出失败',
        message: '没有可导出的数据',
        type: 'warning'
      })
      return false
    }

    try {
      isExporting.value = true

      // 合并配置
      const config = { ...exportConfig.value, ...customConfig }

      // 转换数据格式
      const exportData = convertToExportData(records)

      // 创建工作簿
      const workbook = XLSX.utils.book_new()

      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const columnWidths = [
        { wch: 8 },  // 序号
        { wch: 15 }, // 设备名称
        { wch: 10 }, // 标签ID
        { wch: 12 }, // X坐标
        { wch: 12 }, // Y坐标
        { wch: 15 }, // 时间戳
        { wch: 20 }, // 记录时间
        { wch: 25 }  // 备注
      ]
      worksheet['!cols'] = columnWidths

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, config.sheetName)

      // 生成文件名
      const filename = generateFilename(config.filename)

      // 导出文件
      XLSX.writeFile(workbook, filename)

      // 更新最后导出时间
      lastExportTime.value = new Date()

      ElNotification({
        title: '导出成功',
        message: `已导出 ${records.length} 条记录到 ${filename}`,
        type: 'success',
        duration: 4000
      })

      return true

    } catch (error) {
      console.error('Excel导出失败:', error)
      ElNotification({
        title: '导出失败',
        message: error instanceof Error ? error.message : '未知错误',
        type: 'error'
      })
      return false

    } finally {
      isExporting.value = false
    }
  }

  /**
   * 导出指定时间范围的数据
   */
  const exportByTimeRange = async (
    records: DataRecord[],
    startTime: Date,
    endTime: Date,
    customConfig?: Partial<ExportConfig>
  ): Promise<boolean> => {
    const filteredRecords = records.filter(record => {
      const recordTime = new Date(record.timestamp)
      return recordTime >= startTime && recordTime <= endTime
    })

    const timeRangeConfig = {
      ...customConfig,
      filename: `${exportConfig.value.filename}_${startTime.toISOString().slice(0, 10)}_${endTime.toISOString().slice(0, 10)}`
    }

    return await exportToExcel(filteredRecords, timeRangeConfig)
  }

  /**
   * 导出指定设备的数据
   */
  const exportByDevice = async (
    records: DataRecord[],
    tagId: number,
    deviceName?: string,
    customConfig?: Partial<ExportConfig>
  ): Promise<boolean> => {
    const filteredRecords = records.filter(record => record.data.tagId === tagId)

    const deviceConfig = {
      ...customConfig,
      filename: `${exportConfig.value.filename}_${deviceName || `设备${tagId}`}`
    }

    return await exportToExcel(filteredRecords, deviceConfig)
  }

  /**
   * 批量导出（按设备分组）
   */
  const exportByDeviceGroups = async (
    records: DataRecord[],
    customConfig?: Partial<ExportConfig>
  ): Promise<boolean> => {
    if (records.length === 0) {
      ElNotification({
        title: '导出失败',
        message: '没有可导出的数据',
        type: 'warning'
      })
      return false
    }

    try {
      // 确认导出
      await ElMessageBox.confirm(
        '将按设备分组导出多个Excel文件，是否继续？',
        '批量导出确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )

      isExporting.value = true

      // 按设备分组
      const deviceGroups = new Map<number, DataRecord[]>()
      records.forEach(record => {
        const tagId = record.data.tagId
        if (!deviceGroups.has(tagId)) {
          deviceGroups.set(tagId, [])
        }
        deviceGroups.get(tagId)!.push(record)
      })

      // 逐个导出
      let successCount = 0
      for (const [tagId, deviceRecords] of deviceGroups) {
        const deviceName = deviceRecords[0]?.deviceName || `设备${tagId}`
        const success = await exportByDevice(deviceRecords, tagId, deviceName, customConfig)
        if (success) successCount++
      }

      ElNotification({
        title: '批量导出完成',
        message: `成功导出 ${successCount}/${deviceGroups.size} 个设备的数据`,
        type: successCount === deviceGroups.size ? 'success' : 'warning',
        duration: 4000
      })

      return successCount > 0

    } catch (error) {
      if (error === 'cancel') {
        return false
      }
      throw error
    } finally {
      isExporting.value = false
    }
  }

  /**
   * 更新导出配置
   */
  const updateExportConfig = (config: Partial<ExportConfig>) => {
    Object.assign(exportConfig.value, config)
  }

  // 计算属性
  const canExport = computed(() => !isExporting.value)

  const lastExportTimeText = computed(() => {
    return lastExportTime.value
      ? lastExportTime.value.toLocaleString('zh-CN')
      : '从未导出'
  })

  return {
    // 状态
    exportConfig: readonly(exportConfig),
    isExporting: readonly(isExporting),
    lastExportTime: readonly(lastExportTime),
    canExport,
    lastExportTimeText,

    // 方法
    exportToExcel,
    exportByTimeRange,
    exportByDevice,
    exportByDeviceGroups,
    updateExportConfig,
    generateFilename
  }
}
