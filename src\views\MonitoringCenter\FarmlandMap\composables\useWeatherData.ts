/**
 * 天气数据的可复用逻辑
 */
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { weatherService } from '../services/weatherService';
import type { EnvironmentData, SoilData } from '../types';

export function useWeatherData() {
  // 天气状态
  const weather = ref('晴');
  
  // 环境数据
  const temperature = ref('26');
  const humidity = ref('65');
  const windSpeed = ref('2.3');
  const lightIntensity = ref('50000');
  
  // 土壤数据
  const soilMoisture = ref('68');
  const soilNutrition = ref('75');
  const soilPH = ref('6.8');
  
  // 定时器ID
  let weatherTimer: number | null = null;
  let soilDataTimer: number | null = null;
  
  // 土壤湿度颜色
  const soilMoistureColor = computed(() => {
    return weatherService.getSoilMoistureColor();
  });
  
  // 土壤养分颜色
  const soilNutritionColor = computed(() => {
    return weatherService.getSoilNutritionColor();
  });
  
  // 初始化天气数据
  const initWeatherData = () => {
    // 获取天气状态
    weather.value = weatherService.getWeather();
    
    // 获取环境数据
    const envData = weatherService.getEnvironmentData();
    temperature.value = envData.temperature;
    humidity.value = envData.humidity;
    windSpeed.value = envData.windSpeed;
    lightIntensity.value = envData.lightIntensity;
    
    // 获取土壤数据
    const soilData = weatherService.getSoilData();
    soilMoisture.value = soilData.moisture;
    soilNutrition.value = soilData.nutrition;
    soilPH.value = soilData.pH;
  };
  
  // 更新天气数据
  const updateWeatherData = () => {
    // 更新天气信息
    weatherService.updateWeatherInfo();
    weather.value = weatherService.getWeather();
    
    // 更新环境数据
    const envData = weatherService.getEnvironmentData();
    temperature.value = envData.temperature;
    humidity.value = envData.humidity;
    windSpeed.value = envData.windSpeed;
    lightIntensity.value = envData.lightIntensity;
  };
  
  // 更新土壤数据
  const updateSoilData = () => {
    // 更新土壤数据
    weatherService.updateSoilData();
    const soilData = weatherService.getSoilData();
    soilMoisture.value = soilData.moisture;
    soilNutrition.value = soilData.nutrition;
    soilPH.value = soilData.pH;
  };
  
  // 开始天气模拟
  const startWeatherSimulation = () => {
    // 设置天气信息更新定时器
    weatherTimer = window.setInterval(() => {
      updateWeatherData();
    }, 60000);
    
    // 设置土壤数据更新定时器
    soilDataTimer = window.setInterval(() => {
      updateSoilData();
    }, 30000);
    
    // 初始更新一次
    updateWeatherData();
    updateSoilData();
  };
  
  // 停止天气模拟
  const stopWeatherSimulation = () => {
    if (weatherTimer !== null) {
      window.clearInterval(weatherTimer);
      weatherTimer = null;
    }
    
    if (soilDataTimer !== null) {
      window.clearInterval(soilDataTimer);
      soilDataTimer = null;
    }
  };
  
  // 组件卸载时清理资源
  onUnmounted(() => {
    stopWeatherSimulation();
  });
  
  return {
    weather,
    temperature,
    humidity,
    windSpeed,
    lightIntensity,
    soilMoisture,
    soilNutrition,
    soilPH,
    soilMoistureColor,
    soilNutritionColor,
    initWeatherData,
    startWeatherSimulation,
    stopWeatherSimulation,
    updateWeatherData,
    updateSoilData
  };
} 