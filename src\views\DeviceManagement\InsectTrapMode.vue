<!-- 
  InsectTrapMode.vue
  捕虫灯工作模式切换模块
  控制捕虫灯的光波频率和工作时间，提高捕虫效果和设备的灵活性
-->
<template>
  <div class="insect-trap-mode">
    <!-- 页面标题 -->
    <PageHeader
      title="捕虫灯工作模式切换"
      description="控制捕虫灯的光波频率和工作时间，提高捕虫效果和设备的灵活性"
      icon="Sunny"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ getActiveDevicesCount() }}</span>
            <span class="summary-label">活跃设备</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getSelectedDevicesCount() }}</span>
            <span class="summary-label">已选设备</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 实时监控开关 -->
    <div class="real-time-monitor-toggle">
      <span class="toggle-label">实时监控</span>
      <el-switch
        v-model="realTimeMonitoring"
        active-color="#3b82f6"
        inactive-color="#6b7280"
        @change="handleMonitoringToggle"
      />
      
      <!-- 测试按钮 -->
      <el-button 
        v-if="isDebugMode" 
        type="warning" 
        size="small" 
        class="test-button"
        @click="selectTestDevices"
      >
        选择两个设备(测试)
      </el-button>
    </div>
    
    <!-- 设备卡片网格 -->
    <div class="device-cards">
      <div v-for="(device, index) in devices" :key="device.id" class="device-card">
        <div class="card-header">
          <el-checkbox 
            v-model="device.selected" 
            @change="handleDeviceSelect(device)"
          />
          <div class="device-info">
            <h3 class="device-name">{{ device.name }}</h3>
            <div class="device-location">{{ device.location }}</div>
          </div>
          <div v-if="realTimeMonitoring" class="device-status-indicator" :class="device.status.toLowerCase()">
            <div class="status-icon" :title="getStatusText(device.status)"></div>
            <div class="status-progress">
              <svg viewBox="0 0 36 36" aria-label="设备进度" role="img">
                <title>设备进度: {{ device.progressPercentage }}%</title>
                <path
                  class="circle-bg"
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  class="circle"
                  :stroke-dasharray="`${device.progressPercentage}, 100`"
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
            </div>
            <span class="sr-only">{{ getStatusText(device.status) }}, 进度: {{ device.progressPercentage }}%</span>
          </div>
        </div>
        
        <div class="mode-settings">
          <div class="setting-group">
            <div class="setting-label">光波频率模式</div>
            <el-select 
              v-model="device.frequencyMode" 
              placeholder="请选择频率模式" 
              class="frequency-select"
              :disabled="!device.selected"
              filterable
            >
              <el-option
                v-for="item in frequencyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          
          <div class="time-settings">
            <div class="setting-label">工作时间设置</div>
            <div class="time-pickers">
              <div class="time-picker">
                <div class="time-label">开始时间</div>
                <el-time-picker
                  v-model="device.startTime"
                  placeholder="选择时间"
                  format="HH:mm"
                  :disabled="!device.selected"
                />
              </div>
              <div class="time-divider">至</div>
              <div class="time-picker">
                <div class="time-label">结束时间</div>
                <el-time-picker
                  v-model="device.endTime"
                  placeholder="选择时间"
                  format="HH:mm"
                  :disabled="!device.selected"
                />
              </div>
            </div>
          </div>
        </div>
        
        <div class="device-actions">
          <el-button 
            type="primary" 
            size="small" 
            :disabled="!device.selected" 
            :loading="loading.applySettings[device.id]"
            @click="applySettings(device)"
          >
            <el-icon><Check /></el-icon>
            应用设置
          </el-button>
          <el-button 
            size="small" 
            :disabled="!device.selected" 
            @click="resetSettings(device)"
            :loading="loading.resetSettings[device.id]"
          >
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 批量操作区域 -->
    <div class="bulk-section">
      <DataPanel title="批量操作设置" class="bulk-panel">
        <div class="bulk-operations">
          <div class="bulk-header">
            <el-checkbox 
              v-model="allSelected" 
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              选择全部
            </el-checkbox>
            <div class="selected-count" v-if="selectedCount > 0">
              已选择 {{ selectedCount }} 台设备
            </div>
          </div>
          
          <!-- 调试信息 -->
          <div class="debug-info" v-if="isDebugMode">
            <p>已选择设备数量: {{ selectedCount }}</p>
            <p>条件判断: {{ selectedCount >= 2 ? '显示批量设置' : '显示提示' }}</p>
            <ul>
              <li v-for="device in devices" :key="device.id">
                {{ device.name }}: {{ device.selected ? '已选择' : '未选择' }}
              </li>
            </ul>
          </div>
          
          <!-- 批量设置区域 -->
          <template v-if="selectedCount >= 2">
            <div class="bulk-settings">
              <div class="bulk-setting-group">
                <div class="setting-label">批量设置光波频率</div>
                <div class="setting-controls">
                  <el-select 
                    v-model="bulkFrequencyMode" 
                    placeholder="请选择频率模式" 
                    class="bulk-frequency-select"
                  >
                    <el-option
                      v-for="item in frequencyOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-button 
                    type="primary" 
                    @click="applyBulkFrequency"
                    class="apply-button"
                    :loading="loading.bulkFrequency"
                  >
                    应用频率设置
                  </el-button>
                </div>
              </div>
              
              <div class="bulk-setting-group">
                <div class="setting-label">批量设置工作时间</div>
                <div class="setting-controls">
                  <div class="bulk-time-pickers">
                    <el-time-picker
                      v-model="bulkStartTime"
                      placeholder="开始时间"
                      format="HH:mm"
                    />
                    <div class="time-divider">至</div>
                    <el-time-picker
                      v-model="bulkEndTime"
                      placeholder="结束时间"
                      format="HH:mm"
                    />
                  </div>
                  <el-button 
                    type="primary" 
                    @click="applyBulkTime"
                    class="apply-button"
                    :loading="loading.bulkTime"
                  >
                    应用时间设置
                  </el-button>
                </div>
              </div>
              
              <div class="bulk-apply">
                <el-button 
                  type="success" 
                  @click="applyBulkSettings"
                  :loading="loading.bulkAll"
                >
                  <el-icon><Check /></el-icon>
                  批量应用所有设置
                </el-button>
              </div>
            </div>
          </template>
          
          <!-- 空状态提示 -->
          <template v-else>
            <div class="empty-selection">
              <div class="empty-icon">
                <el-icon><Select /></el-icon>
              </div>
              <p v-if="selectedCount === 0">请选择至少一个设备来进行批量操作</p>
              <p v-else-if="selectedCount === 1">请选择至少两个设备来进行批量操作</p>
            </div>
          </template>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="光波防护" />
        <StatusIndicator type="normal" label="生态友好" />
        <StatusIndicator type="warning" label="智能控制" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain 
          :loading="loading.refresh" 
          @click="refreshData"
        >
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Check, 
  RefreshLeft, 
  Refresh, 
  Sunny, 
  Switch,
  Select
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'

// 设备类型定义
interface InsectTrapDevice {
  id: string;
  name: string;
  location: string;
  selected: boolean;
  status: 'active' | 'inactive' | 'maintenance';
  frequencyMode: string;
  originalFrequencyMode: string;
  startTime: Date;
  endTime: Date;
  originalStartTime: Date;
  originalEndTime: Date;
  progressPercentage: number;
}

// 光波频率选项
const frequencyOptions = [
  { value: 'low', label: '低频模式 (30-50Hz)' },
  { value: 'medium', label: '中频模式 (50-100Hz)' },
  { value: 'high', label: '高频模式 (100-150Hz)' },
  { value: 'ultra', label: '超高频模式 (150-200Hz)' },
  { value: 'variable', label: '变频模式 (30-200Hz)' },
  { value: 'pulse', label: '脉冲模式' },
  { value: 'intelligent', label: '智能感应模式' }
]

// 实时监控开关
const realTimeMonitoring = ref(false)

// 所有捕虫灯设备数据
const devices = reactive<InsectTrapDevice[]>([
  {
    id: 'IT001',
    name: '捕虫灯 Alpha-1',
    location: '东区果园A区',
    selected: false,
    status: 'active',
    frequencyMode: 'medium',
    originalFrequencyMode: 'medium',
    startTime: new Date(new Date().setHours(18, 0, 0)),
    endTime: new Date(new Date().setHours(6, 0, 0)),
    originalStartTime: new Date(new Date().setHours(18, 0, 0)),
    originalEndTime: new Date(new Date().setHours(6, 0, 0)),
    progressPercentage: 65
  },
  {
    id: 'IT002',
    name: '捕虫灯 Beta-2',
    location: '西区蔬菜大棚',
    selected: false,
    status: 'inactive',
    frequencyMode: 'low',
    originalFrequencyMode: 'low',
    startTime: new Date(new Date().setHours(19, 0, 0)),
    endTime: new Date(new Date().setHours(5, 0, 0)),
    originalStartTime: new Date(new Date().setHours(19, 0, 0)),
    originalEndTime: new Date(new Date().setHours(5, 0, 0)),
    progressPercentage: 0
  },
  {
    id: 'IT003',
    name: '捕虫灯 Gamma-3',
    location: '南区果园B区',
    selected: false,
    status: 'active',
    frequencyMode: 'high',
    originalFrequencyMode: 'high',
    startTime: new Date(new Date().setHours(17, 30, 0)),
    endTime: new Date(new Date().setHours(5, 30, 0)),
    originalStartTime: new Date(new Date().setHours(17, 30, 0)),
    originalEndTime: new Date(new Date().setHours(5, 30, 0)),
    progressPercentage: 82
  },
  {
    id: 'IT004',
    name: '捕虫灯 Delta-4',
    location: '北区温室',
    selected: false,
    status: 'maintenance',
    frequencyMode: 'variable',
    originalFrequencyMode: 'variable',
    startTime: new Date(new Date().setHours(18, 30, 0)),
    endTime: new Date(new Date().setHours(6, 30, 0)),
    originalStartTime: new Date(new Date().setHours(18, 30, 0)),
    originalEndTime: new Date(new Date().setHours(6, 30, 0)),
    progressPercentage: 15
  },
  {
    id: 'IT005',
    name: '捕虫灯 Epsilon-5',
    location: '中心区试验田',
    selected: false,
    status: 'active',
    frequencyMode: 'intelligent',
    originalFrequencyMode: 'intelligent',
    startTime: new Date(new Date().setHours(19, 30, 0)),
    endTime: new Date(new Date().setHours(5, 30, 0)),
    originalStartTime: new Date(new Date().setHours(19, 30, 0)),
    originalEndTime: new Date(new Date().setHours(5, 30, 0)),
    progressPercentage: 45
  }
])

// 批量设置值
const bulkFrequencyMode = ref('')
const bulkStartTime = ref(new Date(new Date().setHours(18, 0, 0)))
const bulkEndTime = ref(new Date(new Date().setHours(6, 0, 0)))

// 最后更新时间
const lastUpdateTime = ref(new Date())

// 计算已选中设备数量
const selectedCount = computed(() => {
  return devices.filter(device => device.selected).length
})

// 获取活跃设备数量
const getActiveDevicesCount = () => {
  return devices.filter(device => device.status === 'active').length
}

// 获取已选择设备数量
const getSelectedDevicesCount = () => {
  return selectedCount.value
}

// 全选状态
const allSelected = ref(false)
const isIndeterminate = computed(() => {
  return selectedCount.value > 0 && selectedCount.value < devices.length
})

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 处理实时监控开关
const handleMonitoringToggle = (value: boolean) => {
  if (value) {
    startMonitoring();
    // 保存监控状态到本地存储
    localStorage.setItem('insectTrapMonitoringState', 'active');
  } else {
    stopMonitoring();
    // 保存监控状态到本地存储
    localStorage.setItem('insectTrapMonitoringState', 'inactive');
  }
}

// 启动实时监控
let monitoringInterval: number | null = null
const startMonitoring = () => {
  ElMessage({
    type: 'success',
    message: '已开启捕虫灯实时监控'
  })
  
  // 模拟数据更新
  monitoringInterval = window.setInterval(() => {
    devices.forEach(device => {
      if (device.status === 'active') {
        // 活跃设备进度条变化
        device.progressPercentage = Math.max(0, Math.min(100, device.progressPercentage + (Math.random() * 10 - 5)))
      }
    })
    lastUpdateTime.value = new Date()
  }, 2000)
}

// 停止实时监控
const stopMonitoring = () => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval)
    monitoringInterval = null
  }
  
  ElMessage({
    type: 'info',
    message: '已关闭捕虫灯实时监控'
  })
}

// 调试模式（生产环境设为false）
const isDebugMode = ref(false)

// 处理设备选择
const handleDeviceSelect = (device: InsectTrapDevice) => {
  // 如果取消选中，则恢复原始设置
  if (!device.selected) {
    device.frequencyMode = device.originalFrequencyMode
    device.startTime = new Date(device.originalStartTime)
    device.endTime = new Date(device.originalEndTime)
  }
  
  // 更新全选状态
  updateSelectAllStatus()
  
  // 添加调试日志
  console.log(`设备选择状态更新: ${device.name}, 已选择: ${device.selected}`)
  console.log(`当前已选择设备数量: ${selectedCount.value}`)
  
  // 强制更新视图
  nextTick(() => {
    console.log('视图已更新，当前选择设备数量:', selectedCount.value)
  })
}

// 更新全选状态
const updateSelectAllStatus = () => {
  const selectedDeviceCount = devices.filter(device => device.selected).length
  allSelected.value = selectedDeviceCount === devices.length
  
  // 如果没有设备被选中，清空批量设置值
  if (selectedDeviceCount === 0) {
    bulkFrequencyMode.value = ''
    bulkStartTime.value = new Date(new Date().setHours(18, 0, 0))
    bulkEndTime.value = new Date(new Date().setHours(6, 0, 0))
  }
}

// 处理全选
const handleSelectAll = (val: boolean) => {
  devices.forEach(device => {
    device.selected = val
    
    // 如果取消选中，恢复原始设置
    if (!val) {
      device.frequencyMode = device.originalFrequencyMode
      device.startTime = new Date(device.originalStartTime)
      device.endTime = new Date(device.originalEndTime)
    }
  })
  
  // 添加调试日志
  console.log(`全选状态更新: ${val}, 当前已选择设备数量: ${selectedCount.value}`)
  
  // 强制更新视图
  nextTick(() => {
    console.log('视图已更新，当前选择设备数量:', selectedCount.value)
  })
}

// 加载状态
const loading = ref({
  applySettings: {} as Record<string, boolean>,
  resetSettings: {} as Record<string, boolean>,
  bulkFrequency: false,
  bulkTime: false,
  bulkAll: false,
  refresh: false
});

// 应用单个设备设置
const applySettings = (device: InsectTrapDevice) => {
  loading.value.applySettings[device.id] = true;
  
  // 模拟API请求延迟
  setTimeout(() => {
    // 仅应用当前设备的设置
    device.originalFrequencyMode = device.frequencyMode;
    device.originalStartTime = new Date(device.startTime);
    device.originalEndTime = new Date(device.endTime);
    
    // 模拟发送设置到后端
    // axios.post('/api/insect-trap/mode', {
    //   deviceId: device.id,
    //   frequencyMode: device.frequencyMode,
    //   startTime: device.startTime,
    //   endTime: device.endTime
    // })
    
    ElMessage({
      type: 'success',
      message: `已成功应用 ${device.name} 的设置`
    });
    
    loading.value.applySettings[device.id] = false;
  }, 600);
};

// 重置单个设备设置
const resetSettings = (device: InsectTrapDevice) => {
  loading.value.resetSettings[device.id] = true;
  
  // 模拟API请求延迟
  setTimeout(() => {
    // 仅重置当前设备的设置
    device.frequencyMode = device.originalFrequencyMode;
    device.startTime = new Date(device.originalStartTime);
    device.endTime = new Date(device.originalEndTime);
    
    ElMessage({
      type: 'info',
      message: `已重置 ${device.name} 的设置`
    });
    
    loading.value.resetSettings[device.id] = false;
  }, 400);
};

// 应用批量频率设置
const applyBulkFrequency = () => {
  if (!bulkFrequencyMode.value) {
    ElMessage({
      type: 'warning',
      message: '请先选择频率模式'
    });
    return;
  }
  
  loading.value.bulkFrequency = true;
  
  // 模拟API请求延迟
  setTimeout(() => {
    devices.forEach(device => {
      if (device.selected) {
        device.frequencyMode = bulkFrequencyMode.value;
      }
    });
    
    ElMessage({
      type: 'success',
      message: `已将 ${selectedCount.value} 台设备的频率模式设置为 ${getFrequencyLabel(bulkFrequencyMode.value)}`
    });
    
    loading.value.bulkFrequency = false;
  }, 600);
};

// 获取频率模式标签
const getFrequencyLabel = (value: string) => {
  const option = frequencyOptions.find(opt => opt.value === value)
  return option ? option.label : value
}

// 应用批量时间设置
const applyBulkTime = () => {
  if (!bulkStartTime.value || !bulkEndTime.value) {
    ElMessage({
      type: 'warning',
      message: '请先设置开始和结束时间'
    });
    return;
  }
  
  loading.value.bulkTime = true;
  
  // 模拟API请求延迟
  setTimeout(() => {
    devices.forEach(device => {
      if (device.selected) {
        device.startTime = new Date(bulkStartTime.value);
        device.endTime = new Date(bulkEndTime.value);
      }
    });
    
    const formatTime = (date: Date) => {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    };
    
    ElMessage({
      type: 'success',
      message: `已将 ${selectedCount.value} 台设备的工作时间设置为 ${formatTime(bulkStartTime.value)} 至 ${formatTime(bulkEndTime.value)}`
    });
    
    loading.value.bulkTime = false;
  }, 600);
};

// 应用所有批量设置
const applyBulkSettings = () => {
  let hasAppliedSettings = false;
  
  // 检查是否有设置可应用
  if (!bulkFrequencyMode.value && (!bulkStartTime.value || !bulkEndTime.value)) {
    ElMessage({
      type: 'warning',
      message: '请至少设置一项参数（频率模式或工作时间）'
    });
    return;
  }
  
  loading.value.bulkAll = true;
  
  // 模拟API请求延迟
  setTimeout(() => {
    // 应用频率设置
    if (bulkFrequencyMode.value) {
      devices.forEach(device => {
        if (device.selected) {
          device.frequencyMode = bulkFrequencyMode.value;
        }
      });
      hasAppliedSettings = true;
    }
    
    // 应用时间设置
    if (bulkStartTime.value && bulkEndTime.value) {
      devices.forEach(device => {
        if (device.selected) {
          device.startTime = new Date(bulkStartTime.value);
          device.endTime = new Date(bulkEndTime.value);
        }
      });
      hasAppliedSettings = true;
    }
    
    // 保存原始设置
    devices.forEach(device => {
      if (device.selected) {
        device.originalFrequencyMode = device.frequencyMode;
        device.originalStartTime = new Date(device.startTime);
        device.originalEndTime = new Date(device.endTime);
      }
    });
    
    // 模拟发送批量设置到后端
    // axios.post('/api/insect-trap/bulk-mode', {
    //   devices: devices.filter(d => d.selected).map(d => ({
    //     deviceId: d.id,
    //     frequencyMode: d.frequencyMode,
    //     startTime: d.startTime,
    //     endTime: d.endTime
    //   }))
    // });
    
    ElMessage({
      type: 'success',
      message: `已成功应用 ${selectedCount.value} 台设备的所有设置`
    });
    
    loading.value.bulkAll = false;
  }, 800);
};

// 刷新数据
const refreshData = () => {
  loading.value.refresh = true;
  
  // 模拟API请求延迟
  setTimeout(() => {
    // 模拟数据更新
    devices.forEach(device => {
      if (device.status === 'active') {
        device.progressPercentage = Math.max(0, Math.min(100, device.progressPercentage + (Math.random() * 20 - 10)));
      }
    });
    
    lastUpdateTime.value = new Date();
    
    ElMessage({
      type: 'success',
      message: '数据已更新'
    });
    
    loading.value.refresh = false;
  }, 800);
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '活跃';
    case 'inactive':
      return '非活跃';
    case 'maintenance':
      return '维护中';
    default:
      return '未知状态';
  }
};

// 测试函数 - 选择前两个设备
const selectTestDevices = () => {
  // 先取消所有选择
  devices.forEach(device => {
    device.selected = false;
  });
  
  // 选择前两个设备
  if (devices.length >= 2) {
    devices[0].selected = true;
    devices[1].selected = true;
    
    // 更新全选状态
    updateSelectAllStatus();
    
    ElMessage({
      type: 'success',
      message: '已选择两个设备用于测试'
    });
    
    console.log('已选择两个设备用于测试，当前选择数量:', selectedCount.value);
  }
}

// 组件挂载时
onMounted(() => {
  // 从本地存储恢复实时监控状态
  const savedMonitoringState = localStorage.getItem('insectTrapMonitoringState');
  if (savedMonitoringState === 'active') {
    realTimeMonitoring.value = true;
    startMonitoring();
  }
  
  // 初始时，确保没有设备被选中
  devices.forEach(device => {
    device.selected = false;
  });
  
  // 更新全选状态
  updateSelectAllStatus();
})

// 组件卸载前清理
onUnmounted(() => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval)
  }
})
</script>

<style scoped>
.insect-trap-mode {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 允许整个页面滚动 */
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 实时监控开关 */
.real-time-monitor-toggle {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 15px;
  gap: 15px;
}

.toggle-label {
  margin-right: 10px;
  font-size: 14px;
  color: #9ca3af;
}

.test-button {
  margin-left: auto;
}

/* 设备卡片网格 */
.device-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  max-height: none; /* 移除最大高度限制 */
}

.device-card {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  padding: 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #3b4863;
}

.device-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.device-info {
  flex: 1;
  margin-left: 10px;
}

.device-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #e5e7eb;
}

.device-location {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

/* 设备状态指示器 */
.device-status-indicator {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-progress {
  width: 28px;
  height: 28px;
}

.status-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: #4b5563;
  stroke-width: 3;
}

.circle {
  fill: none;
  stroke-width: 3;
  stroke-linecap: round;
  transition: stroke-dasharray 0.5s ease;
}

.device-status-indicator.active .status-icon {
  background-color: #10b981;
  box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
  animation: pulse 2s infinite;
}

.device-status-indicator.active .circle {
  stroke: #10b981;
}

.device-status-indicator.inactive .status-icon {
  background-color: #6b7280;
}

.device-status-indicator.inactive .circle {
  stroke: #6b7280;
}

.device-status-indicator.maintenance .status-icon {
  background-color: #f59e0b;
  box-shadow: 0 0 5px rgba(245, 158, 11, 0.5);
}

.device-status-indicator.maintenance .circle {
  stroke: #f59e0b;
}

/* 模式设置 */
.mode-settings {
  margin-bottom: 20px;
}

.setting-group {
  margin-bottom: 15px;
}

.setting-label {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 8px;
}

.frequency-select {
  width: 100%;
}

.time-settings .time-pickers {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-picker {
  flex: 1;
  min-width: 120px;
}

.time-picker :deep(.el-input__wrapper) {
  width: 100%;
}

.time-picker .el-time-picker {
  width: 100%;
}

.time-label {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 4px;
}

.time-divider {
  margin: 0 10px;
  color: #6b7280;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 设备操作按钮 */
.device-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 批量操作区域容器 */
.bulk-section {
  width: 100%;
  margin-bottom: 20px;
  position: relative;
  z-index: 5;
}

/* 批量操作面板 */
.bulk-panel {
  margin-bottom: 20px;
  background-color: rgba(17, 24, 39, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(75, 85, 99, 0.3);
  position: relative;
  z-index: 5;
}

/* 批量操作区域 */
.bulk-operations {
  padding: 15px;
  width: 100%;
  overflow: visible;
  display: flex;
  flex-direction: column;
  background-color: rgba(30, 41, 59, 0.5);
  border-radius: 8px;
  position: relative;
  z-index: 6;
}

.bulk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(75, 85, 99, 0.2);
}

.selected-count {
  font-size: 14px;
  color: #9ca3af;
  background-color: rgba(59, 130, 246, 0.2);
  padding: 6px 12px;
  border-radius: 4px;
  white-space: nowrap;
  font-weight: 500;
}

.bulk-settings {
  margin-top: 15px;
  width: 100%;
  padding: 15px;
  min-height: 100px;
  background-color: rgba(30, 41, 59, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(75, 85, 99, 0.3);
  position: relative;
  z-index: 7;
}

.bulk-setting-group {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(75, 85, 99, 0.2);
  width: 100%;
}

.bulk-setting-group:last-child {
  border-bottom: none;
}

.setting-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
}

.setting-controls .apply-button {
  min-width: 120px;
  white-space: nowrap;
}

.setting-controls .apply-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.bulk-apply .el-button:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

.bulk-frequency-select {
  flex: 1;
  min-width: 200px;
}

/* 批量时间选择器 */
.bulk-time-pickers {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 200px;
  gap: 10px;
}

.bulk-time-pickers :deep(.el-input__wrapper) {
  width: 100%;
}

.bulk-time-pickers .el-time-picker {
  flex: 1;
  width: 100%;
  min-width: 120px;
}

.bulk-apply {
  display: flex;
  justify-content: center;
  margin-top: 25px;
  width: 100%;
}

.bulk-apply .el-button {
  padding: 12px 24px;
  font-size: 16px;
}

/* 底部状态指示器 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: 20px; /* 增加与上方元素的间距 */
  position: relative;
  z-index: 4;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 批量操作面板 */
.bulk-panel {
  margin-bottom: 20px;
  background-color: rgba(17, 24, 39, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.empty-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #9ca3af;
  text-align: center;
  min-height: 150px;
  background-color: rgba(30, 41, 59, 0.3);
  border-radius: 8px;
  border: 1px dashed rgba(75, 85, 99, 0.4);
  margin: 10px 0;
}

.empty-icon {
  font-size: 40px;
  margin-bottom: 15px;
  color: #6b7280;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .device-cards {
    grid-template-columns: 1fr;
  }
  
  .bulk-operations {
    padding: 10px;
  }
  
  .bulk-settings {
    padding: 10px;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .bulk-settings .setting-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .setting-controls .apply-button {
    width: 100%;
  }
  
  .bulk-time-pickers {
    flex-direction: column;
    gap: 10px;
  }
  
  .time-divider {
    margin: 0;
    align-self: center;
  }
}

/* 屏幕阅读器支持 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* 调试信息 */
.debug-info {
  margin: 10px 0;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-size: 12px;
  color: #9ca3af;
  border: 1px dashed #4b5563;
}

.debug-info ul {
  margin: 5px 0;
  padding-left: 20px;
}

.debug-info li {
  margin: 2px 0;
}
</style> 