<!--
IMU数据展示组件
功能：
1. 实时显示IMU数据（姿态角度、电池状态、传感器数据）
2. 数据可视化图表展示
3. 历史数据查看
4. 数据导出功能
-->

<template>
  <div class="imu-data-display">
    <!-- 数据概览卡片 -->
    <div class="data-overview">
      <!-- 姿态角度卡片 -->
      <div class="data-card attitude-card">
        <div class="card-header">
          <el-icon class="card-icon"><Compass /></el-icon>
          <span class="card-title">姿态角度</span>
        </div>
        <div class="card-content">
          <div class="attitude-item">
            <span class="label">横滚角 (Roll)</span>
            <span class="value" :class="getAngleClass(currentData?.attitude.roll || 0)">
              {{ formatAngle(currentData?.attitude.roll || 0) }}
            </span>
          </div>
          <div class="attitude-item">
            <span class="label">俯仰角 (Pitch)</span>
            <span class="value" :class="getAngleClass(currentData?.attitude.pitch || 0)">
              {{ formatAngle(currentData?.attitude.pitch || 0) }}
            </span>
          </div>
          <div class="attitude-item">
            <span class="label">偏航角 (Yaw)</span>
            <span class="value">
              {{ formatAngle(currentData?.attitude.yaw || 0) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 电池状态卡片 -->
      <div class="data-card battery-card">
        <div class="card-header">
          <el-icon class="card-icon"><Switch /></el-icon>
          <span class="card-title">电池状态</span>
        </div>
        <div class="card-content">
          <div class="battery-item">
            <span class="label">电量</span>
            <div class="battery-progress">
              <el-progress
                :percentage="currentData?.battery.soc || 0"
                :color="getBatteryColor(currentData?.battery.soc || 0)"
                :show-text="false"
                :stroke-width="8"
              />
              <span class="battery-text">{{ currentData?.battery.soc || 0 }}%</span>
            </div>
          </div>
          <div class="battery-item">
            <span class="label">电流</span>
            <span class="value">{{ formatCurrent(currentData?.battery.current || 0) }}</span>
          </div>
          <div class="battery-item">
            <span class="label">循环次数</span>
            <span class="value">{{ currentData?.battery.cycle || 0 }}次</span>
          </div>
        </div>
      </div>

      <!-- 传感器数据卡片 -->
      <div class="data-card sensor-card">
        <div class="card-header">
          <el-icon class="card-icon"><Monitor /></el-icon>
          <span class="card-title">传感器数据</span>
        </div>
        <div class="card-content">
          <div class="sensor-item">
            <span class="label">电源电压</span>
            <span class="value">{{ formatVoltage(currentData?.sensors.powerVoltage || 0) }}</span>
          </div>
          <div class="sensor-item">
            <span class="label">最高温度</span>
            <span class="value" :class="getTempClass(currentData?.sensors.motorMaxTemp || 0)">
              {{ formatTemperature(currentData?.sensors.motorMaxTemp || 0) }}
            </span>
          </div>
          <div class="sensor-item">
            <span class="label">平均温度</span>
            <span class="value">{{ formatTemperature(currentData?.sensors.motorAvgTemp || 0) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 足部压力显示 -->
    <div class="foot-force-section">
      <div class="section-header">
        <el-icon class="section-icon"><Grid /></el-icon>
        <span class="section-title">足部压力分布</span>
      </div>
      <div class="foot-force-grid">
        <div class="foot-item front-left">
          <span class="foot-label">前左</span>
          <span class="foot-value">{{ currentData?.footForce.frontLeft || 0 }}</span>
        </div>
        <div class="foot-item front-right">
          <span class="foot-label">前右</span>
          <span class="foot-value">{{ currentData?.footForce.frontRight || 0 }}</span>
        </div>
        <div class="foot-item rear-left">
          <span class="foot-label">后左</span>
          <span class="foot-value">{{ currentData?.footForce.rearLeft || 0 }}</span>
        </div>
        <div class="foot-item rear-right">
          <span class="foot-label">后右</span>
          <span class="foot-value">{{ currentData?.footForce.rearRight || 0 }}</span>
        </div>
      </div>
    </div>

    <!-- 数据统计信息 -->
    <div class="data-stats">
      <div class="stats-item">
        <span class="stats-label">数据记录</span>
        <span class="stats-value">{{ stats.totalRecords }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">更新频率</span>
        <span class="stats-value">{{ stats.dataRate.toFixed(1) }} Hz</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">连接时长</span>
        <span class="stats-value">{{ formatDuration(stats.connectionDuration) }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">错误次数</span>
        <span class="stats-value" :class="{ 'error': stats.errorCount > 0 }">
          {{ stats.errorCount }}
        </span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button
        type="primary"
        size="small"
        @click="exportData"
        :disabled="!hasData"
        :icon="Download"
      >
        导出数据
      </el-button>
      <el-button
        type="warning"
        size="small"
        @click="clearHistory"
        :disabled="!hasData"
        :icon="Delete"
      >
        清空历史
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Compass,
  Switch,
  Monitor,
  Grid,
  Download,
  Delete
} from '@element-plus/icons-vue'
import type { IMUData, IMUDataStats } from '../types/imu'
import { IMUDataService } from '../services/imuDataService'

// Props定义
interface Props {
  currentData: IMUData | null
  stats: IMUDataStats
  dataHistory: any[]
}

const props = withDefaults(defineProps<Props>(), {
  currentData: null,
  stats: () => ({
    totalRecords: 0,
    dataRate: 0,
    lastUpdateTime: 0,
    connectionDuration: 0,
    errorCount: 0
  }),
  dataHistory: () => []
})

// Emits定义
const emit = defineEmits<{
  clearHistory: []
}>()

// 计算属性
const hasData = computed(() => props.dataHistory.length > 0)

// 格式化函数
const formatAngle = (angle: number): string => {
  return `${angle.toFixed(2)}°`
}

const formatCurrent = (current: number): string => {
  return `${current.toFixed(2)}A`
}

const formatVoltage = (voltage: number): string => {
  return `${voltage.toFixed(2)}V`
}

const formatTemperature = (temp: number): string => {
  return `${temp.toFixed(1)}°C`
}

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

// 样式类函数
const getAngleClass = (angle: number): string => {
  const absAngle = Math.abs(angle)
  if (absAngle > 30) return 'danger'
  if (absAngle > 15) return 'warning'
  return 'normal'
}

const getBatteryColor = (soc: number): string => {
  if (soc > 60) return '#67c23a'
  if (soc > 30) return '#e6a23c'
  return '#f56c6c'
}

const getTempClass = (temp: number): string => {
  if (temp > 70) return 'danger'
  if (temp > 50) return 'warning'
  return 'normal'
}

// 事件处理函数
const exportData = () => {
  try {
    const csvContent = IMUDataService.exportToCSV(props.dataHistory)
    const filename = `imu_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`
    IMUDataService.downloadData(csvContent, filename, 'text/csv')
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('数据导出失败')
  }
}

const clearHistory = () => {
  emit('clearHistory')
  ElMessage.success('历史数据已清空')
}
</script>

<style lang="scss" scoped>
.imu-data-display {
  padding: 16px;
  background: rgba(31, 41, 55, 0.9);
  border-radius: 12px;
  color: #f3f4f6;

  .data-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 20px;

    .data-card {
      background: rgba(55, 65, 81, 0.8);
      border-radius: 8px;
      padding: 16px;
      border: 1px solid rgba(75, 85, 99, 0.3);

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(75, 85, 99, 0.3);

        .card-icon {
          color: #3b82f6;
          font-size: 18px;
        }

        .card-title {
          font-size: 14px;
          font-weight: 600;
          color: #e5e7eb;
        }
      }

      .card-content {
        .attitude-item,
        .battery-item,
        .sensor-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .label {
            font-size: 12px;
            color: #9ca3af;
          }

          .value {
            font-size: 14px;
            font-weight: 600;
            color: #e5e7eb;

            &.normal { color: #10b981; }
            &.warning { color: #f59e0b; }
            &.danger { color: #ef4444; }
          }
        }

        .battery-progress {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          margin-left: 12px;

          .el-progress {
            flex: 1;
          }

          .battery-text {
            font-size: 12px;
            font-weight: 600;
            color: #e5e7eb;
            min-width: 35px;
          }
        }
      }
    }
  }

  .foot-force-section {
    margin-bottom: 20px;

    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      .section-icon {
        color: #3b82f6;
        font-size: 18px;
      }

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #e5e7eb;
      }
    }

    .foot-force-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .foot-item {
        background: rgba(55, 65, 81, 0.8);
        border-radius: 8px;
        padding: 12px;
        text-align: center;
        border: 1px solid rgba(75, 85, 99, 0.3);

        .foot-label {
          display: block;
          font-size: 12px;
          color: #9ca3af;
          margin-bottom: 4px;
        }

        .foot-value {
          font-size: 16px;
          font-weight: 600;
          color: #3b82f6;
        }
      }
    }
  }

  .data-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 20px;

    .stats-item {
      text-align: center;
      padding: 8px;
      background: rgba(55, 65, 81, 0.6);
      border-radius: 6px;

      .stats-label {
        display: block;
        font-size: 11px;
        color: #9ca3af;
        margin-bottom: 4px;
      }

      .stats-value {
        font-size: 14px;
        font-weight: 600;
        color: #e5e7eb;

        &.error {
          color: #ef4444;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;

    .el-button {
      background: rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.3);
      color: #3b82f6;

      &:hover {
        background: rgba(59, 130, 246, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
      }

      &.el-button--warning {
        background: rgba(245, 158, 11, 0.2);
        border-color: rgba(245, 158, 11, 0.3);
        color: #f59e0b;

        &:hover {
          background: rgba(245, 158, 11, 0.3);
          border-color: rgba(245, 158, 11, 0.5);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .imu-data-display {
    padding: 12px;

    .data-overview {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .data-stats {
      grid-template-columns: repeat(2, 1fr);
    }

    .action-buttons {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
