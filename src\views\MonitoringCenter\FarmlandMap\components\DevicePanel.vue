<template>
  <div class="device-panel">
    <template v-if="deviceData">
      <div class="device-header">
        <div class="device-icon">
          <svg-icon :name="getDeviceIcon(deviceData.type)" size="32" />
        </div>
        <div class="device-title">
          <h3>{{ deviceData.name }}</h3>
          <div class="device-status" :class="getStatusClass(deviceData.status)">
            {{ getStatusText(deviceData.status) }}
          </div>
        </div>
      </div>

      <div class="panel-sections">
        <div class="device-section">
          <h4>设备信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">设备ID</div>
              <div class="info-value">{{ deviceData.id }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">设备类型</div>
              <div class="info-value">{{ getDeviceTypeName(deviceData.type) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">安装位置</div>
              <div class="info-value">{{ deviceData.location }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">上线时间</div>
              <div class="info-value">{{ formatDate(deviceData.onlineTime) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">最后活动</div>
              <div class="info-value">{{ formatDate(deviceData.lastActivity) }}</div>
            </div>
          </div>
        </div>

        <div class="device-section">
          <h4>实时数据</h4>
          <div class="data-list">
            <div 
              v-for="(value, key) in deviceData.data" 
              :key="key" 
              class="data-item"
            >
              <div class="data-label">{{ getDataLabel(key) }}</div>
              <div class="data-value">
                {{ formatDataValue(key, value) }}
                <span class="data-unit">{{ getDataUnit(key) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="device-section actions">
          <el-button type="primary" size="large" icon="Setting">设备配置</el-button>
          <el-button size="large" icon="DocumentCopy">查看历史数据</el-button>
        </div>
      </div>
    </template>
    <div v-else class="no-device">
      <svg-icon name="device-none" size="64" />
      <p>请选择一个设备查看详情</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { formatDate } from '@/utils/date';
import SvgIcon from '@/components/SvgIcon/index.vue';

const props = defineProps({
  deviceData: {
    type: Object,
    default: null
  }
});

const isOpen = ref(false);

// 处理面板收起/展开状态变化
const handleCollapse = (collapsed: boolean) => {
  console.log('面板收起状态:', collapsed);
};

// 当设备数据改变时自动打开面板
watch(() => props.deviceData, (newVal) => {
  if (newVal) {
    isOpen.value = true;
  }
}, { immediate: true });

// 获取设备图标
const getDeviceIcon = (type) => {
  const iconMap = {
    'sensor': 'device-sensor',
    'camera': 'device-camera',
    'controller': 'device-controller',
    'gateway': 'device-gateway',
    'dog': 'device-robot-dog',
    'drone': 'device-drone'
  };
  return iconMap[type] || 'device-unknown';
};

// 获取设备类型名称
const getDeviceTypeName = (type) => {
  const typeMap = {
    'sensor': '传感器',
    'camera': '摄像头',
    'controller': '控制器',
    'gateway': '网关设备',
    'dog': '机器狗',
    'drone': '无人机'
  };
  return typeMap[type] || '未知设备';
};

// 获取状态类
const getStatusClass = (status) => {
  return {
    'status-online': status === 'online',
    'status-offline': status === 'offline',
    'status-warning': status === 'warning',
    'status-error': status === 'error'
  };
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'online': '在线',
    'offline': '离线',
    'warning': '警告',
    'error': '故障'
  };
  return statusMap[status] || '未知';
};

// 获取数据标签
const getDataLabel = (key) => {
  const labelMap = {
    'temperature': '温度',
    'humidity': '湿度',
    'soilMoisture': '土壤湿度',
    'lightIntensity': '光照强度',
    'batteryLevel': '电池电量',
    'co2': 'CO₂浓度',
    'windSpeed': '风速',
    'rainfall': '降雨量'
  };
  return labelMap[key] || key;
};

// 格式化数据值
const formatDataValue = (key, value) => {
  // 确保value是数字类型
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (key === 'batteryLevel') {
    return `${Math.round(numValue)}%`;
  }
  
  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return value; // 如果转换失败，返回原始值
  }
  
  return numValue.toFixed(1);
};

// 获取数据单位
const getDataUnit = (key) => {
  const unitMap = {
    'temperature': '°C',
    'humidity': '%',
    'soilMoisture': '%',
    'lightIntensity': 'lux',
    'batteryLevel': '',  // 已在formatDataValue中处理
    'co2': 'ppm',
    'windSpeed': 'm/s',
    'rainfall': 'mm'
  };
  return unitMap[key] || '';
};
</script>

<style scoped lang="scss">
@use "../styles/variables.scss" as vars;

// 设备面板样式
.device-panel {
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: transparent; // 移除背景色，使用父容器的背景色
}

// 适应折叠状态的样式
:deep(.left-panel.collapsed) {
  .device-panel {
    // 折叠状态下的内容处理
    .device-header {
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 10px 0;
      border-bottom: none;
      
      .device-icon {
        margin-right: 0;
        margin-bottom: 5px;
        width: 32px;
        height: 32px;
      }
      
      .device-title {
        display: none;
      }
    }
    
    .panel-sections {
      display: none;
    }
    
    .no-device {
      padding: 10px 0;
      
      svg {
        width: 30px;
        height: 30px;
        margin-bottom: 5px;
      }
      
      p {
        display: none;
      }
    }
  }
}

// 设备信息头部
.device-header {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(0, 21, 41, 0.3);
  
  .device-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(43, 255, 150, 0.1);
    margin-right: 15px;
    color: vars.$primary-color;
  }
  
  .device-title {
    flex: 1;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: vars.$text-light;
    }
    
    .device-status {
      margin-top: 4px;
      font-size: 13px;
      display: inline-block;
      padding: 2px 8px;
      border-radius: 10px;
      background: rgba(0, 0, 0, 0.2);
      
      &.status-online {
        color: #52c41a;
        background: rgba(82, 196, 26, 0.15);
      }
      
      &.status-offline {
        color: #bfbfbf;
        background: rgba(191, 191, 191, 0.15);
      }
      
      &.status-warning {
        color: #faad14;
        background: rgba(250, 173, 20, 0.15);
      }
      
      &.status-error {
        color: #f5222d;
        background: rgba(245, 34, 45, 0.15);
      }
    }
  }
}

// 面板内容区域
.panel-sections {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px;
  
  // 自定义滚动条
  @include vars.custom-scrollbar();
}

// 设备部分
.device-section {
  padding: 20px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  
  &:last-child {
    border-bottom: none;
  }
  
  h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: vars.$primary-color;
    font-weight: 500;
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background: vars.$primary-color;
      margin-right: 8px;
      border-radius: 2px;
    }
  }
  
  &.actions {
    display: flex;
    gap: 10px;
    
    .el-button {
      flex: 1;
    }
  }
}

// 信息网格
.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  
  .info-item {
    .info-label {
      font-size: 13px;
      color: vars.$text-secondary;
      margin-bottom: 4px;
    }
    
    .info-value {
      font-size: 14px;
      color: vars.$text-light;
      word-break: break-all;
    }
  }
}

// 数据列表
.data-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  
  .data-item {
    padding: 12px;
    background: rgba(0, 21, 41, 0.3);
    border-radius: 8px;
    transition: vars.$transition-normal;
    border: 1px solid rgba(255, 255, 255, 0.05);
    
    &:hover {
      background: rgba(43, 255, 150, 0.08);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }
    
    .data-label {
      font-size: 13px;
      color: vars.$text-secondary;
      margin-bottom: 5px;
    }
    
    .data-value {
      font-size: 20px;
      font-weight: 600;
      color: vars.$text-light;
      
      .data-unit {
        font-size: 14px;
        font-weight: normal;
        color: vars.$text-secondary;
        margin-left: 2px;
      }
    }
  }
}

// 无设备选择时的状态
.no-device {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: vars.$text-secondary;
  padding: 40px;
  text-align: center;
  
  svg {
    margin-bottom: 20px;
    opacity: 0.5;
  }
  
  p {
    font-size: 16px;
    margin: 0;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .info-grid,
  .data-list {
    grid-template-columns: 1fr; // 在移动设备上显示为单列
  }
}
</style> 