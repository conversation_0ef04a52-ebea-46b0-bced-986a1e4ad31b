/**
 * Three.js服务 - 负责3D场景管理和模型渲染
 */
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import type { DeviceInfo } from '../types';

export class ThreeService {
  // Three.js核心对象
  private scene: THREE.Scene | null = null;
  private camera: THREE.PerspectiveCamera | null = null;
  private renderer: THREE.WebGLRenderer | null = null;
  private controls: OrbitControls | null = null;
  private clock: THREE.Clock = new THREE.Clock();
  
  // 农田模型
  private farmlandModel: THREE.Group | null = null;
  
  // 设备模型和标记
  private deviceMarkers: Map<string, THREE.Object3D> = new Map();
  private deviceLabels: Map<string, { element: HTMLDivElement, position: THREE.Vector3 }> = new Map();
  
  // 路径管理
  private dogPaths: Map<string, {points: THREE.Vector3[], currentIndex: number, progress: number}> = new Map();
  private pathUpdateInterval: number = 3; // 增加到3，减少位置更新频率，提高性能
  private frameCounter: number = 0; // 帧计数器
  private lastFrameTime: number = 0; // 帧时间跟踪
  private targetFPS: number = 60; // 目标帧率
  private heightCache: Map<string, number> = new Map(); // 缓存高度检测结果
  
  // 键盘控制相关属性
  private keyStates: Map<string, boolean> = new Map();
  private cameraSpeed: number = 0.1; // 相机移动速度
  private keyboardControlEnabled: boolean = true; // 是否启用键盘控制
  
  // 缓存设备模型
  private dogModel: THREE.Group | null = null;
  
  // 动画帧请求ID
  private animationFrameId: number | null = null;
  
  // 模型加载器
  private gltfLoader: GLTFLoader = new GLTFLoader();
  
  // 场景状态
  private isInitialized: boolean = false;
  private isModelLoaded: boolean = false;
  
  // 容器元素
  private container: HTMLElement | null = null;
  
  // 回调函数
  private onDeviceClickCallback: ((deviceId: string) => void) | null = null;
  private onLoadProgressCallback: ((progress: number) => void) | null = null;
  private onLoadErrorCallback: ((error: any) => void) | null = null;
  private onLoadCompleteCallback: (() => void) | null = null;
  
  // 添加限制同时动画对象数量的属性
  private maxActiveAnimations: number = 2; // 最大同时活动动画数
  private activeAnimationsCount: number = 0; // 当前活动动画计数
  
  /**
   * 初始化Three.js场景
   * @param containerId 容器元素ID
   * @returns 初始化是否成功
   */
  public async initScene(containerId: string): Promise<boolean> {
    try {
      // 获取容器元素
      this.container = document.getElementById(containerId);
      if (!this.container) {
        console.error('找不到容器元素:', containerId);
        return false;
      }
      
      // 创建场景
      this.scene = new THREE.Scene();
      this.scene.background = new THREE.Color(0x0a192f); // 深蓝色背景
      
      // 添加环境光和平行光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
      this.scene.add(ambientLight);
      
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(0, 10, 10);
      directionalLight.castShadow = true;
      this.scene.add(directionalLight);
      
      // 创建相机
      const { width, height } = this.container.getBoundingClientRect();
      this.camera = new THREE.PerspectiveCamera(
        45, // 视野角度
        width / height, // 宽高比
        0.1, // 近裁剪面
        1000 // 远裁剪面
      );
      this.camera.position.set(0, 5, 10);
      
      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
      this.renderer.setSize(width, height);
      this.renderer.setPixelRatio(window.devicePixelRatio);
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      this.container.appendChild(this.renderer.domElement);
      
      // 创建轨道控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true; // 启用阻尼效果
      this.controls.dampingFactor = 0.05;
      this.controls.screenSpacePanning = false;
      this.controls.minDistance = 3;
      this.controls.maxDistance = 30;
      this.controls.maxPolarAngle = Math.PI / 2;
      
      // 添加事件监听
      window.addEventListener('resize', this.handleResize.bind(this));
      this.renderer.domElement.addEventListener('click', this.handleClick.bind(this));
      
      // 初始化键盘控制
      this.initKeyboardControls();
      
      // 标记初始化完成
      this.isInitialized = true;
      
      // 开始渲染循环
      this.startRenderLoop();
      
      return true;
    } catch (error) {
      console.error('初始化Three.js场景失败:', error);
      return false;
    }
  }
  
  /**
   * 加载农田模型
   * @param modelPath 模型路径
   * @returns 加载是否成功
   */
  public loadFarmlandModel(modelPath: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!this.scene) {
        reject(new Error('场景未初始化'));
        return;
      }
      
      // 显示加载进度
      const onProgress = (xhr: ProgressEvent) => {
        const progress = xhr.loaded / xhr.total * 100;
        console.log(`模型加载进度: ${progress.toFixed(2)}%`);
        if (this.onLoadProgressCallback) {
          this.onLoadProgressCallback(progress);
        }
      };
      
      // 加载模型
      this.gltfLoader.load(
        modelPath,
        (gltf) => {
          // 模型加载成功
          this.farmlandModel = gltf.scene;
          
          // 调整模型位置和比例
          this.farmlandModel.scale.set(1, 1, 1);
          this.farmlandModel.position.set(0, 0, 0);
          this.farmlandModel.rotation.y = Math.PI;
          
          // 遍历模型中的所有网格，启用阴影
          this.farmlandModel.traverse((object) => {
            if (object instanceof THREE.Mesh) {
              object.castShadow = true;
              object.receiveShadow = true;
              
              // 如果是地面，接收阴影但不投射阴影
              if (object.name.toLowerCase().includes('ground') || 
                  object.name.toLowerCase().includes('land') || 
                  object.name.toLowerCase().includes('terrain')) {
                object.castShadow = false;
                object.receiveShadow = true;
              }
            }
          });
          
          // 添加模型到场景
          this.scene!.add(this.farmlandModel);
          
          // 标记模型已加载
          this.isModelLoaded = true;
          
          // 调用加载完成回调
          if (this.onLoadCompleteCallback) {
            this.onLoadCompleteCallback();
          }
          
          resolve(true);
        },
        onProgress,
        (error) => {
          // 模型加载失败
          console.error('加载农田模型失败:', error);
          if (this.onLoadErrorCallback) {
            this.onLoadErrorCallback(error);
          }
          reject(error);
        }
      );
    });
  }
  
  /**
   * 通用模型加载方法
   * @param modelPath 模型路径
   * @returns 加载的模型组
   */
  private loadModel(modelPath: string): Promise<THREE.Group> {
    return new Promise((resolve, reject) => {
      // 显示加载进度
      const onProgress = (xhr: ProgressEvent) => {
        const progress = xhr.loaded / xhr.total * 100;
        console.log(`${modelPath}加载进度: ${progress.toFixed(2)}%`);
        if (this.onLoadProgressCallback) {
          this.onLoadProgressCallback(progress);
        }
      };
      
      // 加载模型
      this.gltfLoader.load(
        modelPath,
        (gltf) => {
          const model = gltf.scene;
          
          // 启用阴影
          model.traverse((object) => {
            if (object instanceof THREE.Mesh) {
              object.castShadow = true;
              object.receiveShadow = true;
            }
          });
          
          // 不要将模型添加到场景中，只是缓存它
          resolve(model);
        },
        onProgress,
        (error) => {
          console.error(`加载模型${modelPath}失败:`, error);
          reject(error);
        }
      );
    });
  }
  
  /**
   * 预加载设备模型
   * @returns 加载是否成功
   */
  public async preloadDeviceModels(): Promise<boolean> {
    if (!this.scene) {
      console.error('场景未初始化，无法加载设备模型');
      return false;
    }
    
    try {
      // 加载机器狗模型
      this.dogModel = await this.loadModel('/models/robot_dog_unitree_go2.glb');
      console.log('机器狗模型加载成功');
      return true;
    } catch (error) {
      console.error('加载设备模型失败:', error);
      return false;
    }
  }
  
  /**
   * 更新设备位置和状态
   * @param devices 设备信息列表
   */
  public updateDevices(devices: DeviceInfo[]): void {
    if (!this.scene || !this.isModelLoaded) return;
    
    // 清除现有的设备标记
    this.clearDeviceMarkers();
    
    // 创建新的设备标记
    devices.forEach((device) => {
      this.createDeviceMarker(device);
    });
  }
  
  /**
   * 清除设备标记
   */
  private clearDeviceMarkers(): void {
    // 移除3D标记
    this.deviceMarkers.forEach((marker) => {
      if (this.scene) {
        this.scene.remove(marker);
      }
    });
    this.deviceMarkers.clear();
    
    // 移除HTML标签
    this.deviceLabels.forEach(({ element }) => {
      element.parentNode?.removeChild(element);
    });
    this.deviceLabels.clear();
  }
  
  /**
   * 确定指定位置的路面高度
   * @param x X坐标
   * @param z Z坐标
   * @returns 路面高度（Y坐标）
   */
  private determineSurfaceHeight(x: number, z: number): number {
    if (!this.scene || !this.farmlandModel) return 0.01;
    
    // 从高处向下发射射线
    const raycaster = new THREE.Raycaster();
    const startPoint = new THREE.Vector3(x, 10, z);
    const direction = new THREE.Vector3(0, -1, 0);
    raycaster.set(startPoint, direction.normalize());
    
    // 获取与农田模型的交点
    const intersects = raycaster.intersectObject(this.farmlandModel, true);
    
    if (intersects.length > 0) {
      console.log(`路面检测高度: ${intersects[0].point.y}`);
      return intersects[0].point.y + 0.01; // 添加小偏移防止模型穿透地面
    }
    
    return 0.01; // 默认高度
  }
  
  /**
   * 确定指定位置的路面高度（优化版，使用缓存）
   * @param x X坐标
   * @param z Z坐标
   * @returns 路面高度（Y坐标）
   */
  private determineSurfaceHeightOptimized(x: number, z: number): number {
    // 生成缓存键 - 将坐标四舍五入到一定精度，避免过多的缓存条目
    const cacheKey = `${Math.round(x * 10)},${Math.round(z * 10)}`;
    
    // 检查缓存中是否有结果
    if (this.heightCache.has(cacheKey)) {
      return this.heightCache.get(cacheKey)!;
    }
    
    // 执行原始高度检测
    const height = this.determineSurfaceHeight(x, z);
    
    // 缓存结果
    this.heightCache.set(cacheKey, height);
    
    // 限制缓存大小，防止内存泄漏
    if (this.heightCache.size > 1000) {
      // 删除最早的20%缓存
      const keysToDelete = Array.from(this.heightCache.keys()).slice(0, 200);
      keysToDelete.forEach(key => this.heightCache.delete(key));
    }
    
    return height;
  }
  
  /**
   * 优化渲染设置以提高性能
   */
  private optimizeRendererForPerformance(): void {
    if (!this.renderer) return;
    
    // 关闭阴影或降低阴影质量
    this.renderer.shadowMap.enabled = false;
    
    // 降低像素比，提高性能
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
    
    // 使用基本的WebGL设置
    const gl = this.renderer.getContext();
    if (gl) {
      gl.getExtension('WEBGL_lose_context'); // 允许在页面不可见时释放上下文
    }
    
    console.log('已应用渲染器性能优化');
  }
  
  /**
   * 创建设备标记
   * @param device 设备信息
   */
  private createDeviceMarker(device: DeviceInfo): void {
    if (!this.scene || !this.container) return;
    
    // 设置标记位置
    // 注意：这里需要将2D坐标转换为3D坐标，可能需要根据实际模型调整
    const x = (device.position.x - 400) * 0.01; // 示例转换，需要根据实际模型调整
    const z = (device.position.y - 250) * 0.01; // 示例转换，需要根据实际模型调整
    
    // 根据设备类型确定高度
    let y = 0;
    if (device.type === 'dog') {
      // 使用射线检测确定路面高度，并添加小偏移确保模型放大后不会陷入地面
      y = this.determineSurfaceHeightOptimized(x, z) + 1.75; // 添加0.03的高度偏移
    } else {
      // 无人机保持高空
      y = 2.0;
    }
    
    // 创建标记对象
    let marker: THREE.Object3D;
    
    if (device.type === 'dog' && this.dogModel) {
      // 使用机器狗3D模型
      marker = this.dogModel.clone();
      
      // 调整模型大小和方向
      marker.scale.set(4, 4, 4); // 放大模型尺寸，使机器狗更加明显
      marker.rotation.y = Math.PI * 0.5; // 调整朝向，使机器狗面向正确方向
      
      // 设置状态颜色
      // 为模型的材质应用状态颜色
      let statusColor: THREE.Color;
      switch (device.status) {
        case 'online':
          statusColor = new THREE.Color(0x00ff00); // 绿色
          break;
        case 'standby':
          statusColor = new THREE.Color(0xffff00); // 黄色
          break;
        case 'offline':
          statusColor = new THREE.Color(0xff0000); // 红色
          break;
        default:
          statusColor = new THREE.Color(0xffffff); // 白色
      }
      
      // 遍历模型中的所有部件，为指示灯部分应用状态颜色
      marker.traverse((object) => {
        if (object instanceof THREE.Mesh && 
            (object.name.toLowerCase().includes('light') || 
             object.name.toLowerCase().includes('led') || 
             object.name.toLowerCase().includes('indicator'))) {
          
          if (object.material) {
            // 如果是单个材质
            if (!Array.isArray(object.material)) {
              const newMaterial = object.material.clone();
              newMaterial.emissive = statusColor;
              newMaterial.emissiveIntensity = 1.0;
              object.material = newMaterial;
            }
            // 如果是材质数组
            else {
              object.material = object.material.map(mat => {
                const newMat = mat.clone();
                newMat.emissive = statusColor;
                newMat.emissiveIntensity = 1.0;
                return newMat;
              });
            }
          }
        }
      });
    } else {
      // 根据设备类型和状态创建不同的标记
      let markerColor: number;
      switch (device.status) {
        case 'online':
          markerColor = 0x00ff00; // 绿色
          break;
        case 'standby':
          markerColor = 0xffff00; // 黄色
          break;
        case 'offline':
          markerColor = 0xff0000; // 红色
          break;
        default:
          markerColor = 0xffffff; // 白色
      }
      
      // 创建标记几何体
      let markerGeometry: THREE.BufferGeometry;
      if (device.type === 'drone') {
        markerGeometry = new THREE.ConeGeometry(0.2, 0.4, 8);
      } else {
        markerGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
      }
      
      // 创建标记材质
      const markerMaterial = new THREE.MeshStandardMaterial({
        color: markerColor,
        emissive: markerColor,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
      });
      
      // 创建标记网格
      marker = new THREE.Mesh(markerGeometry, markerMaterial);
      
      // 如果是无人机，调整旋转
      if (device.type === 'drone') {
        marker.rotation.x = Math.PI;
      }
    }
    
    // 设置标记位置
    marker.position.set(x, y, z);
    
    // 添加到场景
    this.scene.add(marker);
    
    // 存储标记引用
    this.deviceMarkers.set(device.id, marker);
    
    // 创建HTML标签
    const labelOffset = device.type === 'dog' ? 0.7 : 0.5; // 为机器狗提供更大的标签偏移
    this.createDeviceLabel(device, new THREE.Vector3(x, y + labelOffset, z)); // 标签位置上移，以适应模型高度
    
    // 为机器狗创建默认路径
    if (device.type === 'dog') {
      // 创建一个围绕当前位置的简单环形路径
      const pathRadius = 3; // 路径半径
      const startX = x;
      const startZ = z;
      
      // 根据农田模型创建适合的路径点
      // 这里创建一条沿着道路的路径（通常是Z轴方向）
      this.defineDogPath(
        device.id,
        {x: startX, y: y, z: startZ},
        {x: startX, y: y, z: startZ},
        [
          {x: startX, y: y, z: startZ + pathRadius}, // 向前
          {x: startX + pathRadius, y: y, z: startZ + pathRadius}, // 向右前
          {x: startX + pathRadius, y: y, z: startZ}, // 向右
          {x: startX + pathRadius, y: y, z: startZ - pathRadius}, // 向右后
          {x: startX, y: y, z: startZ - pathRadius}, // 向后
          {x: startX - pathRadius, y: y, z: startZ - pathRadius}, // 向左后
          {x: startX - pathRadius, y: y, z: startZ}, // 向左
          {x: startX - pathRadius, y: y, z: startZ + pathRadius}, // 向左前
        ]
      );
      
      // 在开发环境下可视化路径（可选）
      // this.visualizeDogPath(device.id, true);
    }
    
    // 添加动画效果
    this.animateMarker(marker, device.type, device.id);
  }
  
  /**
   * 创建设备HTML标签
   * @param device 设备信息
   * @param position 3D位置
   */
  private createDeviceLabel(device: DeviceInfo, position: THREE.Vector3): void {
    if (!this.container) return;
    
    // 创建标签元素
    const labelElement = document.createElement('div');
    labelElement.className = 'device-label';
    labelElement.innerHTML = `
      <div class="device-label-content">
        <div class="device-label-name">${device.name}</div>
        <div class="device-label-status ${device.status}">${device.status}</div>
      </div>
    `;
    
    // 设置样式
    labelElement.style.position = 'absolute';
    labelElement.style.pointerEvents = 'none';
    labelElement.style.zIndex = '10';
    labelElement.style.transform = 'translate(-50%, -100%)';
    
    // 添加到容器
    this.container.appendChild(labelElement);
    
    // 存储标签引用和位置
    this.deviceLabels.set(device.id, {
      element: labelElement,
      position: position
    });
  }
  
  /**
   * 为标记添加动画效果
   * @param marker 标记对象
   * @param deviceType 设备类型
   * @param deviceId 设备ID
   */
  private animateMarker(marker: THREE.Object3D, deviceType: string, deviceId: string): void {
    // 为不同类型的设备添加不同的动画
    if (deviceType === 'drone') {
      // 无人机上下浮动
      const initialY = marker.position.y;
      const amplitude = 0.2;
      const frequency = 0.5;
      
      const animate = () => {
        const time = this.clock.getElapsedTime();
        marker.position.y = initialY + Math.sin(time * frequency) * amplitude;
        marker.rotation.z = time * 0.5; // 旋转
      };
      
      marker.userData.animate = animate;
    } else if (deviceType === 'dog') {
      // 机器狗沿路径行走的动画
      const animate = () => {
        // 性能优化：只在特定帧更新位置
        this.frameCounter++;
        if (this.frameCounter % this.pathUpdateInterval !== 0) return;
        
        const pathData = this.dogPaths.get(deviceId);
        if (!pathData || pathData.points.length < 2) return;
        
        const { points, currentIndex, progress } = pathData;
        
        // 获取当前路径段的起点和终点
        const currentPoint = points[currentIndex];
        const nextPoint = points[(currentIndex + 1) % points.length];
        
        // 计算新的位置
        const newPosition = new THREE.Vector3();
        newPosition.lerpVectors(currentPoint, nextPoint, progress);
        
        // 检测路面高度并应用
        const surfaceHeight = this.determineSurfaceHeightOptimized(newPosition.x, newPosition.z);
        newPosition.y = surfaceHeight + 0.03; // 添加小偏移避免穿模
        
        // 设置新位置
        marker.position.copy(newPosition);
        
        // 计算并设置朝向（面向前进方向）
        const direction = new THREE.Vector3().subVectors(nextPoint, currentPoint).normalize();
        if (direction.length() > 0.01) {
          const targetRotation = Math.atan2(direction.x, direction.z);
          marker.rotation.y = targetRotation;
        }
        
        // 简化的行走动画效果（仅保留最基本的上下运动，移除侧倾效果）
        // 使用clock而不是Date.now()，减少性能消耗
        const time = this.clock.getElapsedTime();
        marker.position.y += Math.sin(time * 5) * 0.01; // 减小振幅，简化动画
        
        // 更新进度
        const newProgress = progress + 0.01; // 增加行走速度，从0.005增加到0.01
        if (newProgress >= 1) {
          // 进入下一路径段
          this.dogPaths.set(deviceId, {
            points,
            currentIndex: (currentIndex + 1) % points.length,
            progress: 0
          });
        } else {
          // 更新当前进度
          this.dogPaths.set(deviceId, {
            points,
            currentIndex,
            progress: newProgress
          });
        }
      };
      
      marker.userData.animate = animate;
    } else {
      // 其他设备的动画
      const initialY = marker.position.y;
      
      const animate = () => {
        const time = this.clock.getElapsedTime();
        
        // 轻微的上下运动
        const bounce = Math.sin(time * 2) * 0.01;
        marker.position.y = initialY + bounce;
      };
      
      marker.userData.animate = animate;
    }
  }
  
  /**
   * 更新HTML标签位置
   */
  private updateLabelsPosition(): void {
    if (!this.camera || !this.renderer) return;
    
    this.deviceLabels.forEach((labelData, deviceId) => {
      const { element, position } = labelData;
      
      // 将3D位置转换为屏幕坐标
      const screenPosition = position.clone();
      screenPosition.project(this.camera!);
      
      // 转换为CSS坐标
      const x = (screenPosition.x * 0.5 + 0.5) * this.renderer!.domElement.clientWidth;
      const y = (screenPosition.y * -0.5 + 0.5) * this.renderer!.domElement.clientHeight;
      
      // 更新标签位置
      element.style.left = `${x}px`;
      element.style.top = `${y}px`;
      
      // 根据深度设置透明度
      const distance = position.distanceTo(this.camera!.position);
      const opacity = Math.max(0, Math.min(1, 1 - (distance - 5) / 15));
      element.style.opacity = opacity.toString();
      
      // 如果在相机后面，隐藏标签
      if (screenPosition.z > 1) {
        element.style.display = 'none';
      } else {
        element.style.display = 'block';
      }
    });
  }
  
  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    if (!this.camera || !this.renderer || !this.container) return;
    
    // 获取容器新尺寸
    const { width, height } = this.container.getBoundingClientRect();
    
    // 更新相机宽高比
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    
    // 更新渲染器尺寸
    this.renderer.setSize(width, height);
  }
  
  /**
   * 处理点击事件
   * @param event 鼠标事件
   */
  private handleClick(event: MouseEvent): void {
    if (!this.camera || !this.renderer || !this.scene) return;
    
    // 计算鼠标位置的标准化设备坐标
    const rect = this.renderer.domElement.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    const y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    
    // 创建射线
    const raycaster = new THREE.Raycaster();
    raycaster.setFromCamera(new THREE.Vector2(x, y), this.camera);
    
    // 获取所有设备标记对象
    const markerObjects = Array.from(this.deviceMarkers.values());
    
    // 检测射线与标记的交点
    const intersects = raycaster.intersectObjects(markerObjects, true); // 添加true参数以递归检测子对象
    
    if (intersects.length > 0) {
      // 找到被点击的设备
      const clickedObject = intersects[0].object;
      let clickedDeviceId: string | null = null;
      
      // 查找对应的设备ID
      this.deviceMarkers.forEach((marker, deviceId) => {
        // 检查点击的对象是否是标记本身或其子对象
        if (marker === clickedObject || marker.getObjectById(clickedObject.id) !== undefined) {
          clickedDeviceId = deviceId;
        }
      });
      
      if (clickedDeviceId && this.onDeviceClickCallback) {
        // 调用设备点击回调
        this.onDeviceClickCallback(clickedDeviceId);
      }
    }
  }
  
  /**
   * 聚焦到指定设备
   * @param deviceId 设备ID
   */
  public focusOnDevice(deviceId: string): void {
    const marker = this.deviceMarkers.get(deviceId);
    if (!marker || !this.controls) return;
    
    // 获取标记位置
    const position = marker.position.clone();
    
    // 创建动画
    const startPosition = this.camera!.position.clone();
    const targetPosition = position.clone().add(new THREE.Vector3(2, 2, 2));
    const duration = 1000; // 毫秒
    const startTime = Date.now();
    
    // 动画函数
    const animate = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      
      if (elapsed < duration) {
        // 计算插值
        const t = elapsed / duration;
        const easeT = this.easeOutQuad(t);
        
        // 更新相机位置
        this.camera!.position.lerpVectors(startPosition, targetPosition, easeT);
        this.controls!.target.lerp(position, easeT);
        
        // 继续动画
        requestAnimationFrame(animate);
      } else {
        // 动画结束，确保相机位于目标位置
        this.camera!.position.copy(targetPosition);
        this.controls!.target.copy(position);
      }
      
      // 更新控制器
      this.controls!.update();
    };
    
    // 开始动画
    animate();
  }
  
  /**
   * 二次缓出函数
   * @param t 时间参数 (0-1)
   * @returns 缓动值 (0-1)
   */
  private easeOutQuad(t: number): number {
    return t * (2 - t);
  }
  
  /**
   * 判断对象是否在视口内
   * @param object 3D对象
   * @returns 是否在视口内
   */
  private isInViewport(object: THREE.Object3D): boolean {
    if (!this.camera) return false;
    
    try {
      const frustum = new THREE.Frustum();
      const matrix = new THREE.Matrix4().multiplyMatrices(
        this.camera.projectionMatrix,
        this.camera.matrixWorldInverse
      );
      frustum.setFromProjectionMatrix(matrix);
      
      // 对于Group对象，检查其世界位置
      if (object instanceof THREE.Group) {
        const position = new THREE.Vector3();
        object.getWorldPosition(position);
        return frustum.containsPoint(position);
      }
      
      // 对于网格对象，确保它有包围球
      if (object instanceof THREE.Mesh && object.geometry && object.geometry.boundingSphere === null) {
        object.geometry.computeBoundingSphere();
      }
      
      // 使用标准的intersectsObject
      return frustum.intersectsObject(object);
    } catch (error) {
      console.warn('视口检查出错:', error);
      return true; // 默认假设对象在视口内，以确保它会被更新
    }
  }

  /**
   * 判断对象是否距离相机较近
   * @param object 3D对象
   * @param maxDistance 最大距离
   * @returns 是否距离相机较近
   */
  private isNearCamera(object: THREE.Object3D, maxDistance: number): boolean {
    if (!this.camera) return false;
    
    const distance = this.camera.position.distanceTo(object.position);
    return distance < maxDistance;
  }
  
  /**
   * 设置设备点击回调
   * @param callback 回调函数
   */
  public setOnDeviceClickCallback(callback: (deviceId: string) => void): void {
    this.onDeviceClickCallback = callback;
  }
  
  /**
   * 设置加载进度回调
   * @param callback 回调函数
   */
  public setOnLoadProgressCallback(callback: (progress: number) => void): void {
    this.onLoadProgressCallback = callback;
  }
  
  /**
   * 设置加载错误回调
   * @param callback 回调函数
   */
  public setOnLoadErrorCallback(callback: (error: any) => void): void {
    this.onLoadErrorCallback = callback;
  }
  
  /**
   * 设置加载完成回调
   * @param callback 回调函数
   */
  public setOnLoadCompleteCallback(callback: () => void): void {
    this.onLoadCompleteCallback = callback;
  }
  
  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    if (this.animationFrameId !== null) return;
    
    // 优化渲染器设置
    this.optimizeRendererForPerformance();
    
    // 帧率计算变量
    let frameCount = 0;
    let lastFPSUpdate = 0;
    let currentFPS = 0;
    
    const animate = () => {
      this.animationFrameId = requestAnimationFrame(animate);
      
      // 计算帧间隔时间
      const currentTime = performance.now();
      const deltaTime = (currentTime - this.lastFrameTime) / 1000;
      this.lastFrameTime = currentTime;
      
      // 帧率计算（每秒更新一次）
      frameCount++;
      if (currentTime - lastFPSUpdate >= 1000) {
        currentFPS = frameCount;
        frameCount = 0;
        lastFPSUpdate = currentTime;
        
        // 动态调整性能参数
        if (currentFPS < 25) { // 如果帧率太低
          this.pathUpdateInterval = Math.min(this.pathUpdateInterval + 1, 5);
          this.maxActiveAnimations = Math.max(1, this.maxActiveAnimations - 1);
          console.log(`性能优化: 当前FPS=${currentFPS}, 降低更新频率, 当前间隔=${this.pathUpdateInterval}帧, 活动动画=${this.maxActiveAnimations}`);
        } else if (currentFPS > 50) { // 如果帧率足够高
          this.pathUpdateInterval = Math.max(1, this.pathUpdateInterval - 1);
          this.maxActiveAnimations = Math.min(5, this.maxActiveAnimations + 1);
          console.log(`性能优化: 当前FPS=${currentFPS}, 提高更新频率, 当前间隔=${this.pathUpdateInterval}帧, 活动动画=${this.maxActiveAnimations}`);
        }
      }
      
      // 更新相机位置（基于键盘控制）
      this.updateCameraPosition();
      
      // 更新控制器
      if (this.controls) {
        this.controls.update();
      }
      
      // 重置活动动画计数
      this.activeAnimationsCount = 0;
      
      // 更新设备标记动画
      this.deviceMarkers.forEach((marker, deviceId) => {
        if (marker.userData.animate) {
          // 性能优化：检查设备是否在视图内或者距离相机较近，并且限制同时动画数量
          if ((this.isInViewport(marker) || this.isNearCamera(marker, 15)) && 
              this.activeAnimationsCount < this.maxActiveAnimations) {
            this.activeAnimationsCount++;
            marker.userData.animate();
          }
        }
      });
      
      // 更新HTML标签位置（减少更新频率）
      if (frameCount % 2 === 0) {
        this.updateLabelsPosition();
      }
      
      // 渲染场景
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera);
      }
    };
    
    this.lastFrameTime = performance.now();
    animate();
  }
  
  /**
   * 停止渲染循环
   */
  public stopRenderLoop(): void {
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }
  
  /**
   * 释放资源
   */
  public dispose(): void {
    // 停止渲染循环
    this.stopRenderLoop();
    
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize.bind(this));
    if (this.renderer) {
      this.renderer.domElement.removeEventListener('click', this.handleClick.bind(this));
    }
    
    // 移除键盘事件监听
    window.removeEventListener('keydown', this.handleKeyDown.bind(this));
    window.removeEventListener('keyup', this.handleKeyUp.bind(this));
    
    // 清除设备标记
    this.clearDeviceMarkers();
    
    // 释放模型资源
    this.dogModel = null;
    
    // 释放Three.js资源
    if (this.renderer) {
      this.renderer.dispose();
    }
    
    // 移除渲染器DOM元素
    if (this.renderer && this.container) {
      this.container.removeChild(this.renderer.domElement);
    }
    
    // 重置属性
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.controls = null;
    this.farmlandModel = null;
    this.container = null;
    this.isInitialized = false;
    this.isModelLoaded = false;
  }
  
  /**
   * 设置预设视角
   * @param viewType 视角类型：'top', 'front', 'side'
   */
  public setViewPosition(viewType: 'top' | 'front' | 'side'): void {
    if (!this.camera || !this.controls) return;
    
    // 动画过渡到新视角
    const startPosition = this.camera.position.clone();
    const startTarget = this.controls.target.clone();
    const duration = 1000; // 毫秒
    const startTime = Date.now();
    
    // 根据视角类型设置目标位置
    let targetPosition: THREE.Vector3;
    let targetTarget: THREE.Vector3 = new THREE.Vector3(0, 0, 0); // 默认目标点
    
    switch (viewType) {
      case 'top':
        targetPosition = new THREE.Vector3(0, 10, 0);
        break;
      case 'front':
        targetPosition = new THREE.Vector3(0, 2, 10);
        break;
      case 'side':
        targetPosition = new THREE.Vector3(10, 2, 0);
        break;
      default:
        targetPosition = new THREE.Vector3(5, 5, 5);
    }
    
    // 动画函数
    const animate = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      
      if (elapsed < duration) {
        // 计算插值
        const t = elapsed / duration;
        const easeT = this.easeOutQuad(t);
        
        // 更新相机位置
        this.camera!.position.lerpVectors(startPosition, targetPosition, easeT);
        this.controls!.target.lerp(targetTarget, easeT);
        
        // 继续动画
        requestAnimationFrame(animate);
      } else {
        // 动画结束，确保相机位于目标位置
        this.camera!.position.copy(targetPosition);
        this.controls!.target.copy(targetTarget);
      }
      
      // 更新控制器
      this.controls!.update();
    };
    
    // 开始动画
    animate();
  }
  
  /**
   * 定义机器狗行走路径
   * @param deviceId 设备ID
   * @param startPoint 起点
   * @param endPoint 终点
   * @param intermediatePoints 中间路径点（可选）
   */
  public defineDogPath(
    deviceId: string,
    startPoint: {x: number, y: number, z: number},
    endPoint: {x: number, y: number, z: number},
    intermediatePoints: {x: number, y: number, z: number}[] = []
  ): void {
    // 转换为Three.js的Vector3对象
    const points: THREE.Vector3[] = [
      new THREE.Vector3(startPoint.x, startPoint.y, startPoint.z)
    ];
    
    // 添加中间点
    intermediatePoints.forEach(point => {
      points.push(new THREE.Vector3(point.x, point.y, point.z));
    });
    
    // 添加终点
    points.push(new THREE.Vector3(endPoint.x, endPoint.y, endPoint.z));
    
    // 存储路径信息
    this.dogPaths.set(deviceId, {
      points,
      currentIndex: 0,
      progress: 0
    });
  }
  
  /**
   * 可视化机器狗路径（用于调试）
   * @param deviceId 设备ID
   * @param visible 是否可见
   */
  public visualizeDogPath(deviceId: string, visible: boolean): void {
    const pathData = this.dogPaths.get(deviceId);
    if (!pathData || !this.scene) return;
    
    // 移除旧的路径可视化
    const oldPath = this.scene.getObjectByName(`path_${deviceId}`);
    if (oldPath) {
      this.scene.remove(oldPath);
    }
    
    if (!visible) return;
    
    // 创建路径几何体
    const points = pathData.points;
    const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
    const lineMaterial = new THREE.LineBasicMaterial({ color: 0x00ff00, linewidth: 2 });
    const pathLine = new THREE.Line(lineGeometry, lineMaterial);
    pathLine.name = `path_${deviceId}`;
    
    // 添加到场景
    this.scene.add(pathLine);
  }
  
  /**
   * 切换设备可见性
   * @param visible 是否可见
   */
  public toggleDevicesVisibility(visible: boolean): void {
    // 遍历所有设备标记，设置可见性
    this.deviceMarkers.forEach((marker) => {
      marker.visible = visible;
    });
    
    // 遍历所有设备标签，设置可见性
    this.deviceLabels.forEach(({ element }) => {
      element.style.display = visible ? 'block' : 'none';
    });
  }
  
  /**
   * 切换标签可见性
   * @param visible 是否可见
   */
  public toggleLabelsVisibility(visible: boolean): void {
    // 遍历所有设备标签，设置可见性
    this.deviceLabels.forEach(({ element }) => {
      element.style.display = visible ? 'block' : 'none';
    });
  }
  
  /**
   * 切换视图模式
   * @param mode 视图模式：'2d' 或 '3d'
   */
  public switchViewMode(mode: '2d' | '3d'): void {
    if (!this.camera || !this.controls) return;
    
    if (mode === '2d') {
      // 切换到2D视图（俯视图）
      this.setViewPosition('top');
      
      // 限制相机移动
      this.controls.minPolarAngle = Math.PI / 2; // 90度
      this.controls.maxPolarAngle = Math.PI / 2; // 90度
      this.controls.enableRotate = false; // 禁用旋转
    } else {
      // 切换到3D视图
      this.setViewPosition('front');
      
      // 恢复相机控制
      this.controls.minPolarAngle = 0; // 0度
      this.controls.maxPolarAngle = Math.PI / 2; // 90度
      this.controls.enableRotate = true; // 启用旋转
    }
  }
  
  /**
   * 更改地图样式
   * @param style 样式名称
   */
  public changeMapStyle(style: string): void {
    if (!this.scene) return;
    
    // 根据样式名称设置场景背景和光照
    switch (style) {
      case 'standard':
        // 标准样式
        this.scene.background = new THREE.Color(0x0a192f); // 深蓝色背景
        break;
      case 'satellite':
        // 卫星影像样式
        this.scene.background = new THREE.Color(0x000000); // 黑色背景
        break;
      case 'terrain':
        // 地形样式
        this.scene.background = new THREE.Color(0x2c3e50); // 深青色背景
        break;
      case 'dark':
        // 暗色模式
        this.scene.background = new THREE.Color(0x000000); // 黑色背景
        break;
      default:
        this.scene.background = new THREE.Color(0x0a192f); // 默认深蓝色背景
    }
  }
  
  /**
   * 初始化键盘控制
   */
  private initKeyboardControls(): void {
    // 添加键盘事件监听
    window.addEventListener('keydown', this.handleKeyDown.bind(this));
    window.addEventListener('keyup', this.handleKeyUp.bind(this));
    
    // 初始化键盘状态
    this.keyStates.set('w', false);
    this.keyStates.set('a', false);
    this.keyStates.set('s', false);
    this.keyStates.set('d', false);
    
    console.log('键盘WASD控制已启用');
  }

  /**
   * 处理键盘按下事件
   * @param event 键盘事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // 记录按键状态
    this.keyStates.set(event.key.toLowerCase(), true);
  }

  /**
   * 处理键盘抬起事件
   * @param event 键盘事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    // 清除按键状态
    this.keyStates.set(event.key.toLowerCase(), false);
  }
  
  /**
   * 基于键盘输入更新摄像机位置
   */
  private updateCameraPosition(): void {
    if (!this.camera || !this.keyboardControlEnabled) return;
    
    // 保存当前位置，用于稍后更新控制器目标
    const prevPosition = this.camera.position.clone();
    
    // 使用相机的局部坐标系移动，这样可以确保 a/d 键确实是左右移动
    if (this.keyStates.get('w')) {
      // 向前移动（相机局部 Z 轴负方向）
      this.camera.translateZ(-this.cameraSpeed);
    }
    if (this.keyStates.get('s')) {
      // 向后移动（相机局部 Z 轴正方向）
      this.camera.translateZ(this.cameraSpeed);
    }
    if (this.keyStates.get('a')) {
      // 向左移动（相机局部 X 轴负方向）
      this.camera.translateX(-this.cameraSpeed);
    }
    if (this.keyStates.get('d')) {
      // 向右移动（相机局部 X 轴正方向）
      this.camera.translateX(this.cameraSpeed);
    }
    
    // 如果使用了OrbitControls，需要更新控制器的target
    if (this.controls) {
      // 计算相机移动的位移向量
      const displacement = new THREE.Vector3().subVectors(this.camera.position, prevPosition);
      
      // 更新轨道控制器目标点，保持相同的相对位置
      this.controls.target.add(displacement);
    }
  }
  
  /**
   * 设置键盘控制开关
   * @param enabled 是否启用键盘控制
   */
  public setKeyboardControlEnabled(enabled: boolean): void {
    this.keyboardControlEnabled = enabled;
    console.log(`键盘WASD控制已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 设置相机移动速度
   * @param speed 移动速度（默认为0.1）
   */
  public setCameraSpeed(speed: number): void {
    this.cameraSpeed = speed;
    console.log(`相机移动速度已设置为: ${speed}`);
  }
}

// 导出单例实例
export const threeService = new ThreeService(); 