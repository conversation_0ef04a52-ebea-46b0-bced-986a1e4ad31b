<!-- 
  CropGrowthAssessment.vue
  作物生长状态评估模块
  通过图像分析评估作物的生长状态、健康程度及虫害情况
-->
<template>
  <div class="crop-growth-assessment">
    <!-- 页面标题 -->
    <PageHeader
      title="作物生长状态评估"
      description="通过图像分析评估作物的生长状态、健康程度及虫害情况"
      icon="DataAnalysis"
    >
      <template #actions>
        <div class="status-indicator-wrapper">
          <StatusIndicator 
            :type="assessmentResults.isAssessed ? 'success' : 'normal'"
            :label="assessmentResults.isAssessed ? 'AI分析完成' : 'AI分析就绪'" 
            size="large" 
          />
        </div>
      </template>
    </PageHeader>
    
    <!-- 主要内容区域 -->
    <div class="assessment-panels">
      <!-- 图像上传与预览面板 -->
      <DataPanel title="图像上传与预览">
        <template #actions>
          <el-tag type="info" effect="dark" size="small">支持JPEG/PNG格式</el-tag>
        </template>
        
        <div class="image-upload-section">
          <div class="upload-toolbar">
            <el-button type="primary" @click="triggerImageUpload">
              <el-icon><Upload /></el-icon> 上传图像
            </el-button>
            <el-button type="primary" @click="openCamera">
              <el-icon><Camera /></el-icon> 拍摄照片
            </el-button>
            <el-button type="primary" @click="openImageLibrary">
              <el-icon><Picture /></el-icon> 图像库
            </el-button>
            <input 
              type="file" 
              ref="imageInput" 
              style="display: none" 
              accept="image/*" 
              @change="handleImageUpload"
            >
          </div>
          
          <div class="image-preview-container">
            <div class="image-preview-area" v-if="currentImage">
              <img :src="currentImage" class="preview-image" alt="作物图像">
              <div class="image-operations">
                <el-button-group>
                  <el-button @click="zoomIn"><el-icon><ZoomIn /></el-icon></el-button>
                  <el-button @click="zoomOut"><el-icon><ZoomOut /></el-icon></el-button>
                  <el-button @click="rotateImage"><el-icon><RefreshRight /></el-icon></el-button>
                  <el-button @click="cropImage"><el-icon><Crop /></el-icon></el-button>
                </el-button-group>
              </div>
            </div>
            
            <div class="image-placeholder" v-else>
              <el-icon><PictureFilled /></el-icon>
              <div class="placeholder-text">请上传作物图像进行生长状态评估</div>
            </div>
          </div>
          
          <div class="image-info" v-if="currentImage">
            <div class="info-item">
              <span class="item-label">图像名称:</span>
              <span class="item-value">{{ imageInfo.name }}</span>
            </div>
            <div class="info-item">
              <span class="item-label">上传时间:</span>
              <span class="item-value">{{ imageInfo.uploadTime }}</span>
            </div>
            <div class="info-item">
              <span class="item-label">分辨率:</span>
              <span class="item-value">{{ imageInfo.resolution }}</span>
            </div>
          </div>
        </div>
      </DataPanel>
      
      <!-- 分析操作面板 -->
      <DataPanel title="分析操作">
        <template #actions>
          <el-tag type="warning" effect="dark" size="small">AI深度分析</el-tag>
        </template>
        
        <div class="analysis-actions">
          <el-button type="success" @click="startAssessment" :disabled="!currentImage">
            <el-icon><VideoPlay /></el-icon> 开始评估
          </el-button>
          <el-button type="warning" @click="startAssessment" :disabled="!currentImage || !assessmentResults.isAssessed">
            <el-icon><RefreshLeft /></el-icon> 重新评估
          </el-button>
          <el-button :disabled="!assessmentResults.isAssessed" @click="exportReport">
            <el-icon><Download /></el-icon> 导出报告
          </el-button>
        </div>
        
        <div class="metrics-overview" v-if="assessmentResults.isAssessed">
          <div class="metric-card">
            <div class="metric-title">健康指数</div>
            <div class="metric-value">
              <el-progress 
                type="dashboard" 
                :percentage="assessmentResults.healthIndex" 
                :color="getHealthColor"
              />
            </div>
          </div>
          
          <div class="metric-card">
            <div class="metric-title">生长阶段</div>
            <div class="metric-value stage-indicator">
              {{ assessmentResults.growthStage }}
            </div>
          </div>
          
          <div class="metric-card">
            <div class="metric-title">病虫害影响</div>
            <div class="metric-value">
              <el-tag :type="getPestImpactType">
                {{ assessmentResults.pestImpact }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="analysis-placeholder" v-if="!assessmentResults.isAssessed">
          <el-icon><DataAnalysis /></el-icon>
          <div class="placeholder-text">上传图像并开始评估以查看结果</div>
        </div>
      </DataPanel>

      <!-- 叶片颜色分析面板 -->
      <DataPanel title="叶片颜色分析" v-if="assessmentResults.isAssessed">
        <template #actions>
          <el-tag type="success" effect="dark" size="small">叶绿素含量分析</el-tag>
        </template>
        
        <div class="color-analysis-content">
          <div class="color-spectrum">
            <div class="spectrum-title">叶片颜色对比</div>
            <div class="spectrum-container">
              <div class="color-item" v-for="(color, index) in leafColorSpectrum" :key="index">
                <div class="color-box" :style="{ backgroundColor: color.hex }"></div>
                <div class="color-label">{{ color.label }}</div>
              </div>
            </div>
          </div>
          
          <div class="color-analysis-text">
            {{ assessmentResults.leafColorAnalysis }}
          </div>
        </div>
      </DataPanel>
      
      <!-- 植株高度分析面板 -->
      <DataPanel title="植株高度分析" v-if="assessmentResults.isAssessed">
        <template #actions>
          <el-tag type="info" effect="dark" size="small">生长速度评估</el-tag>
        </template>
        
        <div class="height-chart-container" ref="heightChartContainer"></div>
      </DataPanel>
      
      <!-- 病斑检测面板 -->
      <DataPanel title="病斑检测" v-if="assessmentResults.isAssessed && assessmentResults.diseaseSpots.length > 0">
        <template #actions>
          <el-tag :type="assessmentResults.diseaseSpots.length > 0 ? 'warning' : 'success'" effect="dark" size="small">
            {{ assessmentResults.diseaseSpots.length > 0 ? '检测到病斑' : '未检测到病斑' }}
          </el-tag>
        </template>
        
        <div class="disease-detection">
          <div class="annotated-image">
            <img :src="assessmentResults.annotatedImage" alt="病斑检测" v-if="assessmentResults.annotatedImage">
            <div class="image-placeholder" v-else>暂无病斑检测图像</div>
          </div>
          
          <div class="disease-info">
            <div class="info-title">检测到的病斑</div>
            <el-table :data="assessmentResults.diseaseSpots" style="width: 100%">
              <el-table-column prop="type" label="类型" width="120" />
              <el-table-column prop="location" label="位置" width="120" />
              <el-table-column prop="severity" label="严重程度" width="120">
                <template #default="scope">
                  <el-tag :type="getSeverityType(scope.row.severity)">
                    {{ scope.row.severity }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </DataPanel>
      
      <!-- 历史对比面板 -->
      <DataPanel title="历史评估记录对比" v-if="assessmentResults.isAssessed">
        <template #actions>
          <el-tag type="primary" effect="dark" size="small">生长趋势分析</el-tag>
        </template>
        
        <div class="historical-comparison">
          <el-table :data="assessmentHistory" style="width: 100%" height="180">
            <el-table-column prop="date" label="评估日期" width="150" />
            <el-table-column prop="healthIndex" label="健康指数" width="100">
              <template #default="scope">
                {{ scope.row.healthIndex }}%
              </template>
            </el-table-column>
            <el-table-column prop="growthStage" label="生长阶段" width="120" />
            <el-table-column prop="keyFindings" label="主要发现" />
          </el-table>
          
          <div class="history-trend-chart" ref="historyTrendChartContainer"></div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="评估系统在线" />
        <StatusIndicator type="warning" label="AI模型已加载" />
        <StatusIndicator type="normal" label="数据库连接正常" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { 
  Upload, Camera, Picture, ZoomIn, ZoomOut, RefreshRight, 
  Crop, PictureFilled, VideoPlay, RefreshLeft, Download,
  DataAnalysis, Refresh
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'

// 定义接口类型
interface ImageInfo {
  name: string
  uploadTime: string
  resolution: string
}

interface DiseaseSpot {
  type: string
  location: string
  severity: string
}

interface AssessmentResult {
  isAssessed: boolean
  healthIndex: number
  growthStage: string
  pestImpact: string
  leafColorAnalysis: string
  annotatedImage: string | null
  diseaseSpots: DiseaseSpot[]
}

interface ColorItem {
  hex: string
  label: string
}

interface HistoryRecord {
  date: string
  healthIndex: number
  growthStage: string
  keyFindings: string
}

// 图像输入引用
const imageInput = ref<HTMLInputElement | null>(null)
const currentImage = ref<string | null>(null)
const activeReportSections = ref(['leafColor'])
const heightChartContainer = ref<HTMLElement | null>(null)
const historyTrendChartContainer = ref<HTMLElement | null>(null)

// 图像信息
const imageInfo = reactive<ImageInfo>({
  name: '',
  uploadTime: '',
  resolution: ''
})

// 评估结果
const assessmentResults = reactive<AssessmentResult>({
  isAssessed: false,
  healthIndex: 0,
  growthStage: '',
  pestImpact: '',
  leafColorAnalysis: '',
  annotatedImage: null,
  diseaseSpots: []
})

// 叶片颜色光谱
const leafColorSpectrum = reactive<ColorItem[]>([
  { hex: '#2bbb4e', label: '健康' },
  { hex: '#9acd32', label: '良好' },
  { hex: '#ffd700', label: '轻度缺素' },
  { hex: '#ffa500', label: '中度缺素' },
  { hex: '#ff4500', label: '严重缺素' }
])

// 历史评估数据
const assessmentHistory = reactive<HistoryRecord[]>([
  { date: '2023-05-15', healthIndex: 92, growthStage: '幼苗期', keyFindings: '生长状态良好，无明显问题' },
  { date: '2023-05-10', healthIndex: 88, growthStage: '幼苗期', keyFindings: '叶片边缘轻微黄化，建议施肥' },
  { date: '2023-05-05', healthIndex: 85, growthStage: '幼苗期', keyFindings: '幼苗出现轻微缺水迹象' },
  { date: '2023-04-30', healthIndex: 78, growthStage: '幼苗期', keyFindings: '发现少量蚜虫，已处理' }
])

// 最后更新时间
const lastUpdateTime = ref(new Date())

// 健康指数颜色
const getHealthColor = computed(() => {
  if (assessmentResults.healthIndex >= 80) return '#67c23a'
  if (assessmentResults.healthIndex >= 60) return '#e6a23c'
  return '#f56c6c'
})

// 病虫害影响类型
const getPestImpactType = computed(() => {
  switch (assessmentResults.pestImpact) {
    case '无': return 'success'
    case '轻微': return 'info'
    case '中等': return 'warning'
    case '严重': return 'danger'
    default: return 'info'
  }
})

// 严重程度标签类型
const getSeverityType = (severity: string): string => {
  switch (severity) {
    case '轻微': return 'info'
    case '中等': return 'warning'
    case '严重': return 'danger'
    default: return 'info'
  }
}

// 格式化时间
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 触发图像上传
const triggerImageUpload = (): void => {
  if (imageInput.value) {
    imageInput.value.click()
  }
}

// 处理图像上传
const handleImageUpload = (event: Event): void => {
  const target = event.target as HTMLInputElement
  if (!target.files || target.files.length === 0) return
  
  const file = target.files[0]
  const reader = new FileReader()
  
  reader.onload = (e: ProgressEvent<FileReader>) => {
    if (e.target && typeof e.target.result === 'string') {
      currentImage.value = e.target.result
      
      // 设置图像信息
      imageInfo.name = file.name
      imageInfo.uploadTime = new Date().toLocaleString()
      
      // 创建图像对象以获取分辨率
      const img = new Image()
      img.onload = () => {
        imageInfo.resolution = `${img.width} x ${img.height}`
      }
      img.src = e.target.result
      
      // 重置评估结果
      assessmentResults.isAssessed = false
    }
  }
  
  reader.readAsDataURL(file)
}

// 打开相机（实际应用需要调用设备相机API）
const openCamera = (): void => {
  ElMessage.info('相机功能需要调用设备API，此处为示例')
}

// 打开图像库（实际应用需要集成图像库）
const openImageLibrary = (): void => {
  ElMessage.info('图像库功能需要后端集成，此处为示例')
}

// 图像操作函数（实际应用需要实现图像处理功能）
const zoomIn = (): void => {
  ElMessage.info('图像放大功能')
}

const zoomOut = (): void => {
  ElMessage.info('图像缩小功能')
}

const rotateImage = (): void => {
  ElMessage.info('图像旋转功能')
}

const cropImage = (): void => {
  ElMessage.info('图像裁剪功能')
}

// 开始评估
const startAssessment = (): void => {
  // 模拟评估过程，实际应用需要调用后端AI分析API
  ElMessage.info('正在进行图像分析，请稍候...')
  
  setTimeout(() => {
    // 设置模拟评估结果
    assessmentResults.isAssessed = true
    assessmentResults.healthIndex = Math.floor(Math.random() * 30) + 70 // 70-100之间的随机值
    
    const growthStages = ['幼苗期', '生长期', '花期', '成熟期']
    assessmentResults.growthStage = growthStages[Math.floor(Math.random() * growthStages.length)]
    
    const pestImpacts = ['无', '轻微', '中等', '严重']
    const randomImpactIndex = Math.floor(Math.random() * 10) / 10
    if (randomImpactIndex < 0.5) {
      assessmentResults.pestImpact = pestImpacts[0]
    } else if (randomImpactIndex < 0.8) {
      assessmentResults.pestImpact = pestImpacts[1]
    } else if (randomImpactIndex < 0.95) {
      assessmentResults.pestImpact = pestImpacts[2]
    } else {
      assessmentResults.pestImpact = pestImpacts[3]
    }
    
    assessmentResults.leafColorAnalysis = '叶片呈现健康的绿色，颜色均匀，无明显黄化或褐变现象，叶绿素含量丰富，光合作用能力强。'
    
    // 设置模拟病斑数据
    if (assessmentResults.pestImpact !== '无') {
      assessmentResults.diseaseSpots = [
        { type: '黄斑', location: '上部叶片', severity: '轻微' }
      ]
      
      if (assessmentResults.pestImpact === '中等' || assessmentResults.pestImpact === '严重') {
        assessmentResults.diseaseSpots.push(
          { type: '黑斑', location: '下部叶片', severity: '中等' }
        )
      }
      
      if (assessmentResults.pestImpact === '严重') {
        assessmentResults.diseaseSpots.push(
          { type: '叶枯', location: '中部叶片', severity: '严重' }
        )
      }
    } else {
      assessmentResults.diseaseSpots = []
    }
    
    // 使用当前图像作为标注图像（实际应用需要后端生成标注图像）
    assessmentResults.annotatedImage = currentImage.value
    
    // 初始化图表
    nextTick(() => {
      initHeightChart()
      initHistoryTrendChart()
    })
    
    ElMessage.success('分析完成！')
  }, 1500) // 模拟评估延迟
}

// 导出报告
const exportReport = (): void => {
  ElMessage.info('报告导出功能需要后端支持，此处为示例')
}

// 刷新数据
const refreshData = (): void => {
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已刷新')
}

// 初始化植株高度图表
let heightChart: echarts.ECharts | null = null
const initHeightChart = (): void => {
  if (!heightChartContainer.value) return
  
  if (heightChart) {
    heightChart.dispose()
  }
  
  heightChart = echarts.init(heightChartContainer.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['标准高度', '实际高度'],
      axisLabel: {
        color: '#d1d5db'
      },
      axisLine: {
        lineStyle: {
          color: '#4b5563'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '高度 (cm)',
      nameTextStyle: {
        color: '#d1d5db'
      },
      axisLabel: {
        color: '#d1d5db'
      },
      splitLine: {
        lineStyle: {
          color: '#374151'
        }
      }
    },
    series: [
      {
        name: '高度',
        type: 'bar',
        data: [
          {
            value: 25,
            itemStyle: {
              color: '#3b82f6'
            }
          },
          {
            value: Math.floor(Math.random() * 10) + 20, // 20-30之间的随机值
            itemStyle: {
              color: '#10b981'
            }
          }
        ],
        label: {
          show: true,
          position: 'top',
          color: '#d1d5db',
          formatter: '{c} cm'
        }
      }
    ]
  }
  
  heightChart.setOption(option)
}

// 初始化历史趋势图表
let historyTrendChart: echarts.ECharts | null = null
const initHistoryTrendChart = (): void => {
  if (!historyTrendChartContainer.value) return
  
  if (historyTrendChart) {
    historyTrendChart.dispose()
  }
  
  historyTrendChart = echarts.init(historyTrendChartContainer.value)
  
  // 准备历史数据
  const dates = assessmentHistory.map(item => item.date).reverse()
  dates.push('今日') // 添加当前评估结果
  
  const healthIndices = assessmentHistory.map(item => item.healthIndex).reverse()
  healthIndices.push(assessmentResults.healthIndex) // 添加当前评估结果
  
  const option = {
    title: {
      text: '健康指数变化趋势',
      textStyle: {
        color: '#d1d5db',
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        color: '#d1d5db'
      },
      axisLine: {
        lineStyle: {
          color: '#4b5563'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 50,
      max: 100,
      name: '健康指数',
      nameTextStyle: {
        color: '#d1d5db'
      },
      axisLabel: {
        color: '#d1d5db'
      },
      splitLine: {
        lineStyle: {
          color: '#374151'
        }
      }
    },
    series: [
      {
        name: '健康指数',
        type: 'line',
        data: healthIndices,
        symbolSize: 8,
        itemStyle: {
          color: '#3b82f6'
        },
        lineStyle: {
          width: 3,
          color: '#3b82f6',
          shadowColor: 'rgba(59, 130, 246, 0.3)',
          shadowBlur: 10
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(59, 130, 246, 0.5)' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
          ])
        },
        markLine: {
          silent: true,
          data: [
            {
              yAxis: 80,
              lineStyle: {
                color: '#67c23a',
                type: 'dashed'
              },
              label: {
                formatter: '健康',
                color: '#67c23a'
              }
            },
            {
              yAxis: 60,
              lineStyle: {
                color: '#e6a23c',
                type: 'dashed'
              },
              label: {
                formatter: '警戒线',
                color: '#e6a23c'
              }
            }
          ]
        }
      }
    ]
  }
  
  historyTrendChart.setOption(option)
}

// 窗口大小变化时重新调整图表
const handleResize = (): void => {
  heightChart?.resize()
  historyTrendChart?.resize()
}

// 组件挂载
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

// 组件销毁
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  heightChart?.dispose()
  historyTrendChart?.dispose()
})
</script>

<style scoped>
.crop-growth-assessment {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.status-indicator-wrapper {
  display: flex;
  align-items: center;
}

/* 评估面板区域 */
.assessment-panels {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

/* 图像上传部分 */
.image-upload-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.upload-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.image-preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.image-preview-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-operations {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(31, 41, 55, 0.7);
  padding: 5px;
  border-radius: 4px;
}

.image-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  margin-bottom: 15px;
  min-height: 200px;
}

.image-placeholder .el-icon {
  font-size: 48px;
  color: #6b7280;
  margin-bottom: 15px;
}

.placeholder-text {
  color: #6b7280;
  font-size: 14px;
}

.image-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
}

.item-label {
  color: #9ca3af;
}

.item-value {
  color: #d1d5db;
  font-weight: 500;
}

/* 分析操作部分 */
.analysis-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.metrics-overview {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.analysis-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 20px;
  height: 150px;
}

.analysis-placeholder .el-icon {
  font-size: 48px;
  color: #6b7280;
  margin-bottom: 15px;
}

.metric-card {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
}

.metric-title {
  color: #9ca3af;
  margin-bottom: 10px;
  font-size: 14px;
}

.metric-value {
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
}

.stage-indicator {
  background-color: #3b82f6;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 14px;
}

/* 叶片颜色分析部分 */
.color-analysis-content {
  padding: 15px;
}

.color-spectrum {
  margin-bottom: 20px;
}

.spectrum-title {
  margin-bottom: 10px;
  color: #9ca3af;
}

.spectrum-container {
  display: flex;
  gap: 10px;
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.color-box {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  margin-bottom: 5px;
}

.color-label {
  font-size: 12px;
  color: #9ca3af;
}

.color-analysis-text {
  line-height: 1.6;
  color: #e5e7eb;
}

/* 植株高度图表 */
.height-chart-container {
  height: 250px;
  width: 100%;
  margin: 10px 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(31, 41, 55, 0.3);
}

/* 病斑检测部分 */
.disease-detection {
  display: flex;
  gap: 20px;
}

.annotated-image {
  width: 50%;
}

.annotated-image img {
  max-width: 100%;
  border-radius: 4px;
}

.disease-info {
  width: 50%;
}

.info-title {
  margin-bottom: 10px;
  color: #9ca3af;
}

/* 历史对比部分 */
.historical-comparison {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.history-trend-chart {
  height: 180px;
  margin-top: 15px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
}

/* 表格样式定制 */
:deep(.el-table) {
  background-color: transparent;
  color: #d1d5db;
}

:deep(.el-table tr) {
  background-color: transparent;
}

:deep(.el-table--border, .el-table--group) {
  border-color: #374151;
}

:deep(.el-table td, .el-table th.is-leaf) {
  border-bottom-color: #374151;
}

:deep(.el-table--border th, .el-table--border td) {
  border-right-color: #374151;
}

:deep(.el-table thead th) {
  background-color: #1f2937;
  color: #d1d5db;
}

:deep(.el-progress-circle__track) {
  stroke: #374151;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .assessment-panels {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .disease-detection {
    flex-direction: column;
  }
  
  .annotated-image,
  .disease-info {
    width: 100%;
  }
  
  .metrics-overview {
    flex-direction: column;
    gap: 10px;
  }
  
  .metric-card {
    width: 100%;
  }
}
</style>

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save
-->