<!-- 
  BiopesticideTracking.vue
  生物农药使用追踪模块
  监控和分析生物农药和化学农药的使用情况、效果评估及环境影响
-->
<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  DocumentAdd, 
  Download, 
  ArrowDown, 
  Search, 
  Refresh,
  DataAnalysis
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'

// 定义类型
interface BiopesticideRecord {
  id: number
  pesticideType: string
  pesticideName: string
  applicationTime: string
  area: string
  cropType: string
  targetPest: string
  quantity: number
  unit: string
  effectiveness: number
  environmentalFriendlinessScore: number
  nonTargetImpact: string
  notes: string
}

interface FilterForm {
  timeRange: string[]
  pesticideType: string
  cropTypes: string[]
  searchKeyword: string
}

// 生物农药使用记录
const biopesticideRecords = ref<BiopesticideRecord[]>([
  {
    id: 1,
    pesticideType: '生物农药',
    pesticideName: '苏云金杆菌',
    applicationTime: '2023-05-15',
    area: '西北苹果园',
    cropType: '苹果',
    targetPest: '苹果小卷叶蛾',
    quantity: 5,
    unit: 'kg',
    effectiveness: 85,
    environmentalFriendlinessScore: 92,
    nonTargetImpact: '低',
    notes: '喷洒后第三天见效，对天敌昆虫影响小'
  },
  {
    id: 2,
    pesticideType: '生物农药',
    pesticideName: '绿僵菌',
    applicationTime: '2023-05-10',
    area: '东南蔬菜区',
    cropType: '茄子',
    targetPest: '茄子粉虱',
    quantity: 3,
    unit: 'L',
    effectiveness: 70,
    environmentalFriendlinessScore: 95,
    nonTargetImpact: '极低',
    notes: '效果持续时间较长，雨后需再次施用'
  },
  {
    id: 3,
    pesticideType: '生物农药',
    pesticideName: '印楝素',
    applicationTime: '2023-05-05',
    area: '中部水稻区',
    cropType: '水稻',
    targetPest: '二化螟',
    quantity: 4,
    unit: 'L',
    effectiveness: 75,
    environmentalFriendlinessScore: 90,
    nonTargetImpact: '低',
    notes: '对水生生物影响较小，水稻长势良好'
  },
  {
    id: 4,
    pesticideType: '化学农药',
    pesticideName: '氯氰菊酯',
    applicationTime: '2023-05-08',
    area: '西部小麦区',
    cropType: '小麦',
    targetPest: '麦蚜',
    quantity: 2,
    unit: 'L',
    effectiveness: 95,
    environmentalFriendlinessScore: 40,
    nonTargetImpact: '高',
    notes: '效果迅速但对益虫影响较大'
  },
  {
    id: 5,
    pesticideType: '化学农药',
    pesticideName: '丙溴磷',
    applicationTime: '2023-05-12',
    area: '东北玉米区',
    cropType: '玉米',
    targetPest: '玉米螟',
    quantity: 2.5,
    unit: 'L',
    effectiveness: 90,
    environmentalFriendlinessScore: 35,
    nonTargetImpact: '高',
    notes: '高效速效，但对土壤微生物影响显著'
  }
])

// 筛选条件
const filterForm = reactive<FilterForm>({
  timeRange: [],
  pesticideType: '',
  cropTypes: [],
  searchKeyword: ''
})

// 农药类型选项
const pesticideTypeOptions = [
  { label: '全部', value: '' },
  { label: '生物农药', value: '生物农药' },
  { label: '化学农药', value: '化学农药' }
]

// 作物类型选项
const cropTypeOptions = [
  { label: '苹果', value: '苹果' },
  { label: '茄子', value: '茄子' },
  { label: '水稻', value: '水稻' },
  { label: '小麦', value: '小麦' },
  { label: '玉米', value: '玉米' }
]

// 图表类型
const chartType = ref('bar') // 'bar', 'line', 'stacked'

// 图表引用
const usageChartRef = ref<HTMLElement | null>(null)
const effectivenessChartRef = ref<HTMLElement | null>(null)
const environmentalChartRef = ref<HTMLElement | null>(null)

// 图表实例
let usageChart: echarts.ECharts | null = null
let effectivenessChart: echarts.ECharts | null = null
let environmentalChart: echarts.ECharts | null = null

// 最后更新时间
const lastUpdateTime = ref(new Date())

// 筛选数据
const filteredRecords = computed(() => {
  return biopesticideRecords.value.filter(item => {
    // 时间范围筛选
    if (filterForm.timeRange && filterForm.timeRange.length === 2) {
      const recordDate = new Date(item.applicationTime)
      const startDate = new Date(filterForm.timeRange[0])
      const endDate = new Date(filterForm.timeRange[1])
      if (recordDate < startDate || recordDate > endDate) {
        return false
      }
    }
    
    // 农药类型筛选
    if (filterForm.pesticideType && item.pesticideType !== filterForm.pesticideType) {
      return false
    }
    
    // 作物类型筛选
    if (filterForm.cropTypes && filterForm.cropTypes.length > 0) {
      if (!filterForm.cropTypes.includes(item.cropType)) {
        return false
      }
    }
    
    // 关键词搜索
    if (filterForm.searchKeyword) {
      const keyword = filterForm.searchKeyword.toLowerCase()
      return item.pesticideName.toLowerCase().includes(keyword) ||
        item.area.toLowerCase().includes(keyword) ||
        item.targetPest.toLowerCase().includes(keyword) ||
        item.notes.toLowerCase().includes(keyword)
    }
    
    return true
  })
})

// 生物农药记录
const bioRecords = computed(() => {
  return filteredRecords.value.filter(item => item.pesticideType === '生物农药')
})

// 化学农药记录
const chemicalRecords = computed(() => {
  return filteredRecords.value.filter(item => item.pesticideType === '化学农药')
})

// 农药使用对比数据
const usageComparisonData = computed(() => {
  // 按农药类型分组
  const bioUsage = bioRecords.value.reduce((sum, record) => sum + record.quantity, 0)
  const chemicalUsage = chemicalRecords.value.reduce((sum, record) => sum + record.quantity, 0)
  
  return {
    types: ['生物农药', '化学农药'],
    usage: [bioUsage, chemicalUsage]
  }
})

// 效果对比数据
const effectivenessComparisonData = computed(() => {
  // 计算平均效果
  const bioEffectiveness = bioRecords.value.length > 0 
    ? bioRecords.value.reduce((sum, record) => sum + record.effectiveness, 0) / bioRecords.value.length 
    : 0
    
  const chemicalEffectiveness = chemicalRecords.value.length > 0 
    ? chemicalRecords.value.reduce((sum, record) => sum + record.effectiveness, 0) / chemicalRecords.value.length 
    : 0
  
  return {
    types: ['生物农药', '化学农药'],
    effectiveness: [Math.round(bioEffectiveness), Math.round(chemicalEffectiveness)]
  }
})

// 环境友好性对比数据
const environmentalFriendlinessData = computed(() => {
  // 计算平均环境友好性
  const bioScore = bioRecords.value.length > 0 
    ? bioRecords.value.reduce((sum, record) => sum + record.environmentalFriendlinessScore, 0) / bioRecords.value.length 
    : 0
    
  const chemicalScore = chemicalRecords.value.length > 0 
    ? chemicalRecords.value.reduce((sum, record) => sum + record.environmentalFriendlinessScore, 0) / chemicalRecords.value.length 
    : 0
  
  return {
    types: ['生物农药', '化学农药'],
    scores: [Math.round(bioScore), Math.round(chemicalScore)]
  }
})

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 初始化使用量对比图表
const initUsageChart = () => {
  if (usageChartRef.value) {
    usageChart = echarts.init(usageChartRef.value)
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} kg/L',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        textStyle: {
          color: '#9ca3af'
        }
      },
      series: [
        {
          name: '农药使用量',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#111827',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
              color: '#e5e7eb'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { 
              value: usageComparisonData.value.usage[0], 
              name: '生物农药',
              itemStyle: { color: '#10b981' }
            },
            { 
              value: usageComparisonData.value.usage[1], 
              name: '化学农药',
              itemStyle: { color: '#ef4444' }
            }
          ]
        }
      ]
    }
    
    usageChart.setOption(option)
    
    window.addEventListener('resize', () => {
      usageChart?.resize()
    })
  }
}

// 初始化效果对比图表
const initEffectivenessChart = () => {
  if (effectivenessChartRef.value) {
    effectivenessChart = echarts.init(effectivenessChartRef.value)
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c}%',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        },
        axisLabel: {
          color: '#9ca3af'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: effectivenessComparisonData.value.types,
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        },
        axisLabel: {
          color: '#9ca3af'
        }
      },
      series: [
        {
          name: '防治效果',
          type: 'bar',
          data: [
            {
              value: effectivenessComparisonData.value.effectiveness[0],
              itemStyle: { color: '#10b981' }
            },
            {
              value: effectivenessComparisonData.value.effectiveness[1],
              itemStyle: { color: '#ef4444' }
            }
          ],
          label: {
            show: true,
            position: 'right',
            formatter: '{c}%',
            color: '#e5e7eb'
          }
        }
      ]
    }
    
    effectivenessChart.setOption(option)
    
    window.addEventListener('resize', () => {
      effectivenessChart?.resize()
    })
  }
}

// 初始化环境友好性图表
const initEnvironmentalChart = () => {
  if (environmentalChartRef.value) {
    environmentalChart = echarts.init(environmentalChartRef.value)
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c}分',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        },
        axisLabel: {
          color: '#9ca3af'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: environmentalFriendlinessData.value.types,
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        },
        axisLabel: {
          color: '#9ca3af'
        }
      },
      series: [
        {
          name: '环境友好度',
          type: 'bar',
          data: [
            {
              value: environmentalFriendlinessData.value.scores[0],
              itemStyle: { color: '#10b981' }
            },
            {
              value: environmentalFriendlinessData.value.scores[1],
              itemStyle: { color: '#ef4444' }
            }
          ],
          label: {
            show: true,
            position: 'right',
            formatter: '{c}分',
            color: '#e5e7eb'
          }
        }
      ]
    }
    
    environmentalChart.setOption(option)
    
    window.addEventListener('resize', () => {
      environmentalChart?.resize()
    })
  }
}

// 切换图表类型
const changeChartType = (type: string) => {
  chartType.value = type
  
  // 在下一个渲染周期初始化图表
  setTimeout(() => {
    if (type === 'bar') {
      initUsageChart()
    } else if (type === 'line') {
      initEffectivenessChart()
    } else if (type === 'stacked') {
      initEnvironmentalChart()
    }
  }, 0)
}

// 重置筛选条件
const resetFilters = () => {
  filterForm.timeRange = []
  filterForm.pesticideType = ''
  filterForm.cropTypes = []
  filterForm.searchKeyword = ''
}

// 更新图表数据
const updateChartData = () => {
  if (chartType.value === 'bar' && usageChart) {
    usageChart.setOption({
      series: [{
        data: [
          { 
            value: usageComparisonData.value.usage[0], 
            name: '生物农药',
            itemStyle: { color: '#10b981' }
          },
          { 
            value: usageComparisonData.value.usage[1], 
            name: '化学农药',
            itemStyle: { color: '#ef4444' }
          }
        ]
      }]
    })
  } else if (chartType.value === 'line' && effectivenessChart) {
    effectivenessChart.setOption({
      series: [{
        data: [
          {
            value: effectivenessComparisonData.value.effectiveness[0],
            itemStyle: { color: '#10b981' }
          },
          {
            value: effectivenessComparisonData.value.effectiveness[1],
            itemStyle: { color: '#ef4444' }
          }
        ]
      }]
    })
  } else if (chartType.value === 'stacked' && environmentalChart) {
    environmentalChart.setOption({
      series: [{
        data: [
          {
            value: environmentalFriendlinessData.value.scores[0],
            itemStyle: { color: '#10b981' }
          },
          {
            value: environmentalFriendlinessData.value.scores[1],
            itemStyle: { color: '#ef4444' }
          }
        ]
      }]
    })
  }
  
  lastUpdateTime.value = new Date()
}

// 刷新数据
const refreshData = () => {
  updateChartData()
  ElMessage.success('数据已更新')
}

// 生成对比报告
const generateComparisonReport = () => {
  ElMessageBox.confirm('确定要生成对比报告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '对比报告生成成功，请在下方预览'
    })
  }).catch(() => {})
}

// 导出数据
const exportData = (type: string) => {
  ElMessage({
    type: 'success',
    message: `数据已导出为${type}格式`
  })
}

// 定义过滤方法，显式类型定义
const filterPesticideType = (value: string, row: BiopesticideRecord) => {
  return row.pesticideType === value
}

onMounted(() => {
  // 初始化第一个图表
  changeChartType('bar')
})

onUnmounted(() => {
  // 销毁图表实例
  usageChart?.dispose()
  effectivenessChart?.dispose()
  environmentalChart?.dispose()
})
</script>

<template>
  <div class="biopesticide-tracking">
    <!-- 页面标题 -->
    <PageHeader
      title="生物农药使用追踪"
      description="监控和分析生物农药和化学农药的使用情况、效果评估及环境影响"
      icon="DataAnalysis"
    >
      <template #actions>
        <div class="status-indicator-wrapper">
          <StatusIndicator type="success" label="数据同步完成" size="large" />
        </div>
      </template>
    </PageHeader>
    
    <!-- 对比分析与数据详情双分屏布局 -->
    <div class="tracking-layout">
      <!-- 左侧：对比分析图表区 -->
      <div class="analysis-container">
        <DataPanel title="农药数据对比分析">
          <template #actions>
            <el-radio-group v-model="chartType" size="small" @change="changeChartType">
              <el-radio-button label="bar">使用量对比</el-radio-button>
              <el-radio-button label="line">效果评估</el-radio-button>
              <el-radio-button label="stacked">环境友好性</el-radio-button>
            </el-radio-group>
          </template>
          
          <div class="chart-area">
            <div v-if="chartType === 'bar'" ref="usageChartRef" class="chart-container"></div>
            <div v-if="chartType === 'line'" ref="effectivenessChartRef" class="chart-container"></div>
            <div v-if="chartType === 'stacked'" ref="environmentalChartRef" class="chart-container"></div>
          </div>
        </DataPanel>
        
        <DataPanel title="筛选条件">
          <el-form :model="filterForm" label-position="top" size="small">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="filterForm.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            
            <el-form-item label="农药类型">
              <el-select 
                v-model="filterForm.pesticideType" 
                placeholder="请选择农药类型"
              >
                <el-option 
                  v-for="item in pesticideTypeOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value" 
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="作物种类">
              <el-select 
                v-model="filterForm.cropTypes" 
                multiple 
                collapse-tags
                placeholder="请选择作物种类"
              >
                <el-option 
                  v-for="item in cropTypeOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value" 
                />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-input 
                v-model="filterForm.searchKeyword" 
                placeholder="搜索关键词..." 
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="resetFilters">重置筛选</el-button>
            </el-form-item>
          </el-form>
        </DataPanel>
      </div>
      
      <!-- 右侧：生物农药使用详情与效果评估区 -->
      <div class="details-container">
        <DataPanel title="农药使用记录">
          <template #actions>
            <el-dropdown @command="exportData">
              <el-button type="info" size="small">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="Excel">Excel 格式</el-dropdown-item>
                  <el-dropdown-item command="PDF">PDF 格式</el-dropdown-item>
                  <el-dropdown-item command="CSV">CSV 格式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          
          <el-table 
            :data="filteredRecords" 
            stripe 
            style="width: 100%" 
            size="small"
            max-height="300"
          >
            <el-table-column 
              prop="pesticideType" 
              label="农药类型" 
              width="100"
              :filters="[
                { text: '生物农药', value: '生物农药' },
                { text: '化学农药', value: '化学农药' }
              ]"
              :filter-method="filterPesticideType"
            >
              <template #default="scope">
                <el-tag 
                  :type="scope.row.pesticideType === '生物农药' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ scope.row.pesticideType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="pesticideName" label="农药名称" width="120" />
            <el-table-column prop="applicationTime" label="使用时间" width="100" sortable />
            <el-table-column prop="area" label="使用区域" width="120" />
            <el-table-column prop="cropType" label="作物种类" width="100" />
            <el-table-column label="使用量" width="100">
              <template #default="scope">
                {{ scope.row.quantity }} {{ scope.row.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="effectiveness" label="效果(%)" width="80" sortable>
              <template #default="scope">
                <span :class="scope.row.effectiveness >= 80 ? 'text-success' : scope.row.effectiveness >= 60 ? 'text-warning' : 'text-danger'">
                  {{ scope.row.effectiveness }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="环境友好度" width="100">
              <template #default="scope">
                <el-progress 
                  :percentage="scope.row.environmentalFriendlinessScore"
                  :color="scope.row.pesticideType === '生物农药' ? '#10b981' : '#ef4444'"
                />
              </template>
            </el-table-column>
          </el-table>
        </DataPanel>
        
        <DataPanel title="效果评估">
          <div class="assessment-metrics">
            <div class="metric-group">
              <div class="metric-title">防治效果对比</div>
              <div class="metrics-comparison">
                <div class="comparison-item">
                  <div class="item-label">生物农药平均效果</div>
                  <div class="item-value">{{ Math.round(effectivenessComparisonData.effectiveness[0]) }}%</div>
                  <el-progress 
                    :percentage="effectivenessComparisonData.effectiveness[0]"
                    :color="'#10b981'"
                  />
                </div>
                <div class="comparison-item">
                  <div class="item-label">化学农药平均效果</div>
                  <div class="item-value">{{ Math.round(effectivenessComparisonData.effectiveness[1]) }}%</div>
                  <el-progress 
                    :percentage="effectivenessComparisonData.effectiveness[1]"
                    :color="'#ef4444'"
                  />
                </div>
              </div>
            </div>
            
            <div class="metric-group">
              <div class="metric-title">环境友好性对比</div>
              <div class="metrics-comparison">
                <div class="comparison-item">
                  <div class="item-label">生物农药友好度</div>
                  <div class="item-value">{{ Math.round(environmentalFriendlinessData.scores[0]) }}分</div>
                  <el-progress 
                    :percentage="environmentalFriendlinessData.scores[0]"
                    :color="'#10b981'"
                  />
                </div>
                <div class="comparison-item">
                  <div class="item-label">化学农药友好度</div>
                  <div class="item-value">{{ Math.round(environmentalFriendlinessData.scores[1]) }}分</div>
                  <el-progress 
                    :percentage="environmentalFriendlinessData.scores[1]"
                    :color="'#ef4444'"
                  />
                </div>
              </div>
            </div>
          </div>
        </DataPanel>
        
        <DataPanel title="生物农药信息">
          <el-tabs type="border-card">
            <el-tab-pane 
              v-for="record in bioRecords" 
              :key="record.id" 
              :label="record.pesticideName"
            >
              <div class="info-content">
                <div class="info-item">
                  <div class="info-label">成分：</div>
                  <div class="info-value">微生物制剂</div>
                </div>
                <div class="info-item">
                  <div class="info-label">作用机制：</div>
                  <div class="info-value">抑制害虫生长发育，破坏害虫消化系统</div>
                </div>
                <div class="info-item">
                  <div class="info-label">适用作物：</div>
                  <div class="info-value">{{ record.cropType }}等多种农作物</div>
                </div>
                <div class="info-item">
                  <div class="info-label">安全间隔期：</div>
                  <div class="info-value">1-3天</div>
                </div>
                <div class="info-item">
                  <div class="info-label">使用方法：</div>
                  <div class="info-value">稀释后均匀喷洒于植物表面</div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </DataPanel>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="生物农药记录" />
        <StatusIndicator type="warning" label="化学农药记录" />
        <StatusIndicator type="normal" label="分析进行中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.biopesticide-tracking {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.status-indicator-wrapper {
  display: flex;
  align-items: center;
}

/* 布局样式 */
.tracking-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  flex: 1;
  margin-bottom: 20px;
  overflow: hidden;
}

/* 左侧分析容器 */
.analysis-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.chart-area {
  flex: 1;
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

/* 右侧详情容器 */
.details-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

/* 效果评估卡片样式 */
.assessment-metrics {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.metric-group {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 6px;
  padding: 10px;
}

.metric-title {
  font-weight: 500;
  color: #d1d5db;
  margin-bottom: 10px;
}

.metrics-comparison {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.item-label {
  font-size: 0.9rem;
  color: #9ca3af;
}

.item-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #f9fafb;
}

/* 生物农药信息样式 */
.info-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-item {
  display: flex;
  gap: 5px;
}

.info-label {
  color: #9ca3af;
  font-weight: 500;
}

.info-value {
  color: #f9fafb;
}

/* 表格文本颜色 */
.text-success {
  color: #10b981;
}

.text-warning {
  color: #f59e0b;
}

.text-danger {
  color: #ef4444;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tracking-layout {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style> 