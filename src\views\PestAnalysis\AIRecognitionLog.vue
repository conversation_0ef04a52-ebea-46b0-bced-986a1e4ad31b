<!-- 
  AIRecognitionLog.vue
  AI图像识别日志追溯模块
  记录和管理AI图像识别系统的识别日志，包括识别时间、识别结果、图像路径等信息
-->
<template>
  <div class="ai-recognition-log">
    <!-- 页面标题 -->
    <PageHeader
      title="AI图像识别日志追溯"
      description="记录和管理AI图像识别系统的识别日志，包括识别时间、识别结果、图像路径等信息"
      icon="PictureFilled"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ totalLogs }}</span>
            <span class="summary-label">识别日志总数</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ successRate }}%</span>
            <span class="summary-label">识别成功率</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 筛选器区域 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <template #header>
          <div class="filter-header">
            <el-icon><Filter /></el-icon>
            <span>日志筛选</span>
          </div>
        </template>
        <div class="filter-form">
          <el-form :inline="true" :model="filterForm">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 300px"
              />
            </el-form-item>
            <el-form-item label="虫害类型">
              <el-select v-model="filterForm.pestTypes" multiple placeholder="选择虫害类型" clearable style="width: 220px">
                <el-option
                  v-for="type in pestTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="识别结果">
              <el-radio-group v-model="filterForm.recognitionResult">
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="success">识别成功</el-radio-button>
                <el-radio-button label="fail">识别失败</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleFilter">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="resetFilter">
                <el-icon><RefreshRight /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-section">
      <el-row :gutter="20">
        <!-- 左侧日志列表面板 -->
        <el-col :span="16">
          <DataPanel title="识别日志列表">
            <template #actions>
              <div class="panel-actions">
                <el-button type="success" size="small" @click="exportLogs">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-button>
                <el-button type="primary" size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </template>
            <div class="log-table-container">
              <div class="bulk-actions">
                <el-button type="danger" size="small" :disabled="!selectedLogs.length" @click="handleBulkDelete">
                  <el-icon><Delete /></el-icon>
                  批量删除
                </el-button>
                <el-button type="primary" size="small" :disabled="!selectedLogs.length" @click="handleBulkExport">
                  <el-icon><Download /></el-icon>
                  批量导出
                </el-button>
              </div>
              
              <el-table
                v-loading="loading"
                :data="logs"
                @selection-change="handleSelectionChange"
                class="log-table"
                border
                stripe
                :header-cell-style="{ background: '#1f2937', color: '#e5e7eb' }"
                height="calc(100% - 50px)"
              >
                <el-table-column type="selection" width="55" />
                
                <el-table-column prop="time" label="识别时间" width="180" sortable>
                  <template #default="scope">
                    {{ formatTime(scope.row.time) }}
                  </template>
                </el-table-column>
                
                <el-table-column prop="imageName" label="图像名称" width="180" />
                
                <el-table-column prop="pestType" label="虫害类型" width="120">
                  <template #default="scope">
                    <el-tag :type="getPestTypeTagType(scope.row.pestType)">
                      {{ getPestTypeName(scope.row.pestType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                
                <el-table-column prop="confidence" label="置信度" width="100">
                  <template #default="scope">
                    <el-progress
                      :percentage="scope.row.confidence"
                      :status="getConfidenceStatus(scope.row.confidence)"
                    />
                  </template>
                </el-table-column>
                
                <el-table-column prop="result" label="识别结果" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.result === 'success' ? 'success' : 'danger'">
                      {{ scope.row.result === 'success' ? '成功' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
                
                <el-table-column label="操作" fixed="right" width="150">
                  <template #default="scope">
                    <el-button link type="primary" size="small" @click="handleViewImage(scope.row)">
                      <el-icon><View /></el-icon>
                      查看
                    </el-button>
                    <el-button link type="success" size="small" @click="handleDownloadImage(scope.row)">
                      <el-icon><Download /></el-icon>
                      下载
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="totalLogs"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </DataPanel>
        </el-col>
        
        <!-- 右侧图像详情面板 -->
        <el-col :span="8">
          <DataPanel title="图像详情">
            <div v-if="selectedImage" class="image-view">
              <div class="image-container">
                <img :src="selectedImage.imageUrl" alt="虫害图像" class="preview-image" />
              </div>
              
              <div class="image-info">
                <h3 class="image-name">{{ selectedImage.imageName }}</h3>
                
                <div class="info-item">
                  <span class="item-label">识别时间:</span>
                  <span class="item-value">{{ formatTime(selectedImage.time) }}</span>
                </div>
                
                <div class="info-item">
                  <span class="item-label">虫害类型:</span>
                  <span class="item-value">
                    <el-tag :type="getPestTypeTagType(selectedImage.pestType)">
                      {{ getPestTypeName(selectedImage.pestType) }}
                    </el-tag>
                  </span>
                </div>
                
                <div class="info-item">
                  <span class="item-label">置信度:</span>
                  <span class="item-value">
                    <el-progress 
                      :percentage="selectedImage.confidence" 
                      :status="getConfidenceStatus(selectedImage.confidence)" 
                    />
                  </span>
                </div>
                
                <div class="info-item">
                  <span class="item-label">识别结果:</span>
                  <span class="item-value">
                    <el-tag :type="selectedImage.result === 'success' ? 'success' : 'danger'">
                      {{ selectedImage.result === 'success' ? '成功' : '失败' }}
                    </el-tag>
                  </span>
                </div>
                
                <div class="info-item">
                  <span class="item-label">算法版本:</span>
                  <span class="item-value">{{ selectedImage.algorithmVersion || 'v3.2.1' }}</span>
                </div>
                
                <div class="info-item">
                  <span class="item-label">图像路径:</span>
                  <span class="item-value">{{ selectedImage.imagePath }}</span>
                </div>
                
                <div class="image-actions">
                  <el-button type="primary" size="small" @click="handleDownloadImage(selectedImage)">
                    <el-icon><Download /></el-icon>
                    下载图像
                  </el-button>
                  <el-button type="primary" size="small" @click="handleViewOriginal(selectedImage)">
                    <el-icon><ZoomIn /></el-icon>
                    查看原图
                  </el-button>
                </div>
              </div>
            </div>
            
            <div v-else class="no-image-selected">
              <el-empty description="请从左侧列表选择一个图像进行查看" />
            </div>
          </DataPanel>
        </el-col>
      </el-row>
      
      <!-- 数据统计图表区域 -->
      <el-row :gutter="20" class="charts-section">
        <el-col :span="12">
          <DataPanel title="识别结果统计">
            <div class="chart-container" ref="recognitionChart"></div>
          </DataPanel>
        </el-col>
        
        <el-col :span="12">
          <DataPanel title="虫害类型分布">
            <div class="chart-container" ref="pestTypeChart"></div>
          </DataPanel>
        </el-col>
      </el-row>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="识别系统正常" />
        <StatusIndicator type="warning" label="AI模型训练中" />
        <StatusIndicator type="normal" label="数据分析中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 原始图像查看对话框 -->
    <el-dialog
      v-model="originalImageDialogVisible"
      title="原始图像"
      width="70%"
      destroy-on-close
    >
      <div class="original-image-container" v-if="selectedImage">
        <img :src="selectedImage.originalImageUrl || selectedImage.imageUrl" alt="原始图像" class="original-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  Search, 
  RefreshRight, 
  View, 
  Download, 
  Delete, 
  ZoomIn,
  Filter,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { format } from 'date-fns'
import * as echarts from 'echarts'

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue'
import DataPanel from '../DeviceManagement/components/DataPanel.vue'
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue'

// 筛选表单
const filterForm = ref({
  dateRange: [] as string[],
  pestTypes: [] as string[],
  recognitionResult: 'all'
})

// 表格数据
const loading = ref(false)
const logs = ref<any[]>([])
const selectedLogs = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalLogs = ref(0)
const lastUpdateTime = ref(new Date())

// 图像详情
const selectedImage = ref<any>(null)
const originalImageDialogVisible = ref(false)

// 图表引用
const recognitionChart = ref<HTMLElement | null>(null);
const pestTypeChart = ref<HTMLElement | null>(null);

// 图表实例
let recChart: echarts.ECharts | null = null;
let typeChart: echarts.ECharts | null = null;

// 计算识别成功率
const successRate = computed(() => {
  if (logs.value.length === 0) return 0
  
  const successCount = logs.value.filter(log => log.result === 'success').length
  return Math.round((successCount / logs.value.length) * 100)
})

// 虫害类型选项
const pestTypes = [
  { label: '蚜虫', value: 'aphid' },
  { label: '飞虱', value: 'planthopper' },
  { label: '螟虫', value: 'borer' },
  { label: '粘虫', value: 'armyworm' },
  { label: '蝗虫', value: 'locust' },
  { label: '叶螨', value: 'mite' },
  { label: '稻纵卷叶螟', value: 'leafroller' },
  { label: '线虫', value: 'nematode' }
]

// 处理筛选
const handleFilter = () => {
  ElMessage.success('应用筛选条件')
  loadLogData()
}

// 重置筛选
const resetFilter = () => {
  filterForm.value.dateRange = []
  filterForm.value.pestTypes = []
  filterForm.value.recognitionResult = 'all'
  ElMessage.info('已重置筛选条件')
  loadLogData()
}

// 表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedLogs.value = selection
}

// 处理批量删除
const handleBulkDelete = () => {
  if (selectedLogs.value.length === 0) return
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedLogs.value.length} 条日志记录吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟删除操作
    ElMessage.success('批量删除成功')
    loadLogData()
  }).catch(() => {
    // 取消删除
  })
}

// 处理批量导出
const handleBulkExport = () => {
  if (selectedLogs.value.length === 0) return
  
  ElMessage.success(`成功导出 ${selectedLogs.value.length} 条日志记录`)
}

// 处理删除单条日志
const handleDeleteLog = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除"${row.imageName}"的日志记录吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟删除操作
    ElMessage.success('删除成功')
    loadLogData()
  }).catch(() => {
    // 取消删除
  })
}

// 处理查看图像
const handleViewImage = (row: any) => {
  selectedImage.value = row
}

// 处理下载图像
const handleDownloadImage = (row: any) => {
  // 实际项目中应该是调用后端API下载图像
  ElMessage.success(`开始下载 ${row.imageName}`)
}

// 查看原始图像
const handleViewOriginal = (row: any) => {
  originalImageDialogVisible.value = true
}

// 分页操作
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadLogData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadLogData()
}

// 导出所有日志
const exportLogs = () => {
  ElMessage.success('成功导出所有日志数据')
}

// 格式化时间
const formatTime = (timestamp: number | Date) => {
  if (!timestamp) return '';
  return format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss');
};

// 获取虫害类型名称
const getPestTypeName = (type: string) => {
  const pest = pestTypes.find(p => p.value === type)
  return pest ? pest.label : type
}

// 获取虫害类型对应的标签类型
const getPestTypeTagType = (type: string) => {
  const typeMap: {[key: string]: string} = {
    'aphid': 'success',
    'planthopper': 'info',
    'borer': 'warning',
    'armyworm': 'danger',
    'locust': '',
    'mite': 'success',
    'leafroller': 'info',
    'nematode': 'warning'
  }
  return typeMap[type] || ''
}

// 获取置信度对应的状态
const getConfidenceStatus = (confidence: number) => {
  if (confidence >= 90) return 'success'
  if (confidence >= 70) return 'warning'
  return 'exception'
}

// 加载日志数据
const loadLogData = () => {
  loading.value = true
  
  // 模拟请求数据，实际项目中应替换为真实API调用
  setTimeout(() => {
    logs.value = generateMockLogs(pageSize.value)
    totalLogs.value = 896
    loading.value = false
    
    // 清空已选择的日志
    selectedLogs.value = []
    
    // 重置选中的图像
    if (selectedImage.value && !logs.value.some(log => log.id === selectedImage.value.id)) {
      selectedImage.value = null
    }
  }, 500)
}

// 生成模拟数据
const generateMockLogs = (count: number) => {
  const logs = []
  const now = Date.now()
  
  for (let i = 0; i < count; i++) {
    const pestType = pestTypes[Math.floor(Math.random() * pestTypes.length)].value
    const confidence = Math.floor(Math.random() * 100)
    const isSuccess = confidence > 60
    
    logs.push({
      id: `log-${i}`,
      time: now - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000), // 30天内随机时间
      imageName: `pest_image_${10000 + i}.jpg`,
      imagePath: `/data/pest_images/2023/08/${10000 + i}.jpg`,
      pestType,
      confidence,
      result: isSuccess ? 'success' : 'fail',
      algorithmVersion: 'v3.2.1',
      imageUrl: `https://via.placeholder.com/800x600?text=Pest+Image+${i}`,
      originalImageUrl: `https://via.placeholder.com/1600x1200?text=Original+Image+${i}`
    })
  }
  
  return logs
}

// 初始化识别结果统计图表
const initRecognitionChart = () => {
  if (recognitionChart.value) {
    recChart = echarts.init(recognitionChart.value as HTMLDivElement);
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          color: '#9ca3af'
        }
      },
      series: [
        {
          name: '识别结果',
          type: 'pie',
          radius: '70%',
          center: ['60%', '50%'],
          data: [
            { value: 735, name: '识别成功', itemStyle: { color: '#10b981' } },
            { value: 161, name: '识别失败', itemStyle: { color: '#ef4444' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            color: '#e5e7eb'
          }
        }
      ]
    };
    
    recChart.setOption(option);
    
    window.addEventListener('resize', () => {
      recChart?.resize();
    });
  }
};

// 初始化虫害类型分布图表
const initPestTypeChart = () => {
  if (pestTypeChart.value) {
    typeChart = echarts.init(pestTypeChart.value as HTMLDivElement);
    
    const pestData = pestTypes.map(type => {
      return {
        name: type.label,
        value: Math.floor(Math.random() * 200) + 50
      };
    });
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
        backgroundColor: 'rgba(31, 41, 55, 0.8)',
        borderColor: '#3b82f6',
        textStyle: {
          color: '#e5e7eb'
        }
      },
      legend: {
        top: 'top',
        textStyle: {
          color: '#9ca3af'
        }
      },
      series: [
        {
          name: '虫害类型',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '55%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 5,
            borderColor: '#1f2937',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center',
            color: '#e5e7eb'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: pestData,
          // 使用不同的颜色
          color: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#14b8a6', '#6366f1']
        }
      ]
    };
    
    typeChart.setOption(option);
    
    window.addEventListener('resize', () => {
      typeChart?.resize();
    });
  }
};

// 初始化所有图表
const initCharts = () => {
  initRecognitionChart();
  initPestTypeChart();
};

// 刷新数据
const refreshData = () => {
  loadLogData();
  initCharts();
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

// 初始加载
onMounted(() => {
  loadLogData();
  initCharts();
});

// 组件卸载前清理
onUnmounted(() => {
  // 销毁图表实例
  recChart?.dispose();
  typeChart?.dispose();
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', () => {});
});
</script>

<style scoped>
.ai-recognition-log {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background-color: #1f2937;
  border: none;
  color: #e5e7eb;
}

.filter-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.filter-header .el-icon {
  margin-right: 8px;
  color: #3b82f6;
}

.filter-form {
  padding: 10px 0;
}

/* 内容区域 */
.content-section {
  flex: 1;
  margin-bottom: 20px;
  min-height: 0;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.log-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.bulk-actions {
  margin-bottom: 15px;
}

.log-table {
  flex: 1;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

/* 图像详情样式 */
.image-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-container {
  height: 250px;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1a2234;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-name {
  font-size: 18px;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 15px;
}

.image-info {
  flex: 1;
  overflow-y: auto;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
}

.item-label {
  width: 80px;
  color: #9ca3af;
  flex-shrink: 0;
}

.item-value {
  color: #e5e7eb;
  flex: 1;
}

.image-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.no-image-selected {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.original-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
  background-color: #0f172a;
}

.original-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .content-section .el-row {
    flex-direction: column;
  }
  
  .content-section .el-col {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
  }
  
  .image-container {
    height: 200px;
  }
}

@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 图表区域 */
.charts-section {
  margin-top: 20px;
}

.chart-container {
  height: 300px;
  width: 100%;
}
</style>

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save
npm install date-fns --save
--> 