<!--
TODO: 资源文件缺失问题
1. 缺少 logo 图片文件: @/assets/logo.svg，该文件在以下位置被引用:
   - 登录页面的 logo 图片
   - 登录页面的背景重复图案
   - 用户登录成功后存储在 localStorage 中的 avatar 字段

2. 功能开发:
   - "忘记密码"功能已集成，但后端API尚未实现
-->

<template>
  <div class="login-container">
    <div class="login-box">
      <LogoHeader />
      <LoginForm />
      <div class="login-footer">
        <p class="copyright">© {{ new Date().getFullYear() }} SkillAI 技能AI系统 版权所有</p>
      </div>
    </div>
    <AnimatedBackground />
  </div>
</template>

<script setup lang="ts">
import LogoHeader from '@/components/login/LogoHeader.vue'
import LoginForm from '@/components/login/LoginForm.vue'
import AnimatedBackground from '@/components/login/AnimatedBackground.vue'

/**
 * TODO: 资源文件缺失问题
 * 1. 缺少 logo 图片文件: @/assets/logo.svg，该文件在以下位置被引用:
 *    - 登录页面的 logo 图片
 *    - 登录页面的背景重复图案
 *    - 用户登录成功后存储在 localStorage 中的 avatar 字段
 */
</script>

<style lang="scss" scoped>
@use '@/styles/login.scss' as login;

.login-container {
  width: 100%;
  height: 100vh;
  min-height: 100vh;
  min-height: -webkit-fill-available;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.login-box {
  width: 420px;
  max-width: 90%;
  padding: 40px;
  background: rgba(18, 24, 38, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.08);
  z-index: 10;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 30px;
  
  animation: fadeIn 0.8s ease-out both;
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @media (max-height: 600px) {
    padding: 20px;
    gap: 15px;
  }
}

.login-footer {
  margin-top: auto;
  text-align: center;
  
  .copyright {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    margin: 0;
  }
}
</style>
