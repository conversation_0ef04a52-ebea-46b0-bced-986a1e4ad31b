/**
 * WebSocket服务类
 * 用于处理机器狗位置数据的实时通信
 */

import envConfig from '@/config/env';

// 基站数据接口
export interface AnchorData {
  anchorId: number;
  x: number;
  y: number;
  z: number;
}

// 机器狗位置数据接口
export interface RobotLocationData {
  anchors: AnchorData[];
  tagId: number;
  timestamp: number;
  x: number;
  y: number;
  z: number;
}

// WebSocket连接状态枚举
export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting'
}

// 事件回调类型
export type LocationDataCallback = (data: RobotLocationData) => void;
export type StatusChangeCallback = (status: WebSocketStatus) => void;
export type ErrorCallback = (error: Error) => void;

export class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private status: WebSocketStatus = WebSocketStatus.DISCONNECTED;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = envConfig.websocketReconnectAttempts;
  private reconnectInterval = envConfig.websocketReconnectInterval; // 重连间隔
  private reconnectTimer: number | null = null;
  private heartbeatTimer: number | null = null;
  private heartbeatInterval = envConfig.websocketHeartbeatInterval; // 心跳间隔

  // 事件回调
  private onLocationData: LocationDataCallback | null = null;
  private onStatusChange: StatusChangeCallback | null = null;
  private onError: ErrorCallback | null = null;

  constructor(url: string) {
    this.url = url;
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.setStatus(WebSocketStatus.CONNECTING);
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket连接成功');
          this.setStatus(WebSocketStatus.CONNECTED);
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭', event.code, event.reason);
          this.setStatus(WebSocketStatus.DISCONNECTED);
          this.stopHeartbeat();

          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000) {
            this.attemptReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          const errorObj = new Error('WebSocket连接错误');
          this.setStatus(WebSocketStatus.ERROR);
          this.handleError(errorObj);
          reject(errorObj);
        };

      } catch (error) {
        const errorObj = error instanceof Error ? error : new Error('WebSocket连接失败');
        this.handleError(errorObj);
        reject(errorObj);
      }
    });
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.stopHeartbeat();

    if (this.ws) {
      this.ws.close(1000, '主动断开连接');
      this.ws = null;
    }

    this.setStatus(WebSocketStatus.DISCONNECTED);
  }

  /**
   * 处理接收到的消息 - 优化为快速处理，包含坐标四舍五入
   */
  private handleMessage(data: string): void {
    console.log('WebSocket收到消息:', data.substring(0, 200) + (data.length > 200 ? '...' : ''))

    try {
      const locationData: RobotLocationData = JSON.parse(data);
      console.log('解析后的位置数据:', locationData)

      // 快速验证关键字段，减少验证开销
      if (locationData &&
          typeof locationData.tagId === 'number' &&
          typeof locationData.x === 'number' &&
          typeof locationData.y === 'number' &&
          Array.isArray(locationData.anchors)) {

        // 对坐标进行四舍五入处理（保留两位小数）
        const processedLocationData: RobotLocationData = {
          ...locationData,
          x: Math.round(locationData.x * 100) / 100,
          y: Math.round(locationData.y * 100) / 100,
          z: Math.round(locationData.z * 100) / 100,
          anchors: locationData.anchors.map(anchor => ({
            ...anchor,
            x: Math.round(anchor.x * 100) / 100,
            y: Math.round(anchor.y * 100) / 100,
            z: Math.round(anchor.z * 100) / 100
          }))
        };

        console.log('数据验证通过，坐标已四舍五入，调用回调函数')
        console.log('处理后的坐标:', { x: processedLocationData.x, y: processedLocationData.y })

        // 立即调用回调，无延迟
        if (this.onLocationData) {
          this.onLocationData(processedLocationData);
        } else {
          console.warn('onLocationData 回调函数未设置')
        }
      } else {
        console.warn('接收到无效的位置数据格式:', data);
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error, data);
      this.handleError(new Error('数据解析失败'));
    }
  }

  /**
   * 验证位置数据格式
   */
  private validateLocationData(data: any): data is RobotLocationData {
    return (
      data &&
      typeof data.tagId === 'number' &&
      typeof data.timestamp === 'number' &&
      typeof data.x === 'number' &&
      typeof data.y === 'number' &&
      typeof data.z === 'number' &&
      Array.isArray(data.anchors) &&
      data.anchors.length === 4 &&
      data.anchors.every((anchor: any) =>
        typeof anchor.anchorId === 'number' &&
        typeof anchor.x === 'number' &&
        typeof anchor.y === 'number' &&
        typeof anchor.z === 'number'
      )
    );
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限');
      this.handleError(new Error('重连失败，已达最大重试次数'));
      return;
    }

    this.reconnectAttempts++;
    this.setStatus(WebSocketStatus.RECONNECTING);

    console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch((error) => {
        console.error('重连失败:', error);
        this.attemptReconnect();
      });
    }, this.reconnectInterval);
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // 发送心跳包
        this.ws.send(JSON.stringify({ type: 'ping' }));
      }
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 设置连接状态
   */
  private setStatus(status: WebSocketStatus): void {
    this.status = status;
    if (this.onStatusChange) {
      this.onStatusChange(status);
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    if (this.onError) {
      this.onError(error);
    }
  }

  /**
   * 获取当前连接状态
   */
  getStatus(): WebSocketStatus {
    return this.status;
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.status === WebSocketStatus.CONNECTED &&
           this.ws &&
           this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 设置位置数据回调
   */
  setLocationDataCallback(callback: LocationDataCallback): void {
    this.onLocationData = callback;
  }

  /**
   * 设置状态变化回调
   */
  setStatusChangeCallback(callback: StatusChangeCallback): void {
    this.onStatusChange = callback;
  }

  /**
   * 设置错误回调
   */
  setErrorCallback(callback: ErrorCallback): void {
    this.onError = callback;
  }

  /**
   * 移除所有回调
   */
  removeAllCallbacks(): void {
    this.onLocationData = null;
    this.onStatusChange = null;
    this.onError = null;
  }
}
