<!--
  PlaneMapCanvas.vue
  平面地图Canvas组件
  使用HTML5 Canvas绘制2D平面地图，显示基站位置和机器狗实时位置
-->
<template>
  <div class="plane-map-container" ref="containerRef">
    <canvas
      ref="canvasRef"
      class="plane-map-canvas"
      @click="handleCanvasClick"
      @mousemove="handleMouseMove"
      @mouseleave="handleMouseLeave"
    ></canvas>



    <!-- 信息面板 - 右下角 -->
    <div class="info-panel bottom-right" v-if="hoveredInfo">
      <div class="info-title">{{ hoveredInfo.title }}</div>
      <div class="info-content">{{ hoveredInfo.content }}</div>
    </div>

    <!-- 图例 - 左下角 -->
    <div class="map-legend bottom-left">
      <div class="legend-item">
        <div class="legend-color anchor"></div>
        <span>基站</span>
      </div>
      <div class="legend-item">
        <div class="legend-color robot active"></div>
        <span>机器狗(活动)</span>
      </div>
      <div class="legend-item">
        <div class="legend-color robot idle"></div>
        <span>机器狗(待机)</span>
      </div>
      <div class="legend-item">
        <div class="legend-color track"></div>
        <span>轨迹</span>
      </div>
    </div>

    <!-- 坐标信息面板 - 左上角 -->
    <div class="coordinate-info top-left" v-if="showCoordinates && coordinateTransform">
      <div class="coord-item">
        <span class="coord-label">中心点:</span>
        <span class="coord-value">{{ formatCoordinate(coordinateTransform.getMapCenter()) }}</span>
      </div>
      <div class="coord-item">
        <span class="coord-label">缩放:</span>
        <span class="coord-value">{{ (zoomLevel * 100).toFixed(0) }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import { ZoomIn, ZoomOut, Refresh } from '@element-plus/icons-vue';
import type { AnchorData, RobotLocationData } from '@/utils/websocketService';
import { CoordinateTransform, createCoordinateTransform, type Point, type CanvasConfig } from '@/utils/coordinateTransform';
import { useResponsiveLayout } from '../composables/useResponsiveLayout';
import { performanceMonitor } from '../utils/performanceMonitor';
import { getOptimalCanvasSize } from '@/config/canvasConfig';

// Props定义
interface Props {
  anchors: AnchorData[];
  robotLocation: RobotLocationData | null;
  robotTrack: Point[];
  robotSmoothedTrack?: Point[];
  smoothingEnabled?: boolean;
  showGrid?: boolean;
  showCoordinates?: boolean;
  robotYaw?: number;  // 机器狗朝向角度 (-180 到 180 度)
}

const props = withDefaults(defineProps<Props>(), {
  robotSmoothedTrack: () => [],
  smoothingEnabled: false,
  showGrid: true,
  showCoordinates: true
});

// Emits定义
const emit = defineEmits<{
  robotClick: [location: RobotLocationData];
  anchorClick: [anchor: AnchorData];
  mapClick: [worldPoint: Point];
}>();

// 模板引用
const containerRef = ref<HTMLDivElement>();
const canvasRef = ref<HTMLCanvasElement>();

// 使用响应式布局管理
const { isMobile, isTablet, windowWidth, windowHeight } = useResponsiveLayout();

// 状态变量
const coordinateTransform = ref<CoordinateTransform | null>(null);
const animationFrameId = ref<number | null>(null);
const hoveredInfo = ref<{ title: string; content: string } | null>(null);

// 防抖定时器
let resizeTimer: number | null = null;

// 缩放控制
const zoomLevel = ref(1);
const minZoom = 0.5;
const maxZoom = 3;
const canZoomIn = computed(() => zoomLevel.value < maxZoom);
const canZoomOut = computed(() => zoomLevel.value > minZoom);

// Canvas配置 - 使用响应式配置
const canvasConfig = ref<CanvasConfig>({
  width: 600,
  height: 400,
  padding: 40
});

/**
 * 初始化Canvas - 优化为可靠的响应式初始化
 */
const initCanvas = async () => {
  // 等待DOM更新并确保容器尺寸稳定
  await nextTick();
  await new Promise(resolve => setTimeout(resolve, 100));

  if (!canvasRef.value || !containerRef.value) return;

  const container = containerRef.value;
  const canvas = canvasRef.value;

  // 验证容器尺寸是否有效
  const rect = container.getBoundingClientRect();
  if (rect.width === 0 || rect.height === 0) {
    // 如果尺寸无效，延迟重试
    setTimeout(() => initCanvas(), 200);
    return;
  }

  const devicePixelRatio = window.devicePixelRatio || 1;

  // 使用响应式配置计算最优Canvas尺寸
  const optimalSize = getOptimalCanvasSize(
    rect.width,
    rect.height,
    window.innerWidth,
    window.innerHeight
  );

  // 设置Canvas显示尺寸
  canvasConfig.value.width = rect.width;
  canvasConfig.value.height = rect.height;
  canvasConfig.value.padding = optimalSize.padding;

  // 设置Canvas实际尺寸（考虑高DPI屏幕）
  canvas.width = rect.width * devicePixelRatio;
  canvas.height = rect.height * devicePixelRatio;

  // 设置Canvas样式尺寸
  canvas.style.width = `${rect.width}px`;
  canvas.style.height = `${rect.height}px`;

  // 缩放绘图上下文以适应高DPI
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.scale(devicePixelRatio, devicePixelRatio);
  }

  // 初始化坐标转换器
  if (props.anchors.length > 0) {
    coordinateTransform.value = createCoordinateTransform(props.anchors, canvasConfig.value);
  }

  // 开始渲染循环
  startRenderLoop();
};

/**
 * 防抖的resize处理
 */
const handleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  resizeTimer = window.setTimeout(() => {
    // 开始性能监控
    performanceMonitor.startMeasure('canvas-resize');

    initCanvas();

    // 结束性能监控并记录
    const resizeTime = performanceMonitor.endMeasure('canvas-resize');
    performanceMonitor.recordResizeTime(resizeTime);
  }, isMobile.value ? 100 : 150); // 移动端更快的响应
};

/**
 * 开始渲染循环 - 优化为高频实时渲染
 */
const startRenderLoop = () => {
  const render = () => {
    drawMap();
    // 立即请求下一帧，确保最高刷新率
    animationFrameId.value = requestAnimationFrame(render);
  };
  // 立即开始渲染
  render();
};

/**
 * 停止渲染循环
 */
const stopRenderLoop = () => {
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value);
    animationFrameId.value = null;
  }
};

/**
 * 绘制地图 - 基于中心点的高性能实时绘制
 */
const drawMap = () => {
  if (!canvasRef.value || !coordinateTransform.value) return;

  // 开始性能监控
  performanceMonitor.startMeasure('canvas-render');

  const canvas = canvasRef.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  // 高效清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 设置基于中心点的缩放变换
  ctx.save();

  // 获取Canvas中心点
  const centerX = canvas.width / 2;
  const centerY = canvas.height / 2;

  // 移动到中心点，进行缩放，再移回原位
  ctx.translate(centerX, centerY);
  ctx.scale(zoomLevel.value, zoomLevel.value);
  ctx.translate(-centerX, -centerY);

  // 立即绘制背景
  drawBackground(ctx);

  // 立即绘制网格（如果启用）
  if (props.showGrid) {
    drawGrid(ctx);
  }

  // 立即绘制基站
  drawAnchors(ctx);

  // 立即绘制机器狗轨迹（无长度检查延迟）
  const trackToRender = props.smoothingEnabled && props.robotSmoothedTrack && props.robotSmoothedTrack.length > 1
    ? props.robotSmoothedTrack
    : props.robotTrack;

  if (trackToRender && trackToRender.length > 1) {
    drawRobotTrack(ctx, trackToRender);
  }

  // 立即绘制机器狗位置
  if (props.robotLocation) {
    drawRobotLocation(ctx);
  }

  // 立即绘制坐标信息（如果启用）
  // 注释：禁用Canvas绘制的坐标信息，避免与工具栏重叠，使用HTML面板代替
  // if (props.showCoordinates) {
  //   drawCoordinateInfo(ctx);
  // }

  ctx.restore();

  // 结束性能监控并记录
  const renderTime = performanceMonitor.endMeasure('canvas-render');
  performanceMonitor.recordRenderTime(renderTime);
  performanceMonitor.updateFPS();
};

/**
 * 绘制背景
 */
const drawBackground = (ctx: CanvasRenderingContext2D) => {
  const { width, height } = canvasConfig.value;

  // 绘制深色背景
  ctx.fillStyle = '#1f2937';
  ctx.fillRect(0, 0, width, height);

  // 绘制边框
  ctx.strokeStyle = '#374151';
  ctx.lineWidth = 2;
  ctx.strokeRect(0, 0, width, height);
};

/**
 * 绘制网格
 */
const drawGrid = (ctx: CanvasRenderingContext2D) => {
  if (!coordinateTransform.value) return;

  const gridLines = coordinateTransform.value.generateGridLines();

  ctx.strokeStyle = '#374151';
  ctx.lineWidth = 1;
  ctx.setLineDash([2, 2]);

  // 绘制垂直线
  gridLines.vertical.forEach(x => {
    const startPoint = coordinateTransform.value!.worldToCanvas({ x, y: coordinateTransform.value!.getMapBounds().minY });
    const endPoint = coordinateTransform.value!.worldToCanvas({ x, y: coordinateTransform.value!.getMapBounds().maxY });

    ctx.beginPath();
    ctx.moveTo(startPoint.x, startPoint.y);
    ctx.lineTo(endPoint.x, endPoint.y);
    ctx.stroke();
  });

  // 绘制水平线
  gridLines.horizontal.forEach(y => {
    const startPoint = coordinateTransform.value!.worldToCanvas({ x: coordinateTransform.value!.getMapBounds().minX, y });
    const endPoint = coordinateTransform.value!.worldToCanvas({ x: coordinateTransform.value!.getMapBounds().maxX, y });

    ctx.beginPath();
    ctx.moveTo(startPoint.x, startPoint.y);
    ctx.lineTo(endPoint.x, endPoint.y);
    ctx.stroke();
  });

  ctx.setLineDash([]);
};

/**
 * 绘制基站
 */
const drawAnchors = (ctx: CanvasRenderingContext2D) => {
  if (!coordinateTransform.value) return;

  props.anchors.forEach(anchor => {
    const canvasPoint = coordinateTransform.value!.worldToCanvas({ x: anchor.x, y: anchor.y });

    // 绘制基站圆圈
    ctx.fillStyle = '#3b82f6';
    ctx.strokeStyle = '#1d4ed8';
    ctx.lineWidth = 2;

    ctx.beginPath();
    ctx.arc(canvasPoint.x, canvasPoint.y, 8, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // 绘制基站ID
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(anchor.anchorId.toString(), canvasPoint.x, canvasPoint.y);

    // 绘制基站标签
    ctx.fillStyle = '#e5e7eb';
    ctx.font = '10px Arial';
    ctx.fillText(`基站${anchor.anchorId}`, canvasPoint.x, canvasPoint.y + 20);
  });
};

/**
 * 绘制机器狗轨迹 - 优化为高性能实时绘制，支持平滑轨迹
 */
const drawRobotTrack = (ctx: CanvasRenderingContext2D, trackPoints: Point[]) => {
  if (!coordinateTransform.value || trackPoints.length < 2) return;

  // 根据是否为平滑轨迹设置不同的样式
  const isSmoothed = props.smoothingEnabled && trackPoints === props.robotSmoothedTrack;

  if (isSmoothed) {
    // 平滑轨迹样式 - 更亮的绿色，更粗的线条
    ctx.strokeStyle = '#22c55e';
    ctx.lineWidth = 12;
    ctx.shadowColor = 'rgba(34, 197, 94, 0.4)';
    ctx.shadowBlur = 8;
  } else {
    // 原始轨迹样式
    ctx.strokeStyle = '#10b981';
    ctx.lineWidth = 10;
    ctx.shadowColor = 'rgba(16, 185, 129, 0.3)';
    ctx.shadowBlur = 6;
  }

  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';

  // 高效绘制轨迹线
  ctx.beginPath();

  // 优化坐标转换 - 批量处理
  const canvasPoints = trackPoints.map(point =>
    coordinateTransform.value!.worldToCanvas(point)
  );

  // 快速绘制路径
  canvasPoints.forEach((canvasPoint, index) => {
    if (index === 0) {
      ctx.moveTo(canvasPoint.x, canvasPoint.y);
    } else {
      ctx.lineTo(canvasPoint.x, canvasPoint.y);
    }
  });

  ctx.stroke();

  // 重置阴影
  ctx.shadowColor = 'transparent';
  ctx.shadowBlur = 0;

  // 绘制轨迹点 - 根据平滑状态调整样式
  canvasPoints.forEach((canvasPoint, index) => {
    if (index === 0) {
      // 起点 - 绿色圆点
      ctx.fillStyle = isSmoothed ? '#22c55e' : '#10b981';
      ctx.beginPath();
      ctx.arc(canvasPoint.x, canvasPoint.y, isSmoothed ? 6 : 5, 0, Math.PI * 2);
      ctx.fill();

      // 起点边框
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();
    } else if (index === canvasPoints.length - 1) {
      // 终点 - 红色圆点
      ctx.fillStyle = '#ef4444';
      ctx.beginPath();
      ctx.arc(canvasPoint.x, canvasPoint.y, isSmoothed ? 6 : 5, 0, Math.PI * 2);
      ctx.fill();

      // 终点边框
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();
    } else {
      // 中间点 - 小圆点
      ctx.fillStyle = isSmoothed ? '#22c55e' : '#10b981';
      ctx.beginPath();
      ctx.arc(canvasPoint.x, canvasPoint.y, 2, 0, 2 * Math.PI);
      ctx.fill();
    }
  });
};

/**
 * 绘制机器狗位置
 */
const drawRobotLocation = (ctx: CanvasRenderingContext2D) => {
  if (!coordinateTransform.value || !props.robotLocation) return;

  const robotPoint = { x: props.robotLocation.x, y: props.robotLocation.y };
  const canvasPoint = coordinateTransform.value.worldToCanvas(robotPoint);

  // 绘制机器狗图标
  ctx.fillStyle = '#f59e0b';
  ctx.strokeStyle = '#d97706';
  ctx.lineWidth = 3;

  // 绘制机器狗身体（矩形）
  const robotSize = 12;
  ctx.fillRect(canvasPoint.x - robotSize/2, canvasPoint.y - robotSize/2, robotSize, robotSize);
  ctx.strokeRect(canvasPoint.x - robotSize/2, canvasPoint.y - robotSize/2, robotSize, robotSize);

  // 绘制朝向指示器（箭头）
  if (typeof props.robotYaw === 'number') {
    // 将角度转换为弧度，考虑Canvas坐标系Y轴向下的特点
    // 需要取负值来确保正角度按逆时针方向显示
    const angleRad = (-props.robotYaw * Math.PI) / 180;

    // 箭头长度
    const arrowLength = robotSize * 1.5;
    const arrowWidth = 4;

    // 计算箭头终点
    const endX = canvasPoint.x + Math.cos(angleRad) * arrowLength;
    const endY = canvasPoint.y + Math.sin(angleRad) * arrowLength;

    // 绘制箭头主线
    ctx.strokeStyle = '#ef4444';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(canvasPoint.x, canvasPoint.y);
    ctx.lineTo(endX, endY);
    ctx.stroke();

    // 绘制箭头头部
    const headLength = 8;
    const headAngle = Math.PI / 6; // 30度

    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - headLength * Math.cos(angleRad - headAngle),
      endY - headLength * Math.sin(angleRad - headAngle)
    );
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - headLength * Math.cos(angleRad + headAngle),
      endY - headLength * Math.sin(angleRad + headAngle)
    );
    ctx.stroke();

    // 绘制角度文本
    ctx.fillStyle = '#ef4444';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${props.robotYaw.toFixed(0)}°`, canvasPoint.x, canvasPoint.y - robotSize - 5);
  } else {
    // 如果没有朝向数据，绘制默认的方向指示器
    ctx.fillStyle = '#ffffff';
    ctx.beginPath();
    ctx.arc(canvasPoint.x + 4, canvasPoint.y - 4, 2, 0, 2 * Math.PI);
    ctx.fill();
  }

  // 绘制机器狗标签
  ctx.fillStyle = '#f59e0b';
  ctx.font = '12px Arial';
  ctx.textAlign = 'center';
  ctx.fillText(`机器狗${props.robotLocation.tagId}`, canvasPoint.x, canvasPoint.y + 25);

  // 绘制坐标信息
  ctx.fillStyle = '#9ca3af';
  ctx.font = '10px Arial';
  const coordText = `(${props.robotLocation.x.toFixed(2)}, ${props.robotLocation.y.toFixed(2)})`;
  ctx.fillText(coordText, canvasPoint.x, canvasPoint.y + 38);
};

/**
 * 绘制坐标信息 (Canvas版本)
 * 注意：此函数已被禁用，避免与地图工具栏重叠
 * 当前使用HTML坐标信息面板代替，位置已优化避免重叠
 */
const drawCoordinateInfo = (ctx: CanvasRenderingContext2D) => {
  if (!coordinateTransform.value) return;

  const bounds = coordinateTransform.value.getMapBounds();

  ctx.fillStyle = '#9ca3af';
  ctx.font = '12px Arial';
  ctx.textAlign = 'left';

  // 绘制坐标范围信息
  const infoText = [
    `X: ${bounds.minX.toFixed(2)} ~ ${bounds.maxX.toFixed(2)}`,
    `Y: ${bounds.minY.toFixed(2)} ~ ${bounds.maxY.toFixed(2)}`,
    `缩放: ${(zoomLevel.value * 100).toFixed(0)}%`
  ];

  infoText.forEach((text, index) => {
    ctx.fillText(text, 10, 20 + index * 15);
  });
};

/**
 * 处理Canvas点击事件
 */
const handleCanvasClick = (event: MouseEvent) => {
  if (!coordinateTransform.value || !canvasRef.value) return;

  const rect = canvasRef.value.getBoundingClientRect();
  const canvasPoint = {
    x: (event.clientX - rect.left) / zoomLevel.value,
    y: (event.clientY - rect.top) / zoomLevel.value
  };

  const worldPoint = coordinateTransform.value.canvasToWorld(canvasPoint);

  // 检查是否点击了机器狗
  if (props.robotLocation) {
    const robotCanvasPoint = coordinateTransform.value.worldToCanvas({
      x: props.robotLocation.x,
      y: props.robotLocation.y
    });

    const distance = Math.sqrt(
      Math.pow(canvasPoint.x - robotCanvasPoint.x, 2) +
      Math.pow(canvasPoint.y - robotCanvasPoint.y, 2)
    );

    if (distance <= 15) {
      emit('robotClick', props.robotLocation);
      return;
    }
  }

  // 检查是否点击了基站
  for (const anchor of props.anchors) {
    const anchorCanvasPoint = coordinateTransform.value.worldToCanvas({
      x: anchor.x,
      y: anchor.y
    });

    const distance = Math.sqrt(
      Math.pow(canvasPoint.x - anchorCanvasPoint.x, 2) +
      Math.pow(canvasPoint.y - anchorCanvasPoint.y, 2)
    );

    if (distance <= 12) {
      emit('anchorClick', anchor);
      return;
    }
  }

  // 普通地图点击
  emit('mapClick', worldPoint);
};

/**
 * 处理鼠标移动事件
 */
const handleMouseMove = (event: MouseEvent) => {
  if (!coordinateTransform.value || !canvasRef.value) return;

  const rect = canvasRef.value.getBoundingClientRect();
  const canvasPoint = {
    x: (event.clientX - rect.left) / zoomLevel.value,
    y: (event.clientY - rect.top) / zoomLevel.value
  };

  const worldPoint = coordinateTransform.value.canvasToWorld(canvasPoint);

  // 检查悬停对象
  let hoverInfo = null;

  // 检查机器狗
  if (props.robotLocation) {
    const robotCanvasPoint = coordinateTransform.value.worldToCanvas({
      x: props.robotLocation.x,
      y: props.robotLocation.y
    });

    const distance = Math.sqrt(
      Math.pow(canvasPoint.x - robotCanvasPoint.x, 2) +
      Math.pow(canvasPoint.y - robotCanvasPoint.y, 2)
    );

    if (distance <= 15) {
      hoverInfo = {
        title: `机器狗${props.robotLocation.tagId}`,
        content: `位置: (${props.robotLocation.x.toFixed(2)}, ${props.robotLocation.y.toFixed(2)})`
      };
    }
  }

  // 检查基站
  if (!hoverInfo) {
    for (const anchor of props.anchors) {
      const anchorCanvasPoint = coordinateTransform.value.worldToCanvas({
        x: anchor.x,
        y: anchor.y
      });

      const distance = Math.sqrt(
        Math.pow(canvasPoint.x - anchorCanvasPoint.x, 2) +
        Math.pow(canvasPoint.y - anchorCanvasPoint.y, 2)
      );

      if (distance <= 12) {
        hoverInfo = {
          title: `基站${anchor.anchorId}`,
          content: `位置: (${anchor.x.toFixed(2)}, ${anchor.y.toFixed(2)})`
        };
        break;
      }
    }
  }

  hoveredInfo.value = hoverInfo;
};

/**
 * 处理鼠标离开事件
 */
const handleMouseLeave = () => {
  hoveredInfo.value = null;
};

/**
 * 放大
 */
const zoomIn = () => {
  if (canZoomIn.value) {
    zoomLevel.value = Math.min(maxZoom, zoomLevel.value * 1.2);
  }
};

/**
 * 缩小
 */
const zoomOut = () => {
  if (canZoomOut.value) {
    zoomLevel.value = Math.max(minZoom, zoomLevel.value / 1.2);
  }
};

/**
 * 重置视图
 */
const resetView = () => {
  zoomLevel.value = 1;
};

/**
 * 格式化坐标显示
 */
const formatCoordinate = (point: Point) => {
  return `(${point.x.toFixed(2)}, ${point.y.toFixed(2)})`;
};

// 监听基站数据变化
watch(() => props.anchors, (newAnchors) => {
  if (newAnchors.length > 0 && coordinateTransform.value) {
    coordinateTransform.value.updateAnchors(newAnchors);
  } else if (newAnchors.length > 0) {
    coordinateTransform.value = createCoordinateTransform(newAnchors, canvasConfig.value);
  }
}, { deep: true });

// 监听窗口尺寸变化
watch([windowWidth, windowHeight], () => {
  handleResize();
});

// 生命周期
onMounted(() => {
  initCanvas();

  // 监听窗口大小变化（备用方案）
  window.addEventListener('resize', handleResize, { passive: true });

  // 监听设备方向变化
  window.addEventListener('orientationchange', () => {
    setTimeout(() => {
      handleResize();
    }, 100);
  }, { passive: true });
});

onUnmounted(() => {
  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
    resizeTimer = null;
  }

  // 移除事件监听器
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('orientationchange', handleResize);

  // 停止渲染循环
  stopRenderLoop();
});

// 暴露方法供父组件调用
defineExpose({
  zoomIn,
  zoomOut,
  resetView
});
</script>

<style lang="scss" scoped>
.plane-map-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #1f2937;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.plane-map-canvas {
  width: 100%;
  height: 100%;
  cursor: crosshair;
  transition: cursor 0.2s ease;

  &:hover {
    cursor: pointer;
  }
}

/* 基础定位类 */
.top-left {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
}

/* 坐标信息面板特殊定位 - 避免与工具栏重叠 */
.coordinate-info.top-left {
  top: 110px; /* 为地图工具栏留出足够空间：工具栏高度约70px + 阴影效果 + 间距40px */
  transition: all 0.3s ease; /* 添加平滑过渡效果 */
  z-index: 5; /* 确保在工具栏下方 */
}

.top-right {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.bottom-left {
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 10;
}

.bottom-right {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
}



.info-panel {
  background: rgba(31, 41, 55, 0.95);
  border-radius: 6px;
  padding: 10px;
  min-width: 150px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(59, 130, 246, 0.3);

  .info-title {
    font-size: 12px;
    font-weight: 600;
    color: #3b82f6;
    margin-bottom: 4px;
  }

  .info-content {
    font-size: 11px;
    color: #e5e7eb;
    line-height: 1.4;
  }
}

/* 坐标信息面板 - 左上角 */
.coordinate-info {
  background: rgba(31, 41, 55, 0.95);
  color: white;
  padding: 10px 12px;
  border-radius: 6px;
  font-size: 12px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  min-width: 140px;

  .coord-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .coord-label {
    color: #9ca3af;
    font-weight: 500;
  }

  .coord-value {
    color: #3b82f6;
    font-weight: 600;
    font-family: 'Courier New', monospace;
  }
}

.map-legend {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(31, 41, 55, 0.9);
  border-radius: 6px;
  padding: 10px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  z-index: 10;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 12px;
  color: #e5e7eb;

  &:last-child {
    margin-bottom: 0;
  }
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;

  &.anchor {
    background: #3b82f6;
    border: 1px solid #1d4ed8;
  }

  &.robot {
    &.active {
      background: #f59e0b;
      border: 1px solid #d97706;
    }

    &.idle {
      background: #6b7280;
      border: 1px solid #4b5563;
    }
  }

  &.track {
    background: #10b981;
    border: 1px solid #059669;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .top-left, .top-right {
    top: 5px;
  }

  .bottom-left, .bottom-right {
    bottom: 5px;
  }

  .top-left, .bottom-left {
    left: 5px;
  }

  .top-right, .bottom-right {
    right: 5px;
  }

  /* 移动端坐标信息面板位置调整 */
  .coordinate-info.top-left {
    top: 90px; /* 移动端工具栏较小，但仍需足够间距避免重叠 */
  }



  .info-panel {
    padding: 8px;
    min-width: 120px;

    .info-title {
      font-size: 11px;
    }

    .info-content {
      font-size: 10px;
    }
  }

  .coordinate-info {
    padding: 8px;
    min-width: 100px;
    font-size: 10px;
  }

  .map-legend {
    padding: 8px;
  }

  .legend-item {
    font-size: 11px;
    margin-bottom: 4px;
  }

  .legend-color {
    width: 10px;
    height: 10px;
  }
}
</style>
