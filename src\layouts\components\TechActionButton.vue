<template>
  <button
    class="tech-action-button"
    :class="[
      `type-${type}`,
      `size-${size}`,
      { 'is-loading': loading, 'is-pulsing': pulsing }
    ]"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    ref="buttonRef"
  >
    <!-- 背景动画层 -->
    <div class="button-bg-effects">
      <div class="glow-effect"></div>
      <div class="corner top-left"></div>
      <div class="corner top-right"></div>
      <div class="corner bottom-left"></div>
      <div class="corner bottom-right"></div>
      <div class="tech-line horizontal-line"></div>
      <div class="tech-line vertical-line"></div>
    </div>

    <!-- 内容层 -->
    <div class="button-content">
      <el-icon v-if="icon && !loading" class="button-icon">
        <component :is="icon"></component>
      </el-icon>

      <div v-if="loading" class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-core"></div>
      </div>

      <span v-if="!loading && (size !== 'mini' || !icon)" class="button-text">
        <slot>{{ text }}</slot>
      </span>
    </div>

    <!-- 悬浮粒子效果 -->
    <div class="particles-container" ref="particlesRef"></div>
  </button>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { gsap } from 'gsap';

const props = defineProps({
  type: {
    type: String,
    default: 'primary',
    validator: (value: string) => ['primary', 'danger', 'success', 'warning', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value: string) => ['mini', 'small', 'medium', 'large'].includes(value)
  },
  icon: {
    type: String,
    default: ''
  },
  text: {
    type: String,
    default: '按钮'
  },
  loading: {
    type: Boolean,
    default: false
  },
  pulsing: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click']);
const buttonRef = ref<HTMLElement | null>(null);
const particlesRef = ref<HTMLElement | null>(null);
const hoverState = ref(false);

// 点击事件处理
const handleClick = (event: MouseEvent) => {
  if (props.loading) return;

  // 点击涟漪动画
  if (buttonRef.value) {
    const rect = buttonRef.value.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    gsap.to(buttonRef.value.querySelector('.glow-effect'), {
      opacity: 0.8,
      scale: 1.5,
      duration: 0.4,
      ease: 'power1.out',
      onComplete: () => {
        gsap.to(buttonRef.value!.querySelector('.glow-effect'), {
          opacity: hoverState.value ? 0.3 : 0,
          scale: 1,
          duration: 0.6
        });
      }
    });
  }

  emit('click', event);
};

// 鼠标进入按钮区域
const handleMouseEnter = () => {
  hoverState.value = true;

  // 悬停动画效果
  if (buttonRef.value) {
    gsap.to(buttonRef.value.querySelector('.glow-effect'), {
      opacity: 0.3,
      duration: 0.3
    });

    // 角落动画
    const corners = buttonRef.value.querySelectorAll('.corner');
    corners.forEach((corner) => {
      gsap.to(corner, {
        opacity: 1,
        duration: 0.3
      });
    });

    // 线条动画
    const lines = buttonRef.value.querySelectorAll('.tech-line');
    lines.forEach((line) => {
      gsap.to(line, {
        opacity: 0.6,
        width: line.classList.contains('horizontal-line') ? '60%' : '2px',
        height: line.classList.contains('vertical-line') ? '60%' : '2px',
        duration: 0.4
      });
    });
  }

  // 粒子效果
  createParticles();
};

// 鼠标离开按钮区域
const handleMouseLeave = () => {
  hoverState.value = false;

  if (buttonRef.value) {
    gsap.to(buttonRef.value.querySelector('.glow-effect'), {
      opacity: 0,
      duration: 0.3
    });

    // 角落动画
    const corners = buttonRef.value.querySelectorAll('.corner');
    corners.forEach((corner) => {
      gsap.to(corner, {
        opacity: 0.5,
        duration: 0.3
      });
    });

    // 线条动画
    const lines = buttonRef.value.querySelectorAll('.tech-line');
    lines.forEach((line) => {
      gsap.to(line, {
        opacity: 0.3,
        width: line.classList.contains('horizontal-line') ? '20%' : '1px',
        height: line.classList.contains('vertical-line') ? '20%' : '1px',
        duration: 0.3
      });
    });
  }
};

// 创建粒子效果
const createParticles = () => {
  if (!particlesRef.value) return;

  // 清除已有的粒子
  particlesRef.value.innerHTML = '';

  // 根据按钮大小决定粒子数量
  const particleCount = props.size === 'large' ? 6 : props.size === 'medium' ? 4 : 2;

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    particle.className = 'particle';

    // 随机位置和大小
    const size = Math.random() * 4 + 2;
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;

    // 随机起始位置
    const xPos = Math.random() * 100;
    const yPos = Math.random() * 100;
    particle.style.left = `${xPos}%`;
    particle.style.top = `${yPos}%`;

    particlesRef.value.appendChild(particle);

    // 粒子动画
    gsap.to(particle, {
      x: (Math.random() - 0.5) * 40,
      y: (Math.random() - 0.5) * 40,
      opacity: 0,
      duration: Math.random() * 1 + 1,
      onComplete: () => {
        if (particlesRef.value && particlesRef.value.contains(particle)) {
          particlesRef.value.removeChild(particle);
        }
      }
    });
  }
};

// 监听加载状态变化
watch(() => props.loading, (newVal) => {
  if (newVal && buttonRef.value) {
    // 加载动画
    const spinner = buttonRef.value.querySelector('.loading-spinner');
    if (spinner) {
      gsap.from(spinner, {
        rotate: '0deg',
        duration: 0.3
      });
    }
  }
});

// 组件挂载时的初始化
onMounted(() => {
  // 初始化角落效果
  if (buttonRef.value) {
    const corners = buttonRef.value.querySelectorAll('.corner');
    corners.forEach((corner) => {
      gsap.set(corner, { opacity: 0.5 });
    });

    // 初始化线条效果
    const lines = buttonRef.value.querySelectorAll('.tech-line');
    lines.forEach((line) => {
      gsap.set(line, {
        opacity: 0.3,
        width: line.classList.contains('horizontal-line') ? '20%' : '1px',
        height: line.classList.contains('vertical-line') ? '20%' : '1px'
      });
    });
  }
});
</script>

<style scoped>
/* 基础按钮样式 */
.tech-action-button {
  position: relative;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  padding: 0 16px;
  background: transparent;
  outline: none;
}

/* 按钮大小 */
.tech-action-button.size-mini {
  height: 32px;
  min-width: 32px;
}

.tech-action-button.size-small {
  height: 36px;
  min-width: 80px;
}

.tech-action-button.size-medium {
  height: 40px;
  min-width: 100px;
}

.tech-action-button.size-large {
  height: 48px;
  min-width: 120px;
}

/* 按钮类型/颜色 */
.tech-action-button.type-primary {
  --button-color: #1976d2;
  --button-hover: #0d47a1;
  --button-active: #2196f3;
  --glow-color: rgba(33, 150, 243, 0.5);
}

.tech-action-button.type-danger {
  --button-color: #d32f2f;
  --button-hover: #b71c1c;
  --button-active: #f44336;
  --glow-color: rgba(244, 67, 54, 0.5);
}

.tech-action-button.type-success {
  --button-color: #2e7d32;
  --button-hover: #1b5e20;
  --button-active: #4caf50;
  --glow-color: rgba(76, 175, 80, 0.5);
}

.tech-action-button.type-warning {
  --button-color: #f57c00;
  --button-hover: #e65100;
  --button-active: #ff9800;
  --glow-color: rgba(255, 152, 0, 0.5);
}

.tech-action-button.type-info {
  --button-color: #0288d1;
  --button-hover: #01579b;
  --button-active: #03a9f4;
  --glow-color: rgba(3, 169, 244, 0.5);
}

/* 背景效果 */
.button-bg-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: rgba(var(--button-color), 0.2);
  border: 1px solid var(--button-color);
  overflow: hidden;
}

/* 发光效果 */
.glow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, var(--glow-color) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 角落装饰 */
.corner {
  position: absolute;
  width: 8px;
  height: 8px;
  opacity: 0.5;
}

.top-left {
  top: 0;
  left: 0;
  border-top: 1px solid var(--button-color);
  border-left: 1px solid var(--button-color);
}

.top-right {
  top: 0;
  right: 0;
  border-top: 1px solid var(--button-color);
  border-right: 1px solid var(--button-color);
}

.bottom-left {
  bottom: 0;
  left: 0;
  border-bottom: 1px solid var(--button-color);
  border-left: 1px solid var(--button-color);
}

.bottom-right {
  bottom: 0;
  right: 0;
  border-bottom: 1px solid var(--button-color);
  border-right: 1px solid var(--button-color);
}


/* 按钮内容 */
.button-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.button-icon {
  font-size: 16px;
  color: var(--button-color);
  transition: transform 0.3s ease;
}

.button-text {
  color: var(--button-color);
  font-weight: 500;
  letter-spacing: 0.5px;
  white-space: nowrap;
  transition: letter-spacing 0.3s ease;
}

/* 加载动画 */
.loading-spinner {
  position: relative;
  width: 18px;
  height: 18px;
  animation: spin 1.2s linear infinite;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--button-color);
  border-radius: 50%;
}

.spinner-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background-color: var(--button-color);
  border-radius: 50%;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 粒子效果 */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  background-color: var(--button-color);
  border-radius: 50%;
  opacity: 0.6;
  pointer-events: none;
}

/* 脉冲效果 */
.tech-action-button.is-pulsing::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 10px;
  border: 2px solid var(--button-color);
  opacity: 0;
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

/* 悬停效果 */
.tech-action-button:hover:not(.is-loading) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.tech-action-button:hover:not(.is-loading) .button-text {
  letter-spacing: 1px;
}

.tech-action-button:hover:not(.is-loading) .button-icon {
  transform: scale(1.1);
}

/* 按钮禁用状态 */
.tech-action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载状态 */
.tech-action-button.is-loading {
  cursor: default;
}

.tech-action-button.is-loading .button-content {
  opacity: 0.8;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .tech-action-button.size-large {
    height: 44px;
    min-width: 110px;
  }

  .tech-action-button.size-medium {
    height: 38px;
    min-width: 90px;
  }
}
</style>
