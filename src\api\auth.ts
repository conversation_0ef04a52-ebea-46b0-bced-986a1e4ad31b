import axios from 'axios'
import { ElMessage } from 'element-plus'
import envConfig from '@/config/env'
import type {
  AgriLoginRequest,
  AgriLoginResponse,
  AgriUserInfo,
  ApiResponse
} from '@/types/user'

// 定义API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: envConfig.apiTimeout
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    console.log('请求URL:', `${API_BASE_URL}${config.url}`)
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

/**
 * 农业用户登录
 * @param loginData 登录数据
 * @returns Promise<AgriLoginResponse>
 */
export async function login(loginData: AgriLoginRequest): Promise<AgriLoginResponse> {
  try {
    console.log('农业用户登录请求URL:', `${API_BASE_URL}/agriUser/login`)
    const response = await apiClient.post<ApiResponse<AgriLoginResponse>>('/agriUser/login', {
      username: loginData.username,
      password: loginData.password,
      code: loginData.code,
      uuid: loginData.uuid,
      rememberMe: loginData.rememberMe
    })

    const res = response.data

    // 检查响应状态
    if (res.code === 200 && res.data) {
      return res.data
    } else {
      const errorMsg = res.msg || res.message || '登录失败'
      return Promise.reject(new Error(errorMsg))
    }
  } catch (error: any) {
    const errorMsg = error.response?.data?.msg || error.response?.data?.message || '登录失败，请稍后再试'
    return Promise.reject(new Error(errorMsg))
  }
}

/**
 * 获取当前登录用户信息
 * @returns Promise<AgriUserInfo>
 */
export async function getCurrentUser(): Promise<AgriUserInfo> {
  try {
    const response = await apiClient.get<ApiResponse<AgriUserInfo>>('/agriUser/profile')
    const res = response.data

    if (res.code === 200 && res.data) {
      return res.data
    } else {
      const errorMsg = res.msg || res.message || '获取用户信息失败'
      return Promise.reject(new Error(errorMsg))
    }
  } catch (error: any) {
    const errorMsg = error.response?.data?.msg || error.response?.data?.message || '获取用户信息失败'
    return Promise.reject(new Error(errorMsg))
  }
}

/**
 * 农业用户退出登录
 * @returns Promise<void>
 */
export async function logoutApi(): Promise<void> {
  try {
    await apiClient.post<ApiResponse<void>>('/agriUser/logout')
  } catch (error: any) {
    // 退出登录失败不影响本地清理
    console.warn('服务端退出登录失败:', error)
  }
}

/**
 * 解析JWT令牌
 * @param token JWT令牌
 * @returns 解析后的用户信息
 */
export function parseJwt(token: string): any {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
      }).join('')
    )

    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('解析JWT令牌失败:', error)
    return {}
  }
}

/**
 * 清理本地存储的认证信息
 */
export function clearAuthStorage(): void {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
  // 可选择是否保留"记住我"的数据
  // localStorage.removeItem('rememberedUser')
}
