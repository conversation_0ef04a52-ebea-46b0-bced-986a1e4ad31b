<template>
  <div 
    class="fireworm-menu-item"
    :class="{ 'is-active': isActive }"
    @click="handleClick"
  >
    <div class="fireworm-button-container">
      <div
        class="fireworm-button-content"
        @mouseenter="onMouseEnter"
        @mousemove="onMouseMove"
        @mouseleave="onMouseLeave"
        ref="containerRef"
      >
        <div class="fireworm-dot dot1"></div>
        <div class="fireworm-dot dot2"></div>
        <div class="fireworm-dot dot3"></div>
        <div class="fireworm-dot dot4"></div>
        <div class="fireworm-dot dot5"></div>
        <div class="fireworm-dot dot6"></div>
        <div class="fireworm-dot dot7"></div>
        <div class="fireworm-button-wrapper">
          <div class="menu-item-content">
            <el-icon v-if="icon"><component :is="icon" /></el-icon>
            <span class="menu-item-title">{{ title }}</span>
          </div>
        </div>
      </div>
      <div
        class="fireworm-cursor"
        :style="{
          visibility: showCursor ? 'visible' : 'hidden',
          width: `${cursorSize === 'big' ? cursorBigRadius * 2 : cursorDefaultRadius * 2}px`,
          height: `${cursorSize === 'big' ? cursorBigRadius * 2 : cursorDefaultRadius * 2}px`
        }"
        ref="cursorRef"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: ''
  },
  route: {
    type: String,
    required: true
  }
});

const router = useRouter();
const route = useRoute();

const isActive = computed(() => {
  return route.path === props.route || route.path.startsWith(`${props.route}/`);
});

const handleClick = () => {
  router.push(props.route);
};

// FirewormButton 功能
const containerRef = ref<HTMLDivElement | null>(null);
const cursorRef = ref<HTMLDivElement | null>(null);
const showCursor = ref(false);
const cursorSize = ref('small');
const cursorBigRadius = ref(15);
const cursorDefaultRadius = 5;

// 鼠标进入按钮区域
const onMouseEnter = (event: MouseEvent) => {
  showCursor.value = true;
  updateCursorPosition(event);
};

// 鼠标在按钮区域移动
const onMouseMove = (event: MouseEvent) => {
  updateCursorPosition(event);
  
  // 检查鼠标是否在按钮上方
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect();
    const buttonWrapper = containerRef.value.querySelector('.fireworm-button-wrapper');
    
    if (buttonWrapper) {
      const buttonRect = buttonWrapper.getBoundingClientRect();
      const isOverButton = 
        event.clientX >= buttonRect.left &&
        event.clientX <= buttonRect.right &&
        event.clientY >= buttonRect.top &&
        event.clientY <= buttonRect.bottom;
      
      cursorSize.value = isOverButton ? 'big' : 'small';
    }
  }
};

// 鼠标离开按钮区域
const onMouseLeave = () => {
  showCursor.value = false;
};

// 更新光标位置
const updateCursorPosition = (event: MouseEvent) => {
  if (containerRef.value && cursorRef.value) {
    const rect = containerRef.value.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // cursorRef.value.style.transform = `translate(${x - cursorRef.value.offsetWidth / 2}px, ${y - cursorRef.value.offsetHeight / 2}px)`;
  }
};
</script>

<style scoped>
.fireworm-menu-item {
  margin: 10px 0;
  padding: 0 15px;
}

.fireworm-button-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.fireworm-button-content {
  --primary-color: #1f2937;
  --border-color: #374151;
  --button-height: 50px;
  --active-color: #3b82f6;
  --active-border: #60a5fa;
  --active-glow: #93c5fd;
  
  box-sizing: border-box;
  width: 100%;
  height: var(--button-height);
  cursor: pointer;
  position: relative;
}

.is-active .fireworm-button-content {
  --primary-color: #2563eb;
  --border-color: #60a5fa;
}

.fireworm-dot {
  position: absolute;
  transition: transform calc(var(--speed) / 14) ease;
  width: var(--size);
  height: var(--size);
  transform: translate(var(--starting-x), var(--starting-y));
  z-index: 2;
}

.fireworm-dot::after {
  content: '';
  animation: hoverFirefly var(--speed) infinite,
    hoverFireFlicker calc(var(--speed) / 3) infinite
      calc(var(--speed) / 3);
  animation-play-state: paused;
  display: block;
  border-radius: 100%;
  background: #60a5fa;
  width: 100%;
  height: 100%;
  box-shadow: 0px 0px 6px 0px #3b82f6, 0px 0px 4px 0px #93c5fd inset,
    0px 0px 2px 1px rgba(255, 255, 255, 0.3);
}

.dot1 {
  --speed: 14s;
  --size: 6px;
  --starting-x: 30px;
  --starting-y: 20px;
  top: 3px;
  left: -14px;
  opacity: 0.7;
}

.dot2 {
  --speed: 16s;
  --size: 3px;
  --starting-x: 40px;
  --starting-y: 10px;
  top: 0px;
  left: -4px;
  opacity: 0.7;
}

.dot3 {
  --speed: 20s;
  --size: 4px;
  --starting-x: -10px;
  --starting-y: 20px;
  top: -8px;
  right: 14px;
}

.dot4 {
  --speed: 18s;
  --size: 2px;
  --starting-x: -30px;
  --starting-y: -5px;
  bottom: 4px;
  right: -14px;
  opacity: 0.9;
}

.dot5 {
  --speed: 22s;
  --size: 5px;
  --starting-x: -40px;
  --starting-y: -20px;
  bottom: -6px;
  right: -3px;
}

.dot6 {
  --speed: 15s;
  --size: 4px;
  --starting-x: 12px;
  --starting-y: -18px;
  bottom: -12px;
  left: 30px;
  opacity: 0.7;
}

.dot7 {
  --speed: 19s;
  --size: 3px;
  --starting-x: 6px;
  --starting-y: -20px;
  bottom: -16px;
  left: 44px;
}

.fireworm-button-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  box-sizing: border-box;
  border-radius: 10px;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: var(--primary-color);
  border: 2px solid var(--border-color);
  z-index: 11;
  padding: 0 15px;
  transition: all 0.3s ease;
}

.fireworm-button-wrapper::before,
.fireworm-button-wrapper::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 10px;
  opacity: 0;
  transition: opacity 0.3s;
}

.fireworm-button-wrapper::before {
  box-shadow: 0px 0px 15px 0px #3b82f6;
}

.fireworm-button-wrapper::after {
  box-shadow: 0px 0px 15px 0px #93c5fd inset, 0px 0px 5px 0px rgba(255, 255, 255, 0.3);
}

.fireworm-button-content:hover .fireworm-dot,
.is-active .fireworm-dot {
  transform: translate(0, 0);
}

.fireworm-button-content:hover .fireworm-dot::after,
.is-active .fireworm-dot::after {
  animation-play-state: running;
}

.fireworm-button-content:hover .fireworm-button-wrapper::before,
.fireworm-button-content:hover .fireworm-button-wrapper::after,
.is-active .fireworm-button-wrapper::before,
.is-active .fireworm-button-wrapper::after {
  opacity: 1;
}

.fireworm-cursor {
  position: absolute;
  border-radius: 50%;
  background-color: #3b82f6;
  mix-blend-mode: difference;
  pointer-events: none;
  transition: width 0.4s, height 0.4s;
  transform-origin: 50% 50% 0;
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #d1d5db;
  font-size: 14px;
  transition: all 0.3s ease;
}

.is-active .menu-item-content {
  color: white;
}

.menu-item-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.5px;
  transition: letter-spacing 0.3s ease;
}

.fireworm-button-content:hover .menu-item-title {
  letter-spacing: 1px;
}

@keyframes hoverFirefly {
  0% {
    transform: translate(0, 0);
  }
  12% {
    transform: translate(4px, 2px);
  }
  24% {
    transform: translate(-3px, 4px);
  }
  37% {
    transform: translate(3px, -3px);
  }
  55% {
    transform: translate(-2px, 0);
  }
  74% {
    transform: translate(0, 3px);
  }
  88% {
    transform: translate(-4px, -2px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes hoverFireFlicker {
  0% {
    opacity: 1;
  }
  25% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
  75% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}
</style> 