/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Vue ESM bundle 声明
declare module 'vue/dist/vue.esm-bundler.js' {
  import * as Vue from 'vue'
  export const createApp: typeof Vue.createApp
  export const h: typeof Vue.h
  export const defineComponent: typeof Vue.defineComponent
  export const ref: typeof Vue.ref
  export const reactive: typeof Vue.reactive
  export const computed: typeof Vue.computed
  export const watch: typeof Vue.watch
  export const watchEffect: typeof Vue.watchEffect
  export const provide: typeof Vue.provide
  export const inject: typeof Vue.inject
  export const onMounted: typeof Vue.onMounted
  export const onBeforeMount: typeof Vue.onBeforeMount
  export const onBeforeUpdate: typeof Vue.onBeforeUpdate
  export const onUpdated: typeof Vue.onUpdated
  export const onBeforeUnmount: typeof Vue.onBeforeUnmount
  export const onUnmounted: typeof Vue.onUnmounted
  export const nextTick: typeof Vue.nextTick
  export const toRef: typeof Vue.toRef
  export const toRefs: typeof Vue.toRefs
  export * from 'vue'
} 