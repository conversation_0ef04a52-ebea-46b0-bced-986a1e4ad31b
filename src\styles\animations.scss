// 智慧农业系统 - 动画
// 这个文件包含了系统中使用的所有动画效果

// 淡入效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 从下方淡入
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 从上方淡入
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 从左方淡入
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 从右方淡入
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 缓慢脉冲
@keyframes slowPulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.03);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

// 浮动动画
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

// 旋转动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 缓慢旋转
@keyframes slowRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 摇摆动画
@keyframes sway {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(3deg);
  }
  75% {
    transform: rotate(-3deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

// 背景移动
@keyframes backgroundMove {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

// 植物生长动画
@keyframes plantGrow {
  0% {
    transform: scale(0.95) translateY(5px);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.02) translateY(-2px);
    opacity: 1;
  }
  100% {
    transform: scale(0.95) translateY(5px);
    opacity: 0.8;
  }
}

// 数据流动画
@keyframes dataFlow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

// 呼吸灯效果
@keyframes breathe {
  0% {
    box-shadow: 0 0 10px rgba(0, 255, 170, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 255, 170, 0.6);
  }
  100% {
    box-shadow: 0 0 10px rgba(0, 255, 170, 0.2);
  }
}

// 打字机效果
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

// 数据加载动画
@keyframes dataLoading {
  0% {
    transform: scaleX(0);
    transform-origin: left;
  }
  50% {
    transform: scaleX(1);
    transform-origin: left;
  }
  50.1% {
    transform-origin: right;
  }
  100% {
    transform: scaleX(0);
    transform-origin: right;
  }
}

// 闪烁动画
@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 应用动画的混合器
@mixin apply-animation($name, $duration: 1s, $timing-function: ease, $delay: 0s, $iteration-count: 1, $direction: normal, $fill-mode: forwards) {
  animation-name: $name;
  animation-duration: $duration;
  animation-timing-function: $timing-function;
  animation-delay: $delay;
  animation-iteration-count: $iteration-count;
  animation-direction: $direction;
  animation-fill-mode: $fill-mode;
} 