/**
 * 聊天记录存储清理工具
 * 在浏览器控制台中运行此脚本来清理localStorage
 */

function cleanupChatStorage() {
    console.log('🧹 开始清理聊天记录存储...');
    
    let totalCleaned = 0;
    let totalSize = 0;
    
    // 计算当前使用的存储空间
    for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
            totalSize += localStorage[key].length + key.length;
        }
    }
    
    console.log(`📊 清理前存储大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
    
    // 1. 删除所有聊天消息记录（保留会话列表）
    const keysToDelete = [];
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('ai-chat-messages-')) {
            keysToDelete.push(key);
        }
    }
    
    keysToDelete.forEach(key => {
        const size = localStorage[key] ? localStorage[key].length : 0;
        localStorage.removeItem(key);
        totalCleaned += size;
        console.log(`🗑️ 已删除: ${key} (${(size / 1024).toFixed(1)}KB)`);
    });
    
    // 2. 清理音频相关的Base64数据（如果有）
    const audioKeys = [];
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('audio') || key.includes('tts'))) {
            audioKeys.push(key);
        }
    }
    
    audioKeys.forEach(key => {
        const size = localStorage[key] ? localStorage[key].length : 0;
        localStorage.removeItem(key);
        totalCleaned += size;
        console.log(`🗑️ 已删除音频数据: ${key} (${(size / 1024).toFixed(1)}KB)`);
    });
    
    // 3. 重置会话相关的设置
    localStorage.removeItem('ai-chat-active-session-id');
    localStorage.setItem('ai-chat-sessions', '[]');
    
    // 计算清理后的大小
    let newTotalSize = 0;
    for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
            newTotalSize += localStorage[key].length + key.length;
        }
    }
    
    console.log(`✅ 清理完成!`);
    console.log(`📊 清理前: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
    console.log(`📊 清理后: ${(newTotalSize / 1024 / 1024).toFixed(2)}MB`);
    console.log(`🎉 释放空间: ${((totalSize - newTotalSize) / 1024 / 1024).toFixed(2)}MB`);
    
    alert('聊天记录清理完成！请刷新页面重新开始对话。');
}

// 检查存储使用情况
function checkStorageUsage() {
    let used = 0;
    let chatMessageSize = 0;
    let sessionCount = 0;
    
    for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
            const size = localStorage[key].length + key.length;
            used += size;
            
            if (key.startsWith('ai-chat-messages-')) {
                chatMessageSize += size;
                sessionCount++;
            }
        }
    }
    
    const available = 5 * 1024 * 1024; // 假设5MB限制
    const percentage = (used / available) * 100;
    
    console.log('📊 localStorage使用情况:');
    console.log(`总使用: ${(used / 1024 / 1024).toFixed(2)}MB (${percentage.toFixed(1)}%)`);
    console.log(`聊天记录: ${(chatMessageSize / 1024 / 1024).toFixed(2)}MB`);
    console.log(`会话数量: ${sessionCount}`);
    
    if (percentage > 80) {
        console.warn('⚠️ 存储空间使用率过高，建议清理！');
        console.log('运行 cleanupChatStorage() 来清理存储');
    }
}

console.log('🛠️ 聊天存储清理工具已加载');
console.log('运行 checkStorageUsage() 检查存储使用情况');
console.log('运行 cleanupChatStorage() 清理所有聊天记录'); 