<!-- 
  WaterCupIcon.vue
  水杯图标组件
  自定义组件，用于替代Element Plus中不可直接使用的WaterCup图标
-->
<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 1024 1024"
    :width="size"
    :height="size"
    :style="{ 'vertical-align': 'middle' }"
  >
    <path
      fill="currentColor"
      d="M590.4 320H128v448c0 53 43 96 96 96h384c53 0 96-43 96-96V512c35.3 0 64-28.7 64-64s-28.7-64-64-64h-16.6c10.1-19.7 16.6-41.6 16.6-64 0-35.3-28.7-64-64-64h-50zM512 832H224c-17.7 0-32-14.3-32-32V384h320v416c0 17.7-14.3 32-32 32zm128-512v320c0 17.7-14.3 32-32 32h-32V384h16c26.5 0 48-21.5 48-48zm-448-128h384c17.7 0 32 14.3 32 32 0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32 0-17.7 14.3-32 32-32z"
    ></path>
  </svg>
</template>

<script setup lang="ts">
defineProps({
  /** 图标大小 */
  size: {
    type: [Number, String],
    default: 16
  }
});
</script> 