<!--
设备列表组件
功能：
1. 显示设备列表
2. 设备状态指示
3. 设备选择和聚焦
4. 设备详情查看
-->

<template>
  <div class="device-list-container">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-info">
        <span class="device-count">{{ deviceList.length }} 台设备</span>
        <span class="online-count">{{ onlineCount }} 在线</span>
      </div>
      <div class="header-actions">
        <el-button
          size="small"
          type="primary"
          :icon="Refresh"
          @click="handleRefresh"
          circle
        />
      </div>
    </div>

    <!-- 设备列表 -->
    <div class="device-list" ref="listContainer">
      <div
        v-for="device in deviceList"
        :key="device.id"
        class="device-item"
        :class="{
          'active': device.tagId === activeRobotId,
          'offline': device.status === 'offline'
        }"
        @click="handleDeviceClick(device)"
        @dblclick="handleDeviceDoubleClick(device)"
      >
        <!-- 设备图标 -->
        <div class="device-icon" :class="getDeviceIconClass(device)">
          <el-icon><Guide /></el-icon>
          <div class="status-dot" :class="device.status"></div>
        </div>

        <!-- 设备信息 -->
        <div class="device-info">
          <div class="device-name">{{ device.name }}</div>
          <div class="device-details">
            <span class="device-id">ID: {{ device.tagId }}</span>
            <span class="device-status" :class="device.status">
              {{ getStatusText(device.status) }}
            </span>
          </div>
          <div class="device-position">
            位置: ({{ device.position.x.toFixed(2) }}, {{ device.position.y.toFixed(2) }})
          </div>
        </div>

        <!-- 设备指标 -->
        <div class="device-metrics">
          <!-- 电池电量 -->
          <div class="metric-item">
            <el-progress
              type="circle"
              :percentage="device.battery"
              :width="32"
              :stroke-width="4"
              :color="getBatteryColor(device.battery)"
              :show-text="false"
            />
            <span class="metric-label">{{ device.battery }}%</span>
          </div>

          <!-- 速度指示 -->
          <div class="metric-item">
            <div class="speed-indicator" :class="getSpeedClass(device.speed)">
              <el-icon><Odometer /></el-icon>
            </div>
            <span class="metric-label">{{ device.speed.toFixed(1) }} m/s</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="device-actions">
          <el-button
            size="small"
            type="primary"
            :icon="Location"
            @click.stop="handleFocusDevice(device)"
            circle
          />
          <el-button
            size="small"
            type="info"
            :icon="View"
            @click.stop="handleViewDetails(device)"
            circle
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="deviceList.length === 0" class="empty-state">
        <el-icon class="empty-icon"><Connection /></el-icon>
        <div class="empty-text">暂无设备数据</div>
        <div class="empty-hint">请检查WebSocket连接状态</div>
      </div>
    </div>

    <!-- 设备统计 -->
    <div class="device-stats">
      <div class="stat-item" v-for="stat in deviceStats" :key="stat.label">
        <div class="stat-value" :style="{ color: stat.color }">{{ stat.value }}</div>
        <div class="stat-label">{{ stat.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import {
  Refresh,
  Guide,
  Location,
  View,
  Connection,
  Odometer
} from '@element-plus/icons-vue'
import type { RobotDevice } from '@/composables/useDeviceTracking'

// Props
interface Props {
  deviceList: RobotDevice[]
  activeRobotId: number | null
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'device-click', device: RobotDevice): void
  (e: 'device-focus', device: RobotDevice): void

  (e: 'refresh'): void
}

const emit = defineEmits<Emits>()

// 组件状态
const listContainer = ref<HTMLElement>()

// 计算属性
const onlineCount = computed(() => {
  return props.deviceList.filter(device => device.status !== 'offline').length
})

const deviceStats = computed(() => [
  {
    label: '活动中',
    value: props.deviceList.filter(d => d.status === 'active').length,
    color: '#10b981'
  },
  {
    label: '待命中',
    value: props.deviceList.filter(d => d.status === 'idle').length,
    color: '#3b82f6'
  },
  {
    label: '警告',
    value: props.deviceList.filter(d => d.status === 'warning').length,
    color: '#f59e0b'
  },
  {
    label: '故障',
    value: props.deviceList.filter(d => d.status === 'error').length,
    color: '#ef4444'
  }
])

/**
 * 获取状态文本
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: '活动中',
    idle: '待命中',
    warning: '警告',
    error: '故障',
    offline: '离线'
  }
  return statusMap[status] || '未知'
}

/**
 * 获取设备图标类名
 */
const getDeviceIconClass = (device: RobotDevice): string => {
  return `robot ${device.status}`
}

/**
 * 获取电池颜色
 */
const getBatteryColor = (battery: number): string => {
  if (battery > 70) return '#10b981'
  if (battery > 30) return '#f59e0b'
  return '#ef4444'
}

/**
 * 获取速度指示类名
 */
const getSpeedClass = (speed: number): string => {
  if (speed > 1.0) return 'high-speed'
  if (speed > 0.1) return 'medium-speed'
  return 'low-speed'
}

/**
 * 处理设备点击
 */
const handleDeviceClick = (device: RobotDevice) => {
  emit('device-click', device)
}

/**
 * 处理设备双击
 */
const handleDeviceDoubleClick = (device: RobotDevice) => {
  emit('device-focus', device)
}

/**
 * 处理聚焦设备
 */
const handleFocusDevice = (device: RobotDevice) => {
  emit('device-focus', device)

  ElNotification({
    title: '设备聚焦',
    message: `已聚焦到设备: ${device.name}`,
    type: 'success',
    duration: 2000
  })
}

/**
 * 处理查看详情
 */
const handleViewDetails = (device: RobotDevice) => {
  emit('device-focus', device)
}

/**
 * 处理刷新
 */
const handleRefresh = () => {
  emit('refresh')

  ElMessage({
    message: '设备列表已刷新',
    type: 'success',
    duration: 2000
  })
}

/**
 * 滚动到指定设备
 */
const scrollToDevice = (deviceId: string) => {
  if (!listContainer.value) return

  const deviceElement = listContainer.value.querySelector(`[data-device-id="${deviceId}"]`)
  if (deviceElement) {
    deviceElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

// 暴露给父组件的方法
defineExpose({
  scrollToDevice
})
</script>

<style lang="scss" scoped>
.device-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: rgba(31, 41, 55, 0.6);
    border-radius: 12px;
    margin-bottom: 16px;
    border: 1px solid rgba(75, 85, 99, 0.3);

    .header-info {
      display: flex;
      gap: 14px;
      align-items: center;

      .device-count {
        font-size: 15px;
        font-weight: 700;
        color: #f3f4f6;
      }

      .online-count {
        font-size: 12px;
        color: #10b981;
        background: rgba(16, 185, 129, 0.15);
        padding: 4px 10px;
        border-radius: 14px;
        border: 1px solid rgba(16, 185, 129, 0.3);
        font-weight: 600;
      }
    }
  }

  .device-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 4px;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(59, 130, 246, 0.3);
      border-radius: 3px;
    }

    .device-item {
      display: flex;
      align-items: center;
      padding: 16px;
      margin-bottom: 10px;
      background: rgba(31, 41, 55, 0.8);
      border-radius: 12px;
      border: 1px solid rgba(75, 85, 99, 0.2);
      position: relative;

      &.active {
        background: rgba(59, 130, 246, 0.2);
        border-color: #3b82f6;
        border-left: 4px solid #3b82f6;
        padding-left: 12px;
      }

      &.offline {
        opacity: 0.6;

        .device-icon {
          filter: grayscale(100%);
        }
      }
    }

    .device-icon {
      position: relative;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 20px;
      transition: all 0.3s ease;

      &.robot {
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;

        &.active {
          // 移除了浮动动画以获得更稳定的视觉效果
        }
      }

      .status-dot {
        position: absolute;
        top: 2px;
        right: 2px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: 2px solid rgba(31, 41, 55, 0.8);

        &.active { background: #10b981; }
        &.idle { background: #3b82f6; }
        &.warning { background: #f59e0b; }
        &.error { background: #ef4444; }
        &.offline { background: #6b7280; }
      }
    }

    .device-info {
      flex: 1;
      min-width: 0;

      .device-name {
        font-size: 14px;
        font-weight: 600;
        color: #f3f4f6;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .device-details {
        display: flex;
        gap: 8px;
        margin-bottom: 2px;

        .device-id {
          font-size: 11px;
          color: #9ca3af;
        }

        .device-status {
          font-size: 11px;
          padding: 1px 6px;
          border-radius: 8px;

          &.active { background: rgba(16, 185, 129, 0.2); color: #10b981; }
          &.idle { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
          &.warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
          &.error { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
          &.offline { background: rgba(107, 114, 128, 0.2); color: #6b7280; }
        }
      }

      .device-position {
        font-size: 11px;
        color: #6b7280;
      }
    }

    .device-metrics {
      display: flex;
      gap: 12px;
      margin-right: 12px;

      .metric-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .metric-label {
          font-size: 10px;
          color: #9ca3af;
        }

        .speed-indicator {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;

          &.low-speed {
            background: rgba(107, 114, 128, 0.2);
            color: #6b7280;
          }

          &.medium-speed {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
          }

          &.high-speed {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
          }
        }
      }
    }

    .device-actions {
      display: flex;
      gap: 4px;
      opacity: 1;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #6b7280;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 16px;
        margin-bottom: 8px;
      }

      .empty-hint {
        font-size: 12px;
        opacity: 0.7;
      }
    }
  }

  .device-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-top: 12px;

    .stat-item {
      text-align: center;
      padding: 8px;
      background: rgba(31, 41, 55, 0.4);
      border-radius: 6px;
      border: 1px solid rgba(75, 85, 99, 0.3);

      .stat-value {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 2px;
      }

      .stat-label {
        font-size: 10px;
        color: #9ca3af;
      }
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(0px); }
}
</style>
