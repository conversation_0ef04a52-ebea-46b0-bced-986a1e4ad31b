/**
 * UI相关配置
 * 存放界面显示、交互相关的配置参数
 */

import envConfig from './env';

// 分页配置
export const PAGINATION_CONFIG = {
  // 默认页面大小
  DEFAULT_PAGE_SIZE: envConfig.defaultPageSize,
  // 页面大小选项
  PAGE_SIZE_OPTIONS: envConfig.pageSizeOptions,
  // 分页布局
  LAYOUT: 'total, sizes, prev, pager, next, jumper'
} as const;

// 表格配置
export const TABLE_CONFIG = {
  // 默认行高
  ROW_HEIGHT: 48,
  // 表头高度
  HEADER_HEIGHT: 56,
  // 最大高度
  MAX_HEIGHT: 600,
  // 是否显示边框
  BORDER: true,
  // 是否显示斑马纹
  STRIPE: true
} as const;

// 图表配置
export const CHART_CONFIG = {
  // 动画持续时间
  ANIMATION_DURATION: envConfig.chartAnimationDuration,
  // 默认主题
  THEME: 'dark',
  // 图表容器最小高度
  MIN_HEIGHT: 300,
  // 图表容器默认高度
  DEFAULT_HEIGHT: 400,
  // 饼图配置
  PIE: {
    RADIUS: ['40%', '70%'],
    CENTER: ['40%', '50%']
  },
  // 柱状图配置
  BAR: {
    BAR_WIDTH: '60%',
    BAR_MAX_WIDTH: 50
  },
  // 折线图配置
  LINE: {
    SMOOTH: true,
    SYMBOL_SIZE: 6
  }
} as const;

// 定时器配置
export const TIMER_CONFIG = {
  // 自动刷新间隔（秒）
  AUTO_REFRESH_INTERVAL: envConfig.autoRefreshInterval,
  // 状态更新间隔（毫秒）
  STATUS_UPDATE_INTERVAL: 5000,
  // 系统状态更新间隔（毫秒）
  SYSTEM_STATUS_INTERVAL: 3000,
  // 心跳检测间隔（毫秒）
  HEARTBEAT_INTERVAL: envConfig.websocketHeartbeatInterval,
  // 命令发送间隔（毫秒）
  COMMAND_INTERVAL: 100
} as const;

// 表单配置
export const FORM_CONFIG = {
  // 标签宽度
  LABEL_WIDTH: '120px',
  // 表单项间距
  ITEM_MARGIN: '20px',
  // 输入框默认大小
  INPUT_SIZE: 'default' as const,
  // 按钮默认大小
  BUTTON_SIZE: 'default' as const,
  // 表单验证触发方式
  VALIDATE_TRIGGER: 'blur' as const
} as const;

// 弹窗配置
export const DIALOG_CONFIG = {
  // 默认宽度
  DEFAULT_WIDTH: '600px',
  // 大弹窗宽度
  LARGE_WIDTH: '800px',
  // 小弹窗宽度
  SMALL_WIDTH: '400px',
  // 是否可以通过点击遮罩关闭
  CLOSE_ON_CLICK_MODAL: false,
  // 是否可以通过按下ESC关闭
  CLOSE_ON_PRESS_ESCAPE: true,
  // 是否显示关闭按钮
  SHOW_CLOSE: true
} as const;

// 消息提示配置
export const MESSAGE_CONFIG = {
  // 显示时间（毫秒）
  DURATION: 3000,
  // 是否显示关闭按钮
  SHOW_CLOSE: true,
  // 是否居中显示
  CENTER: false,
  // 偏移量
  OFFSET: 20
} as const;

// 加载配置
export const LOADING_CONFIG = {
  // 默认加载文本
  DEFAULT_TEXT: '加载中...',
  // 加载动画大小
  SPINNER_SIZE: 'default' as const,
  // 背景颜色
  BACKGROUND: 'rgba(0, 0, 0, 0.7)'
} as const;

// 滚动配置
export const SCROLL_CONFIG = {
  // 滚动行为
  BEHAVIOR: 'smooth' as const,
  // 滚动到顶部的阈值
  SCROLL_TOP_THRESHOLD: 100,
  // 虚拟滚动项目高度
  VIRTUAL_ITEM_HEIGHT: 50
} as const;

// 动画配置
export const ANIMATION_CONFIG = {
  // 过渡持续时间
  TRANSITION_DURATION: '0.3s',
  // 缓动函数
  TIMING_FUNCTION: 'ease-in-out',
  // 淡入淡出持续时间
  FADE_DURATION: 300,
  // 滑动动画持续时间
  SLIDE_DURATION: 300
} as const;

// 布局配置
export const LAYOUT_CONFIG = {
  // 侧边栏宽度
  SIDEBAR_WIDTH: '240px',
  // 侧边栏折叠宽度
  SIDEBAR_COLLAPSED_WIDTH: '64px',
  // 头部高度
  HEADER_HEIGHT: '60px',
  // 底部高度
  FOOTER_HEIGHT: '50px',
  // 内容区域内边距
  CONTENT_PADDING: '20px'
} as const;

// 颜色主题配置
export const THEME_CONFIG = {
  // 主色调
  PRIMARY_COLOR: '#1890ff',
  // 成功色
  SUCCESS_COLOR: '#00ffaa',
  // 警告色
  WARNING_COLOR: '#ffcc00',
  // 危险色
  DANGER_COLOR: '#ff3b5c',
  // 信息色
  INFO_COLOR: '#909399',
  // 文本色
  TEXT_COLOR: '#303133',
  // 边框色
  BORDER_COLOR: '#dcdfe6',
  // 背景色
  BACKGROUND_COLOR: '#f5f7fa'
} as const;

// 字体配置
export const FONT_CONFIG = {
  // 基础字体大小
  BASE_SIZE: '14px',
  // 小字体大小
  SMALL_SIZE: '12px',
  // 大字体大小
  LARGE_SIZE: '16px',
  // 标题字体大小
  TITLE_SIZE: '18px',
  // 字体家族
  FAMILY: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
} as const;

// 间距配置
export const SPACING_CONFIG = {
  // 基础间距
  BASE: '8px',
  // 小间距
  SMALL: '4px',
  // 中等间距
  MEDIUM: '12px',
  // 大间距
  LARGE: '16px',
  // 超大间距
  EXTRA_LARGE: '24px'
} as const;

// 阴影配置
export const SHADOW_CONFIG = {
  // 基础阴影
  BASE: '0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04)',
  // 轻微阴影
  LIGHT: '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
  // 深度阴影
  DARK: '0 4px 16px rgba(0, 0, 0, 0.12)'
} as const;

// 边框圆角配置
export const BORDER_RADIUS_CONFIG = {
  // 小圆角
  SMALL: '2px',
  // 基础圆角
  BASE: '4px',
  // 大圆角
  LARGE: '8px',
  // 圆形
  CIRCLE: '50%'
} as const;
