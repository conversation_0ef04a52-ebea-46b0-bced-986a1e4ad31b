/**
 * 预警服务 - 负责预警信息管理
 */
import type { AlertInfo } from '../types';

export class AlertService {
  // 预警信息列表
  private alerts: AlertInfo[] = [
    {
      id: '1',
      level: 'high',
      title: '湿度过低警告',
      message: '西北区域土壤湿度低于阈值',
      deviceId: 'device-002',
      timestamp: Date.now() - 1000 * 60 * 10, // 10分钟前
      resolved: false
    },
    {
      id: '2',
      level: 'medium',
      title: '设备电量提醒',
      message: '无人机02电量低于20%',
      deviceId: 'device-005',
      timestamp: Date.now() - 1000 * 60 * 25, // 25分钟前
      resolved: false
    },
    {
      id: '3',
      level: 'high',
      title: '病虫害检测',
      message: '东南区域检测到疑似稻飞虱',
      deviceId: 'device-003',
      timestamp: Date.now() - 1000 * 60 * 60, // 1小时前
      resolved: false
    }
  ];
  
  // 预警类型模板
  private alertTypes: Partial<AlertInfo>[] = [
    { level: 'low', title: '设备状态提醒', message: '无人机完成一次巡检任务' },
    { level: 'medium', title: '温度异常警告', message: '西南区域温度升高超过阈值' },
    { level: 'low', title: '灌溉系统通知', message: '东区自动灌溉系统已启动' },
    { level: 'high', title: '安全警报', message: '检测到农田边界异常活动' },
    { level: 'medium', title: '土壤状况警告', message: '北区土壤pH值异常' }
  ];
  
  // 随机设备ID
  private deviceIds = ['device-001', 'device-002', 'device-003', 'device-004', 'device-005', 'device-007'];
  
  // 获取预警信息列表
  public getAlerts(): AlertInfo[] {
    return [...this.alerts];
  }
  
  // 添加预警信息
  public addAlert(alert: AlertInfo): void {
    this.alerts.unshift(alert);
    
    // 保持最多显示10条警报
    if (this.alerts.length > 10) {
      this.alerts.pop();
    }
  }
  
  // 生成随机预警信息
  public generateRandomAlert(): AlertInfo | null {
    // 随机生成新的预警信息
    if (Math.random() > 0.7) {
      const alertTemplate = this.alertTypes[Math.floor(Math.random() * this.alertTypes.length)];
      const deviceId = this.deviceIds[Math.floor(Math.random() * this.deviceIds.length)];
      
      const newAlert: AlertInfo = {
        id: Date.now().toString(),
        level: alertTemplate.level as 'high' | 'medium' | 'low',
        title: alertTemplate.title || '',
        message: alertTemplate.message || '',
        deviceId,
        timestamp: Date.now(),
        resolved: false
      };
      
      return newAlert;
    }
    
    return null;
  }
  
  // 更新预警信息（模拟）
  public updateAlerts(): void {
    // 随机生成新警报
    const newAlert = this.generateRandomAlert();
    if (newAlert) {
      this.addAlert(newAlert);
    }
  }
  
  // 清除所有预警
  public clearAlerts(): void {
    this.alerts = [];
  }
  
  // 清除指定预警
  public removeAlert(index: number): void {
    if (index >= 0 && index < this.alerts.length) {
      this.alerts.splice(index, 1);
    }
  }
}

// 导出单例实例
export const alertService = new AlertService(); 