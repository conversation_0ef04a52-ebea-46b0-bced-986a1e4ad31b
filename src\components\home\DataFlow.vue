<template>
  <div class="data-flow" :class="[direction, { 'dark-mode': darkMode }]" :style="containerStyle">
    <canvas ref="flowCanvas" class="flow-canvas"></canvas>
    <div class="data-overlay" v-if="showOverlay"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  /** 数据流方向，默认为 vertical */
  direction?: 'vertical' | 'horizontal'
  /** 数据流颜色，默认为 #00ffaa */
  flowColor?: string
  /** 数据流速度，值越大越快，默认为 2 */
  flowSpeed?: number
  /** 数据流密度，值越大越密集，默认为 50 */
  flowDensity?: number
  /** 是否为暗色模式，默认为 true */
  darkMode?: boolean
  /** 是否显示叠加效果，默认为 true */
  showOverlay?: boolean
  /** 数据流宽度，默认为 100% */
  width?: string
  /** 数据流高度，默认为 100% */
  height?: string
  /** 数据流透明度，默认为 0.7 */
  opacity?: number
  /** 是否启用闪烁效果，默认为 true */
  enableFlicker?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'vertical',
  flowColor: '#00ffaa',
  flowSpeed: 2,
  flowDensity: 50,
  darkMode: true,
  showOverlay: true,
  width: '100%',
  height: '100%',
  opacity: 0.7,
  enableFlicker: true
})

// 容器样式
const containerStyle = computed(() => {
  return {
    width: props.width,
    height: props.height,
    opacity: props.opacity
  }
})

// Canvas引用
const flowCanvas = ref<HTMLCanvasElement | null>(null)
let ctx: CanvasRenderingContext2D | null = null
let animationFrameId: number | null = null

// 数据流粒子
interface DataParticle {
  x: number
  y: number
  speed: number
  value: string
  size: number
  opacity: number
  flickerRate?: number
}

// 数据粒子数组
let particles: DataParticle[] = []

// 生成随机数据值（看起来像二进制、十六进制或代码片段）
const generateRandomValue = (): string => {
  const types = ['binary', 'hex', 'ascii', 'symbol']
  const type = types[Math.floor(Math.random() * types.length)]
  
  switch (type) {
    case 'binary':
      return Math.random() > 0.5 ? '1' : '0'
    case 'hex':
      return Math.floor(Math.random() * 16).toString(16).toUpperCase()
    case 'ascii':
      // 返回可见ASCII字符
      return String.fromCharCode(Math.floor(Math.random() * 94) + 33)
    case 'symbol':
      const symbols = ['>', '<', '/', '=', '+', '-', '*', '&', '%', '$', '#', '@', '!']
      return symbols[Math.floor(Math.random() * symbols.length)]
    default:
      return '0'
  }
}

// 初始化粒子
const initParticles = () => {
  if (!flowCanvas.value) return
  
  const canvas = flowCanvas.value
  const { width, height } = canvas
  
  particles = []
  
  // 根据方向和密度创建粒子
  const count = Math.floor((props.direction === 'vertical' ? width : height) * props.flowDensity / 100)
  
  for (let i = 0; i < count; i++) {
    let particle: DataParticle
    
    if (props.direction === 'vertical') {
      particle = {
        x: Math.random() * width,
        y: Math.random() * height,
        speed: (Math.random() * 2 + 1) * props.flowSpeed,
        value: generateRandomValue(),
        size: Math.floor(Math.random() * 6) + 8, // 8-14px
        opacity: Math.random() * 0.5 + 0.3 // 0.3-0.8
      }
    } else {
      particle = {
        x: Math.random() * width,
        y: Math.random() * height,
        speed: (Math.random() * 2 + 1) * props.flowSpeed,
        value: generateRandomValue(),
        size: Math.floor(Math.random() * 6) + 8, // 8-14px
        opacity: Math.random() * 0.5 + 0.3 // 0.3-0.8
      }
    }
    
    // 添加闪烁效果
    if (props.enableFlicker && Math.random() > 0.7) {
      particle.flickerRate = Math.random() * 0.1 + 0.05
    }
    
    particles.push(particle)
  }
}

// 更新粒子位置
const updateParticles = () => {
  if (!flowCanvas.value) return
  
  const canvas = flowCanvas.value
  const { width, height } = canvas
  
  particles.forEach(particle => {
    if (props.direction === 'vertical') {
      // 向下移动
      particle.y += particle.speed
      
      // 如果超出底部，重置到顶部
      if (particle.y > height) {
        particle.y = 0
        particle.x = Math.random() * width
        particle.value = generateRandomValue()
      }
    } else {
      // 向右移动
      particle.x += particle.speed
      
      // 如果超出右侧，重置到左侧
      if (particle.x > width) {
        particle.x = 0
        particle.y = Math.random() * height
        particle.value = generateRandomValue()
      }
    }
    
    // 随机更改值
    if (Math.random() > 0.95) {
      particle.value = generateRandomValue()
    }
    
    // 闪烁效果
    if (props.enableFlicker && particle.flickerRate) {
      particle.opacity = 0.3 + Math.abs(Math.sin(Date.now() * particle.flickerRate) * 0.5)
    }
  })
}

// 绘制粒子
const drawParticles = () => {
  if (!ctx || !flowCanvas.value) return
  
  const canvas = flowCanvas.value
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 设置文本样式
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  
  particles.forEach(particle => {
    // 设置颜色和透明度
    ctx.fillStyle = props.flowColor
    ctx.globalAlpha = particle.opacity
    ctx.font = `${particle.size}px monospace`
    
    // 绘制文本
    ctx.fillText(particle.value, particle.x, particle.y)
  })
  
  // 重置透明度
  ctx.globalAlpha = 1.0
}

// 调整canvas大小
const resizeCanvas = () => {
  if (!flowCanvas.value) return
  
  const canvas = flowCanvas.value
  const container = canvas.parentElement
  
  if (container) {
    canvas.width = container.clientWidth
    canvas.height = container.clientHeight
    
    // 重新初始化粒子
    initParticles()
  }
}

// 动画循环
const animate = () => {
  updateParticles()
  drawParticles()
  animationFrameId = requestAnimationFrame(animate)
}

// 监听属性变化
watch([
  () => props.flowColor,
  () => props.flowSpeed,
  () => props.flowDensity,
  () => props.direction,
  () => props.enableFlicker
], () => {
  // 重新初始化粒子
  initParticles()
})

onMounted(() => {
  if (flowCanvas.value) {
    ctx = flowCanvas.value.getContext('2d')
    
    // 设置canvas尺寸
    resizeCanvas()
    
    // 开始动画
    animate()
    
    // 监听窗口大小变化
    window.addEventListener('resize', resizeCanvas)
  }
})

onUnmounted(() => {
  // 清理资源
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId)
  }
  
  window.removeEventListener('resize', resizeCanvas)
})
</script>

<style lang="scss" scoped>
.data-flow {
  position: relative;
  overflow: hidden;
  
  .flow-canvas {
    width: 100%;
    height: 100%;
    display: block;
  }
  
  .data-overlay {
    position: absolute;
    inset: 0;
    pointer-events: none;
  }
  
  // 垂直方向的叠加效果
  &.vertical .data-overlay {
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0) 10%,
      rgba(0, 0, 0, 0) 90%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
  
  // 水平方向的叠加效果
  &.horizontal .data-overlay {
    background: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0) 10%,
      rgba(0, 0, 0, 0) 90%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
  
  // 暗色模式
  &.dark-mode {
    background-color: rgba(0, 10, 30, 0.7);
    
    &.vertical .data-overlay {
      background: linear-gradient(
        to bottom,
        rgba(0, 10, 30, 0.9) 0%,
        rgba(0, 10, 30, 0) 10%,
        rgba(0, 10, 30, 0) 90%,
        rgba(0, 10, 30, 0.9) 100%
      );
    }
    
    &.horizontal .data-overlay {
      background: linear-gradient(
        to right,
        rgba(0, 10, 30, 0.9) 0%,
        rgba(0, 10, 30, 0) 10%,
        rgba(0, 10, 30, 0) 90%,
        rgba(0, 10, 30, 0.9) 100%
      );
    }
  }
}
</style> 