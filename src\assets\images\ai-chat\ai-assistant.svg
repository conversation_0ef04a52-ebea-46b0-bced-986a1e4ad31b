<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="assistantGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00e676" />
      <stop offset="60%" stop-color="#00c853" />
      <stop offset="100%" stop-color="#009624" />
    </linearGradient>
    
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="100" cy="100" r="90" fill="#f0f9f4" />
  <circle cx="100" cy="100" r="70" fill="none" stroke="url(#assistantGradient)" stroke-width="2" stroke-dasharray="4,2" />
  
  <!-- 机器人头部 -->
  <rect x="70" y="60" width="60" height="50" rx="10" fill="url(#assistantGradient)" filter="url(#glow)" />
  
  <!-- 眼睛 -->
  <circle cx="85" cy="80" r="5" fill="white" />
  <circle cx="115" cy="80" r="5" fill="white" />
  <circle cx="85" cy="80" r="2" fill="#333" />
  <circle cx="115" cy="80" r="2" fill="#333" />
  
  <!-- 嘴巴 -->
  <path d="M85,95 Q100,105 115,95" fill="none" stroke="white" stroke-width="2" />
  
  <!-- 天线 -->
  <line x1="85" y1="60" x2="80" y2="45" stroke="url(#assistantGradient)" stroke-width="2" />
  <line x1="115" y1="60" x2="120" y2="45" stroke="url(#assistantGradient)" stroke-width="2" />
  <circle cx="80" cy="45" r="3" fill="url(#assistantGradient)" />
  <circle cx="120" cy="45" r="3" fill="url(#assistantGradient)" />
  
  <!-- 身体 -->
  <path d="M75,110 L70,140 L130,140 L125,110 Z" fill="url(#assistantGradient)" filter="url(#glow)" />
  
  <!-- 手臂 -->
  <path d="M75,115 L55,125 L60,135 L75,125 Z" fill="url(#assistantGradient)" />
  <path d="M125,115 L145,125 L140,135 L125,125 Z" fill="url(#assistantGradient)" />
  
  <!-- 数据流 -->
  <path d="M60,135 C40,120 40,90 60,75" fill="none" stroke="url(#assistantGradient)" stroke-width="1" stroke-dasharray="3,2" />
  <path d="M140,135 C160,120 160,90 140,75" fill="none" stroke="url(#assistantGradient)" stroke-width="1" stroke-dasharray="3,2" />
  
  <!-- 叶子元素 -->
  <path d="M100,140 L100,155 C85,155 75,165 75,175 C85,175 100,165 100,155" fill="url(#assistantGradient)" />
  <path d="M100,140 L100,155 C115,155 125,165 125,175 C115,175 100,165 100,155" fill="url(#assistantGradient)" />
  
  <!-- 科技元素 -->
  <circle cx="100" cy="100" r="90" fill="none" stroke="url(#assistantGradient)" stroke-width="1" />
  <circle cx="100" cy="100" r="95" fill="none" stroke="url(#assistantGradient)" stroke-width="0.5" stroke-dasharray="2,4" />
  
  <!-- 数据点 -->
  <circle cx="40" cy="100" r="2" fill="#00e676" />
  <circle cx="160" cy="100" r="2" fill="#00e676" />
  <circle cx="100" cy="40" r="2" fill="#00e676" />
  <circle cx="100" cy="160" r="2" fill="#00e676" />
</svg> 