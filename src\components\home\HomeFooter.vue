<template>
  <footer class="home-footer">
    <div class="footer-content">
      <!-- 数据可视化装饰 -->
      <div class="data-visualization">
        <div class="data-bar" v-for="n in 10" :key="n"></div>
      </div>
      
      <!-- 版权信息 -->
      <div class="copyright">
        <span class="year">© {{ currentYear }}</span>
        <span class="divider"></span>
        <span class="company">智慧农业病虫防治系统</span>
        <span class="rights">版权所有</span>
      </div>
      
      <!-- 底部链接 -->
      <div class="footer-links" v-if="showLinks">
        <a href="#" class="footer-link" v-for="link in links" :key="link.name" @click.prevent="handleLinkClick(link)">
          {{ link.name }}
        </a>
      </div>
    </div>
    
    <!-- 底部装饰 -->
    <div class="footer-decoration">
      <div class="tech-line"></div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

interface Link {
  name: string
  action: string
}

interface Props {
  showLinks?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showLinks: true
})

// 当前年份
const currentYear = computed(() => new Date().getFullYear())

// 底部链接
const links = ref<Link[]>([
  { name: '系统说明', action: 'about' },
  { name: '联系我们', action: 'contact' },
  { name: '帮助中心', action: 'help' },
  { name: '版本信息', action: 'version' }
])

// 链接点击处理
const handleLinkClick = (link: Link) => {
  ElMessage.info(`${link.name}功能开发中`)
}
</script>

<style lang="scss" scoped>
@use "sass:math";

.home-footer {
  position: relative;
  min-height: 60px;
  width: 100%;
  background: rgba(0, 16, 36, 0.9);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  z-index: 100;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(0, 255, 170, 0.5) 20%, 
      rgba(24, 144, 255, 0.5) 50%, 
      rgba(0, 255, 170, 0.5) 80%, 
      transparent 100%);
  }
  
  .footer-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 15px 30px;
    gap: 10px;
    position: relative;
    z-index: 2;
  }
  
  // 数据可视化装饰
  .data-visualization {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 0 20px;
    opacity: 0.2;
    pointer-events: none;
    
    .data-bar {
      width: 3px;
      height: 100%;
      background-color: #00ffaa;
      animation: dataBarHeight 2s infinite;
      transform-origin: bottom;
      border-radius: 3px 3px 0 0;
      
      @for $i from 1 through 10 {
        &:nth-child(#{$i}) {
          animation-delay: #{$i * 0.2}s;
          height: #{math.random(20) + 5}px;
        }
      }
    }
  }
  
  // 版权信息
  .copyright {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    letter-spacing: 0.5px;
    
    .year {
      color: rgba(0, 255, 170, 0.8);
    }
    
    .divider {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: rgba(0, 255, 170, 0.5);
    }
    
    .company {
      color: rgba(255, 255, 255, 0.8);
      font-weight: 600;
      background: linear-gradient(90deg, #00ffaa 0%, #1890ff 100%);
      -webkit-background-clip: text;
      color: transparent;
    }
  }
  
  // 底部链接
  .footer-links {
    display: flex;
    gap: 20px;
    margin-top: 5px;
    
    .footer-link {
      color: rgba(255, 255, 255, 0.6);
      text-decoration: none;
      position: relative;
      transition: all 0.3s ease;
      font-size: 13px;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 50%;
        width: 0;
        height: 1px;
        background: linear-gradient(90deg, #00ffaa, #1890ff);
        transition: all 0.3s ease;
        transform: translateX(-50%);
      }
      
      &:hover {
        color: #fff;
        
        &::after {
          width: 100%;
        }
      }
    }
  }
  
  // 底部装饰
  .footer-decoration {
    position: absolute;
    inset: 0;
    pointer-events: none;
    overflow: hidden;
    
    .tech-line {
      position: absolute;
      top: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(0, 255, 170, 0.5), transparent);
      animation: techLineScan 8s linear infinite;
    }
  }
}

// 数据条动画
@keyframes dataBarHeight {
  0%, 100% {
    transform: scaleY(0.5);
  }
  
  50% {
    transform: scaleY(1);
  }
}

// 技术线扫描动画
@keyframes techLineScan {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home-footer {
    .footer-content {
      padding: 15px;
    }
    
    .copyright {
      flex-direction: column;
      gap: 3px;
      text-align: center;
      
      .divider {
        display: none;
      }
    }
    
    .footer-links {
      flex-wrap: wrap;
      justify-content: center;
      gap: 15px 20px;
    }
  }
}
</style> 