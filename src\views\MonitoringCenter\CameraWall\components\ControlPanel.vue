<template>
  <div class="control-panel" :class="{ 'collapsed': isCollapsed }">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="panel-title">
        <i class="el-icon-s-operation"></i>
        控制面板
        <div class="panel-badge">
          <span>{{ totalCameras }}</span>个摄像头
        </div>
      </div>
    </div>

    <!-- 折叠/展开按钮 -->
    <div class="panel-toggle" @click="toggleCollapse" :title="isCollapsed ? '展开控制面板' : '折叠控制面板'">
      <i class="el-icon-arrow-up"></i>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content">
      <!-- 布局切换 -->
      <div class="control-group">
        <div class="group-title">
          <i class="el-icon-s-grid"></i>
          布局切换
        </div>
        <div class="layout-buttons">
          <div class="layout-button" :class="{ active: layout === '1x1' }" @click="setLayout('1x1')">
            <div class="layout-icon layout-1-1"></div>
            1 x 1
          </div>
          <div class="layout-button" :class="{ active: layout === '2x2' }" @click="setLayout('2x2')">
            <div class="layout-icon layout-2-2"></div>
            2 x 2
          </div>
          <div class="layout-button" :class="{ active: layout === '3x3' }" @click="setLayout('3x3')">
            <div class="layout-icon layout-3-3"></div>
            3 x 3
          </div>
          <div class="layout-button" :class="{ active: layout === '4x4' }" @click="setLayout('4x4')">
            <div class="layout-icon layout-4-4"></div>
            4 x 4
          </div>
        </div>
      </div>

      <!-- 轮播控制 -->
      <div class="control-group">
        <div class="group-title">
          <i class="el-icon-video-play"></i>
          轮播控制
        </div>
        <div class="carousel-toggle">
          <span class="carousel-toggle-label">轮播模式</span>
          <el-switch v-model="carouselEnabled" @change="toggleCarousel"></el-switch>
        </div>

        <transition name="fade">
          <div class="carousel-options" v-if="carouselEnabled">
            <div class="carousel-option">
              <span class="carousel-option-label">轮播间隔 (秒)</span>
              <el-slider v-model="carouselInterval" :min="5" :max="60" :step="5"
                @change="setCarouselInterval"></el-slider>
            </div>
            <div class="carousel-option">
              <span class="carousel-option-label">轮播顺序</span>
              <el-radio-group v-model="carouselOrder" @change="setCarouselOrder">
                <el-radio label="sequential">顺序播放</el-radio>
                <el-radio label="random">随机播放</el-radio>
              </el-radio-group>
            </div>
          </div>
        </transition>
      </div>

      <!-- 缩放控制 -->
      <div class="control-group">
        <div class="group-title">
          <i class="el-icon-zoom-in"></i>
          缩放控制
        </div>
        <div class="zoom-slider">
          <i class="el-icon-zoom-out"></i>
          <el-slider v-model="zoomLevel" :min="1" :max="5" :step="0.5" @change="setZoom"></el-slider>
          <i class="el-icon-zoom-in"></i>
        </div>
        <div class="zoom-buttons">
          <div class="zoom-button" @click="zoomOut" :disabled="zoomLevel <= 1">
            <i class="el-icon-minus"></i>
            缩小
          </div>
          <div class="zoom-button" @click="resetZoom">
            <i class="el-icon-refresh-right"></i>
            重置
          </div>
          <div class="zoom-button" @click="zoomIn" :disabled="zoomLevel >= 5">
            <i class="el-icon-plus"></i>
            放大
          </div>
        </div>
      </div>

      <!-- 状态筛选 -->
      <div class="control-group">
        <div class="group-title">
          <i class="el-icon-s-data"></i>
          状态筛选
        </div>
        <div class="status-filters">
          <div v-for="status in statuses" :key="status.value" class="status-filter"
            :class="{ active: selectedStatuses.includes(status.value), [status.value]: selectedStatuses.includes(status.value) }"
            @click="toggleStatus(status.value)">
            <div class="status-dot" :class="status.value"></div>
            {{ status.label }}
          </div>
        </div>
      </div>

      <!-- 摄像头统计 -->
      <div class="control-group">
        <div class="group-title">
          <i class="el-icon-data-analysis"></i>
          摄像头统计
        </div>
        <div class="camera-stats">
          <div class="stat-item">
            <div class="stat-value normal">{{ normalCount }}</div>
            <div class="stat-label">正常</div>
          </div>
          <div class="stat-item">
            <div class="stat-value warning">{{ warningCount }}</div>
            <div class="stat-label">警告</div>
          </div>
          <div class="stat-item">
            <div class="stat-value error">{{ errorCount }}</div>
            <div class="stat-label">错误</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ offlineCount }}</div>
            <div class="stat-label">离线</div>
          </div>
        </div>
        <div class="refresh-button" @click="refreshCameras">
          <i class="el-icon-refresh"></i>
          刷新摄像头状态
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';

export default {
  name: 'ControlPanel',
  props: {
    cameras: {
      type: Array,
      default: () => []
    },
    currentLayout: {
      type: String,
      default: '2x2'
    },
    carouselSettings: {
      type: Object,
      default: () => ({
        enabled: false,
        interval: 10,
        order: 'sequential'
      })
    }
  },
  emits: [
    'update:layout',
    'update:carousel',
    'update:status-filter',
    'refresh-cameras',
    'update:zoom'
  ],
  setup(props, { emit }) {
    // 面板折叠状态
    const isCollapsed = ref(false);

    // 布局设置
    const layout = ref(props.currentLayout);

    // 轮播设置
    const carouselEnabled = ref(props.carouselSettings.enabled);
    const carouselInterval = ref(props.carouselSettings.interval);
    const carouselOrder = ref(props.carouselSettings.order);

    // 缩放设置
    const zoomLevel = ref(1);

    // 状态筛选
    const selectedStatuses = ref(['normal', 'warning', 'error', 'offline']);
    const statuses = [
      { value: 'normal', label: '正常' },
      { value: 'warning', label: '警告' },
      { value: 'error', label: '错误' },
      { value: 'offline', label: '离线' }
    ];

    // 摄像头统计
    const totalCameras = computed(() => props.cameras.length);
    const normalCount = computed(() => props.cameras.filter(cam => cam.status === 'normal').length);
    const warningCount = computed(() => props.cameras.filter(cam => cam.status === 'warning').length);
    const errorCount = computed(() => props.cameras.filter(cam => cam.status === 'error').length);
    const offlineCount = computed(() => props.cameras.filter(cam => cam.status === 'offline').length);

    // 监听布局变化
    watch(() => props.currentLayout, (newLayout) => {
      layout.value = newLayout;
    });

    // 监听轮播设置变化
    watch(() => props.carouselSettings, (newSettings) => {
      carouselEnabled.value = newSettings.enabled;
      carouselInterval.value = newSettings.interval;
      carouselOrder.value = newSettings.order;
    }, { deep: true });

    // 方法
    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value;
    };

    const setLayout = (layoutValue) => {
      layout.value = layoutValue;
      emit('update:layout', layoutValue);
    };

    const toggleCarousel = () => {
      emit('update:carousel', {
        enabled: carouselEnabled.value,
        interval: carouselInterval.value,
        order: carouselOrder.value
      });
    };

    const setCarouselInterval = (value) => {
      carouselInterval.value = value;
      emit('update:carousel', {
        enabled: carouselEnabled.value,
        interval: value,
        order: carouselOrder.value
      });
    };

    const setCarouselOrder = (value) => {
      carouselOrder.value = value;
      emit('update:carousel', {
        enabled: carouselEnabled.value,
        interval: carouselInterval.value,
        order: value
      });
    };

    const setZoom = (value) => {
      zoomLevel.value = value;
      emit('update:zoom', value);
    };

    const zoomIn = () => {
      if (zoomLevel.value < 5) {
        zoomLevel.value += 0.5;
        emit('update:zoom', zoomLevel.value);
      }
    };

    const zoomOut = () => {
      if (zoomLevel.value > 1) {
        zoomLevel.value -= 0.5;
        emit('update:zoom', zoomLevel.value);
      }
    };

    const resetZoom = () => {
      zoomLevel.value = 1;
      emit('update:zoom', zoomLevel.value);
    };

    const toggleStatus = (status) => {
      const index = selectedStatuses.value.indexOf(status);
      if (index > -1) {
        // 至少保留一个状态
        if (selectedStatuses.value.length > 1) {
          selectedStatuses.value.splice(index, 1);
        }
      } else {
        selectedStatuses.value.push(status);
      }
      emit('update:status-filter', selectedStatuses.value);
    };

    const refreshCameras = () => {
      emit('refresh-cameras');
    };

    return {
      // 状态
      isCollapsed,
      layout,
      carouselEnabled,
      carouselInterval,
      carouselOrder,
      zoomLevel,
      selectedStatuses,
      statuses,
      totalCameras,
      normalCount,
      warningCount,
      errorCount,
      offlineCount,

      // 方法
      toggleCollapse,
      setLayout,
      toggleCarousel,
      setCarouselInterval,
      setCarouselOrder,
      setZoom,
      zoomIn,
      zoomOut,
      resetZoom,
      toggleStatus,
      refreshCameras
    };
  }
};
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '../../components/styles/common.scss' as common;

// 控制面板基本样式
.control-panel {
  height: 100%;
  width: 100%;
  background: transparent; // 移除背景，因为父元素已经提供背景
  display: flex;
  flex-direction: column;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  color: common.$text-light;
  overflow: auto;
  padding: common.$spacing-md;
  position: relative;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(common.$bg-dark, 0.2);
    border-radius: common.$border-radius-sm;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(common.$primary-color, 0.3);
    border-radius: common.$border-radius-sm;

    &:hover {
      background: rgba(common.$primary-color, 0.5);
    }
  }

  // 折叠状态
  &.collapsed {
    height: 60px;
    overflow: hidden;

    .panel-content {
      opacity: 0;
      transform: translateY(-20px);
    }

    .panel-toggle .el-icon {
      transform: rotate(180deg);
    }
  }
}

// 折叠/展开按钮
.panel-toggle {
  position: absolute;
  top: common.$spacing-md;
  right: common.$spacing-md;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, common.$primary-color, color.scale(common.$primary-color, $lightness: -10%));
  border-radius: 50%;
  @include common.flex-center;
  cursor: pointer;
  color: common.$text-light;
  box-shadow: common.$shadow-md;
  z-index: 101;
  transition: all 0.3s ease;
  border: 1px solid rgba(common.$text-light, 0.1);

  &:hover {
    transform: scale(1.1);
    box-shadow: common.$shadow-lg, 0 0 10px rgba(common.$primary-color, 0.5);
  }

  &:active {
    transform: scale(0.95);
  }

  .el-icon {
    transition: transform 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    font-size: 16px;
  }
}

// 面板头部
.panel-header {
  @include common.flex-between;
  margin-bottom: common.$spacing-md;
  padding-right: 40px; // 为折叠按钮留出空间
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: calc(-1 * common.$spacing-sm);
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(common.$text-light, 0.1), transparent);
  }
}

// 面板标题
.panel-title {
  font-size: 18px;
  font-weight: 600;
  color: common.$text-light;
  @include common.flex-center;
  gap: common.$spacing-sm;

  .el-icon {
    color: common.$primary-color;
    font-size: 20px;
    filter: drop-shadow(0 0 3px rgba(common.$primary-color, 0.5));
  }
}

// 面板徽章
.panel-badge {
  background: rgba(common.$bg-dark, 0.4);
  padding: common.$spacing-xs common.$spacing-sm;
  border-radius: common.$border-radius-lg;
  font-size: 13px;
  color: common.$text-secondary;
  border: 1px solid rgba(common.$text-light, 0.05);

  span {
    font-weight: 600;
    color: common.$primary-color;
    margin-right: 2px;
  }
}

// 面板内容
.panel-content {
  display: flex;
  flex-direction: column;
  gap: common.$spacing-md;
  overflow: auto;
  flex: 1;
  transition: all 0.3s ease;
}

// 控制组
.control-group {
  display: flex;
  flex-direction: column;
  gap: common.$spacing-sm;
  background: rgba(common.$bg-dark, 0.3);
  border-radius: common.$border-radius-md;
  padding: common.$spacing-sm;
  border: 1px solid rgba(common.$text-light, 0.05);
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 10 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.05}s;
    }
  }

  &:hover {
    background: rgba(common.$bg-dark, 0.4);
    border-color: rgba(common.$primary-color, 0.2);
    box-shadow: common.$shadow-sm;
  }
}

// 组标题
.group-title {
  font-size: 14px;
  font-weight: 600;
  color: common.$primary-color;
  margin-bottom: common.$spacing-xs;
  @include common.flex-center;
  gap: common.$spacing-xs;
  justify-content: flex-start;

  .el-icon {
    font-size: 16px;
  }
}

// 状态筛选
.status-filters {
  display: flex;
  flex-wrap: wrap;
  gap: common.$spacing-sm;
}

.status-filter {
  padding: common.$spacing-xs common.$spacing-sm;
  background-color: rgba(common.$bg-card, 0.6);
  border-radius: common.$border-radius-lg;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  border: 1px solid transparent;
  @include common.flex-center;
  gap: common.$spacing-xs;
  color: common.$text-secondary;

  &:hover {
    background-color: rgba(common.$bg-card, 0.8);
    transform: translateY(-2px);
    box-shadow: common.$shadow-sm;
    color: common.$text-light;
  }

  &:active {
    transform: translateY(0);
  }

  &.active {
    color: common.$text-light;
    border-color: rgba(common.$text-light, 0.2);
    box-shadow: common.$shadow-sm;

    &.normal {
      background: linear-gradient(135deg, common.$success-color, color.scale(common.$success-color, $lightness: -10%));
    }

    &.warning {
      background: linear-gradient(135deg, common.$warning-color, color.scale(common.$warning-color, $lightness: -10%));
    }

    &.error {
      background: linear-gradient(135deg, common.$error-color, color.scale(common.$error-color, $lightness: -10%));
    }

    &.offline {
      background: linear-gradient(135deg, common.$text-secondary, color.scale(common.$text-secondary, $lightness: -10%));
    }

    &:hover {
      box-shadow: common.$shadow-md, 0 0 8px rgba(common.$primary-color, 0.5);
    }
  }
}

.status-filter .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);

  &.normal {
    background-color: common.$success-color;
  }

  &.warning {
    background-color: common.$warning-color;
  }

  &.error {
    background-color: common.$error-color;
  }

  &.offline {
    background-color: common.$text-secondary;
  }
}

// 布局按钮
.layout-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: common.$spacing-sm;
}

.layout-button {
  padding: common.$spacing-sm;
  background-color: rgba(common.$bg-card, 0.6);
  border-radius: common.$border-radius-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 13px;
  border: 1px solid transparent;
  color: common.$text-secondary;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: common.$spacing-xs;

  &:hover {
    background-color: rgba(common.$bg-card, 0.8);
    transform: translateY(-2px);
    box-shadow: common.$shadow-sm;
    color: common.$text-light;
  }

  &:active {
    transform: translateY(0);
  }

  &.active {
    background: linear-gradient(135deg, common.$primary-color, color.scale(common.$primary-color, $lightness: -10%));
    color: common.$text-light;
    border-color: rgba(common.$text-light, 0.2);
    box-shadow: common.$shadow-sm;

    .layout-icon {
      border-color: rgba(common.$text-light, 0.3);

      &::before {
        background-color: rgba(common.$text-light, 0.9);
      }
    }

    &:hover {
      box-shadow: common.$shadow-md, 0 0 8px rgba(common.$primary-color, 0.5);
    }
  }

  .layout-icon {
    width: 32px;
    height: 32px;
    border-radius: common.$border-radius-sm;
    border: 1px solid rgba(common.$text-light, 0.1);
    background-color: rgba(common.$bg-dark, 0.5);
    position: relative;
    display: grid;

    &::before {
      content: '';
      position: absolute;
      background-color: rgba(common.$text-light, 0.5);
      border-radius: 2px;
      margin: 2px;
    }

    &.layout-1-1 {
      &::before {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }
    }

    &.layout-2-2 {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 2px;

      &::before {
        content: none;
      }

      &::after {
        content: '';
        grid-column: 1 / span 2;
        grid-row: 1 / span 2;
        background-color: rgba(common.$text-light, 0.5);
        border-radius: 2px;
        margin: 2px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 2px;
      }
    }

    &.layout-3-3 {
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(3, 1fr);
      gap: 1px;

      &::before {
        content: none;
      }

      &::after {
        content: '';
        grid-column: 1 / span 3;
        grid-row: 1 / span 3;
        background-color: rgba(common.$text-light, 0.5);
        border-radius: 2px;
        margin: 2px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(3, 1fr);
        gap: 1px;
      }
    }

    &.layout-4-4 {
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: repeat(4, 1fr);
      gap: 1px;

      &::before {
        content: none;
      }

      &::after {
        content: '';
        grid-column: 1 / span 4;
        grid-row: 1 / span 4;
        background-color: rgba(common.$text-light, 0.5);
        border-radius: 2px;
        margin: 2px;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(4, 1fr);
        gap: 1px;
      }
    }
  }
}

// 轮播控制
.carousel-controls {
  display: flex;
  flex-direction: column;
  gap: common.$spacing-md;
}

.carousel-toggle {
  @include common.flex-between;
}

.carousel-toggle-label {
  font-size: 14px;
  color: common.$text-light;
}

.carousel-options {
  display: flex;
  flex-direction: column;
  gap: common.$spacing-md;
  margin-top: common.$spacing-sm;
  padding-left: common.$spacing-md;
  border-left: 1px solid rgba(common.$text-light, 0.1);
}

.carousel-option {
  @include common.flex-between;
  flex-direction: column;
  align-items: flex-start;
  gap: common.$spacing-xs;
}

.carousel-option-label {
  font-size: 14px;
  color: common.$text-secondary;
}

// 缩放控制
.zoom-controls {
  display: flex;
  flex-direction: column;
  gap: common.$spacing-md;
}

.zoom-slider {
  @include common.flex-center;
  gap: common.$spacing-sm;

  span {
    min-width: 45px;
    text-align: center;
    font-weight: 600;
    color: common.$primary-color;
  }
}

.zoom-buttons {
  display: flex;
  gap: common.$spacing-sm;
  margin-top: common.$spacing-xs;
}

.zoom-button {
  flex: 1;
  padding: common.$spacing-sm 0;
  background-color: rgba(common.$bg-card, 0.6);
  border-radius: common.$border-radius-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 14px;
  @include common.flex-center;
  gap: common.$spacing-xs;
  color: common.$text-secondary;

  &:hover {
    background-color: rgba(common.$bg-card, 0.8);
    transform: translateY(-2px);
    box-shadow: common.$shadow-sm;
    color: common.$text-light;
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  .el-icon {
    font-size: 14px;
  }
}

// 刷新按钮
.refresh-button {
  margin-top: common.$spacing-sm;
  padding: common.$spacing-sm 0;
  background: linear-gradient(135deg, common.$primary-color, color.scale(common.$primary-color, $lightness: -10%));
  border-radius: common.$border-radius-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 14px;
  @include common.flex-center;
  gap: common.$spacing-xs;
  color: common.$text-light;
  border: 1px solid rgba(common.$text-light, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: common.$shadow-md, 0 0 10px rgba(common.$primary-color, 0.5);
  }

  &:active {
    transform: translateY(0);
    box-shadow: common.$shadow-sm;
  }

  .el-icon {
    font-size: 16px;
    transition: all 0.3s ease;
  }

  &:hover .el-icon {
    animation: rotate 2s linear infinite;
  }
}

// 摄像头统计
.camera-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: common.$spacing-sm;
  margin-top: common.$spacing-md;
}

.stat-item {
  background: rgba(common.$bg-card, 0.6);
  border-radius: common.$border-radius-md;
  padding: common.$spacing-sm;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(common.$text-light, 0.05);

  &:hover {
    background: rgba(common.$bg-card, 0.8);
    transform: translateY(-2px);
    box-shadow: common.$shadow-sm;
  }
}

.stat-value {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: common.$spacing-xs;

  &.normal {
    color: common.$success-color;
    text-shadow: 0 0 10px rgba(common.$success-color, 0.3);
  }

  &.warning {
    color: common.$warning-color;
    text-shadow: 0 0 10px rgba(common.$warning-color, 0.3);
  }

  &.error {
    color: common.$error-color;
    text-shadow: 0 0 10px rgba(common.$error-color, 0.3);
  }
}

.stat-label {
  font-size: 12px;
  color: common.$text-secondary;
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

// 响应式设计
@include common.responsive-xl {
  .camera-stats {
    grid-template-columns: repeat(4, 1fr);
  }
}

@include common.responsive-lg {
  .camera-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@include common.responsive-md {
  .control-panel {
    padding: common.$spacing-sm;
  }

  .panel-content {
    gap: common.$spacing-sm;
  }

  .control-group {
    padding: common.$spacing-xs;
  }

  .panel-toggle {
    top: common.$spacing-sm;
    right: common.$spacing-sm;
  }

  .layout-buttons,
  .camera-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .status-filters {
    gap: common.$spacing-xs;
  }

  .status-filter {
    padding: common.$spacing-xs common.$spacing-sm;
    font-size: 12px;
  }
}

@include common.responsive-sm {
  .control-panel.collapsed {
    height: auto;
    max-height: 60px;
  }

  .panel-toggle {
    width: 28px;
    height: 28px;
  }

  .panel-header {
    margin-bottom: common.$spacing-sm;
  }

  .panel-title {
    font-size: 16px;
  }

  .control-group {
    gap: common.$spacing-xs;
  }

  .group-title {
    font-size: 13px;
  }

  .stat-value {
    font-size: 18px;
  }

  .zoom-slider span {
    min-width: 40px;
    font-size: 13px;
  }
}

// 触摸设备优化
@media (hover: none) and (pointer: coarse) {
  .panel-toggle {
    width: 36px;
    height: 36px;

    .el-icon {
      font-size: 18px;
    }
  }

  .status-filter,
  .layout-button,
  .zoom-button,
  .refresh-button {
    padding: common.$spacing-sm common.$spacing-md;
    min-height: 44px; // 确保触摸区域足够大
  }

  .layout-icon {
    width: 36px;
    height: 36px;
  }

  .stat-item {
    padding: common.$spacing-md;
  }

  // 添加触摸反馈
  .status-filter:active,
  .layout-button:active,
  .zoom-button:active,
  .refresh-button:active {
    opacity: 0.7;
    transform: scale(0.98);
  }
}
</style>