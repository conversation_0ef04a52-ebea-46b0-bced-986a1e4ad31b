# 生产环境配置
# NODE_ENV 由 Vite 根据 --mode 参数自动设置，无需在 .env 文件中配置

# API基础URL配置 - 生产环境使用完整的后端服务器地址
VITE_API_BASE_URL=http://************:8080

# 客户端认证配置
VITE_APP_CLIENT_ID=e5cd7e4891bf95d1d19206ce24a7b32e

# 网络配置
VITE_API_TIMEOUT=15000
VITE_AI_API_TIMEOUT=120000
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=5174
VITE_AI_API_PROXY_TARGET=http://***********:5000
VITE_MAIN_API_PROXY_TARGET=http://************:8080

# WebSocket配置
VITE_WEBSOCKET_BASE_URL=ws://************:8080
VITE_WEBSOCKET_ROBOT_LOCATION_PATH=/robot-location
VITE_WEBSOCKET_CAMERA_PATH=/camera
VITE_WEBSOCKET_JIZHAN_PATH=/jizhan
VITE_WEBSOCKET_RECONNECT_ATTEMPTS=5
VITE_WEBSOCKET_RECONNECT_INTERVAL=1000
VITE_WEBSOCKET_HEARTBEAT_INTERVAL=10000

# 设备配置
VITE_ROBOT_IP=************
# VITE_ROBOT_IP=*************
VITE_ROBOT_TOKEN=eyJkYXRhMSI6Ik9rOENIbDdrOGZNSUlCSURBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVEwQU1JSUJDQUtDQVFFQXM1Q1ptZkdGVmhKYVlWMWZXS0V6dmFpYlpzUndKVi9QSkhteHd1QVZhNE9uaTZWbCt4bmc0L1dVQk5CVGhiMUNjZUtMMHEzN0NkZ2doOUNtYS9YNjYwNldJTWdOUmlpVmNrZFMzZUNQTzBUS2NuQUE0eE1reWtzeVBBaGlrejhoaWEvQ1BvLy9vcVdPVmlmZnM4K09uWjlHY1VLZVA3S2hvVU0yVENtRy9pTjB5Nm9RNGxIRURnc3dlMUJ5cVRFbEtHQldqcFdyeFRHaXhzYXZMQVZuZmhTSldoMmlFU0k0S0tHVTN5bm1qK3Q3MUt1NFFaQlA0bmNmWGdNTXZ5bWRxUHhQc2h6REZIbWhJUXNHVnllOXZYSmxOMzNVTVpWcTB2MEU2WGFaSlZvVXRHK3hJUjRROXhGelVyWVpUMkswSmh2cGlHSHFQcm90NFJmSGhTVkprUUlCRVE9PUNHQkdFSlFDN0YifQ==
VITE_SIGNAL_SERVER_PORT=8081
VITE_PYTHON_SERVER_PORT=8081

# 视频流服务配置
VITE_VIDEO_STREAM_HOST=***********
VITE_VIDEO_STREAM_PORT=8000

# 摄像头直播墙配置
VITE_AI_DETECTION_WS_URL=ws://***********:8000/ws/detect
VITE_SIGNAL_SERVER_HOST=************
VITE_CAMERA_API_BASE_URL=http://************:8080

# 第三方服务配置
VITE_AMAP_KEY=becca8d421161d1e6c0e335464bdf86b

# UI配置
VITE_DEFAULT_PAGE_SIZE=10
VITE_PAGE_SIZE_OPTIONS=10,20,50,100
VITE_AUTO_REFRESH_INTERVAL=30
VITE_CHART_ANIMATION_DURATION=1000

# 业务配置
VITE_SIMULATED_DELAY=1000
VITE_MAX_TRACK_POINTS=1000
VITE_DATA_RECORDING_MAX_RECORDS=10000
VITE_DATA_RECORDING_SAVE_INTERVAL=5
