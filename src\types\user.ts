/**
 * 用户相关类型定义
 */

// 农业用户登录请求类型
export interface AgriLoginRequest {
  username: string;
  password: string;
  code?: string;
  uuid?: string;
  rememberMe?: boolean;
}

// 农业用户登录响应类型
export interface AgriLoginResponse {
  access_token: string;
  refresh_token?: string;
  expire_in: number;
  userId: number;
  username: string;
  nickName: string;
  realName: string;
  phone: string;
}

// 农业用户信息类型
export interface AgriUserInfo {
  id: number;
  username: string;
  nickName: string;
  realName: string;
  phone: string;
  idCard?: string;
  createTime?: string;
  updateTime?: string;
}

// 通用API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg?: string;
  message?: string;
  data: T;
}

// 用户认证状态类型
export interface AuthState {
  token: string | null;
  user: AgriUserInfo | null;
  isAuthenticated: boolean;
}

// 记住用户信息类型
export interface RememberedUser {
  username: string;
  password: string;
}
