/**
 * 全局样式覆盖和农田地图全局样式
 */
@use 'variables.scss' as vars;

// 隐藏所有 Element Plus 滚动条
.el-scrollbar {
  @include vars.hide-scrollbar;
  
  .el-scrollbar__bar {
    opacity: 0 !important;
    display: none !important;
  }
  
  .el-scrollbar__wrap {
    overflow-scrolling: touch;
    margin-bottom: 0 !important;
    margin-right: 0 !important;
  }
  
  .el-scrollbar__thumb {
    background-color: transparent !important;
  }
}

/**
 * 农田地图全局样式
 */

// 全局卡片样式
.card {
  background-color: rgba(0, 21, 65, 0.8);
  border: 1px solid vars.$panel-border-color;
  border-radius: vars.$border-radius;
  box-shadow: vars.$shadow-medium;
  padding: 15px;
  transition: vars.$transition-normal;
  backdrop-filter: blur(vars.$panel-blur);
  -webkit-backdrop-filter: blur(vars.$panel-blur);
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: vars.$shadow-large;
  }
}

// 全局图标样式
.svg-icon {
  display: inline-block;
  vertical-align: middle;
}

// 全局状态指示器
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
  
  &.online {
    @include vars.status-dot(vars.$status-online);
  }
  
  &.standby {
    @include vars.status-dot(vars.$status-standby);
  }
  
  &.offline {
    @include vars.status-dot(vars.$status-offline);
  }
  
  &.warning {
    @include vars.status-dot(vars.$status-warning);
  }
  
  &.error {
    @include vars.status-dot(vars.$status-danger);
  }
}

// 全局动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateY(20px);
}

// 全局加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

// 全局标签样式
.tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: normal;
  
  &.primary {
    background-color: rgba(vars.$primary-color, 0.15);
    color: vars.$primary-color;
  }
  
  &.success {
    background-color: rgba(vars.$success-color, 0.15);
    color: vars.$success-color;
  }
  
  &.warning {
    background-color: rgba(vars.$warning-color, 0.15);
    color: vars.$warning-color;
  }
  
  &.danger {
    background-color: rgba(vars.$danger-color, 0.15);
    color: vars.$danger-color;
  }
  
  &.info {
    background-color: rgba(vars.$info-color, 0.15);
    color: vars.$info-color;
  }
}

// 隐藏滚动条
.scrollbar-hide {
  @include vars.hide-scrollbar;
}

/* 设备标签全局样式 */
.device-label {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
  transform: translate(-50%, -100%);
  transition: all 0.2s ease-out;
  
  .device-label-content {
    background-color: rgba(0, 21, 65, 0.85);
    border-radius: 4px;
    padding: 5px 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(43, 255, 150, 0.3);
    transform: translateY(-5px);
    transition: all 0.2s ease;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid rgba(0, 21, 65, 0.85);
    }
  }
  
  .device-label-name {
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 2px;
    white-space: nowrap;
  }
  
  .device-label-status {
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    text-transform: uppercase;
    
    &.online {
      background-color: rgba(0, 255, 0, 0.2);
      color: #00ff00;
    }
    
    &.standby {
      background-color: rgba(255, 255, 0, 0.2);
      color: #ffff00;
    }
    
    &.offline {
      background-color: rgba(255, 0, 0, 0.2);
      color: #ff0000;
    }
  }
} 