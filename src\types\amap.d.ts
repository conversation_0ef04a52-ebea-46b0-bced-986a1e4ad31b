/**
 * 高德地图API类型声明文件
 * 该文件为TypeScript提供AMap相关类型定义
 */

declare namespace AMap {
  // 坐标点类型
  class LngLat {
    constructor(lng: number, lat: number);
    getLng(): number;
    getLat(): number;
    equals(lnglat: LngLat): boolean;
    toString(): string;
    distanceTo(lnglat: LngLat): number;
  }

  // 地图类型
  class Map {
    constructor(container: string | HTMLElement, options?: MapOptions);
    getCenter(): LngLat;
    getZoom(): number;
    getRotation(): number;
    getPitch(): number;
    getStatus(): any;
    getScale(): number;
    getResolution(): number;
    getSize(): Size;
    getBounds(): Bounds;
    setCenter(center: LngLat | [number, number]): void;
    setZoom(zoom: number): void;
    setRotation(rotation: number): void;
    setPitch(pitch: number): void;
    setStatus(status: any): void;
    clearMap(): void;
    destroy(): void;
    plugin(name: string | string[], callback: Function): void;
    addControl(control: Control): void;
    removeControl(control: Control): void;
    add(overlayers: any | any[]): void;
    remove(overlayers: any | any[]): void;
    getAllOverlays(type?: string): any[];
    setFitView(overlayList?: any[], immediately?: boolean, avoid?: [number, number, number, number], maxZoom?: number): void;
    pixelToLngLat(pixel: Pixel | [number, number], level?: number): LngLat;
    lngLatToPixel(lnglat: LngLat | [number, number], level?: number): Pixel;
    setLimitBounds(bounds: Bounds): void;
    clearLimitBounds(): void;
    getLimitBounds(): Bounds;
    setZoomAndCenter(zoom: number, center: LngLat | [number, number]): void;
    setDefaultCursor(cursor: string): void;
    zoomIn(): void;
    zoomOut(): void;
    panTo(position: LngLat | [number, number]): void;
    panBy(x: number, y: number): void;
    setLayers(layers: Layer[]): void;
    getLayers(): Layer[];
    setMapStyle(style: string): void;
    getMapStyle(): string;
    setFeatures(features: string | string[]): void;
    getFeatures(): string | string[];
    setMask(maskPath: any[]): void;
  }

  // 地图选项类型
  interface MapOptions {
    zoom?: number;
    center?: [number, number] | LngLat;
    rotation?: number;
    pitch?: number;
    viewMode?: '2D' | '3D';
    features?: string[];
    layers?: Layer[];
    zooms?: [number, number];
    dragEnable?: boolean;
    zoomEnable?: boolean;
    jogEnable?: boolean;
    pitchEnable?: boolean;
    rotateEnable?: boolean;
    animateEnable?: boolean;
    keyboardEnable?: boolean;
    doubleClickZoom?: boolean;
    scrollWheel?: boolean;
    touchZoom?: boolean;
    touchZoomCenter?: number;
    showBuildingBlock?: boolean;
    showLabel?: boolean;
    showIndoorMap?: boolean;
    defaultCursor?: string;
    isHotspot?: boolean;
    mapStyle?: string;
    wallColor?: string;
    roofColor?: string;
    skyColor?: string;
    labelRejectMask?: boolean;
    mask?: any[];
    resizeEnable?: boolean;
  }

  // 多边形类型
  class Polygon {
    constructor(options: PolygonOptions);
    setPath(path: LngLat[] | [number, number][] | LngLat[][] | [number, number][][]): void;
    getPath(): LngLat[] | LngLat[][];
    setOptions(options: PolygonOptions): void;
    getOptions(): PolygonOptions;
    getArea(): number;
    getExtData(): any;
    setExtData(extData: any): void;
    contains(point: LngLat | [number, number]): boolean;
    on(event: string, handler: Function): void;
  }

  // 多边形选项类型
  interface PolygonOptions {
    path?: LngLat[] | [number, number][] | LngLat[][] | [number, number][][];
    zIndex?: number;
    bubble?: boolean;
    cursor?: string;
    strokeColor?: string;
    strokeOpacity?: number;
    strokeWeight?: number;
    fillColor?: string;
    fillOpacity?: number;
    draggable?: boolean;
    extData?: any;
    strokeStyle?: 'solid' | 'dashed';
    strokeDasharray?: [number, number];
    events?: object;
  }

  // 鼠标工具类型
  class MouseTool {
    constructor(map: Map);
    marker(options?: any): void;
    polyline(options?: any): void;
    polygon(options?: any): void;
    rectangle(options?: any): void;
    circle(options?: any): void;
    rule(options?: any): void;
    measureArea(options?: any): void;
    rectZoomIn(options?: any): void;
    rectZoomOut(options?: any): void;
    close(clear?: boolean): void;
    on(eventName: string, handler: Function): void;
    off(eventName: string, handler?: Function): void;
  }

  // 控件基类
  class Control {
    constructor(options?: any);
    addTo(map: Map): void;
    remove(): void;
  }

  // 比例尺控件
  class Scale extends Control {
    constructor(options?: ScaleOptions);
  }

  // 比例尺控件选项
  interface ScaleOptions {
    position?: string | object;
    offset?: [number, number];
    visible?: boolean;
  }

  // 工具条控件
  class ToolBar extends Control {
    constructor(options?: ToolBarOptions);
  }

  // 工具条控件选项
  interface ToolBarOptions {
    position?: string | object;
    offset?: [number, number];
    visible?: boolean;
    ruler?: boolean;
    direction?: boolean;
    autoPosition?: boolean;
    locationMarker?: any;
    useNative?: boolean;
  }

  // 图层基类
  class Layer {
    show(): void;
    hide(): void;
    setOpacity(opacity: number): void;
    getOpacity(): number;
    setZIndex(zIndex: number): void;
    getZIndex(): number;
  }

  // 图层组类型
  class OverlayGroup {
    constructor(overlays?: any[]);
    addOverlay(overlay: any): void;
    addOverlays(overlays: any[]): void;
    removeOverlay(overlay: any): void;
    removeOverlays(overlays: any[]): void;
    getOverlays(): any[];
    hasOverlay(overlay: any): boolean;
    clearOverlays(): void;
    eachOverlay(iterator: Function, context?: any): void;
    setOptions(options: any): void;
    show(): void;
    hide(): void;
  }

  // 像素点类型
  class Pixel {
    constructor(x: number, y: number);
    getX(): number;
    getY(): number;
    equals(pixel: Pixel): boolean;
    toString(): string;
  }

  // 尺寸类型
  class Size {
    constructor(width: number, height: number);
    getWidth(): number;
    getHeight(): number;
    toString(): string;
  }

  // 边界类型
  class Bounds {
    constructor(southWest: LngLat, northEast: LngLat);
    contains(point: LngLat): boolean;
    getCenter(): LngLat;
    getSouthWest(): LngLat;
    getNorthEast(): LngLat;
    toString(): string;
  }

  // 事件类型
  const event: {
    addListener(instance: any, eventName: string, handler: Function): any;
    removeListener(listener: any): void;
    trigger(instance: any, eventName: string, eventObject?: any): void;
    addDomListener(element: HTMLElement, eventName: string, handler: Function, context?: any): any;
    addListenerOnce(instance: any, eventName: string, handler: Function, context?: any): any;
  };
  
  // 插件加载方法
  function plugin(pluginNames: string | string[], callback: Function): void;
  
  // 版本信息
  const version: string;
}

// 全局AMap对象
interface Window {
  AMap: typeof AMap;
  _AMapSecurityConfig?: {
    securityJsCode?: string;
  };
} 