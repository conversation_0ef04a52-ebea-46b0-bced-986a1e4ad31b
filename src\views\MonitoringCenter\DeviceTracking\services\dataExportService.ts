/**
 * 数据导出服务
 * 提供多种格式的数据导出功能
 */

import * as XLSX from 'xlsx'
import type { DataRecord, ExportDataRow } from '../types'

export class DataExportService {
  /**
   * 导出为JSON格式
   */
  static exportToJSON(records: DataRecord[], filename?: string): void {
    const data = JSON.stringify(records, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    this.downloadFile(blob, filename || `设备追踪数据_${this.getTimestamp()}.json`)
  }

  /**
   * 导出为CSV格式
   */
  static exportToCSV(records: DataRecord[], filename?: string): void {
    if (records.length === 0) return

    const headers = ['序号', '设备名称', '标签ID', 'X坐标', 'Y坐标', '时间戳', '记录时间']
    const csvContent = [
      headers.join(','),
      ...records.map((record, index) => [
        index + 1,
        `"${record.deviceName}"`,
        record.data.tagId,
        record.data.x.toFixed(3),
        record.data.y.toFixed(3),
        record.timestamp,
        `"${record.recordTime}"`
      ].join(','))
    ].join('\n')

    // 添加BOM以支持中文
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' })
    this.downloadFile(blob, filename || `设备追踪数据_${this.getTimestamp()}.csv`)
  }

  /**
   * 导出为Excel格式（高级版本，支持样式）
   */
  static exportToExcelAdvanced(records: DataRecord[], filename?: string): void {
    if (records.length === 0) return

    const workbook = XLSX.utils.book_new()
    
    // 转换数据
    const exportData: ExportDataRow[] = records.map((record, index) => ({
      序号: index + 1,
      设备名称: record.deviceName,
      标签ID: record.data.tagId,
      X坐标: Number(record.data.x.toFixed(3)),
      Y坐标: Number(record.data.y.toFixed(3)),
      时间戳: record.timestamp,
      记录时间: record.recordTime,
      备注: `数据ID: ${record.id}`
    }))

    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(exportData)

    // 设置列宽
    worksheet['!cols'] = [
      { wch: 8 },  // 序号
      { wch: 15 }, // 设备名称
      { wch: 10 }, // 标签ID
      { wch: 12 }, // X坐标
      { wch: 12 }, // Y坐标
      { wch: 15 }, // 时间戳
      { wch: 20 }, // 记录时间
      { wch: 25 }  // 备注
    ]

    // 设置表头样式（如果支持）
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
      if (!worksheet[cellAddress]) continue
      
      // 设置表头样式
      worksheet[cellAddress].s = {
        font: { bold: true },
        fill: { fgColor: { rgb: 'E3F2FD' } },
        alignment: { horizontal: 'center' }
      }
    }

    // 添加统计信息工作表
    const statsData = [
      { 项目: '总记录数', 值: records.length },
      { 项目: '设备数量', 值: new Set(records.map(r => r.data.tagId)).size },
      { 项目: '时间范围', 值: this.getTimeRange(records) },
      { 项目: '导出时间', 值: new Date().toLocaleString('zh-CN') }
    ]
    const statsWorksheet = XLSX.utils.json_to_sheet(statsData)
    statsWorksheet['!cols'] = [{ wch: 15 }, { wch: 30 }]

    // 添加工作表
    XLSX.utils.book_append_sheet(workbook, worksheet, '追踪记录')
    XLSX.utils.book_append_sheet(workbook, statsWorksheet, '统计信息')

    // 导出文件
    XLSX.writeFile(workbook, filename || `设备追踪数据_${this.getTimestamp()}.xlsx`)
  }

  /**
   * 导出轨迹数据为GPX格式（GPS交换格式）
   */
  static exportToGPX(records: DataRecord[], filename?: string): void {
    if (records.length === 0) return

    // 按设备分组
    const deviceGroups = new Map<number, DataRecord[]>()
    records.forEach(record => {
      const tagId = record.data.tagId
      if (!deviceGroups.has(tagId)) {
        deviceGroups.set(tagId, [])
      }
      deviceGroups.get(tagId)!.push(record)
    })

    let gpxContent = `<?xml version="1.0" encoding="UTF-8"?>
<gpx version="1.1" creator="智慧农场设备追踪系统">
  <metadata>
    <name>设备追踪轨迹</name>
    <desc>农场设备实时追踪数据</desc>
    <time>${new Date().toISOString()}</time>
  </metadata>
`

    // 为每个设备创建轨迹
    deviceGroups.forEach((deviceRecords, tagId) => {
      const deviceName = deviceRecords[0]?.deviceName || `设备${tagId}`
      deviceRecords.sort((a, b) => a.timestamp - b.timestamp)

      gpxContent += `  <trk>
    <name>${deviceName}</name>
    <desc>设备ID: ${tagId}</desc>
    <trkseg>
`
      deviceRecords.forEach(record => {
        const time = new Date(record.timestamp).toISOString()
        gpxContent += `      <trkpt lat="${record.data.y}" lon="${record.data.x}">
        <time>${time}</time>
        <desc>TagID: ${record.data.tagId}</desc>
      </trkpt>
`
      })

      gpxContent += `    </trkseg>
  </trk>
`
    })

    gpxContent += '</gpx>'

    const blob = new Blob([gpxContent], { type: 'application/gpx+xml' })
    this.downloadFile(blob, filename || `设备轨迹_${this.getTimestamp()}.gpx`)
  }

  /**
   * 获取时间戳字符串
   */
  private static getTimestamp(): string {
    return new Date().toISOString().slice(0, 19).replace(/:/g, '-')
  }

  /**
   * 获取时间范围描述
   */
  private static getTimeRange(records: DataRecord[]): string {
    if (records.length === 0) return '无数据'
    
    const timestamps = records.map(r => r.timestamp).sort((a, b) => a - b)
    const start = new Date(timestamps[0]).toLocaleString('zh-CN')
    const end = new Date(timestamps[timestamps.length - 1]).toLocaleString('zh-CN')
    
    return `${start} 至 ${end}`
  }

  /**
   * 下载文件
   */
  private static downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(url), 100)
  }

  /**
   * 批量导出多种格式
   */
  static exportMultipleFormats(records: DataRecord[], formats: string[] = ['excel', 'csv', 'json']): void {
    const timestamp = this.getTimestamp()
    
    formats.forEach(format => {
      switch (format.toLowerCase()) {
        case 'excel':
          this.exportToExcelAdvanced(records, `设备追踪数据_${timestamp}.xlsx`)
          break
        case 'csv':
          this.exportToCSV(records, `设备追踪数据_${timestamp}.csv`)
          break
        case 'json':
          this.exportToJSON(records, `设备追踪数据_${timestamp}.json`)
          break
        case 'gpx':
          this.exportToGPX(records, `设备轨迹_${timestamp}.gpx`)
          break
      }
    })
  }
}
