<!-- 
  ReportGeneration.vue
  数据报告自动生成模块
  根据虫害分析数据自动生成专业的报告文档
-->
<template>
  <div class="report-generation">
    <!-- 页面标题 -->
    <PageHeader
      title="虫害分析报告自动生成"
      description="根据虫害分析数据自动生成专业的报告文档"
      icon="Document"
    >
      <template #actions>
        <div class="report-summary">
          <div class="summary-item">
            <span class="summary-value">{{ getReportCount() }}</span>
            <span class="summary-label">已生成报告</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getReportTypeCount() }}</span>
            <span class="summary-label">报告类型</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 报告配置面板 -->
    <div class="report-panels">
      <DataPanel title="报告配置">
        <template #actions>
          <el-tag type="info" effect="dark" size="small">数据分析报告</el-tag>
        </template>
        
        <div class="report-configuration">
          <el-form :model="reportConfig" label-position="top">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="报告类型">
                  <el-select v-model="reportConfig.reportType" placeholder="选择报告类型">
                    <el-option v-for="type in reportTypes" :key="type.value" :value="type.value" :label="type.label">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="虫害类型">
                  <el-select v-model="reportConfig.pestType" placeholder="选择虫害类型" clearable>
                    <el-option v-for="pest in pestTypes" :key="pest.value" :value="pest.value" :label="pest.label">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="地区范围">
                  <el-cascader
                    v-model="reportConfig.region"
                    :options="regionOptions"
                    placeholder="选择地区范围"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="时间范围">
                  <el-date-picker v-model="reportConfig.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="报告格式">
                  <el-radio-group v-model="reportConfig.format">
                    <el-radio label="pdf">PDF文档</el-radio>
                    <el-radio label="word">Word文档</el-radio>
                    <el-radio label="excel">Excel表格</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="报告语言">
                  <el-radio-group v-model="reportConfig.language">
                    <el-radio label="zh">中文</el-radio>
                    <el-radio label="en">英文</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left">报告内容选项</el-divider>

            <el-row :gutter="16">
              <el-col :span="24">
                <el-form-item label="需要包含的内容模块">
                  <el-checkbox-group v-model="reportConfig.contentModules">
                    <el-row :gutter="8">
                      <el-col :xs="12" :sm="8" :md="6" :lg="6">
                        <el-checkbox label="summary">摘要总结</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="6" :lg="6">
                        <el-checkbox label="trends">发生趋势分析</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="6" :lg="6">
                        <el-checkbox label="distribution">地理分布</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="6" :lg="6">
                        <el-checkbox label="damages">危害程度评估</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="6" :lg="6">
                        <el-checkbox label="prevention">防控建议</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="6" :lg="6">
                        <el-checkbox label="comparison">防治效果对比</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="6" :lg="6">
                        <el-checkbox label="prediction">未来趋势预测</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="6" :lg="6">
                        <el-checkbox label="economics">经济影响分析</el-checkbox>
                      </el-col>
                    </el-row>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="24">
                <el-form-item label="图表类型">
                  <el-checkbox-group v-model="reportConfig.chartTypes">
                    <el-row :gutter="8">
                      <el-col :xs="12" :sm="8" :md="4" :lg="4">
                        <el-checkbox label="line">折线图</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="4" :lg="4">
                        <el-checkbox label="bar">柱状图</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="4" :lg="4">
                        <el-checkbox label="pie">饼图</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="4" :lg="4">
                        <el-checkbox label="map">地图</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="4" :lg="4">
                        <el-checkbox label="heatmap">热力图</el-checkbox>
                      </el-col>
                      <el-col :xs="12" :sm="8" :md="4" :lg="4">
                        <el-checkbox label="radar">雷达图</el-checkbox>
                      </el-col>
                    </el-row>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item label="附加说明">
                  <el-input type="textarea" v-model="reportConfig.additionalNotes" rows="4" placeholder="请输入有关报告的特殊要求或附加说明" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24" class="action-buttons">
                <el-button type="primary" @click="generateReport" :loading="generatingReport">
                  <el-icon><Document /></el-icon>
                  生成报告
                </el-button>
                <el-button @click="resetForm">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </DataPanel>
      
      <!-- 报告预览面板 -->
      <DataPanel title="报告预览" v-if="showPreview">
        <template #actions>
          <div class="preview-actions">
            <el-button type="primary" size="small" plain @click="downloadReport">
              <el-icon><Download /></el-icon>
              下载报告
            </el-button>
            <el-button size="small" plain @click="printReport">
              <el-icon><Printer /></el-icon>
              打印报告
            </el-button>
          </div>
        </template>
        
        <div class="preview-content">
          <div class="report-cover">
            <h1>{{ reportTitle }}</h1>
            <p class="report-subtitle">{{ reportSubtitle }}</p>
            <p class="report-date">生成日期: {{ formatDate(new Date()) }}</p>
          </div>
          
          <el-divider />
          
          <div class="report-toc">
            <h2>目录</h2>
            <el-menu mode="vertical">
              <el-menu-item v-for="(section, index) in reportSections" :key="index" :index="index.toString()">
                {{ section.title }}
              </el-menu-item>
            </el-menu>
          </div>
          
          <el-divider />
          
          <div class="report-section" v-for="(section, index) in reportSections" :key="index">
            <h2>{{ section.title }}</h2>
            <p>{{ section.content }}</p>
            
            <div v-if="section.chart" class="section-chart">
              <div :id="'chart-' + index" style="width: 100%; height: 300px;"></div>
            </div>
          </div>
        </div>
      </DataPanel>
      
      <!-- 空预览状态 -->
      <DataPanel title="报告预览" v-if="!showPreview">
        <div class="empty-preview">
          <el-empty description="请配置并生成报告" />
          <p class="empty-tip">选择所需的报告类型和参数，点击"生成报告"按钮开始创建</p>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="数据就绪" />
        <StatusIndicator type="normal" label="模型训练完成" />
        <StatusIndicator type="warning" label="AI分析中" />
      </div>
      <div class="refresh-info">
        <span>最后更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  Document,
  Download,
  Printer,
  Edit,
  Refresh
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'

// 定义接口
interface ReportType {
  value: string
  label: string
}

interface PestType {
  value: string
  label: string
}

interface ReportConfig {
  reportType: string
  pestType: string
  region: string[]
  dateRange: [Date, Date] | null
  format: 'pdf' | 'word' | 'excel'
  language: 'zh' | 'en'
  contentModules: string[]
  chartTypes: string[]
  additionalNotes: string
}

interface ReportSection {
  title: string
  content: string
  chart?: boolean
  chartType?: string
}

// 选项数据
const reportTypes = ref<ReportType[]>([
  { value: 'monitoring', label: '虫情监测报告' },
  { value: 'analysis', label: '虫害分析报告' },
  { value: 'prevention', label: '防治建议报告' },
  { value: 'economic', label: '经济损失评估' },
  { value: 'comprehensive', label: '综合分析报告' }
])

const pestTypes = ref<PestType[]>([
  { value: 'aphid', label: '蚜虫' },
  { value: 'beetle', label: '甲虫' },
  { value: 'moth', label: '蛾类' },
  { value: 'mite', label: '螨虫' },
  { value: 'nematode', label: '线虫' }
])

const regionOptions = [
  {
    value: 'north',
    label: '华北地区',
    children: [
      { value: 'beijing', label: '北京' },
      { value: 'tianjin', label: '天津' },
      { value: 'hebei', label: '河北' }
    ]
  },
  {
    value: 'east',
    label: '华东地区',
    children: [
      { value: 'shanghai', label: '上海' },
      { value: 'jiangsu', label: '江苏' },
      { value: 'zhejiang', label: '浙江' }
    ]
  },
  {
    value: 'south',
    label: '华南地区',
    children: [
      { value: 'guangdong', label: '广东' },
      { value: 'guangxi', label: '广西' },
      { value: 'hainan', label: '海南' }
    ]
  }
]

// 报告配置数据
const reportConfig = reactive<ReportConfig>({
  reportType: '',
  pestType: '',
  region: [],
  dateRange: null,
  format: 'pdf',
  language: 'zh',
  contentModules: ['summary', 'trends', 'prevention'],
  chartTypes: ['line', 'bar', 'map'],
  additionalNotes: ''
})

// UI状态
const generatingReport = ref<boolean>(false)
const showPreview = ref<boolean>(false)
const reportSections = ref<ReportSection[]>([])
const chartInstances: { [key: string]: echarts.ECharts } = {}
const lastUpdateTime = ref(new Date())

// 已生成报告数据
const generatedReports = ref(12) // 模拟数据：已生成的报告数量
const reportTypeCount = ref(5) // 模拟数据：报告类型数量

// 计算属性
const reportTitle = computed(() => {
  const pestName = pestTypes.value.find(p => p.value === reportConfig.pestType)?.label || '全部虫害'
  const reportTypeName = reportTypes.value.find(t => t.value === reportConfig.reportType)?.label || '综合分析'
  return `${pestName}${reportTypeName}`
})

const reportSubtitle = computed(() => {
  // 获取地区名称
  let regionName = '全国地区'
  if (reportConfig.region && reportConfig.region.length > 0) {
    const lastRegion = reportConfig.region[reportConfig.region.length - 1]
    const regionOption = findRegionOption(regionOptions, lastRegion)
    regionName = regionOption?.label || '全国地区'
  }

  // 获取时间范围
  let timeRange = '全时段'
  if (reportConfig.dateRange) {
    const startDate = formatDate(reportConfig.dateRange[0])
    const endDate = formatDate(reportConfig.dateRange[1])
    timeRange = `${startDate} - ${endDate}`
  }

  return `${regionName} · ${timeRange}`
})

// 方法
const findRegionOption = (options: any[], value: string): any => {
  for (const option of options) {
    if (option.value === value) {
      return option
    }
    if (option.children) {
      const found = findRegionOption(option.children, value)
      if (found) return found
    }
  }
  return null
}

const formatDate = (date: Date | undefined): string => {
  if (!date) return ''
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 获取已生成报告数量
const getReportCount = () => {
  return generatedReports.value;
};

// 获取报告类型数量
const getReportTypeCount = () => {
  return reportTypeCount.value;
};

// 生成报告
const generateReport = () => {
  // 验证表单
  if (!reportConfig.reportType) {
    ElMessage.warning('请选择报告类型');
    return;
  }

  generatingReport.value = true;

  // 模拟生成报告
  setTimeout(() => {
    generateMockReport()
    generatingReport.value = false
    showPreview.value = true
    
    // 增加报告计数
    generatedReports.value++;
    
    // 等待DOM更新后渲染图表
    setTimeout(renderCharts, 100)
    
    ElMessage.success('报告生成成功');
  }, 1500)
}

// 重置表单
const resetForm = () => {
  reportConfig.reportType = ''
  reportConfig.pestType = ''
  reportConfig.region = []
  reportConfig.dateRange = null
  reportConfig.format = 'pdf'
  reportConfig.language = 'zh'
  reportConfig.contentModules = ['summary', 'trends', 'prevention']
  reportConfig.chartTypes = ['line', 'bar', 'map']
  reportConfig.additionalNotes = ''
  showPreview.value = false
  
  ElMessage.info('表单已重置');
}

// 生成模拟报告内容
const generateMockReport = () => {
  reportSections.value = []
  
  if (reportConfig.contentModules.includes('summary')) {
    reportSections.value.push({
      title: '摘要总结',
      content: '本报告基于对所选地区的虫害监测数据分析，提供了虫害发生、发展、分布和危害情况的全面概述。通过多维度数据整合，揭示了虫害发生规律和趋势，为科学制定防控策略提供依据。'
    })
  }
  
  if (reportConfig.contentModules.includes('trends')) {
    reportSections.value.push({
      title: '发生趋势分析',
      content: '近期监测数据显示，所选地区虫害密度呈波动上升趋势，与上年同期相比增长了23%。温度和降水量的变化是影响虫口密度的主要因素，数据表明，当温度在25-30℃，相对湿度在60-80%时，虫口繁殖速度最快。',
      chart: true,
      chartType: 'line'
    })
  }
  
  if (reportConfig.contentModules.includes('distribution')) {
    reportSections.value.push({
      title: '地理分布',
      content: '虫害分布呈现明显的地域差异，主要集中在低海拔、水源丰富的平原地区。其中，南部地区的发生密度高于北部地区，可能与当地的耕作模式和气候条件有关。',
      chart: true,
      chartType: 'map'
    })
  }
  
  if (reportConfig.contentModules.includes('damages')) {
    reportSections.value.push({
      title: '危害程度评估',
      content: '根据田间调查数据，当前虫害对作物的危害程度属于中等水平。预计如不采取有效防控措施，将导致产量损失15-20%，经济损失约为每亩200-300元。',
      chart: true,
      chartType: 'bar'
    })
  }
  
  if (reportConfig.contentModules.includes('prevention')) {
    reportSections.value.push({
      title: '防控建议',
      content: '建议采取综合防治措施：1) 化学防治：选用高效低毒农药，如***，每亩用量为***；2) 生物防治：释放天敌，如***；3) 农艺措施：调整种植结构，实行轮作等。防治适期为害虫卵孵化盛期，预计在5月中下旬。'
    })
  }
  
  if (reportConfig.contentModules.includes('prediction')) {
    reportSections.value.push({
      title: '未来趋势预测',
      content: '根据历史数据建立的预测模型显示，未来4周内虫害发生密度将继续上升，预计在6月中旬达到峰值。天气预报显示的温暖湿润条件将有利于虫害繁殖，建议提前做好防控准备。',
      chart: true,
      chartType: 'line'
    })
  }
  
  if (reportConfig.contentModules.includes('economics')) {
    reportSections.value.push({
      title: '经济影响分析',
      content: '根据当前虫害发生情况，预计将影响全区农作物总产值的8-12%。通过及时防控，可以将损失控制在5%以内，综合防治成本投入回报比约为1:4.5，具有良好的经济效益。',
      chart: true,
      chartType: 'pie'
    })
  }
}

// 渲染图表
const renderCharts = () => {
  // 清除已有的图表实例
  Object.values(chartInstances).forEach(chart => chart.dispose())
  
  // 为每个带有图表的部分创建图表
  reportSections.value.forEach((section, index) => {
    if (section.chart) {
      const chartElement = document.getElementById(`chart-${index}`)
      if (chartElement) {
        const chart = echarts.init(chartElement)
        chartInstances[`chart-${index}`] = chart
        
        // 根据不同的图表类型设置不同的选项
        if (section.chartType === 'line') {
          chart.setOption({
            tooltip: {
              trigger: 'axis',
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              borderColor: '#3b82f6',
              textStyle: {
                color: '#e5e7eb'
              }
            },
            grid: {
              top: 40,
              bottom: 30,
              left: 50,
              right: 30
            },
            xAxis: {
              type: 'category',
              data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
              axisLabel: {
                color: '#9ca3af',
                fontSize: 10
              },
              axisLine: {
                lineStyle: {
                  color: '#4b5563'
                }
              }
            },
            yAxis: {
              type: 'value',
              name: '虫口密度(头/平方米)',
              nameTextStyle: {
                color: '#9ca3af'
              },
              axisLabel: {
                color: '#9ca3af'
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(75, 85, 99, 0.1)'
                }
              }
            },
            series: [{
              name: '虫口密度',
              data: Array.from({ length: 12 }, () => Math.floor(Math.random() * 100)),
              type: 'line',
              smooth: true,
              symbol: 'emptyCircle',
              symbolSize: 8,
              lineStyle: {
                width: 3,
                color: '#3b82f6',
                shadowColor: 'rgba(59, 130, 246, 0.3)',
                shadowBlur: 10
              },
              itemStyle: {
                color: '#3b82f6',
                borderColor: '#1f2937',
                borderWidth: 2
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(59, 130, 246, 0.5)' },
                  { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
                ])
              }
            }]
          })
        } else if (section.chartType === 'bar') {
          chart.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              borderColor: '#10b981',
              textStyle: {
                color: '#e5e7eb'
              }
            },
            grid: {
              top: 40,
              bottom: 30,
              left: 50,
              right: 30
            },
            xAxis: {
              type: 'category',
              data: ['轻微', '一般', '中度', '严重', '极重'],
              axisLabel: {
                color: '#9ca3af',
                fontSize: 10
              },
              axisLine: {
                lineStyle: {
                  color: '#4b5563'
                }
              }
            },
            yAxis: {
              type: 'value',
              name: '面积比例(%)',
              nameTextStyle: {
                color: '#9ca3af'
              },
              axisLabel: {
                color: '#9ca3af'
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(75, 85, 99, 0.1)'
                }
              }
            },
            series: [{
              name: '危害面积比例',
              data: [10, 25, 35, 20, 10],
              type: 'bar',
              itemStyle: {
                color: new echarts.graphic.LinearGradient(
                  0, 0, 0, 1,
                  [
                    {offset: 0, color: '#10b981'},
                    {offset: 1, color: '#059669'}
                  ]
                ),
                borderRadius: [4, 4, 0, 0]
              }
            }]
          })
        } else if (section.chartType === 'pie') {
          chart.setOption({
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)',
              backgroundColor: 'rgba(31, 41, 55, 0.8)',
              borderColor: '#f59e0b',
              textStyle: {
                color: '#e5e7eb'
              }
            },
            legend: {
              orient: 'vertical',
              right: 10,
              top: 'center',
              textStyle: {
                color: '#9ca3af'
              }
            },
            series: [
              {
                name: '经济影响',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#1f2937',
                  borderWidth: 2
                },
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '18',
                    fontWeight: 'bold',
                    color: '#e5e7eb'
                  }
                },
                labelLine: {
                  show: false
                },
                data: [
                  { value: 40, name: '减产损失' },
                  { value: 25, name: '防控投入' },
                  { value: 15, name: '质量影响' },
                  { value: 12, name: '人工成本' },
                  { value: 8, name: '其他损失' }
                ]
              }
            ]
          });
        } else if (section.chartType === 'map') {
          // 这里只是一个简化的示例，实际应用中需要引入地图数据
          chart.setOption({
            visualMap: {
              min: 0,
              max: 100,
              text: ['高', '低'],
              realtime: false,
              calculable: true,
              inRange: {
                color: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444']
              },
              textStyle: {
                color: '#9ca3af'
              }
            },
            series: [{
              name: '虫害分布',
              type: 'map',
              map: 'china',
              label: {
                show: true,
                color: '#e5e7eb'
              },
              emphasis: {
                label: {
                  color: '#ffffff'
                },
                itemStyle: {
                  areaColor: '#3b82f6'
                }
              },
              data: [
                { name: '北京', value: Math.floor(Math.random() * 100) },
                { name: '天津', value: Math.floor(Math.random() * 100) },
                { name: '河北', value: Math.floor(Math.random() * 100) },
                { name: '山西', value: Math.floor(Math.random() * 100) },
                { name: '内蒙古', value: Math.floor(Math.random() * 100) }
              ]
            }]
          })
        }
      }
    }
  })
}

// 下载报告
const downloadReport = () => {
  ElMessage.success('报告已下载');
};

// 打印报告
const printReport = () => {
  ElMessage.info('正在打印报告');
};

// 刷新数据
const refreshData = () => {
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
  
  if (showPreview.value) {
    generateMockReport();
    setTimeout(renderCharts, 100);
  }
};

// 窗口大小变化时调整图表大小
const handleResize = () => {
  Object.values(chartInstances).forEach(chart => chart.resize())
}

// 生命周期钩子
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  // 销毁图表实例
  Object.values(chartInstances).forEach(chart => chart.dispose())
})
</script>

<style scoped>
.report-generation {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 报告摘要 */
.report-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 报告面板 */
.report-panels {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

/* 报告配置 */
.report-configuration {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 5px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

/* 表单元素适应容器宽度 */
.el-form {
  width: 100%;
}

.el-select, 
.el-cascader,
.el-date-picker,
.el-radio-group,
.el-checkbox-group {
  width: 100%;
}

/* 确保复选框正常显示 */
.el-checkbox {
  margin-right: 0;
  padding-right: 10px;
  white-space: nowrap;
}

/* 报告预览内容 */
.preview-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
}

.report-cover {
  text-align: center;
  padding: 30px 0;
}

.report-cover h1 {
  color: #e5e7eb;
  margin: 0;
  font-size: 28px;
}

.report-subtitle {
  font-size: 18px;
  color: #9ca3af;
  margin-top: 10px;
}

.report-date {
  margin-top: 20px;
  color: #6b7280;
}

.report-toc {
  margin: 20px 0;
}

.report-toc h2 {
  color: #e5e7eb;
  font-size: 20px;
  margin-bottom: 10px;
}

.report-section {
  margin: 30px 0;
}

.report-section h2 {
  color: #e5e7eb;
  font-size: 20px;
  margin-bottom: 15px;
}

.report-section p {
  color: #9ca3af;
  line-height: 1.6;
}

.section-chart {
  margin: 20px 0;
  background-color: rgba(31, 41, 55, 0.5);
  padding: 15px;
  border-radius: 8px;
}

/* 空预览状态 */
.empty-preview {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #9ca3af;
}

.empty-tip {
  margin-top: 20px;
  color: #6b7280;
  font-size: 14px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .report-panels {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style> 