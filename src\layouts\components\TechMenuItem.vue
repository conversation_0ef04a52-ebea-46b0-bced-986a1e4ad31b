<template>
  <div 
    class="tech-menu-item"
    :class="{ 'is-active': isActive, 'is-collapsed': collapsed }"
    @click="handleClick"
    @keydown.enter="handleClick"
    ref="menuItemRef"
    tabindex="0"
    role="menuitem"
    :aria-label="title"
    :aria-current="isActive ? 'page' : undefined"
  >
    <div class="menu-item-inner" :style="{ '--theme-color': themeColor }">
      <div class="glow-effect"></div>
      <div class="menu-item-content">
        <div class="icon-container">
          <el-icon class="menu-icon"><component :is="icon" /></el-icon>
          <div class="icon-ring"></div>
          <div class="pulse-ring" v-if="isActive"></div>
        </div>
        <span class="menu-item-title" v-if="!collapsed">{{ title }}</span>
      </div>
      <div class="tech-line left-line"></div>
      <div class="tech-line right-line"></div>
    </div>
    
    <!-- 活动状态指示器 -->
    <div class="active-indicator" v-if="isActive">
      <div class="indicator-dot"></div>
      <div class="indicator-line"></div>
    </div>
    
    <!-- 悬浮提示 -->
    <el-tooltip
      v-if="collapsed"
      effect="dark"
      :content="title"
      placement="right"
      :offset="8"
    >
      <div class="tooltip-trigger"></div>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { gsap } from 'gsap';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: 'Document'
  },
  route: {
    type: String,
    required: true
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  themeColor: {
    type: String,
    default: '#10b981'
  }
});

const router = useRouter();
const route = useRoute();
const menuItemRef = ref<HTMLElement | null>(null);
const wasActive = ref(false);

const isActive = computed(() => {
  return route.path === props.route || route.path.startsWith(`${props.route}/`);
});

// Store animation timelines for cleanup
const animations = ref<gsap.core.Timeline[]>([]);

watch(isActive, (newVal, oldVal) => {
  if (newVal && !oldVal) {
    // 菜单项从非活动变为活动状态时的动画
    animateActivation();
    wasActive.value = true;
  } else if (!newVal && wasActive.value) {
    // 从活动状态变为非活动状态时的动画
    wasActive.value = false;
  }
});

const handleClick = () => {
  router.push(props.route);
};

const animateActivation = () => {
  if (!menuItemRef.value) return;
  
  const activeIndicator = menuItemRef.value.querySelector('.active-indicator');
  const glowEffect = menuItemRef.value.querySelector('.glow-effect');
  
  if (activeIndicator && glowEffect) {
    // 使用GSAP timeline以便于清理
    const tl = gsap.timeline();
    
    tl.fromTo(
      activeIndicator,
      { opacity: 0, scale: 0.8 },
      { opacity: 1, scale: 1, duration: 0.5, ease: 'elastic.out(1, 0.3)' }
    );
    
    tl.to(glowEffect, {
      opacity: 0.8,
      duration: 0.3,
      yoyo: true,
      repeat: 1
    }, "-=0.3");
    
    animations.value.push(tl);
  }
};

// 在组件挂载时检查是否处于活动状态
onMounted(() => {
  if (isActive.value) {
    wasActive.value = true;
    
    // 为已激活的菜单项添加初始动画
    setTimeout(() => {
      if (!menuItemRef.value) return;
      
      const activeIndicator = menuItemRef.value.querySelector('.active-indicator');
      if (activeIndicator) {
        const tl = gsap.timeline();
        tl.from(activeIndicator, {
          opacity: 0, 
          x: -10,
          duration: 0.5,
          ease: 'power2.out'
        });
        animations.value.push(tl);
      }
    }, 300);
  }
});

// 清理GSAP动画，避免内存泄漏
onBeforeUnmount(() => {
  animations.value.forEach(animation => animation.kill());
  animations.value = [];
});
</script>

<style scoped>
.tech-menu-item {
  position: relative;
  height: 50px;
  margin: 8px 16px;
  border-radius: 10px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  outline: none;
}

/* 焦点状态样式 */
.tech-menu-item:focus-visible {
  box-shadow: 0 0 0 2px var(--theme-color, #10b981);
  outline: none;
}

.menu-item-inner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: rgba(31, 41, 55, 0.7);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

/* 悬停效果 */
.tech-menu-item:hover .menu-item-inner {
  background-color: rgba(55, 65, 81, 0.7);
  border-color: var(--theme-color, #10b981);
  transform: translateY(-2px);
}

.tech-menu-item.is-active .menu-item-inner {
  background-color: rgba(59, 130, 246, 0.15);
  border-color: var(--theme-color, #10b981);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
}

.glow-effect {
  position: absolute;
  top: -30%;
  left: -30%;
  width: 160%;
  height: 160%;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.2) 0%,
    rgba(59, 130, 246, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  will-change: opacity;
}

.tech-menu-item.is-active .glow-effect {
  opacity: 0.4;
}

.tech-menu-item:hover .glow-effect {
  opacity: 0.2;
}

/* 菜单项内容 */
.menu-item-content {
  padding: 0 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.icon-container {
  position: relative;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon {
  font-size: 18px;
  color: #d1d5db;
  transition: color 0.3s ease;
  z-index: 2;
}

.tech-menu-item.is-active .menu-icon {
  color: var(--theme-color, #10b981);
}

.icon-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid rgba(75, 85, 99, 0.5);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.tech-menu-item.is-active .icon-ring {
  border-color: var(--theme-color, #10b981);
  box-shadow: 0 0 8px var(--theme-color, #10b981);
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(59, 130, 246, 0.5);
  transform: translate(-50%, -50%);
  animation: pulse-animation 2s infinite;
  will-change: width, height, opacity;
}

@keyframes pulse-animation {
  0% {
    width: 24px;
    height: 24px;
    opacity: 0.7;
  }
  70% {
    width: 35px;
    height: 35px;
    opacity: 0;
  }
  100% {
    width: 24px;
    height: 24px;
    opacity: 0;
  }
}

.menu-item-title {
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tech-menu-item.is-active .menu-item-title {
  color: #ffffff;
  letter-spacing: 1px;
}

.tech-menu-item:hover .menu-item-title {
  letter-spacing: 1px;
}

/* 科技线条 */
.tech-line {
  position: absolute;
  height: 1px;
  background-color: var(--theme-color, #10b981);
  opacity: 0;
  transition: all 0.5s ease;
}

.left-line {
  bottom: 10px;
  left: 0;
  width: 20%;
}

.right-line {
  top: 10px;
  right: 0;
  width: 30%;
}

.tech-menu-item.is-active .tech-line {
  opacity: 0.6;
}

.tech-menu-item:hover .tech-line {
  opacity: 0.4;
}

/* 活动状态指示器 */
.active-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  left: -2px;
  display: flex;
  align-items: center;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--theme-color, #10b981);
  box-shadow: 0 0 8px var(--theme-color, #10b981);
}

.indicator-line {
  height: 60%;
  width: 2px;
  background: linear-gradient(to bottom, var(--theme-color, #10b981), transparent);
  margin-left: -5px;
}

/* 悬浮提示 */
.tooltip-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

/* 折叠状态 */
.tech-menu-item.is-collapsed .menu-item-inner {
  padding: 0;
  justify-content: center;
}

.tech-menu-item.is-collapsed .menu-item-content {
  justify-content: center;
  padding: 0;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .tech-menu-item {
    margin: 6px 12px;
  }
}

@media (hover: none) {
  .tech-menu-item:not(.is-active):hover .menu-item-inner {
    transform: none;
  }
}
</style> 