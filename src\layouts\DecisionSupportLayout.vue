<template>
  <BaseLayout
    title="决策支持中心"
    theme="decisionSupport"
    themeColor="#10b981"
    moduleIcon="Cpu"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="专家知识库"
        icon="Document"
        route="/decision-support/expert-knowledge"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="智能决策树推荐"
        icon="Share"
        route="/decision-support/decision-tree"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="应急预案库管理"
        icon="Bell"
        route="/decision-support/emergency-plans"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="成本效益分析仪表盘"
        icon="PieChart"
        route="/decision-support/cost-benefit"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="绿色防控认证管理"
        icon="DocumentChecked"
        route="/decision-support/green-certification"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
    </template>

    <template #header-actions>
      <div class="decision-actions">
        <div class="ai-suggestion">
          <el-icon><Connection /></el-icon>
          <span>AI推荐: </span>
          <span class="suggestion-text">根据当前数据建议使用生物防治策略</span>
        </div>
        <TechActionButton
          type="primary"
          size="small"
          icon="ChatLineRound"
          text="咨询专家"
          @click="consultExpert"
        />
      </div>
    </template>

    <router-view />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Document,
  Share,
  Bell,
  PieChart,
  DocumentChecked,
  ArrowLeft,
  Cpu,
  Connection,
  ChatLineRound
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'
import { getModuleTheme } from './components/layoutConfig'

const route = useRoute()
const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 咨询专家
const consultExpert = () => {
  ElMessage({
    message: '专家系统：正在连接智能专家系统，请稍候...',
    type: 'info',
    duration: 3000
  })

  // 这里可以添加实际的专家系统连接逻辑
  setTimeout(() => {
    router.push('/decision-support/expert-chat')
  }, 1000)
}

// 组件挂载
onMounted(() => {
  // 监听BaseLayout事件
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<style scoped>
.decision-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.ai-suggestion {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(16, 185, 129, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  color: #d1d5db;
  font-size: 14px;
  transition: all 0.3s ease;
}

.ai-suggestion:hover {
  background: rgba(16, 185, 129, 0.2);
  transform: translateY(-2px);
}

.suggestion-text {
  color: #ffffff;
  font-weight: 500;
  white-space: nowrap;
}

@media (max-width: 1024px) {
  .decision-actions {
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .ai-suggestion {
    display: none;
  }
}
</style>
