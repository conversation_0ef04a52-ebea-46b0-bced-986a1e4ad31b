<!--
  ResponsiveContainer.vue
  响应式容器组件 - 提供智能布局和滚动管理
  支持不同屏幕尺寸的自适应布局
-->
<template>
  <div
    ref="containerRef"
    class="responsive-container"
    :class="containerClasses"
    :style="containerStyles"
  >
    <!-- 主要内容区域 -->
    <div class="container-content" :class="contentClasses">
      <slot />
    </div>

    <!-- 滚动指示器 -->
    <div v-if="showScrollIndicator && hasScroll" class="scroll-indicator">
      <div class="scroll-thumb" :style="scrollThumbStyle"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useResponsiveLayout } from '../composables/useResponsiveLayout';

// Props定义
interface Props {
  // 滚动配置
  enableScroll?: boolean;
  scrollDirection?: 'vertical' | 'horizontal' | 'both';
  showScrollIndicator?: boolean;

  // 布局配置
  minHeight?: string;
  maxHeight?: string;
  padding?: string;

  // 响应式配置
  mobileBreakpoint?: number;
  tabletBreakpoint?: number;

  // 性能配置
  debounceDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  enableScroll: true,
  scrollDirection: 'vertical',
  showScrollIndicator: true,
  minHeight: '100vh',
  maxHeight: 'none',
  padding: '0',
  mobileBreakpoint: 768,
  tabletBreakpoint: 1024,
  debounceDelay: 150
});

// 使用响应式布局管理
const {
  screenSize,
  isMobile,
  isTablet,
  isDesktop,
  windowWidth,
  windowHeight
} = useResponsiveLayout({
  mobileBreakpoint: props.mobileBreakpoint,
  tabletBreakpoint: props.tabletBreakpoint,
  debounceDelay: props.debounceDelay
});

// 模板引用
const containerRef = ref<HTMLDivElement>();

// 滚动状态
const scrollTop = ref(0);
const scrollLeft = ref(0);
const scrollHeight = ref(0);
const scrollWidth = ref(0);
const clientHeight = ref(0);
const clientWidth = ref(0);

// 计算属性
const hasScroll = computed(() => {
  if (props.scrollDirection === 'vertical') {
    return scrollHeight.value > clientHeight.value;
  } else if (props.scrollDirection === 'horizontal') {
    return scrollWidth.value > clientWidth.value;
  } else {
    return scrollHeight.value > clientHeight.value || scrollWidth.value > clientWidth.value;
  }
});

const containerClasses = computed(() => ({
  'responsive-container--mobile': isMobile.value,
  'responsive-container--tablet': isTablet.value,
  'responsive-container--desktop': isDesktop.value,
  'responsive-container--scroll-vertical': props.enableScroll && props.scrollDirection === 'vertical',
  'responsive-container--scroll-horizontal': props.enableScroll && props.scrollDirection === 'horizontal',
  'responsive-container--scroll-both': props.enableScroll && props.scrollDirection === 'both',
  'responsive-container--has-scroll': hasScroll.value
}));

const contentClasses = computed(() => ({
  'container-content--mobile': isMobile.value,
  'container-content--tablet': isTablet.value,
  'container-content--desktop': isDesktop.value
}));

const containerStyles = computed(() => ({
  minHeight: props.minHeight,
  maxHeight: props.maxHeight,
  padding: props.padding
}));

const scrollThumbStyle = computed(() => {
  if (props.scrollDirection === 'vertical') {
    const thumbHeight = Math.max((clientHeight.value / scrollHeight.value) * 100, 10);
    const thumbTop = (scrollTop.value / (scrollHeight.value - clientHeight.value)) * (100 - thumbHeight);
    return {
      height: `${thumbHeight}%`,
      top: `${thumbTop}%`,
      width: '4px',
      right: '2px'
    };
  } else {
    const thumbWidth = Math.max((clientWidth.value / scrollWidth.value) * 100, 10);
    const thumbLeft = (scrollLeft.value / (scrollWidth.value - clientWidth.value)) * (100 - thumbWidth);
    return {
      width: `${thumbWidth}%`,
      left: `${thumbLeft}%`,
      height: '4px',
      bottom: '2px'
    };
  }
});

// 更新滚动信息
const updateScrollInfo = () => {
  if (!containerRef.value) return;

  const element = containerRef.value.querySelector('.container-content') as HTMLElement;
  if (!element) return;

  scrollTop.value = element.scrollTop;
  scrollLeft.value = element.scrollLeft;
  scrollHeight.value = element.scrollHeight;
  scrollWidth.value = element.scrollWidth;
  clientHeight.value = element.clientHeight;
  clientWidth.value = element.clientWidth;
};

// 防抖的滚动处理
let scrollTimer: number | null = null;
const handleScroll = () => {
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }

  scrollTimer = window.setTimeout(() => {
    updateScrollInfo();
  }, 16); // 60fps
};

// 监听窗口尺寸变化
watch([windowWidth, windowHeight], async () => {
  await nextTick();
  updateScrollInfo();
});

// 生命周期
onMounted(async () => {
  // 等待多个渲染周期确保DOM完全稳定
  await nextTick();
  await new Promise(resolve => setTimeout(resolve, 50));

  if (containerRef.value && props.enableScroll) {
    const contentElement = containerRef.value.querySelector('.container-content');
    if (contentElement) {
      contentElement.addEventListener('scroll', handleScroll, { passive: true });
    }
  }

  // 使用IntersectionObserver确保容器可见后再更新
  if (containerRef.value) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          updateScrollInfo();
          observer.disconnect();
        }
      });
    });
    observer.observe(containerRef.value);
  }
});

onUnmounted(() => {
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }

  if (containerRef.value && props.enableScroll) {
    const contentElement = containerRef.value.querySelector('.container-content');
    if (contentElement) {
      contentElement.removeEventListener('scroll', handleScroll);
    }
  }
});

// 暴露方法给父组件
defineExpose({
  scrollTo: (options: ScrollToOptions) => {
    const contentElement = containerRef.value?.querySelector('.container-content');
    if (contentElement) {
      contentElement.scrollTo(options);
    }
  },
  scrollToTop: () => {
    const contentElement = containerRef.value?.querySelector('.container-content');
    if (contentElement) {
      contentElement.scrollTo({ top: 0, behavior: 'smooth' });
    }
  },
  getScrollInfo: () => ({
    scrollTop: scrollTop.value,
    scrollLeft: scrollLeft.value,
    scrollHeight: scrollHeight.value,
    scrollWidth: scrollWidth.value,
    clientHeight: clientHeight.value,
    clientWidth: clientWidth.value,
    hasScroll: hasScroll.value
  })
});
</script>

<style lang="scss" scoped>
@use './styles/common.scss' as common;

.responsive-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  height: auto;
  display: flex;
  flex-direction: column;
  background: transparent;

  // 基础滚动样式
  &--scroll-vertical .container-content {
    overflow-y: auto;
    overflow-x: hidden;
  }

  &--scroll-horizontal .container-content {
    overflow-x: auto;
    overflow-y: hidden;
  }

  &--scroll-both .container-content {
    overflow: auto;
  }

  // 响应式断点样式
  &--mobile {
    padding: common.$spacing-sm;

    .container-content {
      gap: common.$spacing-sm;
    }
  }

  &--tablet {
    padding: common.$spacing-md;

    .container-content {
      gap: common.$spacing-md;
    }
  }

  &--desktop {
    padding: common.$spacing-lg;

    .container-content {
      gap: common.$spacing-lg;
    }
  }
}

.container-content {
  flex: 1;
  width: 100%;
  min-height: 0; // 允许flex子项收缩
  position: relative;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(59, 130, 246, 0.5);
    }
  }

  &::-webkit-scrollbar-corner {
    background: rgba(255, 255, 255, 0.05);
  }

  // 移动端优化
  &--mobile {
    // 隐藏滚动条但保持滚动功能
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  // 平板端优化
  &--tablet {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
  }
}

// 滚动指示器
.scroll-indicator {
  position: absolute;
  z-index: 10;
  pointer-events: none;

  .scroll-thumb {
    position: absolute;
    background: rgba(59, 130, 246, 0.6);
    border-radius: 2px;
    transition: all 0.2s ease;
    box-shadow: 0 0 4px rgba(59, 130, 246, 0.3);
  }
}

// 响应式媒体查询
@media (max-width: common.$breakpoint-sm) {
  .responsive-container {
    &--desktop {
      padding: common.$spacing-sm;
    }
  }
}

@media (max-width: common.$breakpoint-md) {
  .responsive-container {
    &--desktop {
      padding: common.$spacing-md;
    }
  }
}

// 触摸设备优化
@media (hover: none) and (pointer: coarse) {
  .container-content {
    // 增强触摸滚动体验
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .container-content {
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.8);
    }
  }

  .scroll-indicator .scroll-thumb {
    background: rgba(255, 255, 255, 0.8);
  }
}

// 减少动画偏好支持
@media (prefers-reduced-motion: reduce) {
  .container-content,
  .scroll-indicator .scroll-thumb {
    transition: none;
  }
}
</style>
