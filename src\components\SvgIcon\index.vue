<template>
  <div class="svg-icon" :style="iconStyle" :class="className">
    <svg v-if="isIconLoaded" class="icon" aria-hidden="true">
      <use :xlink:href="iconName"></use>
    </svg>
    <span v-else class="fallback-icon" :style="{ fontSize: `${size}px` }">
      <el-icon>
        <component :is="fallbackIcon" />
      </el-icon>
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import { Monitor, Warning, InfoFilled, Cpu, Connection, Camera } from '@element-plus/icons-vue';
import { hasIcon, loadIcon } from './svgLoader';

// 定义组件属性
const props = defineProps({
  name: {
    type: String,
    required: true
  },
  size: {
    type: [Number, String],
    default: 16
  },
  color: {
    type: String,
    default: ''
  },
  className: {
    type: String,
    default: ''
  }
});

// 图标加载状态
const isIconLoaded = ref(false);

// 计算图标名称
const iconName = computed(() => {
  return `#icon-${props.name}`;
});

// 计算样式
const iconStyle = computed(() => {
  const style: Record<string, string> = {
    width: `${props.size}px`,
    height: `${props.size}px`,
  };
  
  if (props.color) {
    style.color = props.color;
  }
  
  return style;
});

// 根据图标名称获取备用图标组件
const fallbackIcon = computed(() => {
  // 设备图标
  if (props.name.includes('device')) {
    if (props.name.includes('camera')) return Camera;
    if (props.name.includes('sensor')) return Cpu;
    if (props.name.includes('gateway')) return Connection;
    return Monitor;
  }
  
  // 警报图标
  if (props.name.includes('alert')) {
    if (props.name.includes('critical')) return Warning;
    if (props.name.includes('warning')) return Warning;
    return InfoFilled;
  }
  
  // 默认图标
  return Monitor;
});

// 加载图标
const loadSvgIcon = async () => {
  // 检查图标是否已存在
  if (hasIcon(props.name)) {
    isIconLoaded.value = true;
    return;
  }
  
  try {
    // 尝试加载图标
    const success = await loadIcon(props.name);
    isIconLoaded.value = success;
    
    if (!success) {
      console.warn(`Icon "${props.name}" could not be loaded, using fallback icon.`);
    }
  } catch (error) {
    console.error(`Error loading icon "${props.name}":`, error);
    isIconLoaded.value = false;
  }
};

// 监听name属性变化
watch(() => props.name, loadSvgIcon);

// 组件挂载时加载图标
onMounted(loadSvgIcon);
</script>

<style scoped>
.svg-icon {
  display: inline-block;
  vertical-align: middle;
  fill: currentColor;
}

.icon {
  width: 100%;
  height: 100%;
}

.fallback-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
</style> 