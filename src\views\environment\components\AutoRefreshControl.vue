<template>
  <div class="auto-refresh-control">
    <div class="refresh-label">{{ label }}</div>
    <div class="refresh-controls">
      <el-switch
        v-model="isAutoRefresh"
        @change="handleAutoRefreshChange"
        :active-text="autoRefreshText"
      />
      <el-tooltip content="立即刷新" placement="top">
        <el-button
          type="primary"
          circle
          size="small"
          @click="handleManualRefresh"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { Refresh } from '@element-plus/icons-vue'

const props = defineProps({
  label: {
    type: String,
    default: '自动刷新'
  },
  interval: {
    type: Number,
    default: 30
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh'])

const isAutoRefresh = ref(false)
let refreshTimer: number | null = null

const autoRefreshText = computed(() => {
  return isAutoRefresh.value ? `每${props.interval}秒刷新` : '自动刷新已关闭'
})

const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  refreshTimer = window.setInterval(() => {
    emit('refresh')
  }, props.interval * 1000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const handleManualRefresh = () => {
  emit('refresh')
}

// 组件销毁时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.auto-refresh-control {
  display: flex;
  align-items: center;
}

.refresh-label {
  margin-right: 10px;
  color: #d1d5db;
  font-size: 14px;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style> 