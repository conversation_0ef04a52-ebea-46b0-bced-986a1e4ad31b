<!-- 
  PesticideCard.vue
  农药卡片组件，用于展示单个农药信息
  包括名称、规格、库存等信息
-->
<template>
  <div class="pesticide-card" :class="{ 'low-stock': isLowStock }">
    <div class="card-header">
      <div class="pesticide-name">{{ name }}</div>
      <div class="pesticide-status">
        <StatusIndicator 
          :type="stockStatusType" 
          :label="stockStatusLabel" 
          size="small" 
        />
      </div>
    </div>
    
    <div class="card-content">
      <div class="pesticide-info">
        <div class="info-item">
          <span class="info-label">登记号:</span>
          <span class="info-value">{{ registrationNumber }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">规格:</span>
          <span class="info-value">{{ specification }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">有效期至:</span>
          <span class="info-value">{{ expiryDate }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">库存量:</span>
          <span class="info-value" :class="{ 'warning-text': isLowStock }">
            {{ stockQuantity }} {{ unit }}
          </span>
        </div>
      </div>
      
      <div class="pesticide-props">
        <el-tag size="small" effect="dark" :type="toxicityTagType">{{ toxicityLevel }}</el-tag>
        <el-tag size="small" effect="dark" type="info">{{ formulation }}</el-tag>
      </div>
    </div>
    
    <div class="card-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import StatusIndicator from './StatusIndicator.vue';

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  registrationNumber: {
    type: String,
    default: ''
  },
  specification: {
    type: String,
    default: ''
  },
  stockQuantity: {
    type: Number,
    default: 0
  },
  unit: {
    type: String,
    default: '千克'
  },
  lowStockWarning: {
    type: Number,
    default: 10
  },
  expiryDate: {
    type: String,
    default: ''
  },
  toxicityLevel: {
    type: String,
    default: '低毒'
  },
  formulation: {
    type: String,
    default: '可湿性粉剂'
  }
});

// 是否库存不足
const isLowStock = computed(() => {
  return props.stockQuantity <= props.lowStockWarning;
});

// 库存状态类型
const stockStatusType = computed(() => {
  if (props.stockQuantity <= 0) return 'error';
  if (isLowStock.value) return 'warning';
  return 'success';
});

// 库存状态标签
const stockStatusLabel = computed(() => {
  if (props.stockQuantity <= 0) return '无库存';
  if (isLowStock.value) return '库存不足';
  return '库存充足';
});

// 毒性标签类型
const toxicityTagType = computed(() => {
  const toxicityMap: Record<string, string> = {
    '低毒': 'success',
    '中等毒性': 'warning',
    '高毒': 'danger',
    '剧毒': 'danger'
  };
  return toxicityMap[props.toxicityLevel] || 'info';
});
</script>

<style scoped>
.pesticide-card {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #3b4863;
  position: relative;
}

.pesticide-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.pesticide-card.low-stock {
  border-color: #ef4444;
}

.card-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3b4863;
}

.pesticide-name {
  font-size: 18px;
  font-weight: 600;
  color: #e5e7eb;
}

.card-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pesticide-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.info-label {
  color: #9ca3af;
}

.info-value {
  color: #e5e7eb;
  font-weight: 500;
}

.warning-text {
  color: #ef4444;
}

.pesticide-props {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.card-footer {
  padding: 12px 16px;
  border-top: 1px solid #3b4863;
  background-color: rgba(31, 41, 55, 0.7);
}
</style> 