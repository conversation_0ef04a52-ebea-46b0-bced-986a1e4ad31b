<!-- 
  MicroclimateMonitoring.vue
  微气候监测矩阵模块
  实时监控农田微气候数据，包括温度、湿度、光照和雨量等关键指标
-->
<template>
  <div class="microclimate-monitoring">
    <!-- 页面标题 -->
    <PageHeader
      title="智慧农场微气候监测矩阵"
      description="实时监控农田微气候数据，包括温度、湿度、光照和雨量等关键指标"
      icon="Cloudy"
    >
      <template #actions>
        <div class="status-summary">
          <div v-for="indicator in keyIndicators" :key="indicator.name" class="summary-item">
            <span class="summary-value">{{ indicator.value }}<span class="unit">{{ indicator.unit }}</span></span>
            <span class="summary-label">{{ indicator.name }}</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 数据面板区域 -->
    <div class="data-panels">
      <!-- 实时数据面板 -->
      <DataPanel title="微气候实时趋势">
        <template #actions>
          <TimeIntervalSelector
            v-model="timeInterval"
            label="时间范围:"
            @change="handleTimeIntervalChange"
          />
          <AutoRefreshControl 
            label="数据刷新:" 
            :interval="30" 
            :loading="loading.realtimeData"
            @refresh="refreshMainChart"
          />
        </template>
        <div class="chart-wrapper">
          <div v-if="loading.realtimeData" class="chart-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载数据中...</span>
          </div>
          <div v-if="chartError" class="chart-error-message">
            <el-alert
              :title="chartError"
              type="error"
              show-icon
              :closable="false"
            />
            <el-button 
              type="primary" 
              size="small" 
              @click="retryInitMainChart" 
              class="retry-button"
            >
              重试
            </el-button>
          </div>
          <div 
            class="chart-container" 
            ref="mainChartContainer"
          ></div>
        </div>
      </DataPanel>
      
      <!-- 历史数据面板 -->
      <DataPanel title="历史数据记录">
        <template #actions>
          <div class="history-filter">
            <el-date-picker
              v-model="currentDate"
              type="date"
              placeholder="选择日期"
              :disabled-date="disableFutureDates"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="loadHistoryData"
            />
          </div>
        </template>
        <div class="history-table-container">
          <el-table
            :data="historyData"
            style="width: 100%"
            height="200"
            :border="true"
            stripe
            v-loading="loading.historyData"
          >
            <el-table-column prop="timestamp" label="时间" width="180" />
            <el-table-column prop="temperature" label="温度(°C)" />
            <el-table-column prop="humidity" label="湿度(%)" />
            <el-table-column prop="illuminance" label="光照(lux)" />
            <el-table-column prop="rainfall" label="雨量(mm)" />
          </el-table>
          <div class="pagination-container">
            <el-pagination
              v-if="historyTotal > 0"
              background
              layout="prev, pager, next"
              :total="historyTotal"
              :page-size="pageSize"
              :current-page="currentPage"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </DataPanel>
      
      <!-- 微气候热力图面板 -->
      <DataPanel title="微气候热力图">
        <template #actions>
          <HeatmapTypeSelector
            v-model="selectedDataType"
            :options="dataTypeOptions"
            label="数据类型:"
            @change="changeHeatmapDataType"
          />
        </template>
        <div class="chart-wrapper">
          <div v-if="loading.heatmapData" class="chart-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载热力图数据中...</span>
          </div>
          <div v-if="heatmapError" class="chart-error-message">
            <el-alert
              :title="heatmapError"
              type="error"
              show-icon
              :closable="false"
            />
            <el-button 
              type="primary" 
              size="small" 
              @click="retryInitHeatmapChart" 
              class="retry-button"
            >
              重试
            </el-button>
          </div>
          <div 
            class="heatmap-container" 
            ref="heatmapContainer"
          ></div>
        </div>
      </DataPanel>
      
      <!-- 多日对比面板 -->
      <DataPanel title="微气候多日对比">
        <template #actions>
          <div class="date-selector">
            <el-date-picker
              v-model="selectedDates"
              type="dates"
              placeholder="选择多个日期"
              :disabled-date="disableFutureDates"
              @change="handleDatesChange"
            />
          </div>
          <el-button 
            type="primary" 
            size="small" 
            @click="refreshComparisonCharts" 
            :loading="loading.comparisonData"
          >
            刷新对比图表
          </el-button>
        </template>
        <div class="chart-wrapper">
          <div v-if="loading.comparisonData" class="chart-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载对比数据中...</span>
          </div>
          <div v-if="comparisonChartError" class="chart-error-message">
            <el-alert
              :title="comparisonChartError"
              type="error"
              show-icon
              :closable="false"
            />
            <el-button 
              type="primary" 
              size="small" 
              @click="retryInitComparisonCharts" 
              class="retry-button"
            >
              重试
            </el-button>
          </div>
          <div v-if="selectedDates.length === 0" class="no-dates-selected">
            <el-empty description="请选择要对比的日期" />
          </div>
          <div class="multi-chart-container" v-else>
            <div class="chart-item" v-for="(item, index) in comparisonCharts" :key="index">
              <div class="chart-title">{{ item.title }}</div>
              <div class="chart-content" :id="`comparison-chart-${index}`"></div>
            </div>
          </div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="数据健康" />
        <StatusIndicator type="normal" label="监测中" />
        <StatusIndicator type="warning" label="阈值提醒" />
      </div>
      <div class="refresh-info">
        <data-sync-status
          :status="syncStatus"
          :progress="syncProgress"
          :reason="syncDelayReason"
          :troubleshooting-guide="troubleshootingGuide"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import { 
  Loading, 
  Cloudy, 
  RefreshRight, 
  Calendar, 
  Warning 
} from '@element-plus/icons-vue';

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue';
import DataPanel from '../DeviceManagement/components/DataPanel.vue';
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue';
import HeatmapTypeSelector from './components/HeatmapTypeSelector.vue';
import DataSyncStatus from './components/DataSyncStatus.vue';
import AutoRefreshControl from './components/AutoRefreshControl.vue';
import TimeIntervalSelector from './components/TimeIntervalSelector.vue';

// 导入API请求函数
import { 
  fetchRealtimeMicroclimateData, 
  fetchKeyIndicators, 
  fetchHistoryData, 
  fetchComparisonData,
  fetchHeatmapData,
  fetchSyncStatus
} from '@/api/microclimate';

// 导入工具函数
import { formatDateTime, formatCustomDate } from '@/utils/date';

// 类型定义
interface HistoryRecord {
  timestamp: string;
  temperature: number;
  humidity: number;
  illuminance: number;
  rainfall: number;
}

interface KeyIndicator {
  name: string;
  value: string;
  unit: string;
}

// 状态和引用
const timeInterval = ref('24h'); // 默认显示24小时数据
const selectedDates = ref<Date[]>([]);
const currentDate = ref(formatCustomDate(new Date(), 'YYYY-MM-DD'));
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref({
  indicators: false,
  realtimeData: false,
  historyData: false,
  comparisonData: false,
  heatmapData: false
});

// 图表容器引用
const mainChartContainer = ref<HTMLElement | null>(null);
const heatmapContainer = ref<HTMLElement | null>(null);

// 调试信息
const chartInitialized = ref(false);
const chartError = ref('');
const comparisonChartError = ref('');
const comparisonChartsInitialized = ref(false);
const heatmapError = ref('');

// 数据状态
const keyIndicators = ref<KeyIndicator[]>([
  { name: '当前温度', value: '--', unit: '°C' },
  { name: '当前湿度', value: '--', unit: '%' },
  { name: '光照强度', value: '--', unit: 'lux' },
  { name: '今日雨量', value: '--', unit: 'mm' }
]);

const historyData = ref<HistoryRecord[]>([]);
const historyTotal = ref(0);
const selectedDataType = ref('temperature');
const dataTypeOptions = [
  { label: '温度', value: 'temperature' },
  { label: '湿度', value: 'humidity' },
  { label: '光照', value: 'illuminance' },
  { label: '雨量', value: 'rainfall' }
];

const syncStatus = ref<'normal' | 'warning' | 'error'>('normal');
const syncProgress = ref(100);
const syncDelayReason = ref('');
const troubleshootingGuide = ref([
  '检查传感器网络连接',
  '确认传感器电源供应正常',
  '重启数据采集设备',
  '联系技术支持团队'
]);

// 图表实例
let mainChart: echarts.ECharts | null = null;
let heatmapChart: echarts.ECharts | null = null;
let comparisonChartList: echarts.ECharts[] = [];

// 多日对比图表配置
const comparisonCharts = reactive([
  { title: '温度 (°C)', key: 'temperature', color: '#ef4444' },
  { title: '湿度 (%)', key: 'humidity', color: '#3b82f6' },
  { title: '光照 (lux)', key: 'illuminance', color: '#fbbf24' },
  { title: '雨量 (mm)', key: 'rainfall', color: '#10b981' }
]);

// 禁用未来日期
const disableFutureDates = (date: Date) => {
  return date > new Date();
};

// 处理时间间隔变化
const handleTimeIntervalChange = () => {
  refreshMainChart();
};

// 处理多日对比日期变化
const handleDatesChange = () => {
  refreshComparisonCharts();
};

// 加载关键指标数据
const loadKeyIndicators = async () => {
  loading.value.indicators = true;
  try {
    const { data } = await fetchKeyIndicators();
    keyIndicators.value = data;
  } catch (error) {
    console.error('加载关键指标失败:', error);
  } finally {
    loading.value.indicators = false;
  }
};

// 初始化主图表
const initMainChart = async () => {
  if (!mainChartContainer.value) {
    console.error('主图表容器不存在');
    chartError.value = '主图表容器不存在';
    return;
  }
  
  loading.value.realtimeData = true;
  try {
    const { data } = await fetchRealtimeMicroclimateData(timeInterval.value);
    
    // 打印数据，帮助调试
    console.log('获取到的实时数据:', data);
    
    // 确保在初始化ECharts实例前先销毁旧的实例
    if (mainChart) {
      mainChart.dispose();
    }
    
    // 确保DOM已经渲染完成
    await nextTick();
    
    // 检查容器尺寸
    const containerWidth = mainChartContainer.value.clientWidth;
    const containerHeight = mainChartContainer.value.clientHeight;
    console.log('主图表容器尺寸:', containerWidth, 'x', containerHeight);
    
    if (containerWidth === 0 || containerHeight === 0) {
      console.error('主图表容器尺寸为0，无法初始化图表');
      chartError.value = '主图表容器尺寸为0，无法初始化图表';
      return;
    }
    
    // 使用 as HTMLElement 来解决类型问题
    mainChart = echarts.init(mainChartContainer.value as HTMLElement);
    
    const option = {
      grid: {
        top: 40,
        right: 40,
        bottom: 40,
        left: 60
      },
      legend: {
        data: ['温度', '湿度', '光照', '雨量'],
        textStyle: {
          color: '#d1d5db'
        },
        top: 10
      },
      xAxis: {
        type: 'category',
        data: data.timestamps,
        axisLabel: {
          color: '#d1d5db'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '温度/湿度',
          position: 'left',
          axisLabel: {
            color: '#d1d5db',
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              color: '#374151'
            }
          }
        },
        {
          type: 'value',
          name: '光照',
          position: 'right',
          offset: 0,
          axisLabel: {
            color: '#d1d5db',
            formatter: '{value} lux'
          },
          splitLine: {
            show: false
          }
        },
        {
          type: 'value',
          name: '雨量',
          position: 'right',
          offset: 60,
          axisLabel: {
            color: '#d1d5db',
            formatter: '{value} mm'
          },
          splitLine: {
            show: false
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      series: [
        {
          name: '温度',
          type: 'line',
          data: data.temperature,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#ef4444'
          },
          lineStyle: {
            width: 2,
            color: '#ef4444'
          }
        },
        {
          name: '湿度',
          type: 'line',
          data: data.humidity,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#3b82f6'
          },
          lineStyle: {
            width: 2,
            color: '#3b82f6'
          }
        },
        {
          name: '光照',
          type: 'line',
          yAxisIndex: 1,
          data: data.illuminance,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#fbbf24'
          },
          lineStyle: {
            width: 2,
            color: '#fbbf24'
          }
        },
        {
          name: '雨量',
          type: 'bar',
          yAxisIndex: 2,
          data: data.rainfall,
          itemStyle: {
            color: '#10b981'
          }
        }
      ]
    };
    
    mainChart.setOption(option);
    chartInitialized.value = true;
    chartError.value = '';
    console.log('主图表初始化成功');
  } catch (error) {
    console.error('初始化主图表失败:', error);
    chartError.value = `初始化主图表失败: ${error instanceof Error ? error.message : String(error)}`;
  } finally {
    loading.value.realtimeData = false;
  }
};

// 重试初始化主图表
const retryInitMainChart = async () => {
  chartError.value = '';
  await initMainChart();
};

// 刷新主图表
const refreshMainChart = async () => {
  if (!mainChart) {
    console.error('主图表实例不存在，尝试重新初始化');
    await initMainChart();
    return;
  }
  
  loading.value.realtimeData = true;
  try {
    const { data } = await fetchRealtimeMicroclimateData(timeInterval.value);
    
    mainChart.setOption({
      xAxis: {
        data: data.timestamps
      },
      series: [
        {
          name: '温度',
          data: data.temperature
        },
        {
          name: '湿度',
          data: data.humidity
        },
        {
          name: '光照',
          data: data.illuminance
        },
        {
          name: '雨量',
          data: data.rainfall
        }
      ]
    });
  } catch (error) {
    console.error('刷新主图表失败:', error);
    ElMessage.error('刷新数据失败，请重试');
  } finally {
    loading.value.realtimeData = false;
  }
};

// 加载历史数据
const loadHistoryData = async () => {
  loading.value.historyData = true;
  try {
    const { data } = await fetchHistoryData(currentDate.value, currentPage.value, pageSize.value);
    historyData.value = data.records;
    historyTotal.value = data.total;
    
    // 在加载历史数据后初始化热力图
    await nextTick();
    initHeatmapChart();
  } catch (error) {
    console.error('加载历史数据失败:', error);
    ElMessage.error('加载历史数据失败，请重试');
  } finally {
    loading.value.historyData = false;
  }
};

// 处理历史数据页面变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadHistoryData();
};

// 初始化热力图
const initHeatmapChart = async () => {
  if (!heatmapContainer.value) {
    console.error('热力图容器不存在');
    heatmapError.value = '热力图容器不存在';
    return;
  }
  
  loading.value.heatmapData = true;
  heatmapError.value = '';
  
  try {
    // 检查容器尺寸
    await nextTick();
    const containerWidth = heatmapContainer.value.clientWidth;
    const containerHeight = heatmapContainer.value.clientHeight;
    console.log('热力图容器尺寸:', containerWidth, 'x', containerHeight);
    
    if (containerWidth === 0 || containerHeight < 200) {
      console.warn('热力图容器尺寸不足，调整高度');
      // 确保容器有足够的高度
      heatmapContainer.value.style.height = '300px';
      await nextTick();
    }
    
    const { data } = await fetchHeatmapData(selectedDataType.value);
    console.log('获取到的热力图数据:', data);
    
    // 确保在初始化ECharts实例前先销毁旧的实例
    if (heatmapChart) {
      heatmapChart.dispose();
    }
    
    // 使用 as HTMLElement 来解决类型问题
    heatmapChart = echarts.init(heatmapContainer.value as HTMLElement);
    
    // 如果没有数据，显示空状态
    if (!data || data.length === 0) {
      heatmapChart.setOption({
        title: {
          text: '暂无热力图数据',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#d1d5db',
            fontSize: 14
          }
        }
      });
      return;
    }
    
    // 准备热力图数据
    const days = [...new Set(data.map(item => item.day))];
    const hours = [...new Set(data.map(item => item.hour))].sort((a, b) => parseInt(a) - parseInt(b));
    
    const heatmapData = data.map(item => {
      return [parseInt(item.hour), days.indexOf(item.day), item.value];
    });
    
    let visualMapConfig = {};
    if (selectedDataType.value === 'temperature') {
      visualMapConfig = {
        min: 15,
        max: 35,
        inRange: {
          color: ['#0f172a', '#3b82f6', '#fbbf24', '#ef4444']
        }
      };
    } else if (selectedDataType.value === 'humidity') {
      visualMapConfig = {
        min: 30,
        max: 90,
        inRange: {
          color: ['#f9fafb', '#93c5fd', '#3b82f6', '#1e3a8a']
        }
      };
    } else if (selectedDataType.value === 'illuminance') {
      visualMapConfig = {
        min: 0,
        max: 20000,
        inRange: {
          color: ['#0f172a', '#fef3c7', '#fbbf24', '#f59e0b']
        }
      };
    } else {
      visualMapConfig = {
        min: 0,
        max: 5,
        inRange: {
          color: ['#f9fafb', '#d1fae5', '#34d399', '#059669']
        }
      };
    }
    
    const option = {
      title: {
        text: `近一周${selectedDataType.value === 'temperature' ? '温度' : 
          selectedDataType.value === 'humidity' ? '湿度' : 
          selectedDataType.value === 'illuminance' ? '光照' : '雨量'}分布热力图`,
        left: 'center',
        textStyle: {
          color: '#d1d5db'
        }
      },
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          const unit = selectedDataType.value === 'temperature' ? '°C' : 
            selectedDataType.value === 'humidity' ? '%' : 
            selectedDataType.value === 'illuminance' ? 'lux' : 'mm';
          return `${days[params.data[1]]} ${hours[params.data[0]]}点<br>${
            selectedDataType.value === 'temperature' ? '温度' : 
            selectedDataType.value === 'humidity' ? '湿度' : 
            selectedDataType.value === 'illuminance' ? '光照' : '雨量'
          }: ${params.data[2]}${unit}`;
        }
      },
      grid: {
        top: 60,
        bottom: 60,
        left: 60,
        right: 60
      },
      xAxis: {
        type: 'category',
        data: hours,
        splitArea: {
          show: true
        },
        axisLabel: {
          color: '#d1d5db'
        }
      },
      yAxis: {
        type: 'category',
        data: days,
        splitArea: {
          show: true
        },
        axisLabel: {
          color: '#d1d5db'
        }
      },
      visualMap: {
        ...visualMapConfig,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: 10,
        textStyle: {
          color: '#d1d5db'
        }
      },
      series: [
        {
          name: selectedDataType.value === 'temperature' ? '温度' : 
            selectedDataType.value === 'humidity' ? '湿度' : 
            selectedDataType.value === 'illuminance' ? '光照' : '雨量',
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: false
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    
    heatmapChart.setOption(option);
    console.log('热力图初始化成功');
  } catch (error) {
    console.error('初始化热力图失败:', error);
    heatmapError.value = `初始化热力图失败: ${error instanceof Error ? error.message : String(error)}`;
  } finally {
    loading.value.heatmapData = false;
  }
};

// 重试初始化热力图
const retryInitHeatmapChart = async () => {
  heatmapError.value = '';
  await initHeatmapChart();
};

// 切换热力图数据类型
const changeHeatmapDataType = (type: string) => {
  selectedDataType.value = type;
  if (heatmapChart) {
    initHeatmapChart();
  }
};

// 初始化多日对比图表
const initComparisonCharts = async () => {
  if (selectedDates.value.length === 0) {
    console.log('没有选择日期，无法初始化对比图表');
    return;
  }
  
  loading.value.comparisonData = true;
  comparisonChartError.value = '';
  
  try {
    // 将日期格式化为字符串
    const dateStrings = selectedDates.value.map(date => formatCustomDate(date, 'YYYY-MM-DD'));
    console.log('选择的日期:', dateStrings);
    
    const { data } = await fetchComparisonData(dateStrings);
    console.log('获取到的对比数据:', data);
    
    // 确保在初始化ECharts实例前先销毁旧的实例
    comparisonChartList.forEach(chart => chart?.dispose());
    comparisonChartList = [];
    
    // 确保DOM已经渲染完成
    await nextTick();
    
    // 使用ID选择器获取图表容器
    comparisonCharts.forEach((chartConfig, index) => {
      const containerId = `comparison-chart-${index}`;
      const container = document.getElementById(containerId);
      
      if (!container) {
        console.error(`找不到图表容器: ${containerId}`);
        return;
      }
      
      // 检查容器尺寸
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      console.log(`图表容器 ${containerId} 尺寸:`, containerWidth, 'x', containerHeight);
      
      if (containerWidth === 0 || containerHeight === 0) {
        console.error(`图表容器 ${containerId} 尺寸为0，无法初始化图表`);
        return;
      }
      
      // 初始化图表实例
      const chartInstance = echarts.init(container);
      comparisonChartList[index] = chartInstance;
      
      // 如果没有数据，显示空状态
      if (Object.keys(data).length === 0) {
        chartInstance.setOption({
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#d1d5db',
              fontSize: 14
            }
          }
        });
        return;
      }
      
      // 准备数据
      const series = Object.keys(data).map(date => {
        return {
          name: date,
          type: 'line',
          data: data[date][chartConfig.key as keyof typeof data[typeof date]],
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            width: 2,
            color: chartConfig.color
          },
          itemStyle: {
            color: chartConfig.color
          }
        };
      });
      
      const option = {
        grid: {
          top: 60,
          right: 20,
          bottom: 40,
          left: 60
        },
        title: {
          text: chartConfig.title,
          left: 'center',
          textStyle: {
            color: '#d1d5db',
            fontSize: 14
          }
        },
        legend: {
          data: Object.keys(data),
          textStyle: {
            color: '#d1d5db'
          },
          top: 30
        },
        xAxis: {
          type: 'category',
          data: data[Object.keys(data)[0]].timestamps,
          axisLabel: {
            color: '#d1d5db',
            interval: 3
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#d1d5db'
          },
          splitLine: {
            lineStyle: {
              color: '#374151'
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        series: series
      };
      
      chartInstance.setOption(option);
    });
    
    comparisonChartsInitialized.value = true;
    console.log('多日对比图表初始化成功');
  } catch (error) {
    console.error('初始化多日对比图表失败:', error);
    comparisonChartError.value = `初始化多日对比图表失败: ${error instanceof Error ? error.message : String(error)}`;
  } finally {
    loading.value.comparisonData = false;
  }
};

// 重试初始化多日对比图表
const retryInitComparisonCharts = async () => {
  comparisonChartError.value = '';
  await initComparisonCharts();
};

// 刷新多日对比图表
const refreshComparisonCharts = () => {
  initComparisonCharts();
};

// 获取同步状态
const getSyncStatus = async () => {
  try {
    const { data } = await fetchSyncStatus();
    syncStatus.value = data.status;
    syncProgress.value = data.progress;
    syncDelayReason.value = data.reason || '';
  } catch (error) {
    console.error('获取同步状态失败:', error);
  }
};

// 模拟数据同步状态变化
let syncStatusInterval: number | null = null;
const simulateSyncStatus = () => {
  syncStatusInterval = window.setInterval(() => {
    getSyncStatus();
  }, 10000); // 每10秒获取一次状态
};

// 窗口大小变化时重新调整图表
const handleResize = () => {
  mainChart?.resize();
  heatmapChart?.resize();
  comparisonChartList.forEach(chart => chart?.resize());
};

// 监听标签页变化
watch(() => [mainChartContainer.value, heatmapContainer.value], () => {
  nextTick(() => {
    if (mainChartContainer.value && !chartInitialized.value) {
      initMainChart();
    }
    if (heatmapContainer.value && !heatmapChart) {
      initHeatmapChart();
    }
  });
});

// 组件挂载
onMounted(async () => {
  loadKeyIndicators();
  
  // 确保DOM已经渲染完成
  await nextTick();
  
  initMainChart();
  loadHistoryData();
  getSyncStatus();
  simulateSyncStatus();
  
  // 如果没有选择日期，默认选择最近3天
  if (selectedDates.value.length === 0) {
    const dates: Date[] = [];
    for (let i = 2; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(date);
    }
    selectedDates.value = dates;
    nextTick(() => initComparisonCharts());
  }
  
  window.addEventListener('resize', handleResize);
});

// 组件销毁
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  
  mainChart?.dispose();
  heatmapChart?.dispose();
  comparisonChartList.forEach(chart => chart?.dispose());
  
  if (syncStatusInterval) {
    clearInterval(syncStatusInterval);
  }
});
</script>

<style scoped>
.microclimate-monitoring {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.unit {
  font-size: 14px;
  margin-left: 2px;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 数据面板网格 */
.data-panels {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

/* 图表容器 */
.chart-wrapper {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-container, 
.heatmap-container {
  flex: 1;
  width: 100%;
  min-height: 200px;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  background-color: rgba(31, 41, 55, 0.3);
}

/* 历史数据表格 */
.history-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

/* 多日对比图表 */
.multi-chart-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
  height: 100%;
}

.chart-item {
  display: flex;
  flex-direction: column;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 6px;
  padding: 8px;
}

.chart-content {
  flex: 1;
  min-height: 100px;
}

.chart-title {
  font-size: 12px;
  color: #9ca3af;
  text-align: center;
  margin-bottom: 5px;
}

/* 加载和错误状态 */
.chart-loading, 
.chart-error-message {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(31, 41, 55, 0.7);
  z-index: 10;
  border-radius: 6px;
}

.chart-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #3b82f6;
}

.no-dates-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #9ca3af;
}

.retry-button {
  margin-top: 10px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .data-panels {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .summary-item {
    min-width: 80px;
  }
}
</style>