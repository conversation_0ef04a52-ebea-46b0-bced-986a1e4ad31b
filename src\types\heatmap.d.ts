declare module 'heatmap.js' {
  interface HeatmapConfiguration {
    container: HTMLElement | string;
    radius?: number;
    maxOpacity?: number;
    minOpacity?: number;
    blur?: number;
    gradient?: Record<string, string>;
    backgroundColor?: string;
    opacity?: number;
    width?: number;
    height?: number;
  }

  interface HeatmapData {
    max: number;
    data: Array<{
      x: number;
      y: number;
      value: number;
      [key: string]: any;
    }>;
  }

  interface Heatmap {
    setData: (data: HeatmapData) => void;
    addData: (point: HeatmapData['data'][0]) => void;
    getDataURL: () => string;
    getValueAt: (point: { x: number; y: number }) => number | null;
    repaint: () => void;
  }

  function create(config: HeatmapConfiguration): Heatmap;

  export default {
    create
  };
} 