<template>
  <BaseLayout
    title="虫害大数据分析平台"
    theme="pestAnalysis"
    themeColor="#10b981"
    moduleIcon="DataAnalysis"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="平台概览"
        icon="DataAnalysis"
        route="/pest-analysis/dashboard"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="多维度虫害数据库"
        icon="DataLine"
        route="/pest-analysis/pest-database"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="AI图像识别日志追溯"
        icon="PictureFilled"
        route="/pest-analysis/ai-recognition-log"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="虫害爆发趋势预测"
        icon="TrendCharts"
        route="/pest-analysis/outbreak-prediction"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="消杀效果对比分析"
        icon="DataBoard"
        route="/pest-analysis/control-comparison"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="智能用药推荐系统"
        icon="Guide"
        route="/pest-analysis/smart-recommendation"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="数据报告自动生成"
        icon="Document"
        route="/pest-analysis/report-generation"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
    </template>

    <template #header-actions>
      <div class="analysis-actions">
        <TechActionButton
          type="warning"
          size="small"
          icon="Refresh"
          text="刷新数据"
          @click="refreshData"
        />
        <TechActionButton
          type="primary"
          size="small"
          icon="Download"
          text="导出报告"
          @click="exportReport"
        />
      </div>
    </template>

    <router-view />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  DataAnalysis,
  DataLine,
  PictureFilled,
  TrendCharts,
  DataBoard,
  Guide,
  Document,
  HomeFilled,
  Refresh,
  Download
} from '@element-plus/icons-vue'
import { ElMessage, ElNotification } from 'element-plus'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'
import { getModuleTheme } from './components/layoutConfig'

const route = useRoute()
const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 刷新数据
const refreshData = () => {
  ElMessage({
    message: '虫害分析数据已更新',
    type: 'success',
    duration: 3000
  })
}

// 导出报告
const exportReport = () => {
  ElMessage({
    message: '正在生成虫害分析报告，请稍候...',
    type: 'info',
    duration: 3000
  })

  // 这里可以添加实际的报告导出逻辑
  setTimeout(() => {
    ElMessage({
      message: '虫害分析报告已生成，可在下载中心查看',
      type: 'success',
      duration: 3000
    })
  }, 2000)
}

onMounted(() => {
  // 如果直接访问父路由，重定向到默认子路由
  if (route.path === '/pest-analysis') {
    router.push('/pest-analysis/dashboard')
  }

  // 监听BaseLayout事件
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<style scoped>
.analysis-actions {
  display: flex;
  gap: 10px;
}

@media (max-width: 768px) {
  .analysis-actions {
    gap: 5px;
  }
}
</style>
