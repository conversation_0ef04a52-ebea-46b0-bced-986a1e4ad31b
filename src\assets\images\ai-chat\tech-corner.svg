<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50">
  <defs>
    <linearGradient id="cornerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00e676" />
      <stop offset="100%" stop-color="#00c853" />
    </linearGradient>
  </defs>
  
  <!-- 角落装饰 -->
  <path d="M0,0 L40,0 L40,5 L5,5 L5,40 L0,40 Z" fill="none" stroke="url(#cornerGradient)" stroke-width="1" />
  <path d="M10,0 L10,10 L0,10" fill="none" stroke="url(#cornerGradient)" stroke-width="0.7" />
  <path d="M20,0 L20,20 L0,20" fill="none" stroke="url(#cornerGradient)" stroke-width="0.5" />
  
  <!-- 节点 -->
  <circle cx="10" cy="10" r="2" fill="#00e676" />
  <circle cx="20" cy="20" r="1.5" fill="#00e676" />
  <circle cx="5" cy="5" r="1" fill="#00e676" />
  <circle cx="30" cy="0" r="1" fill="#00e676" />
  <circle cx="0" cy="30" r="1" fill="#00e676" />
  
  <!-- 装饰线 -->
  <line x1="0" y1="15" x2="15" y2="15" stroke="url(#cornerGradient)" stroke-width="0.5" stroke-dasharray="1,1" />
  <line x1="15" y1="0" x2="15" y2="15" stroke="url(#cornerGradient)" stroke-width="0.5" stroke-dasharray="1,1" />
</svg> 