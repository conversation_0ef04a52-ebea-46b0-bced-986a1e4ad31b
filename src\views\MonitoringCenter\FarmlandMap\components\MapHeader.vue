<template>
  <div class="bigscreen-header">
    <div class="header-title">数字农田监控中心</div>
    <div class="header-info">
      <div class="info-item">
        <span class="info-label">时间：</span>
        <span class="info-value">{{ currentDateTime }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">天气：</span>
        <span class="info-value">{{ weather }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { format } from 'date-fns';
import { useWeatherData } from '../composables/useWeatherData';

// 获取天气数据
const { weather } = useWeatherData();

// 当前时间
const currentDateTime = ref(format(new Date(), 'yyyy-MM-dd HH:mm:ss'));

// 定时器ID
let datetimeTimer: number | null = null;

// 更新当前时间
const updateDateTime = () => {
  currentDateTime.value = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
};

// 组件挂载时的操作
onMounted(() => {
  // 设置时间更新定时器
  datetimeTimer = window.setInterval(updateDateTime, 1000);
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (datetimeTimer !== null) {
    window.clearInterval(datetimeTimer);
  }
});
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as vars;

.bigscreen-header {
  z-index: 10;
  width: 100%;
  height: vars.$header-height;
  @include vars.flex-between;
  background: vars.$background-header;
  border-bottom: 1px solid vars.$primary-color-light;
  padding: 0 20px;
  box-shadow: 0 2px 15px vars.$shadow-color;
  @include vars.glass-effect;

  .header-title {
    font-size: 24px;
    font-weight: bold;
    color: vars.$primary-color;
    text-shadow: 0 0 10px rgba(0, 255, 170, 0.5);
    letter-spacing: 2px;
  }

  .header-info {
    display: flex;
    gap: 20px;

    .info-item {
      display: flex;
      align-items: center;
      
      .info-label {
        color: vars.$text-secondary;
        margin-right: 5px;
      }
      
      .info-value {
        color: vars.$text-light;
        font-weight: bold;
      }
    }
  }
}
</style> 