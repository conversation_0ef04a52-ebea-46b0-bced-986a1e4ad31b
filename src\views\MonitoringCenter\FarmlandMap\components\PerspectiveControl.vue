<template>
  <div class="perspective-control map-card card-compact" :class="{ 'minimized': isMinimized }">
    <div v-if="!isMinimized" class="card-header">
      <h3 class="card-title">
        <el-icon class="title-icon"><View /></el-icon>
        视角控制
      </h3>
      <div class="card-actions">
        <el-button type="text" size="small" @click="isMinimized = true" title="最小化">
          <el-icon><Minus /></el-icon>
        </el-button>
      </div>
    </div>
    
    <div v-if="!isMinimized" class="card-body">
      <div class="control-section">
        <div class="section-title">视角倾斜</div>
        <el-slider v-model="tiltValue" :min="0" :max="60" :step="1" show-stops></el-slider>
      </div>
      
      <div class="control-section">
        <div class="section-title">视角旋转</div>
        <div class="rotation-control">
          <el-button circle icon="ArrowLeft" @click="rotate('left')"></el-button>
          <el-button type="primary" @click="resetRotation">重置</el-button>
          <el-button circle icon="ArrowRight" @click="rotate('right')"></el-button>
        </div>
      </div>
      
      <div class="control-section">
        <div class="section-title">预设视角</div>
        <div class="preset-buttons">
          <el-button size="small" @click="applyPreset('top')">俯视图</el-button>
          <el-button size="small" @click="applyPreset('oblique')">斜视图</el-button>
        </div>
      </div>
    </div>
    
    <!-- 最小化时只显示图标 -->
    <div v-if="isMinimized" class="minimized-card" @click="isMinimized = false">
      <el-icon><View /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { View, Minus, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import { useMapInteraction } from '../composables/useMapInteraction';

// 修改初始值，设置为默认最小化
const isMinimized = ref(true);
const tiltValue = ref(30);
const rotationValue = ref(0);

// 获取地图交互功能
const { viewMode, changeViewMode } = useMapInteraction('farmlandCanvas');

// 计算属性，用于双向绑定
const viewModeValue = computed({
  get: () => viewMode.value,
  set: (value) => changeViewMode(value)
});

// 处理视图模式变化
const handleViewModeChange = (value: number) => {
  changeViewMode(value);
};

// 旋转视角
const rotate = (direction) => {
  if (direction === 'left') {
    rotationValue.value = (rotationValue.value - 15) % 360;
  } else {
    rotationValue.value = (rotationValue.value + 15) % 360;
  }
  // 这里可以添加实际旋转地图的逻辑
};

// 重置旋转
const resetRotation = () => {
  rotationValue.value = 0;
  // 这里可以添加重置地图旋转的逻辑
};

// 应用预设视角
const applyPreset = (preset) => {
  if (preset === 'top') {
    tiltValue.value = 0;
  } else if (preset === 'oblique') {
    tiltValue.value = 45;
  }
  // 这里可以添加应用预设视角的逻辑
};
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as vars;

.perspective-control {
  width: 280px;
  transition: all 0.3s ease;
  
  &.minimized {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    border: 1px solid rgba(0, 255, 170, 0.25);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    
    &:hover {
      border-color: rgba(0, 255, 170, 0.5);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3),
                  0 0 5px rgba(0, 255, 170, 0.3);
    }
  }
  
  .minimized-card {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    .el-icon {
      font-size: 20px;
      color: vars.$primary-color;
      filter: drop-shadow(0 0 3px rgba(0, 255, 170, 0.5));
    }
  }
  
  .control-section {
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 14px;
      color: vars.$text-secondary;
      margin-bottom: 8px;
      font-weight: 500;
    }
  }
  
  .rotation-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .preset-buttons {
    display: flex;
    gap: 10px;
  }
}
</style> 