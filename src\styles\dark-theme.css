/* 暗黑主题全局样式
 * 此文件包含暗黑主题下的全局样式定义
 * 主要针对Element Plus组件的样式覆盖
 */

/* 下拉菜单暗黑主题适配 */
.dark-select-dropdown {
  background-color: #1a1a1a !important;
  border: 1px solid #333 !important;
  color: #e0e0e0 !important;
}

.dark-select-dropdown .el-select-dropdown__item {
  color: #e0e0e0 !important;
}

.dark-select-dropdown .el-select-dropdown__item.hover,
.dark-select-dropdown .el-select-dropdown__item:hover {
  background-color: #2c2c2c !important;
}

.dark-select-dropdown .el-select-dropdown__item.selected {
  background-color: #0a4983 !important;
  color: #ffffff !important;
}

/* Popper暗黑主题样式 */
.el-popper.is-dark {
  background-color: #1a1a1a !important;
  border-color: #333 !important;
}

.el-popper.is-dark .el-popper__arrow::before {
  background-color: #1a1a1a !important;
  border-color: #333 !important;
} 