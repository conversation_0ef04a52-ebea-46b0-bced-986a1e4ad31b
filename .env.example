# 智慧农业系统环境变量配置示例
# 复制此文件为 .env.local 并根据实际环境修改配置值

# ===========================================
# 基础服务配置
# ===========================================

# API服务地址
VITE_API_BASE_URL=http://localhost:8080

# WebSocket服务地址
VITE_WEBSOCKET_BASE_URL=ws://localhost:8080

# ===========================================
# WebSocket路径配置
# ===========================================

# 机器狗位置追踪WebSocket路径
VITE_WEBSOCKET_ROBOT_LOCATION_PATH=/robot-location

# 机器狗控制WebSocket路径
VITE_WEBSOCKET_ROBOT_CONTROL_PATH=/robot-control

# 摄像头WebSocket路径
VITE_WEBSOCKET_CAMERA_PATH=/camera

# 基站WebSocket路径
VITE_WEBSOCKET_JIZHAN_PATH=/jizhan

# ===========================================
# WebSocket连接配置
# ===========================================

# WebSocket重连尝试次数
VITE_WEBSOCKET_RECONNECT_ATTEMPTS=5

# WebSocket重连间隔（毫秒）
VITE_WEBSOCKET_RECONNECT_INTERVAL=1000

# WebSocket心跳间隔（毫秒）
VITE_WEBSOCKET_HEARTBEAT_INTERVAL=10000

# ===========================================
# 设备配置
# ===========================================

# 机器狗IP地址
VITE_ROBOT_IP=***********

# 机器狗认证Token
VITE_ROBOT_TOKEN=your_robot_token_here

# 信号服务器端口
VITE_SIGNAL_SERVER_PORT=9002

# Python服务器端口
VITE_PYTHON_SERVER_PORT=5000

# ===========================================
# 视频流服务配置
# ===========================================

# 视频流服务主机地址
VITE_VIDEO_STREAM_HOST=***********

# 视频流服务端口
VITE_VIDEO_STREAM_PORT=8000

# ===========================================
# 机器狗控制配置
# ===========================================

# 机器狗移动速度（0.1-1.0）
VITE_ROBOT_CONTROL_MOVE_SPEED=0.5

# 机器狗旋转速度（0.1-1.0）
VITE_ROBOT_CONTROL_ROTATE_SPEED=0.5

# 机器狗控制心跳间隔（毫秒）
VITE_ROBOT_CONTROL_HEARTBEAT_INTERVAL=5000

# 是否启用键盘控制（true/false）
VITE_ROBOT_CONTROL_KEYBOARD_ENABLED=true

# ===========================================
# 摄像头直播墙配置
# ===========================================

# AI检测WebSocket URL
VITE_AI_DETECTION_WS_URL=ws://localhost:8080/ai-detection

# 摄像头直播墙WebSocket URL
VITE_CAMERA_WALL_WS_URL=ws://localhost:8080/camera-wall

# ===========================================
# 地图配置
# ===========================================

# 地图中心点经度
VITE_MAP_CENTER_LNG=116.404

# 地图中心点纬度
VITE_MAP_CENTER_LAT=39.915

# 地图默认缩放级别
VITE_MAP_DEFAULT_ZOOM=15

# 地图最小缩放级别
VITE_MAP_MIN_ZOOM=10

# 地图最大缩放级别
VITE_MAP_MAX_ZOOM=20

# ===========================================
# 业务配置
# ===========================================

# 模拟延迟（毫秒，开发环境使用）
VITE_SIMULATED_DELAY=1000

# 最大轨迹点数
VITE_MAX_TRACK_POINTS=1000

# 数据记录最大记录数
VITE_DATA_RECORDING_MAX_RECORDS=10000

# 数据记录保存间隔（秒）
VITE_DATA_RECORDING_SAVE_INTERVAL=5

# ===========================================
# 开发环境配置
# ===========================================

# 是否启用开发模式
VITE_DEV_MODE=true

# 是否启用调试日志
VITE_DEBUG_LOG=true

# 是否启用性能监控
VITE_PERFORMANCE_MONITOR=false

# ===========================================
# 生产环境配置示例
# ===========================================

# 生产环境API地址
# VITE_API_BASE_URL=https://your-production-api.com

# 生产环境WebSocket地址
# VITE_WEBSOCKET_BASE_URL=wss://your-production-websocket.com

# 生产环境机器狗IP
# VITE_ROBOT_IP=your.production.robot.ip

# 生产环境视频流地址
# VITE_VIDEO_STREAM_HOST=your.production.video.host
# VITE_VIDEO_STREAM_PORT=8000
