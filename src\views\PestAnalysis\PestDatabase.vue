<!-- 
  PestDatabase.vue
  多维度虫害数据库模块
  整合虫害种类、数量、分布等多维数据，形成全面的虫害数据库
-->
<template>
  <div class="pest-database">
    <!-- 页面标题 -->
    <PageHeader
      title="多维度虫害数据库"
      description="整合虫害种类、数量、分布等多维数据，形成全面的虫害数据库，方便查询分析"
      icon="DataLine"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ totalPestRecords }}</span>
            <span class="summary-label">虫害记录总数</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ totalPestTypes }}</span>
            <span class="summary-label">虫害种类数量</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 筛选器区域 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <template #header>
          <div class="filter-header">
            <el-icon><Filter /></el-icon>
            <span>数据筛选</span>
          </div>
        </template>
        <div class="filter-form">
          <el-form :inline="true" :model="filterForm">
            <el-form-item label="虫害种类">
              <el-select 
                v-model="filterForm.pestType" 
                placeholder="全部" 
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in pestTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="农田区域">
              <el-select 
                v-model="filterForm.region" 
                placeholder="全部" 
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in regionOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="危害程度">
              <el-select 
                v-model="filterForm.damageLevel" 
                placeholder="全部" 
                clearable
                style="width: 180px"
              >
                <el-option
                  v-for="item in damageLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 300px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleFilter">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="resetFilter">
                <el-icon><RefreshRight /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
    
    <!-- 数据展示区域 -->
    <div class="data-section">
      <el-row :gutter="20">
        <!-- 左侧虫害统计面板 -->
        <el-col :span="8">
          <div class="stats-panels">
            <DataPanel title="虫害分布热点区域">
              <div class="chart-container" ref="regionDistributionChart"></div>
            </DataPanel>
            
            <DataPanel title="虫害类型占比分析">
              <div class="chart-container" ref="pestTypeChart"></div>
            </DataPanel>
            
            <DataPanel title="近期虫害趋势">
              <div class="chart-container" ref="trendChart"></div>
            </DataPanel>
          </div>
        </el-col>
        
        <!-- 右侧虫害数据表格 -->
        <el-col :span="16">
          <el-card class="data-card">
            <template #header>
              <div class="data-header">
                <div>
                  <el-icon><List /></el-icon>
                  <span>虫害数据列表</span>
                </div>
                <div class="header-actions">
                  <el-button type="success" size="small" @click="exportData">
                    <el-icon><Download /></el-icon>
                    导出数据
                  </el-button>
                  <el-button type="primary" size="small" @click="refreshData">
                    <el-icon><Refresh /></el-icon>
                    刷新数据
                  </el-button>
                </div>
              </div>
            </template>
            
            <el-table
              :data="pestData"
              style="width: 100%"
              border
              stripe
              :header-cell-style="{ background: '#1f2937', color: '#e5e7eb' }"
              v-loading="loading"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="pestName" label="虫害名称" width="150" />
              <el-table-column prop="pestType" label="虫害类型" width="120" />
              <el-table-column prop="region" label="发现区域" width="150" />
              <el-table-column prop="count" label="数量" width="100" />
              <el-table-column prop="damageLevel" label="危害程度" width="120">
                <template #default="scope">
                  <el-tag :type="getDamageLevelType(scope.row.damageLevel)" effect="dark">
                    {{ scope.row.damageLevel }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="discoverTime" label="发现时间" width="180" />
              <el-table-column label="操作" fixed="right" width="150">
                <template #default="scope">
                  <el-button link type="primary" size="small" @click="viewDetail(scope.row)">
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                  <el-button link type="success" size="small" @click="showAnalysis(scope.row)">
                    <el-icon><DataAnalysis /></el-icon>
                    分析
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalRecords"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="数据完整" />
        <StatusIndicator type="normal" label="实时更新" />
        <StatusIndicator type="warning" label="危害预警" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  DataLine,
  Filter, 
  Search, 
  RefreshRight, 
  List, 
  Download, 
  Refresh,
  View,
  DataAnalysis
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import { useRouter } from 'vue-router';

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue';
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue';
import DataPanel from '../DeviceManagement/components/DataPanel.vue';

const router = useRouter();

// 数据统计
const totalPestRecords = ref(3245);
const totalPestTypes = ref(127);
const totalRecords = ref(3245);
const lastUpdateTime = ref(new Date());

// 筛选表单
const filterForm = reactive({
  pestType: '',
  region: '',
  damageLevel: '',
  dateRange: []
});

// 选项数据
const pestTypeOptions = [
  { value: 'lepidoptera', label: '鳞翅目害虫' },
  { value: 'coleoptera', label: '鞘翅目害虫' },
  { value: 'hemiptera', label: '半翅目害虫' },
  { value: 'diptera', label: '双翅目害虫' },
  { value: 'orthoptera', label: '直翅目害虫' }
];

const regionOptions = [
  { value: 'north', label: '北部农田' },
  { value: 'south', label: '南部农田' },
  { value: 'east', label: '东部农田' },
  { value: 'west', label: '西部农田' },
  { value: 'central', label: '中央农田' }
];

const damageLevelOptions = [
  { value: 'low', label: '轻微' },
  { value: 'medium', label: '中等' },
  { value: 'high', label: '严重' },
  { value: 'critical', label: '危急' }
];

// 表格数据
const pestData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);

// 图表引用
const regionDistributionChart = ref(null);
const pestTypeChart = ref(null);
const trendChart = ref(null);

// 图表实例
let regionChart = null;
let typeChart = null;
let trendChartInstance = null;

// 获取危害程度标签类型
const getDamageLevelType = (level) => {
  switch(level) {
    case '轻微': return 'info';
    case '中等': return 'warning';
    case '严重': return 'danger';
    case '危急': return 'error';
    default: return 'info';
  }
};

// 格式化时间
const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 处理筛选
const handleFilter = () => {
  ElMessage.success('应用筛选条件');
  loadPestData();
};

// 重置筛选
const resetFilter = () => {
  filterForm.pestType = '';
  filterForm.region = '';
  filterForm.damageLevel = '';
  filterForm.dateRange = [];
  ElMessage.info('已重置筛选条件');
  loadPestData();
};

// 查看详情
const viewDetail = (row) => {
  router.push(`/pest-analysis/pest-detail/${row.id}`);
};

// 显示分析
const showAnalysis = (row) => {
  ElMessage.success(`分析 ${row.pestName} 数据`);
};

// 导出数据
const exportData = () => {
  ElMessage.success('导出数据成功');
};

// 加载虫害数据
const loadPestData = () => {
  loading.value = true;
  
  // 模拟API调用延迟
  setTimeout(() => {
    // 模拟数据
    const mockData = [];
    for (let i = 1; i <= 100; i++) {
      mockData.push({
        id: i,
        pestName: ['稻飞虱', '玉米螟', '棉铃虫', '稻纵卷叶螟', '小麦蚜虫'][Math.floor(Math.random() * 5)],
        pestType: ['鳞翅目害虫', '鞘翅目害虫', '半翅目害虫', '双翅目害虫', '直翅目害虫'][Math.floor(Math.random() * 5)],
        region: ['北部农田', '南部农田', '东部农田', '西部农田', '中央农田'][Math.floor(Math.random() * 5)],
        count: Math.floor(Math.random() * 1000) + 100,
        damageLevel: ['轻微', '中等', '严重', '危急'][Math.floor(Math.random() * 4)],
        discoverTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toLocaleString()
      });
    }
    
    pestData.value = mockData.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
    loading.value = false;
    lastUpdateTime.value = new Date();
  }, 500);
};

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  loadPestData();
};

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadPestData();
};

// 刷新数据
const refreshData = () => {
  loadPestData();
  initCharts();
  ElMessage.success('数据已更新');
};

// 初始化区域分布图表
const initRegionDistributionChart = () => {
  if (regionDistributionChart.value) {
    regionChart = echarts.init(regionDistributionChart.value);
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          color: '#9ca3af'
        }
      },
      series: [
        {
          name: '区域分布',
          type: 'pie',
          radius: '70%',
          center: ['60%', '50%'],
          data: [
            { value: 1048, name: '北部农田' },
            { value: 735, name: '南部农田' },
            { value: 580, name: '东部农田' },
            { value: 484, name: '西部农田' },
            { value: 398, name: '中央农田' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            borderRadius: 5,
            borderColor: '#1f2937',
            borderWidth: 2
          },
          label: {
            color: '#e5e7eb'
          }
        }
      ]
    };
    
    regionChart.setOption(option);
  }
};

// 初始化虫害类型图表
const initPestTypeChart = () => {
  if (pestTypeChart.value) {
    typeChart = echarts.init(pestTypeChart.value);
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: ['鳞翅目', '鞘翅目', '半翅目', '双翅目', '直翅目'],
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            color: '#9ca3af'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            color: '#9ca3af'
          }
        }
      ],
      series: [
        {
          name: '虫害数量',
          type: 'bar',
          barWidth: '60%',
          data: [
            {
              value: 1200,
              itemStyle: { color: '#3b82f6' }
            },
            {
              value: 800,
              itemStyle: { color: '#10b981' }
            },
            {
              value: 600,
              itemStyle: { color: '#f59e0b' }
            },
            {
              value: 400,
              itemStyle: { color: '#ef4444' }
            },
            {
              value: 245,
              itemStyle: { color: '#8b5cf6' }
            }
          ]
        }
      ]
    };
    
    typeChart.setOption(option);
  }
};

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChart.value) {
    trendChartInstance = echarts.init(trendChart.value);
    
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['北部农田', '南部农田', '东部农田'],
        textStyle: {
          color: '#9ca3af'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        axisLabel: {
          color: '#9ca3af'
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#9ca3af'
        }
      },
      series: [
        {
          name: '北部农田',
          type: 'line',
          stack: 'Total',
          data: [120, 132, 101, 134, 90, 230, 210],
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#3b82f6'
          }
        },
        {
          name: '南部农田',
          type: 'line',
          stack: 'Total',
          data: [220, 182, 191, 234, 290, 330, 310],
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#10b981'
          }
        },
        {
          name: '东部农田',
          type: 'line',
          stack: 'Total',
          data: [150, 232, 201, 154, 190, 330, 410],
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#f59e0b'
          }
        }
      ]
    };
    
    trendChartInstance.setOption(option);
  }
};

// 初始化所有图表
const initCharts = () => {
  initRegionDistributionChart();
  initPestTypeChart();
  initTrendChart();
};

// 窗口大小变化时重新调整图表大小
window.addEventListener('resize', () => {
  regionChart?.resize();
  typeChart?.resize();
  trendChartInstance?.resize();
});

onMounted(() => {
  // 加载初始数据
  loadPestData();
  
  // 初始化图表
  initCharts();
});

onUnmounted(() => {
  // 清除图表实例
  regionChart?.dispose();
  typeChart?.dispose();
  trendChartInstance?.dispose();
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', () => {});
});
</script>

<style scoped>
.pest-database {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background-color: #1f2937;
  border: none;
  color: #e5e7eb;
}

.filter-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.filter-header .el-icon {
  margin-right: 8px;
  color: #3b82f6;
}

.filter-form {
  padding: 10px 0;
}

/* 数据展示区域 */
.data-section {
  flex: 1;
  margin-bottom: 20px;
}

.stats-panels {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.data-card {
  background-color: #1f2937;
  border: none;
  color: #e5e7eb;
  height: 100%;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.data-header .el-icon {
  margin-right: 8px;
  color: #3b82f6;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.chart-container {
  height: 200px;
  width: 100%;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .data-section .el-row {
    flex-direction: column;
  }
  
  .data-section .el-col {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
  }
  
  .stats-panels {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save
--> 