// 摄像头工作状态
export enum CameraStatus {
  NORMAL = 'normal',     // 工作正常
  OFFLINE = 'offline',   // 离线
  LOW_BATTERY = 'lowBattery' // 低电量预警
}

// 视频流状态
export enum StreamStatus {
  NORMAL = 'normal',     // 正常
  STUTTERING = 'stuttering', // 卡顿
  INTERRUPTED = 'interrupted' // 中断
}

// 布局类型
export enum LayoutType {
  LAYOUT_1X1 = '1x1',
  LAYOUT_2X2 = '2x2',
  LAYOUT_3X3 = '3x3',
  LAYOUT_4X4 = '4x4'
}

// 摄像头信息接口
export interface Camera {
  id: string;           // 摄像头ID
  name: string;         // 摄像头名称
  status: CameraStatus; // 工作状态
  streamUrl: string;    // 视频流地址
  streamStatus: StreamStatus; // 视频流状态
  lastUpdateTime?: Date; // 最后更新时间
  location?: string;    // 摄像头区域位置
}

// 布局配置接口
export interface LayoutConfig {
  type: LayoutType;     // 布局类型
  columns: number;      // 列数
  rows: number;         // 行数
}

// 轮播模式配置
export interface CarouselConfig {
  enabled: boolean;     // 是否启用
  interval: number;     // 轮播间隔（毫秒）
  currentIndex: number; // 当前显示的索引
  isPaused: boolean;    // 是否暂停
  showControls: boolean; // 是否显示控制按钮
  fadeEffect: boolean;  // 是否启用淡入淡出效果
} 