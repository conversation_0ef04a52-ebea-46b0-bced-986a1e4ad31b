import { ComponentPublicInstance } from 'vue'

declare module '@vue/runtime-core' {
  export interface ComponentCustomProperties {
    $on: (event: string, callback: Function) => void
    $emit: (event: string, ...args: any[]) => void
    $off: (event: string, callback?: Function) => void
  }
}

// 扩展组件实例类型，添加$on等方法
declare module 'vue' {
  interface ComponentPublicInstance {
    $on: (event: string, callback: Function) => void
    $emit: (event: string, ...args: any[]) => void
    $off: (event: string, callback?: Function) => void
  }
} 