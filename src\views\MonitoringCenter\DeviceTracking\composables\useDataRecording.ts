/**
 * 数据记录管理 Composable
 * 负责WebSocket数据的记录、存储和管理
 */

import { ref, reactive, computed, watch, onUnmounted, readonly } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import type { RobotLocationData } from '@/utils/websocketService'
import type { DataRecord, RecordingConfig, DataStatistics } from '../types'
import { DATA_RECORDING_CONFIG } from '@/config/business'

// 默认配置
const DEFAULT_CONFIG: RecordingConfig = {
  enabled: DATA_RECORDING_CONFIG.ENABLED,
  maxRecords: DATA_RECORDING_CONFIG.MAX_RECORDS,
  autoSave: DATA_RECORDING_CONFIG.AUTO_SAVE,
  saveInterval: DATA_RECORDING_CONFIG.SAVE_INTERVAL // 分钟自动保存
}

export function useDataRecording() {
  // 记录配置
  const config = reactive<RecordingConfig>({ ...DEFAULT_CONFIG })

  // 数据记录列表
  const records = ref<DataRecord[]>([])

  // 记录开始时间
  const recordingStartTime = ref<Date | null>(null)

  // 自动保存定时器
  let autoSaveTimer: number | null = null

  // 计算属性
  const isRecording = computed(() => config.enabled)

  const recordCount = computed(() => records.value.length)

  const recordingDuration = computed(() => {
    if (!recordingStartTime.value) return '0分钟'
    const now = new Date()
    const diff = now.getTime() - recordingStartTime.value.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`
    }
    return `${minutes}分钟`
  })

  // 数据统计
  const statistics = computed<DataStatistics>(() => {
    const deviceIds = new Set(records.value.map(r => r.data.tagId))
    const dataSize = (JSON.stringify(records.value).length / 1024).toFixed(2)

    let averageFrequency = 0
    if (recordingStartTime.value && records.value.length > 0) {
      const duration = (new Date().getTime() - recordingStartTime.value.getTime()) / 1000 / 60 // 分钟
      averageFrequency = duration > 0 ? Math.round(records.value.length / duration * 100) / 100 : 0
    }

    return {
      totalRecords: records.value.length,
      recordingDuration: recordingDuration.value,
      averageFrequency,
      deviceCount: deviceIds.size,
      dataSize: `${dataSize} KB`
    }
  })

  /**
   * 开始记录数据
   */
  const startRecording = () => {
    console.log('开始记录数据 - 当前状态:', {
      enabled: config.enabled,
      recordsCount: records.value.length
    })

    config.enabled = true
    recordingStartTime.value = new Date()

    console.log('记录已启用:', {
      enabled: config.enabled,
      startTime: recordingStartTime.value
    })

    // 启动自动保存
    if (config.autoSave) {
      startAutoSave()
    }

    ElNotification({
      title: '开始记录',
      message: 'WebSocket数据记录已启动',
      type: 'success'
    })
  }

  /**
   * 停止记录数据
   */
  const stopRecording = () => {
    config.enabled = false

    // 停止自动保存
    stopAutoSave()

    ElNotification({
      title: '停止记录',
      message: `已停止记录，共记录 ${records.value.length} 条数据`,
      type: 'info'
    })
  }

  /**
   * 切换记录状态
   */
  const toggleRecording = () => {
    if (config.enabled) {
      stopRecording()
    } else {
      startRecording()
    }
  }

  /**
   * 添加数据记录
   */
  const addRecord = (data: RobotLocationData, deviceName: string = '') => {
    console.log('addRecord 被调用:', {
      enabled: config.enabled,
      data: data,
      deviceName: deviceName,
      recordsCount: records.value.length
    })

    if (!config.enabled) {
      console.log('记录功能未启用，跳过记录')
      return
    }

    try {
      const record: DataRecord = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: data.timestamp,
        data: { ...data },
        deviceName: deviceName || `设备-${data.tagId}`,
        recordTime: new Date().toLocaleString('zh-CN')
      }

      records.value.push(record)
      console.log('成功添加记录:', record.id, '总记录数:', records.value.length)

      // 检查记录数量限制
      if (records.value.length > config.maxRecords) {
        const removed = records.value.splice(0, records.value.length - config.maxRecords)
        console.log(`已删除 ${removed.length} 条旧记录，保持在最大限制内`)
      }
    } catch (error) {
      console.error('添加记录时出错:', error)
    }
  }

  /**
   * 清空记录
   */
  const clearRecords = () => {
    records.value = []
    recordingStartTime.value = null

    ElMessage({
      message: '所有记录数据已清空',
      type: 'success'
    })
  }

  /**
   * 获取指定时间范围的记录
   */
  const getRecordsByTimeRange = (startTime: Date, endTime: Date): DataRecord[] => {
    return records.value.filter(record => {
      const recordTime = new Date(record.timestamp)
      return recordTime >= startTime && recordTime <= endTime
    })
  }

  /**
   * 获取指定设备的记录
   */
  const getRecordsByDevice = (tagId: number): DataRecord[] => {
    return records.value.filter(record => record.data.tagId === tagId)
  }

  /**
   * 启动自动保存
   */
  const startAutoSave = () => {
    if (autoSaveTimer) return

    autoSaveTimer = window.setInterval(() => {
      // 这里可以实现自动保存到本地存储的逻辑
      console.log(`自动保存: ${records.value.length} 条记录`)
    }, config.saveInterval * 60 * 1000)
  }

  /**
   * 停止自动保存
   */
  const stopAutoSave = () => {
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer)
      autoSaveTimer = null
    }
  }

  /**
   * 更新配置
   */
  const updateConfig = (newConfig: Partial<RecordingConfig>) => {
    Object.assign(config, newConfig)

    // 如果修改了自动保存设置，重新启动定时器
    if (config.enabled && config.autoSave) {
      stopAutoSave()
      startAutoSave()
    }
  }

  // 监听配置变化
  watch(() => config.autoSave, (newValue) => {
    if (config.enabled) {
      if (newValue) {
        startAutoSave()
      } else {
        stopAutoSave()
      }
    }
  })

  // 组件卸载时清理
  onUnmounted(() => {
    stopAutoSave()
  })

  return {
    // 状态
    config: readonly(config),
    records: readonly(records),
    isRecording,
    recordCount,
    recordingDuration,
    statistics,

    // 方法
    startRecording,
    stopRecording,
    toggleRecording,
    addRecord,
    clearRecords,
    getRecordsByTimeRange,
    getRecordsByDevice,
    updateConfig
  }
}
