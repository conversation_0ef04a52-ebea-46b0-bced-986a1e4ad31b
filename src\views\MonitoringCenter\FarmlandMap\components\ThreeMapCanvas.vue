<template>
  <div id="threeMapContainer" class="three-map-container">
    <!-- 加载指示器 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">
          <span>加载农田模型</span>
          <div class="loading-progress">
            <div class="progress-bar" :style="{ width: `${loadingProgress}%` }"></div>
            <span class="progress-text">{{ loadingProgress.toFixed(0) }}%</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="error-message">{{ errorMessage }}</div>
        <el-button type="primary" @click="retryLoading">重试</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { threeService } from '../services/threeService';
import { useDeviceData } from '../composables/useDeviceData';

// 状态变量
const loading = ref(true);
const loadingProgress = ref(0);
const error = ref(false);
const errorMessage = ref('');

// 从设备数据composable获取设备数据
const { devices, selectDevice } = useDeviceData();

// 监听设备数据变化
watch(devices, (newDevices) => {
  // 更新3D场景中的设备位置和状态
  threeService.updateDevices(newDevices);
}, { deep: true });

// 初始化3D场景
const initThreeScene = async () => {
  try {
    loading.value = true;
    error.value = false;
    
    // 初始化场景
    const initialized = await threeService.initScene('threeMapContainer');
    if (!initialized) {
      throw new Error('初始化3D场景失败');
    }
    
    // 设置回调函数
    threeService.setOnDeviceClickCallback((deviceId) => {
      console.log('设备被点击:', deviceId);
      selectDevice(deviceId);
    });
    
    threeService.setOnLoadProgressCallback((progress) => {
      loadingProgress.value = progress;
    });
    
    threeService.setOnLoadErrorCallback((error) => {
      showError('加载模型失败: ' + error.message);
    });
    
    threeService.setOnLoadCompleteCallback(() => {
      loading.value = false;
    });
    
    // 加载农田模型
    await threeService.loadFarmlandModel('/models/farmland.glb');
    
    // 预加载设备模型
    console.log('开始预加载设备模型...');
    await threeService.preloadDeviceModels();
    console.log('设备模型预加载完成');
    
    // 更新设备位置
    threeService.updateDevices(devices.value);
    
    // 加载完成
    loading.value = false;
  } catch (err: any) {
    showError('初始化失败: ' + (err.message || '未知错误'));
  }
};

// 显示错误
const showError = (message: string) => {
  error.value = true;
  errorMessage.value = message;
  loading.value = false;
  console.error(message);
};

// 重试加载
const retryLoading = () => {
  error.value = false;
  initThreeScene();
};

// 组件挂载时初始化
onMounted(() => {
  console.log('ThreeMapCanvas组件已挂载，初始化3D场景');
  initThreeScene();
});

// 组件卸载时释放资源
onUnmounted(() => {
  console.log('ThreeMapCanvas组件将卸载，释放资源');
  threeService.dispose();
});

// 暴露方法供父组件调用
defineExpose({
  focusOnDevice: (deviceId: string) => {
    threeService.focusOnDevice(deviceId);
  }
});
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as vars;

.three-map-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: vars.$background-dark;
  border-radius: vars.$border-radius;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%);
    pointer-events: none;
    z-index: 1;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 10, 30, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  backdrop-filter: blur(5px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(43, 255, 150, 0.3);
  border-top-color: vars.$primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 18px;
  margin-bottom: 10px;
  text-align: center;
}

.loading-progress {
  width: 200px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
  
  .progress-bar {
    height: 100%;
    background-color: vars.$primary-color;
    transition: width 0.3s ease;
  }
  
  .progress-text {
    position: absolute;
    top: 8px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(30, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  backdrop-filter: blur(5px);
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  padding: 30px;
  background-color: rgba(50, 0, 0, 0.5);
  border-radius: 10px;
  border: 1px solid rgba(255, 100, 100, 0.3);
  max-width: 80%;
}

.error-icon {
  font-size: 40px;
  color: #ff5252;
  margin-bottom: 20px;
}

.error-message {
  font-size: 16px;
  margin-bottom: 20px;
  text-align: center;
  white-space: pre-wrap;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 设备标签样式 */
:global(.device-label) {
  pointer-events: none;
  transition: transform 0.2s ease;
  
  .device-label-content {
    background-color: rgba(0, 21, 65, 0.85);
    border-radius: 4px;
    padding: 5px 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(43, 255, 150, 0.3);
    transform: translateY(-5px);
    transition: all 0.2s ease;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid rgba(0, 21, 65, 0.85);
    }
  }
  
  .device-label-name {
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 2px;
    white-space: nowrap;
  }
  
  .device-label-status {
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    text-transform: uppercase;
    
    &.online {
      background-color: rgba(0, 255, 0, 0.2);
      color: #00ff00;
    }
    
    &.standby {
      background-color: rgba(255, 255, 0, 0.2);
      color: #ffff00;
    }
    
    &.offline {
      background-color: rgba(255, 0, 0, 0.2);
      color: #ff0000;
    }
  }
}
</style> 