# 智慧农场环境监测模块

本模块包含智慧农场环境监测相关的功能组件，用于实时监控和分析农田环境数据。

## 组件列表

### 土壤墒情监测图层 (SoilMoistureMonitoring.vue)

实时监控农田土壤水分含量和墒情分布，提供灌溉决策支持。

#### 主要功能

- **墒情地图可视化**：通过三维地图展示农田土壤墒情分布
- **监测点列表**：显示所有土壤墒情监测点及其状态
- **详细数据查看**：点击监测点可查看详细数据和历史趋势
- **智能灌溉决策**：基于监测数据提供灌溉建议和决策支持

#### 使用方法

1. 通过图层控制器选择需要显示的图层（墒情图层、地形图层、管理区图层）
2. 使用地图工具栏中的按钮切换视图（俯视图、侧视图、三维视图）
3. 在监测点列表中点击任意监测点，查看其详细信息和历史数据
4. 查看灌溉决策支持区域，获取当前的灌溉建议

#### 技术依赖

- ECharts：用于数据可视化
- Element Plus：UI组件库
- 三维地图（实际项目中需要集成具体的地图库，如Cesium、Mapbox等）

## 页面截图

（此处可插入页面截图）

## 开发指南

- 在添加新的环境监测组件时，请保持与现有组件风格一致
- 共用组件请放置在 `components` 目录下
- 对于需要在多个环境监测页面使用的功能，请抽取为独立的工具函数或组件

## 数据流向

1. 数据来源：传感器网络 → 后端API → 前端组件
2. 数据处理：原始数据 → 数据转换/计算 → 数据可视化
3. 决策支持：数据分析 → 决策算法 → 推荐结果展示 