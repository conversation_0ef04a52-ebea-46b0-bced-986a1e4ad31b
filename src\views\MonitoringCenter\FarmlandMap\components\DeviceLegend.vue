<template>
  <div class="device-legend map-card card-compact" :class="{ 'minimized': isMinimized }">
    <div v-if="!isMinimized" class="card-header">
      <h3 class="card-title">
        <el-icon class="title-icon"><InfoFilled /></el-icon>
        设备图例
      </h3>
      <div class="card-actions">
        <el-button type="text" size="small" @click="isMinimized = true" title="最小化">
          <el-icon><Minus /></el-icon>
        </el-button>
      </div>
    </div>
    
    <div v-if="!isMinimized" class="card-body">
      <div class="legend-item">
        <div class="legend-icon online"></div>
        <div class="legend-label">正常在线</div>
      </div>
      <div class="legend-item">
        <div class="legend-icon standby"></div>
        <div class="legend-label">待机状态</div>
      </div>
      <div class="legend-item">
        <div class="legend-icon offline"></div>
        <div class="legend-label">离线设备</div>
      </div>
      <div class="legend-item">
        <div class="legend-icon warning"></div>
        <div class="legend-label">警告状态</div>
      </div>
    </div>
    
    <!-- 最小化时只显示图标 -->
    <div v-if="isMinimized" class="minimized-card" @click="isMinimized = false">
      <el-icon><InfoFilled /></el-icon>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { InfoFilled, Minus } from '@element-plus/icons-vue';

const isMinimized = ref(false);
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as vars;

.device-legend {
  width: 200px;
  z-index: 2;
  position: relative;
  transition: all 0.3s ease;
  
  &.minimized {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    border: 1px solid rgba(0, 255, 170, 0.25);
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    
    &:hover {
      border-color: rgba(0, 255, 170, 0.5);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3),
                  0 0 5px rgba(0, 255, 170, 0.3);
    }
  }
  
  .minimized-card {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    .el-icon {
      font-size: 20px;
      color: vars.$primary-color;
      filter: drop-shadow(0 0 3px rgba(0, 255, 170, 0.5));
    }
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .legend-icon {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 10px;
      
      &.online {
        @include vars.status-dot(vars.$status-online);
      }
      
      &.standby {
        @include vars.status-dot(vars.$status-standby);
      }
      
      &.offline {
        @include vars.status-dot(vars.$status-offline);
      }
      
      &.warning {
        @include vars.status-dot(vars.$warning-color);
      }
    }
    
    .legend-label {
      font-size: 14px;
      color: vars.$text-light;
    }
  }
}

@media (max-width: 768px) {
  .device-legend {
    width: 180px;
    
    &:not(.minimized) {
      bottom: 70px;
    }
  }
}
</style> 