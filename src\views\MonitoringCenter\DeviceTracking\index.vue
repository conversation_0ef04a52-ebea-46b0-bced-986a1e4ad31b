<!--
智慧农场设备追踪主页面 - 重构版本
功能特性：
1. 模块化组件结构
2. WebSocket数据记录功能
3. Excel数据导出功能
4. 响应式布局设计
5. 实时设备状态监控
-->

<template>
  <ResponsiveContainer
    class="device-tracking-container"
    :enable-scroll="true"
    min-height="100vh"
  >
    <!-- 页面标题 -->
    <PageHeader
      title="智慧农场设备追踪"
      description="实时监控农业设备位置、轨迹及运行状态，支持数据记录与导出"
      icon="Location"
    >
      <template #actions>
        <div class="header-actions">
          <!-- 连接状态指示 -->
          <div class="connection-status">
            <div class="status-dot" :class="{ connected: isConnected }"></div>
            <span class="status-text">{{ connectionStatusText }}</span>
          </div>

          <!-- IMU数据状态指示 -->
          <div class="imu-status">
            <div class="status-dot" :class="{ connected: imuReceiving }"></div>
            <span class="status-text">{{ imuStatusText }}</span>
            <span v-if="hasValidOrientation" class="orientation-info">
              朝向: {{ robotYaw?.toFixed(1) }}°
            </span>
          </div>

          <!-- 设备统计 -->
          <div class="device-stats">
            <div class="stat-item">
              <span class="stat-value">{{ onlineDevicesCount }}</span>
              <span class="stat-label">在线设备</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ movingDevicesCount }}</span>
              <span class="stat-label">移动中</span>
            </div>
          </div>
        </div>
      </template>
    </PageHeader>

    <!-- SVG渐变定义 -->
    <svg width="0" height="0" style="position: absolute; overflow: hidden;">
      <defs>
        <linearGradient id="robotTrackGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#00e676" />
          <stop offset="100%" stop-color="#004d40" />
        </linearGradient>
      </defs>
    </svg>

    <!-- 主要内容区域 -->
    <div class="tracking-content">
      <!-- 左侧地图区域 -->
      <div class="map-section">
        <div class="map-container">
          <div class="map-wrapper">
            <PlaneMapCanvas
              ref="mapCanvasRef"
              :anchors="anchors"
              :robot-location="latestLocationData"
              :robot-track="activeRobotTrack"
              :robot-smoothed-track="activeRobotSmoothedTrack"
              :smoothing-enabled="smoothingEnabled"
              :show-grid="true"
              :show-coordinates="true"
              :robot-yaw="robotYaw"
              @robot-click="handleRobotClick"
              @anchor-click="handleAnchorClick"
              @map-click="handleMapClick"
              class="tracking-map"
            />

            <!-- 地图工具栏 -->
            <div class="map-toolbar" role="toolbar" aria-label="地图控制工具栏">
              <el-button-group size="small">
                <el-button
                  type="primary"
                  :icon="ZoomIn"
                  @click="handleZoomIn"
                  circle
                  title="放大地图"
                  aria-label="放大地图"
                />
                <el-button
                  type="primary"
                  :icon="ZoomOut"
                  @click="handleZoomOut"
                  circle
                  title="缩小地图"
                  aria-label="缩小地图"
                />
                <el-button
                  type="primary"
                  :icon="Refresh"
                  @click="handleResetView"
                  circle
                  title="重置地图视图"
                  aria-label="重置地图视图"
                />
              </el-button-group>
            </div>

            <!-- 数据操作按钮组 -->
            <div class="data-operation-toolbar">
              <!-- 记录状态信息 -->
              <div class="record-status-info" v-if="isRecording">
                <div class="status-indicator">
                  <div class="recording-dot"></div>
                  <span class="status-text">正在记录</span>
                </div>
                <div class="record-info">
                  <span class="record-count">{{ recordCount }} 条</span>
                  <span class="record-duration">{{ recordingDuration }}</span>
                </div>
              </div>

              <!-- 操作按钮组 -->
              <div class="operation-buttons">
                <el-button
                  :type="isRecording ? 'danger' : 'primary'"
                  :icon="isRecording ? 'VideoPause' : 'VideoPlay'"
                  @click="toggleRecording"
                  size="small"
                >
                  {{ isRecording ? '停止记录' : '开始记录' }}
                </el-button>

                <el-button
                  type="success"
                  :icon="'Download'"
                  @click="showExportDialog"
                  :disabled="recordCount === 0"
                  size="small"
                >
                  导出数据
                </el-button>


              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧控制面板 -->
      <div class="control-section">


        <!-- 设备列表面板 -->
        <div class="control-panel device-panel-wrapper">
          <div class="panel-header">
            <div class="panel-title">
              <el-icon class="title-icon"><Monitor /></el-icon>
              <span>设备列表</span>
              <el-badge :value="deviceList.length" :max="99" type="primary" />
            </div>
            <div class="panel-actions">
              <el-button size="small" type="primary" @click="refreshDevices" circle>
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>

          <div class="panel-content">
            <DeviceList
              :device-list="deviceList"
              :active-robot-id="activeRobotId"
              @device-click="handleDeviceClick"
              @device-focus="handleDeviceFocus"

              @refresh="refreshDevices"
            />
          </div>
        </div>

        <!-- 功能面板组 -->
        <div class="function-panels">
          <!-- 机器狗视频面板 -->
          <div class="control-panel robot-video-panel">
            <RobotVideoPanel
              :auto-connect="true"
              @connection-change="handleRobotConnectionChange"
              @video-status-change="handleVideoStatusChange"
              @error="handleRobotError"
            />
          </div>

          <!-- 机器狗控制面板 -->
          <div class="control-panel robot-control-panel">
            <RobotControlPanel
              :auto-enable="false"
              @control-change="handleRobotControlChange"
              @move-command="handleRobotMoveCommand"
              @error="handleRobotError"
            />
          </div>

          <!-- 轨迹优化控制面板 -->
          <div class="control-panel trajectory-optimization-panel">
            <TrajectoryOptimizationPanel
              :smoothing-enabled="smoothingEnabled"
              :smoothing-config="smoothingConfig"
              :original-points-count="originalPointsCount"
              :smoothed-points-count="smoothedPointsCount"
              @toggle-smoothing="handleToggleSmoothing"
              @update-config="handleUpdateSmoothingConfig"
              @apply-optimization="handleApplyOptimization"
            />
          </div>

          <!-- 自动巡航控制面板 - 暂时禁用（依赖WebRTC） -->
          <!--
          <div class="control-panel auto-cruise-panel">
            <AutoCruiseControlPanel
              注释：自动巡航功能依赖WebRTC，在HTTP API重构完成后需要单独适配
            />
          </div>
          -->
        </div>
      </div>
    </div>



    <!-- 数据导出对话框 -->
    <DataExportDialog
      v-model:visible="exportDialogVisible"
      :records="exportableRecords"
      :record-count="recordCount"
      :available-devices="deviceList"
      @export="handleDataExport"
    />

    <!-- 设备详情弹窗 -->
    <DeviceDetailDialog
      v-model:visible="deviceDetailVisible"
      :device-info="selectedDevice"
      @data-received="handleIMUDataReceived"
      @connection-status-change="handleIMUConnectionStatusChange"
      @error="handleIMUError"
    />

  </ResponsiveContainer>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { Refresh, ZoomIn, ZoomOut, Monitor } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'

// 导入组件
import PageHeader from '../components/PageHeader.vue'
import PlaneMapCanvas from '../components/PlaneMapCanvas.vue'
import ResponsiveContainer from '../components/ResponsiveContainer.vue'

// 导入新的模块化组件
import DeviceList from './components/DeviceList.vue'
import RobotVideoPanel from './components/RobotVideoPanel.vue'
import RobotControlPanel from './components/RobotControlPanel.vue'

import TrajectoryOptimizationPanel from '../components/TrajectoryOptimizationPanel.vue'
import DataExportDialog from './components/DataExportDialog.vue'
import AutoCruiseControlPanel from './components/AutoCruiseControlPanel.vue'
import DeviceDetailDialog from './components/DeviceDetailDialog.vue'

// 导入Composables
import { useDeviceTracking } from '@/composables/useDeviceTracking'
import { useDataRecording } from './composables/useDataRecording'
import { useExcelExport } from './composables/useExcelExport'
import { useAutoCruise } from './composables/useAutoCruise'
import { useGlobalIMU, GlobalIMUService } from '@/services/globalIMUService'
import { DataExportService } from './services/dataExportService'
import type { RobotDevice } from '@/composables/useDeviceTracking'
import type { AnchorData, RobotLocationData } from '@/utils/websocketService'
import type { Point } from '@/utils/coordinateTransform'
import type { PathPoint, CruiseConfig } from './composables/useAutoCruise'
import { WEBSOCKET_URLS } from '@/config/env'
import { robotWebSocketService } from '@/services/robotWebSocketService'

// 导入样式
import './styles/index.scss'
import './styles/device-detail-dialog.scss'

// 初始化设备追踪Hook
const {
  connectionStatus,
  isConnected,
  anchors,
  activeRobotId,
  activeRobotTrack,
  activeRobotSmoothedTrack,
  latestLocationData,
  onlineDevicesCount,
  movingDevicesCount,
  smoothingEnabled,
  smoothingConfig,
  disconnect,
  setActiveRobot,
  getDeviceList,
  toggleTrajectorySmoothing,
  updateSmoothingConfig
} = useDeviceTracking({
  url: WEBSOCKET_URLS.ROBOT_LOCATION,
  autoConnect: true,
  maxTrackPoints: 1000
})

// 初始化数据记录Hook
const dataRecordingInstance = useDataRecording()
const {
  records: dataRecords,
  recordCount,
  isRecording,
  addRecord,
  startRecording,
  stopRecording,
  recordingDuration
} = dataRecordingInstance

// 导出对话框状态
const exportDialogVisible = ref(false)

// 设备详情弹窗状态
const deviceDetailVisible = ref(false)

// 页面状态
const selectedDevice = ref<RobotDevice | null>(null)

const lastUpdateTime = ref(new Date())

// 地图组件引用
const mapCanvasRef = ref<InstanceType<typeof PlaneMapCanvas> | null>(null)



// 自动巡航功能已移除（依赖WebRTC）
// const autoCruise = useAutoCruise(() => rtcInstance)

// 创建一个空的autoCruise对象来避免运行时错误
const autoCruise = {
  status: ref('stopped'),
  config: ref({}),
  pathPoints: ref([]),
  currentPointIndex: ref(0),
  currentPosition: ref({ x: 0, y: 0 }),
  isActive: ref(false),
  progress: ref(0),
  runningTime: ref(0),
  totalDistance: ref(0),
  completedLoops: ref(0),
  lastError: ref(''),
  updateCurrentPosition: () => {},
  setSafeBoundaryFromAnchors: () => {},
  updateCurrentYaw: () => {},
  start: () => false,
  stop: () => {},
  pause: () => {},
  resume: () => {},
  updateConfig: (config: any) => {},
  setPathPoints: (points: any) => false,
  generateDefaultPath: (center: any, anchors: any) => [],
  generateCircularPath: (center: any, anchors: any) => [],
  generateSShapePath: (center: any, anchors: any) => [],
  generateSpiralPath: (center: any, anchors: any) => [],
  generatePathFromAnchors: (anchors: any) => [],
  optimizePath: (path: any) => []
}

// 初始化全局IMU数据管理
const {
  currentData: currentIMUData,
  isConnected: imuConnected,
  isReceiving: imuReceiving,
  currentYaw: globalYaw,
  batteryLevel,
  stats: imuStats,
  start: startGlobalIMU,
  stop: stopGlobalIMU
} = useGlobalIMU()

// 机器狗朝向角度状态（从IMU数据实时更新）
const robotYaw = ref<number | undefined>(undefined)

// 监听全局IMU数据变化，实时更新朝向角度
watch(globalYaw, (newYaw) => {
  if (newYaw !== undefined && !isNaN(newYaw)) {
    robotYaw.value = newYaw
    console.log('🧭 机器狗朝向更新:', robotYaw.value.toFixed(1) + '°')
  }
}, { immediate: true })

// 计算属性：IMU连接状态文本
const imuStatusText = computed(() => {
  if (imuReceiving.value) return 'IMU数据接收中'
  if (imuConnected.value) return 'IMU已连接'
  return 'IMU未连接'
})

// 计算属性：是否有有效的朝向数据
const hasValidOrientation = computed(() => {
  return robotYaw.value !== undefined && !isNaN(robotYaw.value)
})



// 自动巡航位置更新已禁用（依赖WebRTC）
// watch(() => latestLocationData.value, (newData) => {
//   if (newData) {
//     autoCruise.updateCurrentPosition({
//       x: newData.x,
//       y: newData.y
//     })
//   }
// }, { immediate: true })

// 自动巡航边界设置已禁用（依赖WebRTC）
// watch(() => anchors.value, (newAnchors) => {
//   if (newAnchors && newAnchors.length >= 4) {
//     const anchorPoints = newAnchors.map(anchor => ({
//       x: anchor.x,
//       y: anchor.y
//     }))
//     autoCruise.setSafeBoundaryFromAnchors(anchorPoints) // 使用基站完整覆盖范围
//   }
// }, { immediate: true })

// 计算属性
const deviceList = computed(() => getDeviceList())

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'disconnected': return '已断开'
    case 'error': return '连接错误'
    default: return '未知状态'
  }
})

// 轨迹点数统计
const originalPointsCount = computed(() => {
  return activeRobotTrack.value?.length || 0
})

const smoothedPointsCount = computed(() => {
  return activeRobotSmoothedTrack.value?.length || 0
})

// 转换readonly数据为可变数据用于导出
const exportableRecords = computed(() => {
  return dataRecords.value.map(record => ({
    id: record.id,
    timestamp: record.timestamp,
    data: {
      anchors: [...record.data.anchors.map(anchor => ({
        anchorId: anchor.anchorId,
        x: anchor.x,
        y: anchor.y,
        z: anchor.z
      }))],
      tagId: record.data.tagId,
      timestamp: record.data.timestamp,
      x: record.data.x,
      y: record.data.y,
      z: record.data.z
    },
    deviceName: record.deviceName,
    recordTime: record.recordTime
  }))
})

// 监听WebSocket数据，自动记录
watch(latestLocationData, (newData) => {
  console.log('WebSocket数据更新:', {
    hasData: !!newData,
    isRecording: isRecording.value,
    tagId: newData?.tagId,
    timestamp: newData?.timestamp
  })

  if (newData && isRecording.value) {
    const device = deviceList.value.find(d => d.tagId === newData.tagId)
    const deviceName = device?.name || `设备-${newData.tagId}`

    console.log('准备记录数据:', { deviceName, tagId: newData.tagId })

    // 转换readonly数据为可变数据
    const recordData = {
      anchors: [...newData.anchors], // 转换readonly数组为可变数组
      tagId: newData.tagId,
      x: newData.x,
      y: newData.y,
      z: newData.z,
      timestamp: newData.timestamp
    }
    addRecord(recordData, deviceName)
  } else if (newData && !isRecording.value) {
    console.log('收到数据但记录功能未启用')
  }

  // 更新最后更新时间
  lastUpdateTime.value = new Date()
}, { deep: true })

/**
 * 格式化时间
 */
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

/**
 * Canvas地图事件处理
 */
const handleRobotClick = (robotData: RobotLocationData) => {
  console.log('机器狗点击事件触发:', robotData)
  const device = deviceList.value.find(d => d.tagId === robotData.tagId)
  if (device) {
    console.log('找到设备:', device)
    selectedDevice.value = device

    // 显示设备详情弹窗
    console.log('显示设备详情弹窗')
    deviceDetailVisible.value = true

  } else {
    console.log('未找到对应设备')
  }
}

const handleAnchorClick = (anchor: AnchorData) => {
  ElNotification({
    title: '基站信息',
    message: `基站${anchor.anchorId}: (${anchor.x.toFixed(2)}, ${anchor.y.toFixed(2)})`,
    type: 'info'
  })
}

const handleMapClick = (worldPoint: Point) => {
  console.log('地图点击位置:', worldPoint)
}



/**
 * IMU数据相关事件处理
 */
const handleIMUDataReceived = (data: any) => {
  console.log('收到IMU数据:', data)

  // 提取偏航角并更新自动巡航系统和地图显示
  if (data && data.attitude && typeof data.attitude.yaw === 'number') {
    const yaw = data.attitude.yaw
    console.log(`🧭 更新机器狗朝向: ${yaw.toFixed(1)}°`)

    // 自动巡航偏航角更新已禁用（依赖WebRTC）
    // autoCruise.updateCurrentYaw(yaw)

    // 更新地图显示的朝向角度
    robotYaw.value = yaw
  }
}

const handleIMUConnectionStatusChange = (status: string) => {
  console.log('IMU连接状态变化:', status)
  // 可以在这里处理IMU连接状态变化，比如显示通知
}

const handleIMUError = (error: Error) => {
  console.error('IMU数据错误:', error)
  ElNotification({
    title: 'IMU数据错误',
    message: error.message,
    type: 'error',
    duration: 5000
  })
}

/**
 * 数据记录和导出功能
 */
const toggleRecording = () => {
  if (isRecording.value) {
    stopRecording()
    console.log('停止数据记录')
  } else {
    startRecording()
    console.log('开始数据记录')
  }
}

const showExportDialog = () => {
  exportDialogVisible.value = true
}

const handleDataExport = async (exportParams: any) => {
  console.log('开始导出数据:', exportParams)

  try {
    // 根据导出格式执行不同的导出逻辑
    const { formats, records, filename } = exportParams

    for (const format of formats) {
      switch (format) {
        case 'excel':
          await exportToExcel(records, filename)
          break
        case 'csv':
          await exportToCSV(records, filename)
          break
        case 'json':
          await exportToJSON(records, filename)
          break
        case 'gpx':
          await exportToGPX(records, filename)
          break
      }
    }

  } catch (error) {
    console.error('导出失败:', error)
    throw error
  }
}

// 导出函数实现
const exportToExcel = async (records: any[], filename: string) => {
  console.log('导出Excel:', filename)
  DataExportService.exportToExcelAdvanced(records, `${filename}.xlsx`)
}

const exportToCSV = async (records: any[], filename: string) => {
  console.log('导出CSV:', filename)
  DataExportService.exportToCSV(records, `${filename}.csv`)
}

const exportToJSON = async (records: any[], filename: string) => {
  console.log('导出JSON:', filename)
  DataExportService.exportToJSON(records, `${filename}.json`)
}

const exportToGPX = async (records: any[], filename: string) => {
  console.log('导出GPX:', filename)
  DataExportService.exportToGPX(records, `${filename}.gpx`)
}

/**
 * 设备列表事件处理
 */
const handleDeviceClick = (device: RobotDevice) => {
  setActiveRobot(device.tagId)
  selectedDevice.value = device
}

const handleDeviceFocus = (device: RobotDevice) => {
  setActiveRobot(device.tagId)
  selectedDevice.value = device
}





/**
 * 刷新设备数据
 */
const refreshDevices = () => {
  lastUpdateTime.value = new Date()
  ElNotification({
    title: '刷新完成',
    message: '设备数据已刷新',
    type: 'success'
  })
}

/**
 * 轨迹优化事件处理
 */
const handleToggleSmoothing = (enabled: boolean) => {
  toggleTrajectorySmoothing(enabled)
}

const handleUpdateSmoothingConfig = (newConfig: any) => {
  updateSmoothingConfig(newConfig)
}

const handleApplyOptimization = () => {
  ElNotification({
    title: '轨迹优化已应用',
    message: '轨迹平滑设置已更新并生效',
    type: 'success',
    duration: 2000
  })
}

/**
 * 自动巡航事件处理
 */
const handleAutoCruiseStart = () => {
  ElNotification({
    title: '功能暂不可用',
    message: '自动巡航功能依赖WebRTC，正在适配HTTP API',
    type: 'warning',
    duration: 3000
  })
  // 原有代码已注释（依赖WebRTC）
  // if (!videoConnected.value) {
  //   ElNotification({
  //     title: '无法启动巡航',
  //     message: '请先连接机器狗视频',
  //     type: 'warning',
  //     duration: 3000
  //   })
  //   return
  // }
  // const success = autoCruise.start()
  // if (success) {
  //   ElNotification({
  //     title: '自动巡航已启动',
  //     message: '机器狗开始按设定路径巡航',
  //     type: 'success',
  //     duration: 3000
  //   })
  // }
}

const handleAutoCruiseStop = () => {
  ElNotification({
    title: '功能暂不可用',
    message: '自动巡航功能依赖WebRTC，正在适配HTTP API',
    type: 'warning',
    duration: 3000
  })
  // autoCruise.stop()
}

const handleAutoCruisePause = () => {
  autoCruise.pause()
  ElNotification({
    title: '自动巡航已暂停',
    message: '机器狗暂停在当前位置',
    type: 'warning',
    duration: 3000
  })
}

const handleAutoCruiseResume = () => {
  autoCruise.resume()
  ElNotification({
    title: '自动巡航已恢复',
    message: '机器狗继续巡航',
    type: 'success',
    duration: 3000
  })
}

const handleAutoCruiseConfigUpdate = (config: Partial<CruiseConfig>) => {
  autoCruise.updateConfig(config)
}

const handleAutoCruiseSetPath = (points: PathPoint[]) => {
  const success = autoCruise.setPathPoints(points)
  if (success) {
    ElNotification({
      title: '巡航路径已设置',
      message: `已设置 ${points.length} 个路径点`,
      type: 'success',
      duration: 3000
    })
  }
}

const handleGenerateDefaultPath = (center: PathPoint) => {
  if (!anchors.value || anchors.value.length < 4) {
    ElNotification({
      title: '无法生成路径',
      message: '需要至少4个基站来确定安全边界',
      type: 'error',
      duration: 3000
    })
    return
  }

  // 转换anchors格式
  const anchorPoints = anchors.value.map(anchor => ({
    x: anchor.x,
    y: anchor.y
  }))

  const points = autoCruise.generateDefaultPath(center, anchorPoints)
  if (points.length === 0) {
    ElNotification({
      title: '路径生成失败',
      message: '基站覆盖区域太小，无法生成安全路径',
      type: 'error',
      duration: 3000
    })
    return
  }

  autoCruise.setPathPoints(points)
  ElNotification({
    title: '矩形路径已生成',
    message: `已生成 ${points.length} 个路径点，严格限制在基站边界内`,
    type: 'success',
    duration: 3000
  })
}

const handleGenerateCircularPath = (center: PathPoint) => {
  if (!anchors.value || anchors.value.length < 4) {
    ElNotification({
      title: '无法生成路径',
      message: '需要至少4个基站来确定安全边界',
      type: 'error',
      duration: 3000
    })
    return
  }

  // 转换anchors格式
  const anchorPoints = anchors.value.map(anchor => ({
    x: anchor.x,
    y: anchor.y
  }))

  const points = autoCruise.generateCircularPath(center, anchorPoints)
  if (points.length === 0) {
    ElNotification({
      title: '路径生成失败',
      message: '基站覆盖区域太小，无法生成安全路径',
      type: 'error',
      duration: 3000
    })
    return
  }

  autoCruise.setPathPoints(points)
  ElNotification({
    title: '圆形路径已生成',
    message: `已生成 ${points.length} 个路径点，严格限制在基站边界内`,
    type: 'success',
    duration: 3000
  })
}

const handleGenerateSShapePath = (center: PathPoint) => {
  if (!anchors.value || anchors.value.length < 4) {
    ElNotification({
      title: '无法生成路径',
      message: '需要至少4个基站来确定安全边界',
      type: 'error',
      duration: 3000
    })
    return
  }

  // 转换anchors格式
  const anchorPoints = anchors.value.map(anchor => ({
    x: anchor.x,
    y: anchor.y
  }))

  const points = autoCruise.generateSShapePath(center, anchorPoints)
  if (points.length === 0) {
    ElNotification({
      title: '路径生成失败',
      message: '基站覆盖区域太小，无法生成S形路径',
      type: 'error',
      duration: 3000
    })
    return
  }

  autoCruise.setPathPoints(points)
  ElNotification({
    title: 'S形路径已生成',
    message: `已生成 ${points.length} 个路径点，适合农田巡检，严格限制在基站边界内`,
    type: 'success',
    duration: 3000
  })
}

const handleGenerateSpiralPath = (center: PathPoint) => {
  if (!anchors.value || anchors.value.length < 4) {
    ElNotification({
      title: '无法生成路径',
      message: '需要至少4个基站来确定安全边界',
      type: 'error',
      duration: 3000
    })
    return
  }

  // 转换anchors格式
  const anchorPoints = anchors.value.map(anchor => ({
    x: anchor.x,
    y: anchor.y
  }))

  const points = autoCruise.generateSpiralPath(center, anchorPoints)
  if (points.length === 0) {
    ElNotification({
      title: '路径生成失败',
      message: '基站覆盖区域太小，无法生成螺旋路径',
      type: 'error',
      duration: 3000
    })
    return
  }

  autoCruise.setPathPoints(points)
  ElNotification({
    title: '螺旋路径已生成',
    message: `已生成 ${points.length} 个路径点，严格限制在基站边界内`,
    type: 'success',
    duration: 3000
  })
}

const handleGenerateAnchorPath = () => {
  if (!anchors.value || anchors.value.length < 4) {
    ElNotification({
      title: '无法生成路径',
      message: '基站数量不足，需要至少4个基站',
      type: 'error',
      duration: 3000
    })
    return
  }

  // 转换anchors格式
  const anchorPoints = anchors.value.map(anchor => ({
    x: anchor.x,
    y: anchor.y
  }))

  const points = autoCruise.generatePathFromAnchors(anchorPoints)
  autoCruise.setPathPoints(points)
  ElNotification({
    title: '基站路径已生成',
    message: `基于基站位置生成 ${points.length} 个路径点`,
    type: 'success',
    duration: 3000
  })
}

const handleGenerateCustomPath = (customPoints: PathPoint[]) => {
  if (!customPoints || customPoints.length === 0) {
    ElNotification({
      title: '自定义路径无效',
      message: '请先绘制或添加路径点',
      type: 'error',
      duration: 3000
    })
    return
  }

  // 验证所有路径点是否在基站边界内
  if (anchors.value && anchors.value.length >= 4) {
    const anchorPoints = anchors.value.map(anchor => ({
      x: anchor.x,
      y: anchor.y
    }))

    const bounds = {
      minX: Math.min(...anchorPoints.map(a => a.x)),
      maxX: Math.max(...anchorPoints.map(a => a.x)),
      minY: Math.min(...anchorPoints.map(a => a.y)),
      maxY: Math.max(...anchorPoints.map(a => a.y))
    }

    const invalidPoints = customPoints.filter(point =>
      point.x < bounds.minX ||
      point.x > bounds.maxX ||
      point.y < bounds.minY ||
      point.y > bounds.maxY
    )

    if (invalidPoints.length > 0) {
      ElNotification({
        title: '路径验证失败',
        message: `有 ${invalidPoints.length} 个路径点超出基站边界`,
        type: 'error',
        duration: 3000
      })
      return
    }
  }

  autoCruise.setPathPoints(customPoints)
  ElNotification({
    title: '自定义路径已应用',
    message: `成功应用 ${customPoints.length} 个自定义路径点`,
    type: 'success',
    duration: 3000
  })
}

const handleOptimizePath = () => {
  const currentPath = autoCruise.pathPoints.value
  if (currentPath.length < 3) {
    ElNotification({
      title: '无法优化路径',
      message: '路径点数量不足，需要至少3个点',
      type: 'warning',
      duration: 3000
    })
    return
  }

  const optimizedPath = autoCruise.optimizePath(currentPath)
  autoCruise.setPathPoints(optimizedPath)
  ElNotification({
    title: '路径优化完成',
    message: `路径已优化，减少急转弯，提高巡航平滑度`,
    type: 'success',
    duration: 3000
  })
}

const handleEditPath = (editedPoints: PathPoint[]) => {
  // 验证编辑后的路径点是否都在基站边界内
  if (!anchors.value || anchors.value.length < 4) {
    ElNotification({
      title: '路径验证失败',
      message: '缺少基站数据，无法验证路径安全性',
      type: 'error',
      duration: 3000
    })
    return
  }

  // 转换anchors格式
  const anchorPoints = anchors.value.map(anchor => ({
    x: anchor.x,
    y: anchor.y
  }))

  // 验证所有路径点是否在基站边界内
  const bounds = {
    minX: Math.min(...anchorPoints.map(a => a.x)),
    maxX: Math.max(...anchorPoints.map(a => a.x)),
    minY: Math.min(...anchorPoints.map(a => a.y)),
    maxY: Math.max(...anchorPoints.map(a => a.y))
  }

  const invalidPoints = editedPoints.filter(point =>
    point.x < bounds.minX ||
    point.x > bounds.maxX ||
    point.y < bounds.minY ||
    point.y > bounds.maxY
  )

  if (invalidPoints.length > 0) {
    ElNotification({
      title: '路径验证失败',
      message: `有 ${invalidPoints.length} 个路径点超出基站安全边界`,
      type: 'error',
      duration: 3000
    })
    return
  }

  // 应用编辑后的路径
  autoCruise.setPathPoints(editedPoints)
  ElNotification({
    title: '路径编辑成功',
    message: `已应用编辑后的路径，共 ${editedPoints.length} 个路径点`,
    type: 'success',
    duration: 3000
  })
}

/**
 * 刷新所有数据
 */
const refreshData = () => {
  lastUpdateTime.value = new Date()
  ElNotification({
    title: '数据已更新',
    message: `系统数据已刷新，更新时间: ${formatTime(lastUpdateTime.value)}`,
    type: 'success',
    duration: 2000
  })
}

/**
 * 地图控制功能
 */
const handleZoomIn = () => {
  if (mapCanvasRef.value) {
    mapCanvasRef.value.zoomIn()
  }
}

const handleZoomOut = () => {
  if (mapCanvasRef.value) {
    mapCanvasRef.value.zoomOut()
  }
}

const handleResetView = () => {
  if (mapCanvasRef.value) {
    mapCanvasRef.value.resetView()
  }
  // 同时刷新数据
  refreshData()
}

/**
 * 机器狗相关事件处理
 */
const handleRobotConnectionChange = (connected: boolean) => {
  console.log('机器狗连接状态变化:', connected)
  if (connected) {
    ElNotification({
      title: '机器狗已连接',
      message: '机器狗连接成功，可以开始控制',
      type: 'success',
      duration: 3000
    })
  } else {
    ElNotification({
      title: '机器狗已断开',
      message: '机器狗连接已断开',
      type: 'warning',
      duration: 3000
    })
  }
}

const handleVideoStatusChange = (status: string) => {
  console.log('机器狗视频状态变化:', status)
  // 可以根据需要处理视频状态变化
}

const handleRobotControlChange = (controlling: boolean) => {
  console.log('机器狗控制状态变化:', controlling)
  // 可以根据需要处理控制状态变化
}

const handleRobotMoveCommand = (params: { x: number, y: number, z: number }) => {
  console.log('机器狗移动命令:', params)
  // 可以记录移动命令或更新UI状态
}

const handleRobotError = (error: Error) => {
  console.error('机器狗错误:', error)
  ElNotification({
    title: '机器狗错误',
    message: error.message,
    type: 'error',
    duration: 5000
  })
}

// 生命周期
onMounted(async () => {
  console.log('设备追踪页面已加载 - 重构版本')
  console.log('当前设备列表长度:', deviceList.value.length)

  // 立即开始获取IMU数据，为地图朝向显示和自动巡航做准备
  console.log('🚀 启动全局IMU数据服务...')
  try {
    const success = await startGlobalIMU()
    if (success) {
      console.log('✅ 全局IMU数据服务启动成功')
      ElNotification({
        title: '实时数据已启动',
        message: '机器狗朝向和IMU数据正在实时更新',
        type: 'success',
        duration: 3000
      })
    } else {
      console.warn('⚠️ 全局IMU数据服务启动失败')
      ElNotification({
        title: 'IMU数据获取失败',
        message: '将在后台继续尝试连接',
        type: 'warning',
        duration: 3000
      })
    }
  } catch (error) {
    console.error('❌ 全局IMU数据服务启动异常:', error)
  }

  // 设备列表监听逻辑
  watch(() => deviceList.value.length, (newCount, oldCount) => {
    console.log(`设备列表变化: ${oldCount} -> ${newCount}`)
  }, { immediate: true })
})

onUnmounted(() => {
  // 清理资源
  console.log('🧹 清理设备追踪页面资源...')

  // 断开设备追踪连接
  disconnect()

  // 停止全局IMU数据服务
  stopGlobalIMU()

  console.log('✅ 资源清理完成')
})
</script>

<style lang="scss" scoped>
.device-tracking-container {
  background: #0f172a;
  color: #f3f4f6;
  position: relative;
  min-height: 100vh;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 24px;

  .connection-status,
  .imu-status {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #6b7280;
      transition: all 0.3s ease;

      &.connected {
        background: #10b981;
      }
    }

    .status-text {
      font-size: 14px;
      color: #d1d5db;
    }

    .orientation-info {
      font-size: 12px;
      color: #3b82f6;
      font-weight: 600;
      margin-left: 8px;
      padding: 2px 6px;
      background: rgba(59, 130, 246, 0.1);
      border-radius: 4px;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }
  }

  .device-stats {
    display: flex;
    gap: 16px;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: #3b82f6;
      }

      .stat-label {
        font-size: 12px;
        color: #9ca3af;
      }
    }
  }
}

.tracking-content {
  display: flex;
  gap: 20px;
  flex: 1;
  height: calc(100vh - 140px);
  min-height: 500px;
  max-height: calc(100vh - 140px);
  padding: 0 4px;

  .map-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;

    .map-container {
      flex: 1;
      min-height: 400px;
      max-height: calc(100vh - 300px);
      position: relative;

      .map-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 16px;
        overflow: hidden;
        background: rgba(31, 41, 55, 0.9);
        border: 1px solid rgba(75, 85, 99, 0.3);

        .tracking-map {
          width: 100%;
          height: 100%;
        }

        .map-toolbar {
          position: absolute;
          top: 16px;
          left: 16px;
          z-index: 10;

          .el-button-group {
            background: rgba(31, 41, 55, 0.95);
            border-radius: 8px;
            padding: 6px;
            backdrop-filter: blur(12px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
            display: flex;
            gap: 4px;

            .el-button {
              background: rgba(59, 130, 246, 0.1);
              border: 1px solid rgba(59, 130, 246, 0.3);
              color: #3b82f6;
              transition: all 0.2s ease;

              &:hover {
                background: rgba(59, 130, 246, 0.2);
                border-color: rgba(59, 130, 246, 0.5);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
              }

              &:active {
                transform: translateY(0);
              }

              &:focus {
                outline: 2px solid rgba(59, 130, 246, 0.5);
                outline-offset: 2px;
              }

              .el-icon {
                font-size: 16px;
              }
            }
          }
        }

        .data-operation-toolbar {
          position: absolute;
          top: 16px;
          right: 16px;
          z-index: 10;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 12px;

          .record-status-info {
            background: rgba(31, 41, 55, 0.9);
            border-radius: 8px;
            padding: 12px 16px;
            border: 1px solid rgba(75, 85, 99, 0.3);
            min-width: 200px;
            backdrop-filter: blur(10px);

            .status-indicator {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;

              .recording-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #ef4444;
                animation: pulse 2s infinite;
              }

              .status-text {
                font-size: 14px;
                font-weight: 600;
                color: #ef4444;
              }
            }

            .record-info {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              color: #9ca3af;

              .record-count {
                color: #10b981;
                font-weight: 600;
              }

              .record-duration {
                color: #f59e0b;
              }
            }
          }

          .operation-buttons {
            display: flex;
            gap: 8px;

            .el-button {
              background: rgba(31, 41, 55, 0.9);
              border: 1px solid rgba(75, 85, 99, 0.3);
              backdrop-filter: blur(10px);
              color: #ffffff;

              &:hover {
                background: rgba(31, 41, 55, 1);
              }

              &.el-button--primary {
                background: rgba(64, 158, 255, 0.9);
                border-color: rgba(64, 158, 255, 0.5);

                &:hover {
                  background: rgba(64, 158, 255, 1);
                }
              }

              &.el-button--danger {
                background: rgba(239, 68, 68, 0.9);
                border-color: rgba(239, 68, 68, 0.5);

                &:hover {
                  background: rgba(239, 68, 68, 1);
                }
              }

              &.el-button--success {
                background: rgba(16, 185, 129, 0.9);
                border-color: rgba(16, 185, 129, 0.5);

                &:hover {
                  background: rgba(16, 185, 129, 1);
                }
              }
            }
          }
        }
      }
    }
  }

  .control-section {
    width: 400px;
    display: flex;
    flex-direction: column;
    gap: 20px; // 增加面板间距，防止重叠

    .control-panel {
      background: rgba(31, 41, 55, 0.9);
      border-radius: 16px;
      border: 1px solid rgba(75, 85, 99, 0.3);
      overflow: hidden;
    }



    .device-panel-wrapper {
      flex: 1;
      min-height: 420px;

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background: rgba(31, 41, 55, 0.8);
        border-bottom: 1px solid rgba(75, 85, 99, 0.3);

        .panel-title {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 16px;
          font-weight: 600;
          color: #f3f4f6;

          .title-icon {
            color: #3b82f6;
            font-size: 18px;
          }
        }

        .panel-actions {
          .el-button {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.3);
          }
        }
      }

      .panel-content {
        padding: 0;
        height: calc(100% - 60px);
      }
    }

    .function-panels {
      display: grid;
      grid-template-columns: 1fr;
      gap: 16px;

      .robot-video-panel {
        min-height: 350px;
      }

      .robot-control-panel {
        min-height: 400px;
      }

      .trajectory-optimization-panel,
      .data-record-panel,
      .export-panel {
        min-height: 280px;
      }

      .trajectory-optimization-panel {
        .control-panel {
          padding: 0;
        }
      }
    }
  }
}



/* 响应式调整 */
@media (max-height: 800px) {
  .tracking-content {
    height: calc(100vh - 120px);
    min-height: 450px;
    max-height: calc(100vh - 120px);

    .map-section {
      .map-container {
        min-height: 350px;
        max-height: calc(100vh - 200px);
      }
    }

    .control-section {
      .device-panel-wrapper {
        min-height: 350px;
      }
    }
  }
}

@media (max-height: 700px) {
  .tracking-content {
    height: calc(100vh - 100px);
    min-height: 400px;
    max-height: calc(100vh - 100px);

    .map-section {
      .map-container {
        min-height: 300px;
        max-height: calc(100vh - 160px);
      }
    }

    .control-section {
      .device-panel-wrapper {
        min-height: 300px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .tracking-content {
    flex-direction: column;
    height: auto;
    min-height: auto;
    max-height: none;

    .map-section {
      .map-container {
        min-height: 400px;
        max-height: 500px;
      }
    }

    .control-section {
      width: 100%;
      flex-direction: row;
      gap: 20px; // 增加响应式布局下的间距



      .device-panel-wrapper {
        flex: 1;
        min-height: 300px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .tracking-content {
    .control-section {
      width: 360px;

      .function-panels {
        grid-template-columns: 1fr;
        gap: 12px;

        .trajectory-optimization-panel,
        .data-record-panel,
        .export-panel {
          min-height: 240px;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .tracking-content {
    flex-direction: column;
    gap: 16px;
    padding: 0 2px;

    .map-section {
      .map-container {
        min-height: 500px;

        .map-wrapper .map-toolbar {
          top: 12px;
          left: 12px;

          .el-button-group {
            padding: 4px;

            .el-button {
              .el-icon {
                font-size: 14px;
              }
            }
          }
        }

        .map-wrapper .data-operation-toolbar {
          top: 12px;
          right: 12px;

          .record-status-info {
            min-width: 180px;
            padding: 10px 12px;
          }

          .operation-buttons {
            .el-button {
              font-size: 12px;
              padding: 6px 12px;
            }
          }
        }
      }
    }

    .control-section {
      width: 100%;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 20px; // 增加flex-wrap布局的间距

      .device-panel-wrapper {
        flex: 1;
        min-width: 320px;
        min-height: 350px;
      }

      .function-panels {
        flex: 1;
        min-width: 300px;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .robot-video-panel {
          min-height: 300px;
        }

        .robot-control-panel {
          min-height: 350px;
        }

        .trajectory-optimization-panel {
          grid-column: 1 / -1; // 跨越所有列
          min-height: 280px;
        }

        .data-record-panel,
        .export-panel {
          min-height: 320px;
        }
      }
    }
  }


}

@media (max-width: 768px) {
  .device-tracking-container {
    padding: 0 8px;
  }

  .header-actions {
    flex-direction: column;
    gap: 12px;

    .device-stats {
      gap: 12px;
    }
  }

  .tracking-content {
    gap: 12px;
    padding: 0;

    .map-section .map-container {
      min-height: 400px;

      .map-wrapper {
        border-radius: 12px;

        .map-toolbar {
          top: 8px;
          left: 8px;

          .el-button-group {
            padding: 3px;
            gap: 2px;

            .el-button {
              width: 32px;
              height: 32px;

              .el-icon {
                font-size: 14px;
              }
            }
          }
        }

        .data-operation-toolbar {
          top: 8px;
          right: 8px;
          gap: 8px;

          .record-status-info {
            min-width: 160px;
            padding: 8px 12px;

            .status-indicator {
              margin-bottom: 6px;

              .status-text {
                font-size: 12px;
              }
            }

            .record-info {
              font-size: 11px;
            }
          }

          .operation-buttons {
            flex-direction: column;
            gap: 4px;

            .el-button {
              font-size: 11px;
              padding: 4px 8px;
              min-width: 80px;
            }
          }
        }
      }
    }

    .control-section {
      flex-direction: column;
      gap: 16px; // 增加移动端的面板间距

      .device-panel-wrapper {
        min-width: 100%;
        min-height: 280px;
      }

      .function-panels {
        grid-template-columns: 1fr;
        gap: 12px;

        .robot-video-panel {
          min-height: 250px;
        }

        .robot-control-panel {
          min-height: 300px;
        }

        .trajectory-optimization-panel,
        .data-record-panel,
        .export-panel {
          min-height: 200px;
        }
      }
    }
  }


}

@media (max-width: 480px) {
  .tracking-content {
    .control-section {
      .control-panel {
        border-radius: 12px;
      }

      .device-panel-wrapper .panel-header {
        padding: 12px 16px;

        .panel-title {
          font-size: 14px;
          gap: 8px;
        }
      }
    }
  }


}

// 脉冲动画
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
