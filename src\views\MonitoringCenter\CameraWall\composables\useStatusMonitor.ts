import { ref, onMounted, onUnmounted } from 'vue';
import { getCameraStatus } from '../api/camera';
import { CameraStatus, StreamStatus } from '../types';
import type { Camera } from '../types';

export function useStatusMonitor(cameras: Camera[]) {
  const cameraStatuses = ref<Record<string, CameraStatus>>({});
  const streamStatuses = ref<Record<string, StreamStatus>>({});
  let statusInterval: number | null = null;
  
  // 初始化状态
  const initStatuses = () => {
    cameras.forEach(camera => {
      cameraStatuses.value[camera.id] = camera.status;
      streamStatuses.value[camera.id] = camera.streamStatus;
    });
  };
  
  // 更新单个摄像头状态
  const updateCameraStatus = async (cameraId: string) => {
    try {
      const { status, streamStatus } = await getCameraStatus(cameraId);
      cameraStatuses.value[cameraId] = status;
      streamStatuses.value[cameraId] = streamStatus;
    } catch (error) {
      console.error(`更新摄像头 ${cameraId} 状态失败:`, error);
      // 如果获取状态失败，假设摄像头离线
      cameraStatuses.value[cameraId] = CameraStatus.OFFLINE;
      streamStatuses.value[cameraId] = StreamStatus.INTERRUPTED;
    }
  };
  
  // 更新所有摄像头状态
  const updateAllStatuses = async () => {
    await Promise.all(cameras.map(camera => updateCameraStatus(camera.id)));
  };
  
  // 开始定期监控
  const startMonitoring = (intervalMs = 10000) => {
    if (statusInterval) return;
    
    // 立即更新一次
    updateAllStatuses();
    
    // 设置定期更新
    statusInterval = window.setInterval(() => {
      updateAllStatuses();
    }, intervalMs);
  };
  
  // 停止监控
  const stopMonitoring = () => {
    if (statusInterval) {
      clearInterval(statusInterval);
      statusInterval = null;
    }
  };
  
  // 获取摄像头状态
  const getCameraStatusById = (cameraId: string) => {
    return cameraStatuses.value[cameraId] || CameraStatus.OFFLINE;
  };
  
  // 获取视频流状态
  const getStreamStatusById = (cameraId: string) => {
    return streamStatuses.value[cameraId] || StreamStatus.INTERRUPTED;
  };
  
  onMounted(() => {
    initStatuses();
    startMonitoring();
  });
  
  onUnmounted(() => {
    stopMonitoring();
  });
  
  return {
    cameraStatuses,
    streamStatuses,
    updateCameraStatus,
    updateAllStatuses,
    getCameraStatusById,
    getStreamStatusById
  };
} 