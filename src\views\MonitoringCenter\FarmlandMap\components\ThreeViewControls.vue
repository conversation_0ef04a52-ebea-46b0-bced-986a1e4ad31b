<template>
  <div class="three-view-controls">
    <div class="control-section">
      <div class="section-title">视角控制</div>
      <div class="control-buttons">
        <div class="control-button" @click="resetView">
          <el-icon><Refresh /></el-icon>
          <span>重置视角</span>
        </div>
        <div class="control-button" @click="zoomIn">
          <el-icon><ZoomIn /></el-icon>
          <span>放大</span>
        </div>
        <div class="control-button" @click="zoomOut">
          <el-icon><ZoomOut /></el-icon>
          <span>缩小</span>
        </div>
      </div>
    </div>
    
    <div class="control-section">
      <div class="section-title">预设视角</div>
      <div class="control-buttons">
        <div class="control-button" @click="setTopView">
          <el-icon><TopRight /></el-icon>
          <span>顶视图</span>
        </div>
        <div class="control-button" @click="setFrontView">
          <el-icon><Right /></el-icon>
          <span>正视图</span>
        </div>
        <div class="control-button" @click="setSideView">
          <el-icon><Bottom /></el-icon>
          <span>侧视图</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { threeService } from '../services/threeService';
import { Refresh, ZoomIn, ZoomOut, TopRight, Right, Bottom } from '@element-plus/icons-vue';

// 重置视图
const resetView = () => {
  threeService.resetView();
};

// 缩放控制
const zoomIn = () => {
  threeService.zoomIn();
};

const zoomOut = () => {
  threeService.zoomOut();
};

// 预设视角
const setTopView = () => {
  threeService.setViewPosition('top');
};

const setFrontView = () => {
  threeService.setViewPosition('front');
};

const setSideView = () => {
  threeService.setViewPosition('side');
};
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as vars;

.three-view-controls {
  background-color: rgba(0, 21, 65, 0.85);
  border-radius: 8px;
  padding: 15px;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(43, 255, 150, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.control-section {
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 14px;
  color: vars.$text-secondary;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.control-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.control-button {
  flex: 1;
  min-width: 80px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(43, 255, 150, 0.2);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  .el-icon {
    font-size: 20px;
    margin-bottom: 5px;
    color: vars.$primary-color;
  }
  
  span {
    font-size: 12px;
    color: vars.$text-light;
  }
  
  &:hover {
    background-color: rgba(43, 255, 150, 0.1);
    border-color: rgba(43, 255, 150, 0.4);
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
}
</style> 