<!-- 
  PathPlanningEngine.vue
  智能路径规划引擎模块
  依据农田地图信息及设备当前位置，生成避障的作业路径，提升设备作业的安全性与效率。
-->
<template>
  <div class="path-planning-engine">
    <!-- 页面标题 -->
    <PageHeader
      title="智能路径规划引擎"
      description="依据农田地图信息及设备当前位置，生成避障的作业路径，提升设备作业的安全性与效率"
      icon="Location"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ savedPathsCount }}</span>
            <span class="summary-label">已保存路径</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ activeTasksCount }}</span>
            <span class="summary-label">活跃任务</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 农田地图区域 -->
      <div class="farm-map-container">
        <div ref="mapContainer" class="farm-map"></div>
        
        <!-- 地图工具栏 -->
        <div class="map-toolbar">
          <el-tooltip content="放大" placement="top">
            <el-button circle class="toolbar-button" @click="zoomIn">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="缩小" placement="top">
            <el-button circle class="toolbar-button" @click="zoomOut">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="重置视图" placement="top">
            <el-button circle class="toolbar-button" @click="resetView">
              <el-icon><RefreshRight /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="智能路径规划" placement="top">
            <el-button circle class="toolbar-button path-planning-button" @click="showPathPlanningPanel">
              <el-icon><Location /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="地图数据已加载" />
        <StatusIndicator type="normal" label="设备位置实时" />
        <StatusIndicator type="warning" label="AI路径规划就绪" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 路径规划参数面板 -->
    <el-dialog
      v-model="showPanel"
      title="智能路径规划参数设置"
      width="70%"
      class="path-planning-panel"
      :show-close="true"
      :close-on-click-modal="false"
      :destroy-on-close="false"
    >
      <div class="panel-content">
        <div class="parameter-section">
          <h3 class="section-title">参数设置</h3>
          
          <div class="parameters-container">
            <div class="parameter-item">
              <span class="parameter-label">起点</span>
              <div class="point-selector">
                <el-input v-model="startPointDisplay" placeholder="点击地图选择起点" readonly>
                  <template #prepend>
                    <el-button @click="selectingPoint = 'start'">
                      <el-icon><Pointer /></el-icon>
                    </el-button>
                  </template>
                </el-input>
              </div>
            </div>
            
            <div class="parameter-item">
              <span class="parameter-label">终点</span>
              <div class="point-selector">
                <el-input v-model="endPointDisplay" placeholder="点击地图选择终点" readonly>
                  <template #prepend>
                    <el-button @click="selectingPoint = 'end'">
                      <el-icon><Pointer /></el-icon>
                    </el-button>
                  </template>
                </el-input>
              </div>
            </div>
            
            <div class="parameter-item">
              <span class="parameter-label">设备类型</span>
              <el-select v-model="planningParams.deviceType" placeholder="请选择设备类型">
                <el-option label="机器狗" value="robotDog" />
                <el-option label="无人机" value="drone" />
              </el-select>
            </div>
            
            <div class="parameter-item slider-container">
              <span class="parameter-label">避障灵敏度</span>
              <el-slider
                v-model="planningParams.avoidanceLevel"
                :min="1"
                :max="10"
                :step="1"
                :show-tooltip="true"
                class="avoidance-slider"
              />
            </div>

            <div class="parameter-item">
              <span class="parameter-label">路径类型</span>
              <el-radio-group v-model="planningParams.routeType">
                <el-radio label="shortest">最短路径</el-radio>
                <el-radio label="safest">最安全路径</el-radio>
                <el-radio label="optimal">最优路径</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
        
        <div class="preview-section">
          <h3 class="section-title">实时预览</h3>
          <div class="preview-map-container">
            <div ref="previewMap" class="preview-map"></div>
            <div class="preview-info">
              <p v-if="pathPreview">预估距离: {{ pathPreview.distance }}米</p>
              <p v-if="pathPreview">预估时间: {{ formatDuration(pathPreview.estimatedTime || 0) }}</p>
              <p v-if="pathPreview">避障数量: {{ pathPreview.obstacleCount }}个</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="panel-footer">
        <el-button @click="showPanel = false">取消</el-button>
        <el-button
          type="primary"
          :loading="isGenerating"
          @click="generatePath"
        >
          {{ isGenerating ? '生成中...' : '生成路径' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 路径详情侧边栏 -->
    <el-drawer
      v-model="showPathDetail"
      title="路径详情"
      direction="rtl"
      size="30%"
      :destroy-on-close="false"
      class="path-detail-drawer"
    >
      <div v-if="generatedPath" class="path-detail-container">
        <div class="path-overview">
          <h3>路径概览</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="总距离">{{ generatedPath.distance }}米</el-descriptions-item>
            <el-descriptions-item label="预计用时">{{ formatDuration(generatedPath.estimatedTime) }}</el-descriptions-item>
            <el-descriptions-item label="避障数量">{{ generatedPath.obstacleCount }}个</el-descriptions-item>
            <el-descriptions-item label="设备类型">
              {{ generatedPath.deviceType === 'robotDog' ? '机器狗' : '无人机' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ new Date(generatedPath.createdAt).toLocaleString() }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="path-actions">
          <el-button type="primary" @click="savePath">保存路径</el-button>
          <el-button type="success" @click="createTask">创建任务</el-button>
          <el-button @click="showPathDetail = false">关闭</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Location,
  ZoomIn,
  ZoomOut,
  RefreshRight,
  Pointer,
  Refresh,
  View,
  Setting
} from '@element-plus/icons-vue'
import * as d3 from 'd3'
import { pathPlanningApi } from '@/api/taskScheduling'
import type { PathPlanningParams, Coordinate, PathPlanningResult } from '@/types/taskScheduling'

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue'
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue'

const router = useRouter()

// 状态变量
const savedPathsCount = ref(12)
const activeTasksCount = ref(3)
const lastUpdateTime = ref(new Date())

// 地图相关的引用
const mapContainer = ref<HTMLElement | null>(null)
const previewMap = ref<HTMLElement | null>(null)

// 路径规划参数面板
const showPanel = ref(false)
const showPathDetail = ref(false)
const isGenerating = ref(false)
const selectingPoint = ref<'start' | 'end' | null>(null)

// 点坐标显示
const startPointDisplay = computed(() => {
  if (!planningParams.startPoint) return '';
  return `X: ${planningParams.startPoint.x.toFixed(2)}, Y: ${planningParams.startPoint.y.toFixed(2)}`;
});

const endPointDisplay = computed(() => {
  if (!planningParams.endPoint) return '';
  return `X: ${planningParams.endPoint.x.toFixed(2)}, Y: ${planningParams.endPoint.y.toFixed(2)}`;
});

// 地图交互变量
const mapScale = ref(1);
const mapTranslate = ref({ x: 0, y: 0 });
const svg: any = null;
const mapGroup: any = null;

// 路径规划参数
const planningParams = reactive<PathPlanningParams>({
  startPoint: null as unknown as Coordinate, // 类型断言以避免null类型错误
  endPoint: null as unknown as Coordinate, // 类型断言以避免null类型错误
  fieldId: 'field-001', // 默认农田ID
  avoidanceLevel: 5, // 默认避障灵敏度
  deviceType: 'robotDog', // 默认设备类型
  routeType: 'optimal' // 默认路径类型
})

// 路径预览数据
const pathPreview = ref<Partial<PathPlanningResult> | null>(null)
// 生成的路径数据
const generatedPath = ref<PathPlanningResult | null>(null)

// 地图交互函数
const zoomIn = () => {
  mapScale.value *= 1.2;
  updateMapTransform();
  ElMessage.success('地图已放大');
};

const zoomOut = () => {
  mapScale.value /= 1.2;
  updateMapTransform();
  ElMessage.success('地图已缩小');
};

const resetView = () => {
  mapScale.value = 1;
  mapTranslate.value = { x: 0, y: 0 };
  updateMapTransform();
  ElMessage.success('地图视图已重置');
};

const updateMapTransform = () => {
  if (mapGroup) {
    mapGroup.attr('transform', `translate(${mapTranslate.value.x}, ${mapTranslate.value.y}) scale(${mapScale.value})`);
  }
};

// 显示路径规划面板
const showPathPlanningPanel = () => {
  showPanel.value = true;
  // 初始化预览地图
  setTimeout(() => {
    initPreviewMap();
  }, 100);
};

// 格式化时间（Date类型）
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 格式化时间（秒数）
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  let result = '';
  if (hours > 0) {
    result += `${hours}小时`;
  }
  if (minutes > 0 || hours > 0) {
    result += `${minutes}分钟`;
  }
  result += `${remainingSeconds}秒`;
  
  return result;
};

// 初始化地图
const initMap = () => {
  if (mapContainer.value) {
    // 简单地图初始化，避免类型错误
    // 在实际项目中，这里应该使用d3.js或其他地图库进行初始化
    // 由于TypeScript类型问题，这里使用简化版本
    
    // 模拟地图加载
    setTimeout(() => {
      ElMessage.success('地图数据已加载');
    }, 500);
  }
};

// 初始化预览地图
const initPreviewMap = () => {
  if (!previewMap.value) return;
  
  // 简单预览地图初始化，避免类型错误
  // 在实际项目中，这里应该使用d3.js或其他地图库进行初始化
  
  // 模拟路径预览数据
  pathPreview.value = {
    distance: 120,
    estimatedTime: 300,
    obstacleCount: 3
  };
};

// 监听地图点击事件，用于选择起点和终点
const handleMapClick = (event: MouseEvent) => {
  if (!selectingPoint.value) return;
  
  // 获取点击位置相对于地图容器的坐标
  const mapRect = mapContainer.value?.getBoundingClientRect();
  if (!mapRect) return;
  
  const x = Math.round(event.clientX - mapRect.left);
  const y = Math.round(event.clientY - mapRect.top);
  
  // 设置选择的点
  if (selectingPoint.value === 'start') {
    planningParams.startPoint = { x, y };
    ElMessage.success('已设置起点');
  } else {
    planningParams.endPoint = { x, y };
    ElMessage.success('已设置终点');
  }
  
  // 清除选择模式
  selectingPoint.value = null;
  
  // 如果两个点都已设置，生成预览
  if (planningParams.startPoint && planningParams.endPoint) {
    generatePreview();
  }
};

// 生成预览
const generatePreview = () => {
  // 实际项目中应调用API获取预览数据
  // 这里使用模拟数据
  pathPreview.value = {
    distance: Math.floor(Math.random() * 100) + 50,
    estimatedTime: Math.floor(Math.random() * 200) + 100,
    obstacleCount: Math.floor(Math.random() * 5)
  };
};

// 生成路径
const generatePath = async () => {
  // 类型检查，确保起点和终点已设置
  if (!planningParams.startPoint || !planningParams.endPoint || 
      typeof planningParams.startPoint.x === 'undefined' || 
      typeof planningParams.endPoint.x === 'undefined') {
    ElMessage.warning('请先选择起点和终点');
    return;
  }
  
  try {
    isGenerating.value = true;
    // 实际项目中应调用API获取路径规划结果
    // const response = await pathPlanningApi.getPathPlanning(planningParams);
    // generatedPath.value = response.data;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 模拟返回数据
    generatedPath.value = {
      id: 'path-' + Date.now(),
      path: [
        { position: planningParams.startPoint },
        { position: { x: planningParams.startPoint.x + 20, y: planningParams.startPoint.y + 10 } },
        { position: { x: planningParams.startPoint.x + 40, y: planningParams.startPoint.y + 30 } },
        { position: { x: planningParams.startPoint.x + 60, y: planningParams.startPoint.y + 20 } },
        { position: planningParams.endPoint }
      ],
      distance: 120,
      estimatedTime: 300,
      obstacleCount: 3,
      createdAt: new Date().toISOString(),
      fieldId: planningParams.fieldId,
      deviceType: planningParams.deviceType
    };
    
    showPanel.value = false;
    showPathDetail.value = true;
    ElMessage.success('路径规划成功!');
    
    // 在地图上显示生成的路径
    drawPathOnMap();
  } catch (error) {
    console.error('路径规划失败:', error);
    ElMessage.error('路径规划失败，请重试');
  } finally {
    isGenerating.value = false;
  }
};

// 在地图上绘制路径
const drawPathOnMap = () => {
  if (!mapContainer.value || !generatedPath.value) return;
  
  // 使用D3.js在地图上绘制路径
  // 这里应实现实际的路径绘制逻辑
  ElMessage.info('路径已在地图上显示');
};

// 保存路径
const savePath = async () => {
  if (!generatedPath.value) return;
  
  try {
    // 实际项目中应调用API保存路径
    // await pathPlanningApi.savePlannedPath(generatedPath.value);
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    savedPathsCount.value++;
    ElMessage.success('路径已保存');
  } catch (error) {
    console.error('保存路径失败:', error);
    ElMessage.error('保存路径失败，请重试');
  }
};

// 创建任务
const createTask = () => {
  if (!generatedPath.value) return;
  
  ElMessageBox.confirm(
    '是否要基于此路径创建周期性巡航任务?',
    '创建任务',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'info'
    }
  )
    .then(() => {
      // 跳转到任务创建页面，并传递路径数据
      router.push({
        path: '/task-scheduling/periodic-tasks',
        query: { pathId: generatedPath.value?.id }
      });
    })
    .catch(() => {
      // 取消操作
    });
};

// 刷新数据
const refreshData = () => {
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
}

onMounted(() => {
  // 初始化地图
  initMap();
  
  // 添加地图点击事件监听
  if (mapContainer.value) {
    mapContainer.value.addEventListener('click', handleMapClick);
  }
})

onUnmounted(() => {
  // 移除地图点击事件监听
  if (mapContainer.value) {
    mapContainer.value.removeEventListener('click', handleMapClick);
  }
})
</script>

<style scoped lang="scss">
.path-planning-engine {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  margin-bottom: 20px;
}

/* 农田地图容器 */
.farm-map-container {
  flex: 1;
  position: relative;
  min-height: 500px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #1f2937;
  
  .farm-map {
    width: 100%;
    height: 100%;
  }
  
  .map-toolbar {
    position: absolute;
    top: 16px;
    right: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 10;
    
    .toolbar-button {
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      &.path-planning-button {
        background-color: #3b82f6;
        color: white;
        margin-top: 8px;
      }
    }
  }
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 路径规划面板样式 */
:deep(.path-planning-panel) {
  .el-dialog__header {
    background-color: #1f2937;
    padding: 16px 20px;
    
    .el-dialog__title {
      color: white;
      font-weight: bold;
    }
    
    .el-dialog__headerbtn .el-dialog__close {
      color: white;
    }
  }
  
  .el-dialog__body {
    padding: 0;
  }
}

.panel-content {
  display: flex;
  min-height: 400px;
  
  .parameter-section {
    flex: 1;
    padding: 20px;
    border-right: 1px solid #374151;
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #1f2937;
    }
    
    .parameters-container {
      display: flex;
      flex-direction: column;
      gap: 16px;
      
      .parameter-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .parameter-label {
          font-size: 14px;
          color: #4b5563;
        }
        
        &.slider-container {
          margin: 16px 0;
          
          .avoidance-slider {
            width: 100%;
          }
        }
      }
    }
  }
  
  .preview-section {
    flex: 1;
    padding: 20px;
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #1f2937;
    }
    
    .preview-map-container {
      height: 300px;
      background-color: #1f2937;
      border-radius: 8px;
      overflow: hidden;
      
      .preview-map {
        width: 100%;
        height: 100%;
      }
      
      .preview-info {
        margin-top: 16px;
        
        p {
          margin: 8px 0;
          font-size: 14px;
          color: #4b5563;
        }
      }
    }
  }
}

.panel-footer {
  padding: 16px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #374151;
}

/* 路径详情抽屉样式 */
:deep(.path-detail-drawer) {
  .el-drawer__header {
    background-color: #1f2937;
    padding: 16px 20px;
    margin-bottom: 0;
    color: white;
    
    .el-drawer__title {
      color: white;
      font-weight: bold;
    }
    
    .el-drawer__close-btn {
      color: white;
    }
  }
}

.path-detail-container {
  padding: 16px;
  
  .path-overview {
    margin-bottom: 24px;
    
    h3 {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #1f2937;
    }
  }
  
  .path-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 24px;
  }
}
</style> 