<!-- 
  StatusIndicator.vue
  通用状态指示器组件，用于显示设备状态
  支持不同的状态类型和显示方式
-->
<template>
  <div class="status-indicator" :class="[size, { 'with-label': showLabel }]">
    <div class="indicator-dot" :class="type"></div>
    <span v-if="showLabel" class="indicator-label">{{ label || typeLabel }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'normal', // normal, success, warning, error, offline
    validator: (value: string) => {
      return ['normal', 'success', 'warning', 'error', 'offline'].includes(value);
    }
  },
  label: {
    type: String,
    default: ''
  },
  showLabel: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value: string) => {
      return ['small', 'medium', 'large'].includes(value);
    }
  }
});

// 根据类型自动生成标签文本
const typeLabel = computed(() => {
  const labels = {
    normal: '正常',
    success: '成功',
    warning: '警告',
    error: '错误',
    offline: '离线'
  };
  return labels[props.type as keyof typeof labels];
});
</script>

<style scoped>
.status-indicator {
  display: inline-flex;
  align-items: center;
}

.status-indicator.with-label {
  gap: 6px;
}

/* 指示器点的基本样式 */
.indicator-dot {
  border-radius: 50%;
  box-shadow: 0 0 5px currentColor;
}

/* 尺寸变体 */
.status-indicator.small .indicator-dot {
  width: 8px;
  height: 8px;
}

.status-indicator.medium .indicator-dot {
  width: 12px;
  height: 12px;
}

.status-indicator.large .indicator-dot {
  width: 16px;
  height: 16px;
}

/* 状态类型 */
.indicator-dot.normal {
  background-color: #60a5fa;
  color: #60a5fa;
}

.indicator-dot.success {
  background-color: #10b981;
  color: #10b981;
}

.indicator-dot.warning {
  background-color: #f59e0b;
  color: #f59e0b;
}

.indicator-dot.error {
  background-color: #ef4444;
  color: #ef4444;
}

.indicator-dot.offline {
  background-color: #6b7280;
  color: #6b7280;
}

/* 标签样式 */
.indicator-label {
  font-size: 14px;
  color: #4b5563;
}

.status-indicator.small .indicator-label {
  font-size: 12px;
}

.status-indicator.large .indicator-label {
  font-size: 16px;
}
</style> 