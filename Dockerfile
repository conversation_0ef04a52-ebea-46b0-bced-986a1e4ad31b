# 智慧农业前端 Dockerfile
# 支持开发环境和CI/CD构建的多用途配置
# 基于 Node.js 22 Alpine 版本，轻量且高效

FROM node:22-alpine

# 设置构建参数，支持不同的运行模式
ARG BUILD_MODE=development
ENV BUILD_MODE=${BUILD_MODE}

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
# 某些 npm 包可能需要 python3、make、g++ 等编译工具
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    curl

# 设置构建参数用于npm镜像源
ARG NPM_REGISTRY=https://registry.npm.taobao.org/

# 设置 npm 配置以提高安装速度和稳定性
RUN npm config set registry ${NPM_REGISTRY} && \
    npm config set cache /tmp/.npm-cache && \
    npm config set network-timeout 300000 && \
    npm config set fetch-timeout 300000 && \
    npm config set fetch-retries 5

# 设置环境变量以优化依赖安装
ENV SASS_BINARY_SITE=https://npm.taobao.org/mirrors/node-sass/ \
    ELECTRON_MIRROR=https://npm.taobao.org/mirrors/electron/ \
    PUPPETEER_DOWNLOAD_HOST=https://npm.taobao.org/mirrors \
    CHROMEDRIVER_CDNURL=https://npm.taobao.org/mirrors/chromedriver

# 复制 package.json 和 package-lock.json（如果存在）
# 这样做可以利用 Docker 的层缓存机制，只有当依赖发生变化时才重新安装
COPY package*.json ./

# 安装项目依赖
# 使用优化的参数进行更快、更可靠的安装
RUN echo "开始安装依赖..." && \
    if [ -f package-lock.json ]; then \
        npm ci --prefer-offline --no-audit --no-fund --registry=${NPM_REGISTRY}; \
    else \
        npm install --prefer-offline --no-audit --no-fund --registry=${NPM_REGISTRY}; \
    fi && \
    echo "依赖安装完成"

# 复制项目源代码
# 注意：.dockerignore 文件会排除不必要的文件
COPY . .

# 根据构建模式执行不同的构建步骤
RUN if [ "$BUILD_MODE" = "build" ]; then \
        echo "执行构建模式..." && \
        npm run lint && \
        npm run type-check && \
        npm run build-skip-check; \
    else \
        echo "开发模式，跳过构建步骤..."; \
    fi

# 暴露 Vite 开发服务器端口
EXPOSE 5174

# 创建非 root 用户以提高安全性
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 更改文件所有权
RUN chown -R nextjs:nodejs /app
USER nextjs

# 根据构建模式设置不同的启动命令
CMD if [ "$BUILD_MODE" = "build" ]; then \
        echo "构建模式完成，容器将退出" && exit 0; \
    else \
        echo "启动开发服务器..." && npm run dev; \
    fi
