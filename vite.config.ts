import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import cesium from 'vite-plugin-cesium'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())

  return {
    plugins: [
      vue({
        // 添加Vue插件选项，避免一些警告
        template: {
          compilerOptions: {
            // 避免一些模板编译警告
            whitespace: 'preserve',
          }
        }
      }),
      cesium({
        // 指定 Cesium 的路径
        rebuildCesium: false,
      }),
    ],
    server: {
      host: env.VITE_DEV_SERVER_HOST || '0.0.0.0', // 监听所有地址，包括局域网和公网地址
      port: parseInt(env.VITE_DEV_SERVER_PORT) || 5174, // 默认端口，可以根据需要修改
      open: true, // 自动打开浏览器
      proxy: {
        // AI API 代理配置 - 独立的AI服务
        '/api': {
          target: env.VITE_AI_API_PROXY_TARGET || 'http://***********:5000',
          changeOrigin: true,
          rewrite: (path) => path
        },
        // 主要后端API代理配置 - 开发环境代理到本地后端服务器
        [env.VITE_API_BASE_URL || '/api-default']: {
          target: env.VITE_MAIN_API_PROXY_TARGET || 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_API_BASE_URL || '/api-default'}`), '')
        }
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
      // 移除对JSX的支持
      extensions: ['.mjs', '.js', '.ts', '.json', '.vue']
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`
        }
      }
    },
    build: {
      sourcemap: true,
      // 增加chunk大小警告限制，避免不必要的警告
      chunkSizeWarningLimit: 2000,
      rollupOptions: {
        output: {
          manualChunks: {
            echarts: ['echarts'],
            'element-plus': ['element-plus'],
            // 添加更多库的手动分块
            'vue-vendor': ['vue', 'vue-router', 'pinia']
            // cesium 由 vite-plugin-cesium 处理为 external 模块，不需要手动分块
          }
        }
      },
      // 添加构建优化选项
      minify: 'terser',
      terserOptions: {
        compress: {
          // 生产环境下移除console和debugger
          drop_console: command === 'build' && (mode === 'production' || mode === 'staging'),
          drop_debugger: command === 'build' && (mode === 'production' || mode === 'staging')
        }
      }
    }
  }
})
