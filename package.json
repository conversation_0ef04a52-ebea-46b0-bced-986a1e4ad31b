{"name": "web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:prod": "vite build --mode production", "build:stage": "vite build --mode staging", "build-fixed": "npm run lint && npm run build-skip-check", "preview": "vite preview", "build-only": "vite build", "build-skip-check": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@headlessui/vue": "^1.7.23", "@loaders.gl/core": "^4.3.3", "@loaders.gl/gltf": "^4.3.3", "@tailwindcss/postcss": "^4.1.10", "@types/cesium": "^1.67.14", "@types/d3": "^7.4.3", "@types/dompurify": "^3.0.5", "@types/exceljs": "^0.5.3", "@types/highlight.js": "^9.12.4", "@types/leaflet": "^1.9.18", "@types/marked": "^5.0.2", "@types/three": "^0.177.0", "@types/xlsx": "^0.0.35", "@vueuse/motion": "^3.0.3", "animate.css": "^4.1.1", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "bezier-js": "^6.1.4", "canvas": "^3.1.1", "cesium": "^1.129.0", "crypto-js": "^4.2.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "echarts": "^5.6.0", "element-plus": "^2.10.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "gsap": "^3.12.2", "heatmap.js": "^2.0.5", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "joy": "^0.1.1", "js-md5": "^0.8.3", "jsencrypt": "^3.3.2", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "markdown-it": "^14.1.0", "marked": "^15.0.12", "md5": "^2.3.0", "nipplejs": "^0.10.2", "particles.js": "^2.0.0", "pinia": "^3.0.1", "screenfull": "^5.0.2", "simple-statistics": "^7.8.8", "three": "^0.177.0", "three-gltf-loader": "^1.111.0", "three-orbit-controls": "^82.1.0", "uuid": "^11.1.0", "vite-plugin-cesium": "^1.2.23", "vue": "^3.5.16", "vue-echarts": "^7.0.3", "vue-kinesis": "^2.0.5", "vue-material-3": "^0.7.0", "vue-router": "^4.5.0", "vuestic-ui": "^1.10.3", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/vue": "^5.0.0", "@tsconfig/node22": "^22.0.1", "@types/echarts": "^4.9.22", "@types/file-saver": "^2.0.7", "@types/html2canvas": "^0.5.35", "@types/md5": "^2.3.5", "@types/node": "^22.15.21", "@types/uuid": "^10.0.0", "@types/vue": "^1.0.31", "@vitejs/plugin-vue": "^5.2.3", "@vitest/eslint-plugin": "^1.2.2", "@vue/eslint-config-typescript": "^14.5.0", "@vue/runtime-dom": "^3.5.16", "@vue/tsconfig": "^0.7.0", "@vueuse/core": "^13.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss": "^8.5.6", "sass": "^1.69.5", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}