<template>
  <div class="error-container">
    <div class="error-card">
      <div class="error-icon">
        <el-icon><WarningFilled /></el-icon>
      </div>
      <h2 class="error-title">{{ title || '组件加载失败' }}</h2>
      <p class="error-message">{{ message || '加载此页面时发生错误，请稍后再试。' }}</p>
      <div class="error-actions">
        <el-button type="primary" @click="reload">重新加载</el-button>
        <el-button @click="goBack">返回上一页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { WarningFilled } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { defineProps } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  }
});

const router = useRouter();

// 重新加载当前页面
const reload = () => {
  window.location.reload();
};

// 返回上一页
const goBack = () => {
  router.back();
};
</script>

<style scoped>
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
}

.error-card {
  background: rgba(30, 41, 59, 0.8);
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.error-icon {
  font-size: 48px;
  color: #e67e22;
  margin-bottom: 20px;
}

.error-title {
  color: #ffffff;
  font-size: 24px;
  margin: 0 0 10px;
}

.error-message {
  color: #94a3b8;
  font-size: 16px;
  margin: 0 0 30px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}
</style>