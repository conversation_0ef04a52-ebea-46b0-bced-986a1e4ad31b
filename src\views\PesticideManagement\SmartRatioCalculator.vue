<!-- 
  SmartRatioCalculator.vue
  智能配比计算器模块
  根据作物种类、病虫害类型、防治面积等参数，智能计算农药配比方案
-->
<template>
  <div class="smart-ratio-calculator">
    <!-- 页面标题 -->
    <PageHeader
      title="智能配比计算器"
      description="根据作物种类、病虫害类型、防治面积等参数，自动计算出所需的农药用量和配比方案"
      icon="Monitor"
    >
      <template #actions>
        <div class="calculation-status" v-if="calculating">
          <StatusIndicator type="warning" label="正在计算..." size="large" />
        </div>
      </template>
    </PageHeader>
    
    <!-- 引导式表单区域 -->
    <div class="calculator-panels">
      <!-- 步骤表单面板 -->
      <DataPanel title="配比计算参数" dark>
        <div class="step-form">
          <el-steps :active="activeStep" finish-status="success" simple>
            <el-step title="作物与病虫害" />
            <el-step title="环境与限制条件" />
            <el-step title="生成推荐方案" />
          </el-steps>
          
          <!-- 步骤1: 作物与病虫害信息 -->
          <div v-if="activeStep === 1" class="step-content">
            <el-form :model="formData" label-position="top">
              <el-form-item label="作物种类">
                <div class="crop-selector">
                  <div 
                    v-for="crop in cropOptions" 
                    :key="crop.value"
                    class="crop-item"
                    :class="{ 'active': formData.cropType === crop.value }"
                    @click="handleCropSelect(crop.value)"
                  >
                    <el-icon class="crop-icon"><component :is="crop.icon" /></el-icon>
                    <span class="crop-name">{{ crop.label }}</span>
                  </div>
                </div>
              </el-form-item>
              
              <el-form-item label="病虫害类型">
                <el-select
                  v-model="formData.pestType"
                  filterable
                  placeholder="选择或搜索病虫害类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in pestTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="作物生长阶段">
                <el-select
                  v-model="formData.cropGrowthStage"
                  placeholder="选择作物生长阶段"
                  style="width: 100%"
                >
                  <el-option label="幼苗期" value="seedling" />
                  <el-option label="生长期" value="growing" />
                  <el-option label="开花期" value="flowering" />
                  <el-option label="结果期" value="fruiting" />
                  <el-option label="成熟期" value="mature" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="防治面积">
                <el-input-number
                  v-model="formData.fieldArea"
                  :min="0.1"
                  :step="0.1"
                  :precision="1"
                  style="width: 180px"
                />
                <el-select
                  v-model="formData.areaUnit"
                  style="width: 100px; margin-left: 10px"
                >
                  <el-option label="亩" value="亩" />
                  <el-option label="平方米" value="平方米" />
                  <el-option label="公顷" value="公顷" />
                </el-select>
              </el-form-item>
            </el-form>
            
            <div class="step-actions">
              <el-button type="primary" @click="nextStep">下一步</el-button>
            </div>
          </div>
          
          <!-- 步骤2: 环境与限制条件 -->
          <div v-if="activeStep === 2" class="step-content">
            <el-form :model="formData" label-position="top">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="环境温度 (°C)">
                    <el-input-number
                      v-model="formData.temperature"
                      :min="-20"
                      :max="50"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="环境湿度 (%)">
                    <el-input-number
                      v-model="formData.humidity"
                      :min="0"
                      :max="100"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="风速 (m/s)">
                    <el-input-number
                      v-model="formData.windSpeed"
                      :min="0"
                      :max="30"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="病虫害严重程度">
                <el-radio-group v-model="formData.infestationLevel">
                  <el-radio label="light">轻度</el-radio>
                  <el-radio label="medium">中度</el-radio>
                  <el-radio label="severe">重度</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="施药方式">
                <el-select
                  v-model="formData.applicationMethod"
                  placeholder="选择施药方式"
                  style="width: 100%"
                >
                  <el-option label="无人机喷洒" value="drone" />
                  <el-option label="人工喷洒" value="manual" />
                  <el-option label="机械牵引式喷洒" value="tractor" />
                  <el-option label="固定式喷灌" value="irrigation" />
                </el-select>
              </el-form-item>
            </el-form>
            
            <div class="step-actions">
              <el-button @click="prevStep">上一步</el-button>
              <el-button type="primary" @click="nextStep">下一步</el-button>
            </div>
          </div>
          
          <!-- 步骤3: 推荐方案生成 -->
          <div v-if="activeStep === 3" class="step-content">
            <div class="calculate-section">
              <el-button 
                type="primary" 
                :icon="Lightning" 
                :loading="calculating"
                @click="calculateRatio"
                size="large"
              >
                {{ calculating ? '计算中...' : '计算配比方案' }}
              </el-button>
              
              <div class="progress-text" v-if="calculating">
                {{ calculationProgressText }}
              </div>
            </div>
            
            <div class="recommendations-section" v-if="recommendationResults.length > 0">
              <div class="recommendations-title">推荐方案</div>
              <div class="recommendations-wrapper">
                <div 
                  v-for="(item, index) in recommendationResults" 
                  :key="item.id"
                  class="recommendation-card"
                  :class="{ 'active': activeRecommendationIndex === index }"
                  @click="setActiveRecommendation(index)"
                >
                  <div class="recommendation-score">
                    <el-rate
                      v-model="item.effectiveness"
                      disabled
                      :max="5"
                      :texts="['极差', '差', '一般', '好', '极好']"
                      show-score
                      text-color="#ff9900"
                    />
                  </div>
                  <h3 class="recommendation-title">{{ item.pesticide.name }}</h3>
                  <div class="recommendation-type">{{ item.pesticide.formulation }}</div>
                  <div class="recommendation-dosage">
                    推荐用量: {{ item.quantity }} {{ item.unit }}
                  </div>
                  <div class="recommendation-mixing">
                    配比方案: {{ item.dilutionRatio }}
                  </div>
                  <div class="recommendation-water">
                    用水量: {{ item.waterVolume }} L
                  </div>
                  <div class="recommendation-actions">
                    <el-button size="small" type="default" @click.stop="viewRecommendationDetails(item)">
                      <el-icon><Document /></el-icon>
                      查看详情
                    </el-button>
                    <el-button size="small" type="primary" @click.stop="favoriteRecommendation(item)">
                      <el-icon><Star /></el-icon>
                      收藏
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="step-actions">
              <el-button @click="prevStep">上一步</el-button>
              <el-button type="primary" @click="reset" v-if="recommendationResults.length > 0">重新计算</el-button>
            </div>
          </div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="参数验证通过" />
        <StatusIndicator type="normal" label="AI引擎就绪" />
        <StatusIndicator type="warning" label="仅供参考" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 方案详情对话框 -->
    <el-dialog
      v-model="detailsDialogVisible"
      title="推荐方案详情"
      width="600px"
    >
      <div class="recommendation-details" v-if="selectedRecommendation">
        <h2>{{ selectedRecommendation.pesticide.name }}</h2>
        <div class="details-meta">
          <div class="details-type">类型: {{ selectedRecommendation.pesticide.formulation }}</div>
          <div class="details-score">
            评分:
            <el-rate
              v-model="selectedRecommendation.effectiveness"
              disabled
              show-score
              text-color="#ff9900"
            />
          </div>
        </div>
        
        <el-divider />
        
        <div class="details-section">
          <h3>用量信息</h3>
          <div class="details-item">
            <span class="item-label">推荐用量:</span>
            <span class="item-value">{{ selectedRecommendation.quantity }} {{ selectedRecommendation.unit }}</span>
          </div>
          <div class="details-item">
            <span class="item-label">配比方案:</span>
            <span class="item-value">{{ selectedRecommendation.dilutionRatio }}</span>
          </div>
          <div class="details-item">
            <span class="item-label">用水量:</span>
            <span class="item-value">{{ selectedRecommendation.waterVolume }} L</span>
          </div>
          <div class="details-item">
            <span class="item-label">安全分数:</span>
            <span class="item-value">{{ selectedRecommendation.safetyScore }}/5</span>
          </div>
          <div class="details-item">
            <span class="item-label">环境影响:</span>
            <span class="item-value">{{ selectedRecommendation.environmentalImpact }}/5</span>
          </div>
          <div class="details-item">
            <span class="item-label">成本估算:</span>
            <span class="item-value">¥{{ selectedRecommendation.costEstimate.toFixed(2) }}</span>
          </div>
        </div>
        
        <el-divider />
        
        <div class="details-section">
          <h3>注意事项</h3>
          <ul class="details-list precautions">
            <li v-for="(item, index) in selectedRecommendation.precautions" :key="index">
              {{ item }}
            </li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailsDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="favoriteRecommendation(selectedRecommendation)">
            收藏方案
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Monitor, 
  Lightning, 
  Document, 
  Star,
  Refresh
} from '@element-plus/icons-vue'
import { ratioCalculatorApi } from '@/api/pesticideApi'
import type { RatioCalculationParams, RatioRecommendation } from '@/types/pesticide'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import DataPanel from './components/DataPanel.vue'
import StatusIndicator from './components/StatusIndicator.vue'

// 激活步骤
const activeStep = ref(1)

// 表单数据
const formData = ref<RatioCalculationParams>({
  cropType: '',
  pestType: '',
  cropGrowthStage: 'growing',
  fieldArea: 1,
  areaUnit: '亩',
  infestationLevel: 'medium',
  applicationMethod: 'manual',
  temperature: 25,
  humidity: 65,
  windSpeed: 2
})

// 作物选项
const cropOptions = [
  { label: '水稻', value: 'rice', icon: 'Grape' },
  { label: '小麦', value: 'wheat', icon: 'Orange' },
  { label: '玉米', value: 'corn', icon: 'Pear' },
  { label: '大豆', value: 'soybean', icon: 'Cherry' },
  { label: '蔬菜', value: 'vegetable', icon: 'Apple' }
]

// 病虫害类型选项接口
interface PestTypeOption {
  label: string;
  value: string;
  disabled?: boolean;
}

// 病虫害类型选项
const pestTypeOptions = ref<PestTypeOption[]>([
  { label: '稻飞虱', value: 'rice_planthopper' },
  { label: '稻纵卷叶螟', value: 'rice_leaf_roller' },
  { label: '小麦蚜虫', value: 'wheat_aphid' },
  { label: '玉米螟', value: 'corn_borer' },
  { label: '大豆食心虫', value: 'soybean_pod_borer' },
  { label: '蔬菜炭疽病', value: 'vegetable_anthracnose' },
])

// 计算状态
const calculating = ref(false)
const calculationProgressText = ref('')

// 最后更新时间
const lastUpdateTime = ref(new Date())

// 推荐结果
const recommendationResults = ref<RatioRecommendation[]>([])
const activeRecommendationIndex = ref(0)
const selectedRecommendation = ref<RatioRecommendation | null>(null)
const detailsDialogVisible = ref(false)

// 加载状态
const loadingPestTypes = ref(false)

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 下一步
const nextStep = () => {
  if (activeStep.value < 3) {
    if (activeStep.value === 1) {
      // 验证第一步表单
      if (!formData.value.cropType || !formData.value.pestType || !formData.value.fieldArea) {
        ElMessage.warning('请填写必要的计算参数')
        return
      }
    }
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 1) {
    activeStep.value--
  }
}

// 重置表单
const reset = () => {
  activeStep.value = 1
  recommendationResults.value = []
  activeRecommendationIndex.value = 0
  lastUpdateTime.value = new Date()
}

// 根据作物类型加载模拟病虫害数据
const loadMockPestTypes = (cropType: string) => {
  let options = [];
  
  switch(cropType) {
    case 'rice':
      options = [
        { label: '稻飞虱', value: 'rice_planthopper' },
        { label: '稻纵卷叶螟', value: 'rice_leaf_roller' },
        { label: '稻瘟病', value: 'rice_blast' },
        { label: '水稻细菌性条斑病', value: 'rice_bacterial_streak' },
        { label: '水稻恶苗病', value: 'rice_bakanae_disease' },
        { label: '水稻稻曲病', value: 'rice_false_smut' },
        { label: '水稻白叶枯病', value: 'rice_bacterial_blight' },
        { label: '稻水象甲', value: 'rice_water_weevil' }
      ];
      break;
    case 'wheat':
      options = [
        { label: '小麦蚜虫', value: 'wheat_aphid' },
        { label: '小麦锈病', value: 'wheat_rust' },
        { label: '小麦条纹花叶病', value: 'wheat_streak_mosaic' },
        { label: '小麦白粉病', value: 'wheat_powdery_mildew' },
        { label: '小麦根腐病', value: 'wheat_root_rot' },
        { label: '小麦赤霉病', value: 'wheat_head_blight' },
        { label: '小麦黑穗病', value: 'wheat_common_bunt' },
        { label: '小麦吸浆虫', value: 'wheat_midge' }
      ];
      break;
    case 'corn':
      options = [
        { label: '玉米螟', value: 'corn_borer' },
        { label: '玉米灰斑病', value: 'corn_gray_leaf_spot' },
        { label: '玉米大斑病', value: 'corn_southern_leaf_blight' },
        { label: '玉米丝黑穗病', value: 'corn_head_smut' },
        { label: '玉米茎腐病', value: 'corn_stalk_rot' },
        { label: '玉米粗缩病', value: 'corn_rough_dwarf' },
        { label: '玉米蚜虫', value: 'corn_aphid' },
        { label: '玉米叶斑病', value: 'corn_leaf_spot' }
      ];
      break;
    case 'soybean':
      options = [
        { label: '大豆食心虫', value: 'soybean_pod_borer' },
        { label: '大豆根腐病', value: 'soybean_root_rot' },
        { label: '大豆花叶病毒病', value: 'soybean_mosaic_virus' },
        { label: '大豆疫病', value: 'soybean_phytophthora_rot' },
        { label: '大豆锈病', value: 'soybean_rust' },
        { label: '大豆蚜虫', value: 'soybean_aphid' },
        { label: '大豆菌核病', value: 'soybean_sclerotinia_stem_rot' },
        { label: '大豆褐斑病', value: 'soybean_brown_spot' }
      ];
      break;
    case 'vegetable':
      options = [
        { label: '蔬菜炭疽病', value: 'vegetable_anthracnose' },
        { label: '蔬菜白粉病', value: 'vegetable_powdery_mildew' },
        { label: '蔬菜霜霉病', value: 'vegetable_downy_mildew' },
        { label: '蔬菜黑斑病', value: 'vegetable_black_spot' },
        { label: '蔬菜根结线虫病', value: 'vegetable_root_knot_nematode' },
        { label: '蔬菜软腐病', value: 'vegetable_soft_rot' },
        { label: '蔬菜灰霉病', value: 'vegetable_gray_mold' },
        { label: '蔬菜蚜虫', value: 'vegetable_aphid' },
        { label: '蔬菜菜青虫', value: 'vegetable_cabbage_worm' },
        { label: '蔬菜菜螟', value: 'vegetable_cabbage_webworm' }
      ];
      break;
    default:
      options = [
        { label: '请先选择作物类型', value: '' }
      ];
  }
  
  return options;
}

// 更新病虫害选项
const updatePestTypeOptions = (cropType: string) => {
  // 显示加载状态
  loadingPestTypes.value = true;
  pestTypeOptions.value = [{ label: '加载中...', value: '', disabled: true }];
  
  // 使用setTimeout模拟加载过程，提供更好的用户体验
  setTimeout(() => {
    try {
      // 直接使用预设的模拟数据
      pestTypeOptions.value = loadMockPestTypes(cropType);
    } catch (error) {
      console.error('Failed to load pest types:', error);
      pestTypeOptions.value = [
        { label: '请先选择作物类型', value: '' }
      ];
    } finally {
      loadingPestTypes.value = false;
      
      // 如果有选项，默认选择第一个
      if (pestTypeOptions.value.length > 0 && pestTypeOptions.value[0].value) {
        formData.value.pestType = pestTypeOptions.value[0].value;
      }
    }
  }, 300); // 添加300毫秒延迟，模拟加载过程
}

// 处理作物选择
const handleCropSelect = (cropValue: string) => {
  formData.value.cropType = cropValue
  // 重置已选的病虫害类型
  formData.value.pestType = ''
  // 根据作物类型更新病虫害选项
  updatePestTypeOptions(cropValue)
}

// 计算配比方案
const calculateRatio = async () => {
  // 表单验证
  if (!formData.value.cropType || !formData.value.pestType || !formData.value.fieldArea) {
    ElMessage.warning('请填写必要的计算参数')
    return
  }
  
  // 执行计算
  calculating.value = true
  calculationProgressText.value = '正在分析作物与病虫害信息...'
  
  try {
    // 模拟计算进度
    await simulateCalculationProgress()
    
    // 调用API
    const results = await ratioCalculatorApi.calculateRatio(formData.value)
    
    if (results.length > 0) {
      recommendationResults.value = results
      activeRecommendationIndex.value = 0
      lastUpdateTime.value = new Date()
      ElMessage.success('配比方案计算完成')
    } else {
      ElMessage.warning('没有找到合适的配比方案，请调整参数后重试')
    }
  } catch (error) {
    ElMessage.error('计算配比方案失败')
    console.error('Failed to calculate ratio:', error)
  } finally {
    calculating.value = false
    calculationProgressText.value = ''
  }
}

// 模拟计算进度
const simulateCalculationProgress = async () => {
  return new Promise<void>((resolve) => {
    let progress = 0
    const progressSteps = [
      '正在分析作物与病虫害信息...',
      '正在评估环境参数...',
      '正在计算适配药剂...',
      '正在生成推荐方案...',
      '正在优化配比结果...'
    ]
    
    const interval = setInterval(() => {
      progress += 25
      const stepIndex = Math.min(Math.floor(progress / 20), progressSteps.length - 1)
      calculationProgressText.value = progressSteps[stepIndex]
      
      if (progress >= 100) {
        clearInterval(interval)
        resolve()
      }
    }, 500)
  })
}

// 设置活动推荐
const setActiveRecommendation = (index: number) => {
  activeRecommendationIndex.value = index
}

// 查看推荐详情
const viewRecommendationDetails = (recommendation: RatioRecommendation) => {
  selectedRecommendation.value = recommendation
  detailsDialogVisible.value = true
}

// 收藏推荐方案
const favoriteRecommendation = async (recommendation: RatioRecommendation | null) => {
  if (!recommendation) return
  
  try {
    await ratioCalculatorApi.saveFavoriteRecommendation(recommendation.id)
    ElMessage.success('已成功收藏推荐方案')
  } catch (error) {
    ElMessage.error('收藏推荐方案失败')
    console.error('Failed to favorite recommendation:', error)
  }
}

// 刷新数据
const refreshData = () => {
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
}

// 初始化
onMounted(() => {
  // 如果已经选择了作物类型，加载对应的病虫害类型选项
  if (formData.value.cropType) {
    updatePestTypeOptions(formData.value.cropType)
  } else {
    // 如果没有选择作物类型，默认选择第一个作物并加载对应的病虫害类型
    if (cropOptions.length > 0) {
      formData.value.cropType = cropOptions[0].value;
      updatePestTypeOptions(cropOptions[0].value);
    }
  }
})
</script>

<style scoped>
.smart-ratio-calculator {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.calculation-status {
  display: flex;
  align-items: center;
}

/* 计算面板区域 */
.calculator-panels {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
  overflow: auto;
}

/* 步骤表单 */
.step-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-content {
  padding: 20px 0;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* 作物选择器 */
.crop-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 15px;
}

.crop-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border-radius: 8px;
  background-color: rgba(31, 41, 55, 0.2);
  cursor: pointer;
  transition: all 0.3s;
}

.crop-item:hover {
  background-color: rgba(31, 41, 55, 0.3);
}

.crop-item.active {
  background-color: rgba(59, 130, 246, 0.2);
  border: 1px solid #3b82f6;
}

.crop-icon {
  font-size: 32px;
  color: #e5e7eb;
}

.crop-name {
  font-size: 14px;
  font-weight: 500;
  color: #e5e7eb;
}

/* 计算区域 */
.calculate-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.progress-text {
  color: #3b82f6;
  font-size: 14px;
}

/* 推荐方案区域 */
.recommendations-section {
  margin-bottom: 24px;
}

.recommendations-title {
  font-size: 16px;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 16px;
}

.recommendations-wrapper {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding-bottom: 16px;
}

.recommendation-card {
  min-width: 280px;
  flex: 0 0 auto;
  background-color: rgba(31, 41, 55, 0.2);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.recommendation-card:hover {
  background-color: rgba(31, 41, 55, 0.3);
}

.recommendation-card.active {
  background-color: rgba(59, 130, 246, 0.2);
  border: 1px solid #3b82f6;
}

.recommendation-score {
  margin-bottom: 12px;
}

.recommendation-title {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #e5e7eb;
}

.recommendation-type {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 12px;
}

.recommendation-dosage,
.recommendation-mixing,
.recommendation-water {
  margin-bottom: 8px;
  font-size: 14px;
  color: #e5e7eb;
}

.recommendation-actions {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 详情对话框样式 */
.recommendation-details {
  color: #333;
}

.recommendation-details h2 {
  margin-top: 0;
  color: #1e3a8a;
}

.details-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.details-type {
  font-size: 14px;
  color: #666;
}

.details-section {
  margin-bottom: 20px;
}

.details-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #1e3a8a;
}

.details-item {
  margin-bottom: 8px;
}

.item-label {
  font-weight: 500;
  color: #666;
}

.item-value {
  margin-left: 8px;
}

.details-list {
  margin: 0;
  padding-left: 20px;
}

.details-list li {
  margin-bottom: 6px;
}

.details-list.precautions li {
  color: #f59e0b;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .recommendations-wrapper {
    flex-direction: column;
  }
  
  .recommendation-card {
    width: 100%;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style> 