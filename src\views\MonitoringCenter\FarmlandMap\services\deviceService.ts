/**
 * 设备服务 - 负责设备数据和模拟
 */
import type { DeviceInfo, DeviceStats, FarmlandArea } from '../types';

export class DeviceService {
  private devices: DeviceInfo[] = [
    {
      id: 'dog-01',
      name: '机器狗-01',
      type: 'dog',
      position: {
        x: 200,
        y: 300
      },
      status: 'online',
      location: '东区农田边界',
      installDate: '2023-05-10',
      battery: 85,
      data: {
        temperature: '26.5',
        humidity: '68',
        windSpeed: '3.2',
        soilMoisture: '72'
      }
    },
    {
      id: 'dog-02',
      name: '机器狗-02',
      type: 'dog',
      position: {
        x: 350,
        y: 250
      },
      status: 'standby',
      location: '南区农田巡逻点',
      installDate: '2023-06-18',
      battery: 45,
      data: {
        temperature: '25.8',
        humidity: '65',
        windSpeed: '2.8',
        soilMoisture: '68'
      }
    },
    {
      id: 'drone-01',
      name: '无人机-01',
      type: 'drone',
      position: {
        x: 400,
        y: 150
      },
      status: 'online',
      location: '北区上空',
      installDate: '2023-04-22',
      battery: 92,
      data: {
        temperature: '24.2',
        humidity: '60',
        windSpeed: '4.5',
        light: '85000'
      }
    },
    {
      id: 'drone-02',
      name: '无人机-02',
      type: 'drone',
      position: {
        x: 500,
        y: 200
      },
      status: 'offline',
      location: '西区维修站',
      installDate: '2023-03-15',
      battery: 12,
      data: {
        temperature: '23.8',
        humidity: '63',
        windSpeed: '3.0',
        light: '78000'
      }
    }
  ];
  
  private farmlandArea: FarmlandArea | null = null;
  
  // 设备工作统计数据
  private droneFlyingHours: string = '128.5';
  private dogPatrolHours: string = '356.2';
  private monitorWorkDays: string = '95';
  private dataCollectionSize: string = '1.28';
  
  // 设置农田区域数据
  public setFarmlandArea(area: FarmlandArea): void {
    this.farmlandArea = area;
  }
  
  // 获取设备列表
  public getDevices(): DeviceInfo[] {
    return this.devices;
  }
  
  // 获取设备统计数据
  public getDeviceStats(): DeviceStats {
    // 统计各种状态的设备数量
    let onlineCount = 0;
    let standbyCount = 0;
    let offlineCount = 0;
    
    this.devices.forEach(device => {
      if (device.status === 'online') onlineCount++;
      else if (device.status === 'standby') standbyCount++;
      else if (device.status === 'offline') offlineCount++;
    });
    
    return {
      onlineCount,
      standbyCount,
      offlineCount,
      droneFlyingHours: this.droneFlyingHours,
      dogPatrolHours: this.dogPatrolHours,
      monitorWorkDays: this.monitorWorkDays,
      dataCollectionSize: this.dataCollectionSize
    };
  }
  
  // 模拟设备状态变化
  public simulateDeviceStatusChanges(): void {
    // 随机选择一个设备
    const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
    // 随机选择一个状态
    const statuses: ('online' | 'standby' | 'offline')[] = ['online', 'standby', 'offline'];
    randomDevice.status = statuses[Math.floor(Math.random() * statuses.length)];
  }
  
  // 模拟设备移动
  public simulateDeviceMovement(): void {
    if (!this.farmlandArea) return;
    
    this.devices.forEach(device => {
      // 只移动在线的设备
      if (device.status === 'online') {
        // 添加小范围随机移动
        device.position.x += (Math.random() - 0.5) * 10;
        device.position.y += (Math.random() - 0.5) * 10;
        
        // 确保设备不会移出农田范围太远
        const padding = 50;
        const minX = this.farmlandArea!.x - padding;
        const maxX = this.farmlandArea!.x + this.farmlandArea!.width + padding;
        const minY = this.farmlandArea!.y - padding;
        const maxY = this.farmlandArea!.y + this.farmlandArea!.height + padding;
        
        device.position.x = Math.max(minX, Math.min(maxX, device.position.x));
        device.position.y = Math.max(minY, Math.min(maxY, device.position.y));
      }
    });
  }
  
  // 更新设备工作时间统计
  public updateWorkStats(): void {
    // 更新设备工作时间（模拟随时间增加）
    this.droneFlyingHours = (parseFloat(this.droneFlyingHours) + 0.01).toFixed(1);
    this.dogPatrolHours = (parseFloat(this.dogPatrolHours) + 0.02).toFixed(1);
    this.dataCollectionSize = (parseFloat(this.dataCollectionSize) + 0.001).toFixed(3);
  }
  
  // 添加设备
  public addDevice(device: DeviceInfo): void {
    this.devices.push(device);
  }
  
  // 更新设备状态
  public updateDeviceStatus(deviceId: string, status: 'online' | 'standby' | 'offline'): void {
    const device = this.devices.find(d => d.id === deviceId);
    if (device) {
      device.status = status;
    }
  }
  
  // 更新设备位置
  public updateDevicePosition(deviceId: string, x: number, y: number): void {
    const device = this.devices.find(d => d.id === deviceId);
    if (device) {
      device.position = { x, y };
    }
  }
}

// 导出单例实例
export const deviceService = new DeviceService(); 