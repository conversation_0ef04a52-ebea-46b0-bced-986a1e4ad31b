// 布局主题配置
export interface ThemeColors {
  primary: string;
  primaryLight: string;
  primaryDark: string;
  primaryGlow: string;
  textPrimary: string;
  textSecondary: string;
  background: string;
  backgroundSecondary: string;
  border: string;
  success: string;
  warning: string;
  danger: string;
  info: string;
}

// 各模块主题色配置
export const moduleThemes: Record<string, ThemeColors> = {
  // 监控中心模块主题
  monitoringCenter: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // 环境感知模块主题
  environment: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // AI助手模块主题
  aiAssistant: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // 虫害分析模块主题
  pestAnalysis: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // 决策支持模块主题
  decisionSupport: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // 生物防治模块主题
  biologicalControl: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // 任务调度模块主题
  taskScheduling: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // 农药管理模块主题
  pesticideManagement: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // 设备管理模块主题
  deviceManagement: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  },
  
  // 默认主题
  default: {
    primary: '#10b981',
    primaryLight: '#34d399',
    primaryDark: '#059669',
    primaryGlow: 'rgba(16, 185, 129, 0.5)',
    textPrimary: '#ffffff',
    textSecondary: '#d1d5db',
    background: '#1f2937',
    backgroundSecondary: '#374151',
    border: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6366f1'
  }
};

// 获取模块主题色
export function getModuleTheme(moduleName: string): ThemeColors {
  return moduleThemes[moduleName] || moduleThemes.default;
}

// 常量配置
export const LAYOUT_CONSTANTS = {
  ASIDE_WIDTH_EXPANDED: '240px',
  ASIDE_WIDTH_COLLAPSED: '60px',
  HEADER_HEIGHT: '60px',
  ANIMATION_DURATION: '0.3s',
  TRANSITION_TIMING: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  BREAKPOINTS: {
    MOBILE: '480px',
    TABLET: '768px',
    LAPTOP: '1024px'
  }
};

// 定义农业主题元素
export const AGRI_TECH_ELEMENTS = {
  // 作物图标
  crops: ['wheat', 'corn', 'rice', 'soybean', 'cotton', 'vegetable'],
  // 环境图标
  environment: ['temperature', 'humidity', 'soil', 'rain', 'wind', 'light'],
  // 虫害图标
  pests: ['beetle', 'worm', 'moth', 'aphid', 'locust'],
  // 设备图标
  devices: ['sensor', 'drone', 'tractor', 'irrigation', 'robot', 'sprayer']
};

// 动画预设
export const ANIMATION_PRESETS = {
  FADE_IN: {
    initial: { opacity: 0 },
    enter: { opacity: 1, transition: { duration: 0.5 } }
  },
  SLIDE_UP: {
    initial: { opacity: 0, y: 20 },
    enter: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  },
  SLIDE_LEFT: {
    initial: { opacity: 0, x: 20 },
    enter: { opacity: 1, x: 0, transition: { duration: 0.5 } }
  },
  ZOOM_IN: {
    initial: { opacity: 0, scale: 0.9 },
    enter: { opacity: 1, scale: 1, transition: { duration: 0.5 } }
  }
}; 