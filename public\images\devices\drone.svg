<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <style>
    .body { fill: #3b82f6; }
    .arm { fill: #1e3a8a; }
    .propeller { fill: #60a5fa; }
    .propeller-blur { fill: #60a5fa; opacity: 0.5; }
    .camera { fill: #10b981; }
    .highlight { fill: #ffffff; opacity: 0.3; }
    .sensor { fill: #f59e0b; }
  </style>
  
  <!-- Body -->
  <circle class="body" cx="100" cy="100" r="25" />
  <circle class="highlight" cx="90" cy="90" r="10" />
  
  <!-- Arms -->
  <rect class="arm" x="40" y="40" width="40" height="8" rx="4" transform="rotate(-45 40 40)" />
  <rect class="arm" x="120" y="40" width="40" height="8" rx="4" transform="rotate(45 120 40)" />
  <rect class="arm" x="40" y="160" width="40" height="8" rx="4" transform="rotate(45 40 160)" />
  <rect class="arm" x="120" y="160" width="40" height="8" rx="4" transform="rotate(-45 120 160)" />
  
  <!-- Propellers -->
  <circle class="propeller" cx="40" cy="40" r="15" />
  <ellipse class="propeller-blur" cx="40" cy="40" rx="25" ry="5" transform="rotate(45 40 40)" />
  
  <circle class="propeller" cx="160" cy="40" r="15" />
  <ellipse class="propeller-blur" cx="160" cy="40" rx="25" ry="5" transform="rotate(-45 160 40)" />
  
  <circle class="propeller" cx="40" cy="160" r="15" />
  <ellipse class="propeller-blur" cx="40" cy="160" rx="25" ry="5" transform="rotate(-45 40 160)" />
  
  <circle class="propeller" cx="160" cy="160" r="15" />
  <ellipse class="propeller-blur" cx="160" cy="160" rx="25" ry="5" transform="rotate(45 160 160)" />
  
  <!-- Camera -->
  <circle class="camera" cx="100" cy="115" r="8" />
  <circle class="highlight" cx="97" cy="112" r="3" />
  
  <!-- Sensor -->
  <circle class="sensor" cx="100" cy="85" r="5" />
</svg> 