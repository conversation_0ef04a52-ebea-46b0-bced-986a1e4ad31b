<!-- 
  StatusIndicator.vue
  状态指示器组件
  用于在监控中心各页面中显示不同状态的指示器
-->
<template>
  <div class="status-indicator" :class="type">
    <div class="indicator-dot"></div>
    <span class="indicator-label">{{ label }}</span>
  </div>
</template>

<script setup lang="ts">
defineProps({
  type: {
    type: String,
    default: 'normal',
    validator: (value: string) => {
      return ['success', 'warning', 'error', 'offline', 'normal'].includes(value);
    }
  },
  label: {
    type: String,
    required: true
  }
});
</script>

<style scoped>
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: rgba(31, 41, 55, 0.3);
}

.indicator-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.indicator-label {
  font-size: 14px;
  color: #ffffff;
}

/* 状态颜色 */
.success .indicator-dot {
  background-color: #10b981; /* 绿色 */
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.warning .indicator-dot {
  background-color: #f59e0b; /* 黄色 */
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.6);
}

.error .indicator-dot {
  background-color: #ef4444; /* 红色 */
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
}

.offline .indicator-dot {
  background-color: #6b7280; /* 灰色 */
  box-shadow: 0 0 8px rgba(107, 114, 128, 0.6);
}

.normal .indicator-dot {
  background-color: #3b82f6; /* 蓝色 */
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}
</style> 