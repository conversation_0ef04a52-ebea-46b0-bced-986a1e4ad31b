/**
 * 设备相关配置
 * 存放设备连接、控制相关的配置参数
 */

import envConfig from './env';



// WebSocket连接配置
export const WEBSOCKET_CONFIG = {
  // 最大重连次数
  MAX_RECONNECT_ATTEMPTS: envConfig.websocketReconnectAttempts,
  // 重连间隔（毫秒）
  RECONNECT_INTERVAL: envConfig.websocketReconnectInterval,
  // 心跳间隔（毫秒）
  HEARTBEAT_INTERVAL: envConfig.websocketHeartbeatInterval,
  // 连接超时时间（毫秒）
  CONNECTION_TIMEOUT: 5000,
  // 消息队列最大长度
  MAX_MESSAGE_QUEUE: 100
} as const;

// 传感器配置
export const SENSOR_CONFIG = {
  // 温度传感器配置
  TEMPERATURE: {
    MIN_VALUE: -40,
    MAX_VALUE: 80,
    UNIT: '°C',
    PRECISION: 1
  },
  // 湿度传感器配置
  HUMIDITY: {
    MIN_VALUE: 0,
    MAX_VALUE: 100,
    UNIT: '%',
    PRECISION: 1
  },
  // 光照传感器配置
  ILLUMINANCE: {
    MIN_VALUE: 0,
    MAX_VALUE: 100000,
    UNIT: 'lux',
    PRECISION: 0
  },
  // 土壤湿度传感器配置
  SOIL_MOISTURE: {
    MIN_VALUE: 0,
    MAX_VALUE: 100,
    UNIT: '%',
    PRECISION: 1
  },
  // 风速传感器配置
  WIND_SPEED: {
    MIN_VALUE: 0,
    MAX_VALUE: 50,
    UNIT: 'm/s',
    PRECISION: 1
  },
  // 雨量传感器配置
  RAINFALL: {
    MIN_VALUE: 0,
    MAX_VALUE: 200,
    UNIT: 'mm',
    PRECISION: 1
  }
} as const;

// 摄像头配置
export const CAMERA_CONFIG = {
  // 默认分辨率
  DEFAULT_RESOLUTION: '1920x1080',
  // 支持的分辨率列表
  SUPPORTED_RESOLUTIONS: [
    '640x480',
    '1280x720',
    '1920x1080',
    '2560x1440',
    '3840x2160'
  ],
  // 默认帧率
  DEFAULT_FPS: 30,
  // 支持的帧率列表
  SUPPORTED_FPS: [15, 24, 30, 60],
  // 视频编码格式
  VIDEO_CODEC: 'H.264',
  // 音频编码格式
  AUDIO_CODEC: 'AAC',
  // 录制时长限制（分钟）
  MAX_RECORDING_DURATION: 60
} as const;

// 无人机配置
export const UAV_CONFIG = {
  // 支持的无人机型号
  SUPPORTED_MODELS: [
    { label: 'DJI Agras T30', value: 'dji_agras_t30' },
    { label: 'DJI Agras T20P', value: 'dji_agras_t20p' },
    { label: 'DJI Agras T10', value: 'dji_agras_t10' },
    { label: 'XAG P100', value: 'xag_p100' },
    { label: 'XAG P50', value: 'xag_p50' },
    { label: 'XAG P40', value: 'xag_p40' }
  ],
  // 飞行模式
  FLIGHT_MODES: [
    { label: '手动模式', value: 'manual' },
    { label: '半自动模式', value: 'semi_auto' },
    { label: '全自动模式', value: 'auto' }
  ],
  // 默认飞行参数
  DEFAULT_PARAMS: {
    FLIGHT_HEIGHT: 2.0, // 飞行高度（米）
    FLIGHT_SPEED: 3.0, // 飞行速度（m/s）
    SPRAYING_QUANTITY: 15, // 喷洒量（L/ha）
    SAFETY_DISTANCE_OBSTACLES: 5, // 障碍物安全距离（米）
    SAFETY_DISTANCE_NON_TARGET: 10, // 非目标区域安全距离（米）
    EMERGENCY_BRAKING: true // 紧急制动
  },
  // 液滴大小选项
  DROPLET_SIZES: [
    { label: '细', value: 'fine' },
    { label: '中', value: 'medium' },
    { label: '粗', value: 'coarse' }
  ]
} as const;

// 设备类型配置
export const DEVICE_TYPES = {
  SENSOR: {
    label: '传感器',
    icon: 'device-sensor',
    color: '#1890ff'
  },
  CAMERA: {
    label: '摄像头',
    icon: 'device-camera',
    color: '#52c41a'
  },
  CONTROLLER: {
    label: '控制器',
    icon: 'device-controller',
    color: '#faad14'
  },
  GATEWAY: {
    label: '网关',
    icon: 'device-gateway',
    color: '#722ed1'
  },

  UAV: {
    label: '无人机',
    icon: 'drone',
    color: '#13c2c2'
  },
  ULTRASONIC: {
    label: '超声波设备',
    icon: 'ultrasonic',
    color: '#f5222d'
  },
  INSECT_TRAP: {
    label: '诱虫器',
    icon: 'insect-trap',
    color: '#fa8c16'
  },
  UNKNOWN: {
    label: '未知设备',
    icon: 'device-unknown',
    color: '#8c8c8c'
  }
} as const;

// 设备状态配置
export const DEVICE_STATUS_CONFIG = {
  ONLINE: {
    label: '在线',
    color: '#52c41a',
    icon: 'check-circle'
  },
  OFFLINE: {
    label: '离线',
    color: '#f5222d',
    icon: 'close-circle'
  },
  STANDBY: {
    label: '待机',
    color: '#faad14',
    icon: 'pause-circle'
  },
  ERROR: {
    label: '故障',
    color: '#ff4d4f',
    icon: 'exclamation-circle'
  },
  MAINTENANCE: {
    label: '维护中',
    color: '#722ed1',
    icon: 'tool'
  }
} as const;

// 通信协议配置
export const COMMUNICATION_PROTOCOLS = {
  HTTP: {
    label: 'HTTP',
    port: 80,
    secure: false
  },
  HTTPS: {
    label: 'HTTPS',
    port: 443,
    secure: true
  },
  WEBSOCKET: {
    label: 'WebSocket',
    port: 8080,
    secure: false
  },
  WEBSOCKET_SECURE: {
    label: 'WebSocket Secure',
    port: 8443,
    secure: true
  },
  MQTT: {
    label: 'MQTT',
    port: 1883,
    secure: false
  },
  MQTT_SECURE: {
    label: 'MQTT Secure',
    port: 8883,
    secure: true
  },
  TCP: {
    label: 'TCP',
    port: 8080,
    secure: false
  },
  UDP: {
    label: 'UDP',
    port: 8080,
    secure: false
  }
} as const;

// 数据采集配置
export const DATA_COLLECTION_CONFIG = {
  // 采集间隔（秒）
  COLLECTION_INTERVAL: 30,
  // 数据保留时间（天）
  DATA_RETENTION_DAYS: 365,
  // 批量上传大小
  BATCH_UPLOAD_SIZE: 100,
  // 离线数据缓存大小
  OFFLINE_CACHE_SIZE: 1000,
  // 数据压缩启用
  COMPRESSION_ENABLED: true,
  // 数据加密启用
  ENCRYPTION_ENABLED: true
} as const;
