<!-- 
  OutbreakItem.vue
  虫害爆发记录项组件，用于展示单条虫害爆发记录
-->
<template>
  <div class="outbreak-item">
    <div class="outbreak-severity" :class="severityClass"></div>
    <div class="outbreak-content">
      <div class="outbreak-title">
        <span class="pest-name">{{ pestName }}</span>
        <span class="outbreak-location">{{ location }}</span>
      </div>
      <div class="outbreak-date">{{ date }}</div>
      <div class="outbreak-details">
        <span>影响面积: {{ area }}</span>
        <span>严重程度: {{ severityText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  pestName: {
    type: String,
    required: true
  },
  location: {
    type: String,
    required: true
  },
  date: {
    type: String,
    required: true
  },
  area: {
    type: String,
    required: true
  },
  severityText: {
    type: String,
    required: true
  },
  severityClass: {
    type: String,
    default: 'medium',
    validator: (value: string) => {
      return ['low', 'medium', 'high'].includes(value);
    }
  }
})
</script>

<style scoped>
.outbreak-item {
  display: flex;
  padding: 12px;
  border-radius: 6px;
  background-color: rgba(31, 41, 55, 0.3);
  margin-bottom: 10px;
  transition: all 0.2s ease;
}

.outbreak-item:hover {
  background-color: rgba(31, 41, 55, 0.5);
}

.outbreak-severity {
  width: 4px;
  border-radius: 2px;
  margin-right: 12px;
}

.outbreak-severity.high {
  background-color: #ef4444;
  box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
}

.outbreak-severity.medium {
  background-color: #f59e0b;
  box-shadow: 0 0 5px rgba(245, 158, 11, 0.5);
}

.outbreak-severity.low {
  background-color: #10b981;
  box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
}

.outbreak-content {
  flex-grow: 1;
}

.outbreak-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.pest-name {
  font-weight: 600;
  color: #e5e7eb;
}

.outbreak-location {
  color: #9ca3af;
  font-size: 12px;
}

.outbreak-date {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 5px;
}

.outbreak-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #9ca3af;
}
</style> 