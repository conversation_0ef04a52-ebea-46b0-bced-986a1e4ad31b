/**
 * 格式化日期为本地字符串
 * @param dateString ISO日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString: string): string {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString;
  }
}

/**
 * 格式化日期时间为 YYYY-MM-DD HH:MM:SS 格式
 * @param date 日期字符串或日期对象
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(date: string | Date): string {
  if (!date) return '';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 计算两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差
 */
export function daysBetween(startDate: string | Date, endDate: string | Date): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // 重置时间部分，只保留日期
  start.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);
  
  // 计算毫秒差并转换为天数
  const differenceMs = end.getTime() - start.getTime();
  return Math.round(differenceMs / (1000 * 60 * 60 * 24));
}

/**
 * 获取相对于当前的日期描述
 * @param date 日期字符串或日期对象
 * @returns 相对日期描述，如"今天"、"昨天"、"明天"等
 */
export function getRelativeDateDesc(date: string | Date): string {
  const targetDate = new Date(date);
  const today = new Date();
  
  // 重置时间部分，只保留日期
  targetDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  
  const diffDays = Math.round((targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  switch (diffDays) {
    case 0:
      return '今天';
    case -1:
      return '昨天';
    case 1:
      return '明天';
    case -2:
      return '前天';
    case 2:
      return '后天';
    default:
      if (diffDays < 0) {
        return `${Math.abs(diffDays)} 天前`;
      } else {
        return `${diffDays} 天后`;
      }
  }
}

/**
 * 使用指定格式格式化日期
 * @param date 日期对象或日期字符串
 * @param format 格式字符串，例如 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export function formatCustomDate(date: Date | string, format: string = 'YYYY-MM-DD'): string {
  if (!date) return '';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
} 