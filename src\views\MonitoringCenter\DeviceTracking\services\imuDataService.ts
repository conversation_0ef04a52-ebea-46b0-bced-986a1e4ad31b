/**
 * IMU数据处理服务
 * 提供数据验证、格式化、导出等功能
 */

import type {
  IMUData,
  IMUDataRecord,
  IMUExportData,
  ChartDataPoint,
  AttitudeData,
  BatteryState
} from '../types/imu'

export class IMUDataService {
  /**
   * 验证IMU数据的有效性
   */
  static validateIMUData(data: IMUData): boolean {
    try {
      // 检查必要字段
      if (!data || typeof data.timestamp !== 'number') {
        return false
      }

      // 验证姿态角度数据
      if (!data.attitude ||
          typeof data.attitude.roll !== 'number' ||
          typeof data.attitude.pitch !== 'number' ||
          typeof data.attitude.yaw !== 'number') {
        return false
      }

      // 验证角度范围 (-180 到 180 度)
      const { roll, pitch, yaw } = data.attitude
      if (Math.abs(roll) > 180 || Math.abs(pitch) > 180 || Math.abs(yaw) > 180) {
        console.warn('姿态角度超出正常范围:', { roll, pitch, yaw })
      }

      // 验证电池数据
      if (!data.battery ||
          typeof data.battery.soc !== 'number' ||
          data.battery.soc < 0 || data.battery.soc > 100) {
        return false
      }

      return true
    } catch (error) {
      console.error('IMU数据验证失败:', error)
      return false
    }
  }

  /**
   * 格式化姿态角度数据用于显示
   */
  static formatAttitudeData(attitude: AttitudeData): Record<string, string> {
    return {
      roll: `${attitude.roll.toFixed(2)}°`,
      pitch: `${attitude.pitch.toFixed(2)}°`,
      yaw: `${attitude.yaw.toFixed(2)}°`
    }
  }

  /**
   * 格式化电池状态数据用于显示
   */
  static formatBatteryData(battery: BatteryState): Record<string, string> {
    return {
      soc: `${battery.soc}%`,
      current: `${battery.current.toFixed(2)}A`,
      cycle: `${battery.cycle}次`,
      avgBqTemp: battery.bqTemp.length > 0 ?
        `${(battery.bqTemp.reduce((a, b) => a + b, 0) / battery.bqTemp.length).toFixed(1)}°C` :
        '0°C',
      avgMcuTemp: battery.mcuTemp.length > 0 ?
        `${(battery.mcuTemp.reduce((a, b) => a + b, 0) / battery.mcuTemp.length).toFixed(1)}°C` :
        '0°C'
    }
  }

  /**
   * 将IMU数据转换为图表数据点
   */
  static convertToChartData(
    records: IMUDataRecord[],
    dataKey: string,
    maxPoints: number = 100
  ): ChartDataPoint[] {
    // 如果数据点太多，进行采样
    const step = Math.max(1, Math.floor(records.length / maxPoints))
    const sampledRecords = records.filter((_, index) => index % step === 0)

    return sampledRecords.map(record => {
      let value = 0

      // 根据数据键提取对应的值
      switch (dataKey) {
        case 'attitude.roll':
          value = record.data.attitude.roll
          break
        case 'attitude.pitch':
          value = record.data.attitude.pitch
          break
        case 'attitude.yaw':
          value = record.data.attitude.yaw
          break
        case 'battery.soc':
          value = record.data.battery.soc
          break
        case 'battery.current':
          value = record.data.battery.current
          break
        case 'sensors.powerVoltage':
          value = record.data.sensors.powerVoltage
          break
        case 'sensors.motorMaxTemp':
          value = record.data.sensors.motorMaxTemp
          break
        case 'sensors.motorAvgTemp':
          value = record.data.sensors.motorAvgTemp
          break
        case 'sensors.temperatureNtc1':
          value = record.data.sensors.temperatureNtc1
          break
        default:
          console.warn(`未知的数据键: ${dataKey}`)
      }

      return {
        timestamp: record.timestamp,
        value,
        label: new Date(record.timestamp).toLocaleTimeString()
      }
    })
  }

  /**
   * 计算数据统计信息
   */
  static calculateDataStatistics(records: IMUDataRecord[]) {
    if (records.length === 0) {
      return {
        count: 0,
        timeRange: { start: 0, end: 0 },
        attitude: { rollRange: [0, 0], pitchRange: [0, 0], yawRange: [0, 0] },
        battery: { socRange: [0, 0], currentRange: [0, 0] },
        temperature: { maxTemp: 0, minTemp: 0, avgTemp: 0 }
      }
    }

    const attitudes = records.map(r => r.data.attitude)
    const batteries = records.map(r => r.data.battery)
    const temperatures = records.map(r => r.data.sensors.motorMaxTemp).filter(t => t > 0)

    return {
      count: records.length,
      timeRange: {
        start: records[0].timestamp,
        end: records[records.length - 1].timestamp
      },
      attitude: {
        rollRange: [
          Math.min(...attitudes.map(a => a.roll)),
          Math.max(...attitudes.map(a => a.roll))
        ],
        pitchRange: [
          Math.min(...attitudes.map(a => a.pitch)),
          Math.max(...attitudes.map(a => a.pitch))
        ],
        yawRange: [
          Math.min(...attitudes.map(a => a.yaw)),
          Math.max(...attitudes.map(a => a.yaw))
        ]
      },
      battery: {
        socRange: [
          Math.min(...batteries.map(b => b.soc)),
          Math.max(...batteries.map(b => b.soc))
        ],
        currentRange: [
          Math.min(...batteries.map(b => b.current)),
          Math.max(...batteries.map(b => b.current))
        ]
      },
      temperature: {
        maxTemp: temperatures.length > 0 ? Math.max(...temperatures) : 0,
        minTemp: temperatures.length > 0 ? Math.min(...temperatures) : 0,
        avgTemp: temperatures.length > 0 ?
          temperatures.reduce((a, b) => a + b, 0) / temperatures.length : 0
      }
    }
  }

  /**
   * 导出数据为JSON格式
   */
  static exportToJSON(records: IMUDataRecord[], deviceId: string = 'unknown'): string {
    const exportData: IMUExportData = {
      metadata: {
        exportTime: Date.now(),
        deviceId,
        recordCount: records.length,
        timeRange: {
          start: records.length > 0 ? records[0].timestamp : 0,
          end: records.length > 0 ? records[records.length - 1].timestamp : 0
        }
      },
      records
    }

    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 导出数据为CSV格式
   */
  static exportToCSV(records: IMUDataRecord[]): string {
    if (records.length === 0) {
      return ''
    }

    // CSV头部
    const headers = [
      'Timestamp',
      'DateTime',
      'Roll(°)',
      'Pitch(°)',
      'Yaw(°)',
      'Battery SOC(%)',
      'Battery Current(A)',
      'Battery Cycle',
      'Power Voltage(V)',
      'Motor Max Temp(°C)',
      'Motor Avg Temp(°C)',
      'NTC1 Temp(°C)',
      'Foot FL',
      'Foot FR',
      'Foot RL',
      'Foot RR'
    ]

    // 数据行
    const rows = records.map(record => {
      const { data } = record
      return [
        record.timestamp,
        new Date(record.timestamp).toISOString(),
        data.attitude.roll.toFixed(2),
        data.attitude.pitch.toFixed(2),
        data.attitude.yaw.toFixed(2),
        data.battery.soc,
        data.battery.current.toFixed(2),
        data.battery.cycle,
        data.sensors.powerVoltage.toFixed(2),
        data.sensors.motorMaxTemp.toFixed(1),
        data.sensors.motorAvgTemp.toFixed(1),
        data.sensors.temperatureNtc1.toFixed(1),
        data.footForce.frontLeft,
        data.footForce.frontRight,
        data.footForce.rearLeft,
        data.footForce.rearRight
      ].join(',')
    })

    return [headers.join(','), ...rows].join('\n')
  }

  /**
   * 下载数据文件
   */
  static downloadData(content: string, filename: string, mimeType: string = 'text/plain') {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
  }

  /**
   * 数据质量检查
   */
  static checkDataQuality(records: IMUDataRecord[]): {
    score: number;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = []
    const recommendations: string[] = []
    let score = 100

    if (records.length === 0) {
      return {
        score: 0,
        issues: ['无数据记录'],
        recommendations: ['请确保设备连接正常并开始数据采集']
      }
    }

    // 检查数据连续性
    const timeGaps = []
    for (let i = 1; i < records.length; i++) {
      const gap = records[i].timestamp - records[i - 1].timestamp
      if (gap > 2000) { // 超过2秒的间隔
        timeGaps.push(gap)
      }
    }

    if (timeGaps.length > 0) {
      issues.push(`发现 ${timeGaps.length} 个数据间隔异常`)
      recommendations.push('检查网络连接稳定性')
      score -= Math.min(20, timeGaps.length * 2)
    }

    // 检查数据异常值（添加数据验证）
    const attitudes = records
      .filter(r => r.data && r.data.attitude) // 过滤掉无效数据
      .map(r => r.data.attitude)

    const extremeAngles = attitudes.filter(a =>
      a && typeof a.roll === 'number' && typeof a.pitch === 'number' &&
      (Math.abs(a.roll) > 45 || Math.abs(a.pitch) > 45)
    )

    if (extremeAngles.length > records.length * 0.1) {
      issues.push('检测到大量极端姿态角度')
      recommendations.push('检查设备安装和校准状态')
      score -= 15
    }

    // 检查电池数据（添加数据验证）
    const batteries = records
      .filter(r => r.data && r.data.battery) // 过滤掉无效数据
      .map(r => r.data.battery)

    const lowBattery = batteries.filter(b =>
      b && typeof b.soc === 'number' && b.soc < 20
    )

    if (lowBattery.length > 0) {
      issues.push('检测到低电量状态')
      recommendations.push('及时为设备充电')
      score -= 10
    }

    return {
      score: Math.max(0, score),
      issues,
      recommendations
    }
  }
}
