<template>
  <div class="chat-input">
    <!-- 录音状态显示 -->
    <div v-if="isRecording || isProcessingVoice" class="recording-status">
      <div v-if="isRecording" class="recording-indicator">
        <div class="recording-dot"></div>
        <span>正在录音 {{ recordingTime }}s</span>
        <span class="recording-tip">点击麦克风按钮停止录音</span>
      </div>
      <div v-else-if="isProcessingVoice" class="processing-indicator">
        <el-icon class="rotating"><Loading /></el-icon>
        <span>正在识别语音...</span>
      </div>
    </div>
    
    <div class="input-container">
      <el-input
        v-model="message"
        type="textarea"
        :rows="1"
        :placeholder="placeholder"
        :disabled="disabled || isRecording"
        resize="none"
        autosize
        @keydown.enter.prevent="handleEnterKey"
        ref="inputRef"
      />
      
      <div class="input-actions">
        <div class="left-actions">
          <el-button 
            circle 
            size="small"
            :class="['voice-btn', { 
              'voice-available': voiceAvailable, 
              'recording': isRecording,
              'processing': isProcessingVoice 
            }]"
            :disabled="!voiceAvailable || props.disabled"
            @click="toggleRecording"
            :title="voiceTooltip"
          >
            <el-icon><component :is="voiceButtonIcon" /></el-icon>
          </el-button>
        </div>
        
        <div class="right-actions">
          <div class="char-count" :class="{ 'is-warning': message.length > 1000 }">
            {{ message.length }}/2000
          </div>
          
          <!-- 发送按钮 -->
          <el-button
            type="primary"
            :disabled="!canSend || disabled"
            @click="sendMessage"
            :loading="disabled"
          >
            <el-icon class="send-icon"><Position /></el-icon>
            发送
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';
import { Position, Microphone, Close, Loading } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const props = defineProps<{
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: 'send', message: string): void;
}>();

// 响应式状态
const message = ref('');
const isRecording = ref(false);
const isProcessingVoice = ref(false);
const voiceAvailable = ref(false);
const recordingTime = ref(0);
const recordingTimer = ref<number | null>(null);

// DOM引用
const inputRef = ref();

// 占位符文本
const placeholder = computed(() => {
  if (props.disabled) return 'AI 正在思考中...';
  if (isRecording.value) return '正在录音...';
  if (isProcessingVoice.value) return '正在识别语音...';
  return '输入您的问题或指令... (支持语音输入)';
});

// 是否可以发送消息
const canSend = computed(() => {
  return message.value.trim().length > 0 && message.value.length <= 2000;
});

// 语音按钮提示文本
const voiceTooltip = computed(() => {
  if (!voiceAvailable.value) return '语音输入不可用';
  if (isProcessingVoice.value) return '正在识别语音...';
  if (isRecording.value) return '点击停止录音';
  return '点击开始录音';
});

// 语音按钮图标
const voiceButtonIcon = computed(() => {
  if (!voiceAvailable.value) return Close;
  if (isProcessingVoice.value) return Loading;
  if (isRecording.value) return Close; // 录音时显示停止图标
  return Microphone;
});

// 本地语音识别API调用函数
const speechToText = async (audioBlob: Blob): Promise<string> => {
  const formData = new FormData();
  formData.append('audio', audioBlob, 'voice_input.wav');
  
  try {
    const response = await fetch('/api/speech-to-text', {
      method: 'POST',
      body: formData,
      signal: AbortSignal.timeout(60000) // 60秒超时
    });
    
    const contentType = response.headers.get('content-type');
    const isJson = contentType && contentType.includes('application/json');
    
    if (!response.ok) {
      let errorMessage = '语音识别失败';
      
      if (isJson) {
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
          console.warn('无法解析错误响应JSON:', jsonError);
        }
      } else {
        const textResponse = await response.text();
        console.error('服务器返回非JSON响应:', textResponse.substring(0, 200));
        errorMessage = `服务器错误 (${response.status}): 请检查后端服务状态`;
      }
      
      throw new Error(errorMessage);
    }
    
    if (!isJson) {
      throw new Error('服务器返回了非JSON格式的响应');
    }
    
    const result = await response.json();
    
    if (result.success && result.text) {
      return result.text;
    } else {
      throw new Error(result.error || '语音识别失败');
    }
  } catch (error) {
    console.error('语音识别API调用失败:', error);
    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error('语音识别服务异常');
    }
  }
};

// 检查语音输入支持
const checkVoiceSupport = async () => {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.warn('浏览器不支持语音录制');
      return;
    }
    
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    voiceAvailable.value = true;
    
    // 立即停止流，我们只是检查权限
    stream.getTracks().forEach(track => track.stop());
    
    console.log('语音输入功能已启用');
  } catch (error) {
    console.warn('无法启用语音输入:', error);
    voiceAvailable.value = false;
  }
};

// 音频数据转换为WAV格式
const convertToWav = (audioBuffer: AudioBuffer, sampleRate: number = 16000): Blob => {
  const length = audioBuffer.length;
  const numberOfChannels = audioBuffer.numberOfChannels;
  const sampleRateFinal = sampleRate;
  
  // 重采样到指定采样率（如果需要）
  let resampledBuffer;
  if (audioBuffer.sampleRate !== sampleRateFinal) {
    const resampleRatio = audioBuffer.sampleRate / sampleRateFinal;
    const newLength = Math.round(length / resampleRatio);
    resampledBuffer = new Float32Array(newLength);
    
    for (let i = 0; i < newLength; i++) {
      const originalIndex = Math.round(i * resampleRatio);
      if (originalIndex < length) {
        resampledBuffer[i] = audioBuffer.getChannelData(0)[originalIndex];
      }
    }
  } else {
    resampledBuffer = audioBuffer.getChannelData(0);
  }
  
  // 转换为16位PCM
  const buffer = new ArrayBuffer(44 + resampledBuffer.length * 2);
  const view = new DataView(buffer);
  
  // WAV文件头
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };
  
  writeString(0, 'RIFF');                                    // ChunkID
  view.setUint32(4, 36 + resampledBuffer.length * 2, true); // ChunkSize
  writeString(8, 'WAVE');                                    // Format
  writeString(12, 'fmt ');                                   // Subchunk1ID
  view.setUint32(16, 16, true);                             // Subchunk1Size
  view.setUint16(20, 1, true);                              // AudioFormat (PCM)
  view.setUint16(22, 1, true);                              // NumChannels (Mono)
  view.setUint32(24, sampleRateFinal, true);                // SampleRate
  view.setUint32(28, sampleRateFinal * 2, true);            // ByteRate
  view.setUint16(32, 2, true);                              // BlockAlign
  view.setUint16(34, 16, true);                             // BitsPerSample
  writeString(36, 'data');                                   // Subchunk2ID
  view.setUint32(40, resampledBuffer.length * 2, true);     // Subchunk2Size
  
  // 写入音频数据
  let offset = 44;
  for (let i = 0; i < resampledBuffer.length; i++) {
    const sample = Math.max(-1, Math.min(1, resampledBuffer[i]));
    const int16Sample = Math.round(sample * 0x7FFF);
    view.setInt16(offset, int16Sample, true);
    offset += 2;
  }
  
  return new Blob([buffer], { type: 'audio/wav' });
};

// 开始录音 - 修改为支持WAV格式
const startRecording = async () => {
  if (!voiceAvailable.value || isRecording.value || props.disabled) return;
  
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 16000,     // 16kHz采样率，适合语音识别
        channelCount: 1        // 单声道
      }
    });
    
    recordingTime.value = 0;
    
    // 创建音频上下文用于处理音频数据
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
      sampleRate: 16000
    });
    const source = audioContext.createMediaStreamSource(stream);
    const processor = audioContext.createScriptProcessor(4096, 1, 1);
    
    const audioBuffers: Float32Array[] = [];
    
    processor.onaudioprocess = (event) => {
      const inputBuffer = event.inputBuffer.getChannelData(0);
      audioBuffers.push(new Float32Array(inputBuffer));
    };
    
    source.connect(processor);
    processor.connect(audioContext.destination);
    
    isRecording.value = true;
    
    // 保存到全局变量
    (window as any).currentRecordingData = {
      stream,
      audioContext,
      processor,
      audioBuffers
    };
    
    // 开始计时，最大录音60秒
    recordingTimer.value = window.setInterval(() => {
      recordingTime.value++;
      // 最大录音60秒
      if (recordingTime.value >= 60) {
        stopRecording();
        ElMessage.warning('录音时间达到最大限制（60秒），已自动停止');
      }
    }, 1000);
    
    ElMessage.success('开始录音，点击停止按钮结束录音');
    
  } catch (error) {
    console.error('开始录音失败:', error);
    ElMessage.error('无法开始录音，请检查麦克风权限');
  }
};

// 停止录音 - 修改为处理WAV数据
const stopRecording = () => {
  if (!isRecording.value) return;
  
  isRecording.value = false;
  
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value);
    recordingTimer.value = null;
  }
  
  const recordingData = (window as any).currentRecordingData;
  if (recordingData) {
    const { stream, audioContext, processor, audioBuffers } = recordingData;
    
    try {
      // 停止音频处理
      processor.disconnect();
      audioContext.close();
      
      // 停止媒体流
      stream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
      
      // 合并音频缓冲区
      if (audioBuffers && audioBuffers.length > 0) {
        const totalLength = audioBuffers.reduce((sum: number, buffer: Float32Array) => sum + buffer.length, 0);
        const combinedBuffer = new Float32Array(totalLength);
        
        let offset = 0;
        for (const buffer of audioBuffers) {
          combinedBuffer.set(buffer, offset);
          offset += buffer.length;
        }
        
        // 创建AudioBuffer
        const audioBuffer = audioContext.createBuffer(1, combinedBuffer.length, 16000);
        audioBuffer.getChannelData(0).set(combinedBuffer);
        
        // 转换为WAV格式
        const wavBlob = convertToWav(audioBuffer, 16000);
        
        console.log('生成WAV文件，大小:', wavBlob.size, 'bytes');
        console.log('文件类型:', wavBlob.type);
        
        // 处理WAV数据
        processWavRecording(wavBlob);
      }
    } catch (error) {
      console.error('停止录音处理失败:', error);
      ElMessage.error('录音处理失败');
    }
    
    (window as any).currentRecordingData = null;
  }
};

// 取消录音 - 更新清理逻辑
const cancelRecording = () => {
  if (!isRecording.value) return;
  
  isRecording.value = false;
  
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value);
    recordingTimer.value = null;
  }
  
  const recordingData = (window as any).currentRecordingData;
  if (recordingData) {
    const { stream, audioContext, processor } = recordingData;
    
    try {
      if (processor) processor.disconnect();
      if (audioContext) audioContext.close();
      stream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
    } catch (error) {
      console.warn('清理录音资源失败:', error);
    }
    
    (window as any).currentRecordingData = null;
  }
  
  ElMessage.info('录音已取消');
};

// 处理WAV录音数据
const processWavRecording = async (wavBlob: Blob) => {
  // 检查录音时长
  if (recordingTime.value < 1) {
    ElMessage.warning('录音时间太短，请重新录制');
    return;
  }
  
  isProcessingVoice.value = true;
  
  try {
    console.log('开始语音识别，WAV文件大小:', wavBlob.size, 'bytes');
    
    // 调用语音识别API
    const recognizedText = await speechToText(wavBlob);
    
    if (recognizedText && recognizedText.trim()) {
      message.value = recognizedText.trim();
      ElMessage.success('语音识别成功');
      
      // 自动发送识别的文本
      nextTick(() => {
        if (message.value.trim()) {
          sendMessage();
        }
      });
    } else {
      ElMessage.warning('未能识别到有效语音内容，请重新录制');
    }
    
  } catch (error) {
    console.error('语音识别失败:', error);
    ElMessage.error('语音识别失败，请重试');
  } finally {
    isProcessingVoice.value = false;
  }
};

// 处理回车键
const handleEnterKey = (e: KeyboardEvent) => {
  // Shift+Enter 换行
  if (e.shiftKey) {
    return;
  }
  
  // 普通回车发送消息
  if (canSend.value && !props.disabled) {
    sendMessage();
  }
};

// 发送消息
const sendMessage = () => {
  if (!canSend.value || props.disabled) return;
  
  const trimmedMessage = message.value.trim();
  emit('send', trimmedMessage);
  message.value = '';
  
  // 重置输入框高度
  nextTick(() => {
    if (inputRef.value) {
      const input = inputRef.value as any;
      input.resizeTextarea();
    }
  });
};

// 切换录音状态
const toggleRecording = () => {
  if (isProcessingVoice.value) return;
  
  if (isRecording.value) {
    stopRecording();
  } else {
    startRecording();
  }
};

// 组件挂载时检查语音支持
onMounted(() => {
  checkVoiceSupport();
});

// 组件卸载时清理
onUnmounted(() => {
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value);
  }
});
</script>

<style lang="scss" scoped>
.chat-input {
  .input-container {
    position: relative;
    border-radius: 8px;
    background: rgba(0, 21, 65, 0.5);
    backdrop-filter: blur(5px);
    padding: 8px 12px;
    
    :deep(.el-textarea__inner) {
      background: transparent;
      border: 1px solid rgba(0, 255, 170, 0.3);
      color: rgba(255, 255, 255, 0.9);
      border-radius: 8px;
      padding: 10px 12px 10px 50px; // 左侧留出语音按钮空间
      max-height: 150px;
      font-size: 14px;
      
      &:focus {
        border-color: rgba(0, 255, 170, 0.6);
        box-shadow: 0 0 0 2px rgba(0, 255, 170, 0.2);
      }
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
      
      &:disabled {
        background: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.5);
      }
    }
    
    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 8px;
      
      .left-actions {
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        
        .el-button {
          transition: all 0.3s ease;
          
          &.voice-available {
            border-color: rgba(0, 255, 170, 0.5);
            color: rgba(0, 255, 170, 0.8);
            
            &:hover {
              border-color: rgba(0, 255, 170, 0.8);
              color: rgba(0, 255, 170, 1);
              transform: scale(1.05);
            }
          }
          
          &.recording {
            background: rgba(255, 77, 79, 0.2);
            border-color: #ff4d4f;
            color: #ff4d4f;
            animation: pulse 1.5s infinite;
          }
          
          &.processing {
            background: rgba(0, 255, 170, 0.2);
            border-color: rgba(0, 255, 170, 0.6);
            color: rgba(0, 255, 170, 1);
          }
          
          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
      
      .right-actions {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .char-count {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.5);
          
          &.is-warning {
            color: #ff9800;
          }
        }
        
        .send-icon {
          margin-right: 4px;
        }
      }
    }
    
    // 录音状态显示
    .recording-status {
      position: absolute;
      bottom: 100%;
      left: 0;
      right: 0;
      background: rgba(255, 77, 79, 0.1);
      border: 1px solid rgba(255, 77, 79, 0.3);
      border-radius: 8px 8px 0 0;
      padding: 8px 12px;
      margin-bottom: -1px;
      
      .recording-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ff4d4f;
        font-size: 14px;
        font-weight: 500;
        
        .recording-dot {
          width: 8px;
          height: 8px;
          background: #ff4d4f;
          border-radius: 50%;
          animation: pulse 1s infinite;
        }
        
        .recording-time {
          margin-left: auto;
          color: rgba(255, 255, 255, 0.8);
        }
      }
      
      .recording-tip {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        margin-top: 4px;
      }
    }
    
    // 语音处理状态
    .voice-processing {
      position: absolute;
      bottom: 100%;
      left: 0;
      right: 0;
      background: rgba(0, 255, 170, 0.1);
      border: 1px solid rgba(0, 255, 170, 0.3);
      border-radius: 8px 8px 0 0;
      padding: 8px 12px;
      margin-bottom: -1px;
      
      .processing-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        color: rgba(0, 255, 170, 0.8);
        font-size: 14px;
        
        .rotating {
          animation: rotate 1s linear infinite;
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chat-input .input-container {
    .input-actions .left-actions {
      left: 16px;
    }
    
    :deep(.el-textarea__inner) {
      padding-left: 45px;
    }
  }
}
</style> 