import { ref, onMounted, onUnmounted } from 'vue';
import { getCameraStream } from '../api/camera';
import { StreamStatus } from '../types';

export function useCameraStream(cameraId: string) {
  const videoElement = ref<HTMLVideoElement | null>(null);
  const streamStatus = ref<StreamStatus>(StreamStatus.NORMAL);
  const isLoading = ref(true);
  const error = ref<Error | null>(null);
  
  // 检测视频流状态
  let checkStatusInterval: number | null = null;
  
  // 开始播放视频流
  const startStream = async () => {
    if (!videoElement.value) return;
    
    try {
      isLoading.value = true;
      error.value = null;
      
      const streamUrl = getCameraStream(cameraId);
      videoElement.value.src = streamUrl;
      
      // 视频加载完成后
      videoElement.value.onloadeddata = () => {
        isLoading.value = false;
        streamStatus.value = StreamStatus.NORMAL;
      };
      
      // 视频错误处理
      videoElement.value.onerror = (e) => {
        console.error('视频流错误:', e);
        error.value = new Error('视频流加载失败');
        isLoading.value = false;
        streamStatus.value = StreamStatus.INTERRUPTED;
      };
      
      // 视频卡顿检测
      videoElement.value.onstalled = () => {
        streamStatus.value = StreamStatus.STUTTERING;
      };
      
      // 视频结束处理（对于非直播视频）
      videoElement.value.onended = () => {
        // 如果视频结束，重新播放
        if (videoElement.value) {
          videoElement.value.currentTime = 0;
          videoElement.value.play().catch(err => {
            console.error('重新播放视频失败:', err);
          });
        }
      };
      
      await videoElement.value.play();
    } catch (err) {
      console.error('启动视频流失败:', err);
      error.value = err instanceof Error ? err : new Error('未知错误');
      isLoading.value = false;
      streamStatus.value = StreamStatus.INTERRUPTED;
    }
  };
  
  // 监控视频流状态
  const startStatusMonitoring = () => {
    if (checkStatusInterval) return;
    
    checkStatusInterval = window.setInterval(() => {
      if (!videoElement.value) return;
      
      // 检查视频是否卡顿
      const now = Date.now();
      const lastTimeUpdate = (videoElement.value as any).lastTimeUpdateTime || now;
      
      // 对于test.mp4本地视频，不进行卡顿检测
      if (videoElement.value.src.includes('test.mp4')) {
        streamStatus.value = StreamStatus.NORMAL;
        return;
      }
      
      if (now - lastTimeUpdate > 3000) {
        streamStatus.value = StreamStatus.STUTTERING;
      } else {
        streamStatus.value = StreamStatus.NORMAL;
      }
    }, 1000);
  };
  
  // 停止监控
  const stopStatusMonitoring = () => {
    if (checkStatusInterval) {
      clearInterval(checkStatusInterval);
      checkStatusInterval = null;
    }
  };
  
  // 重新连接
  const reconnect = async () => {
    await startStream();
  };
  
  onMounted(() => {
    startStream();
    startStatusMonitoring();
  });
  
  onUnmounted(() => {
    stopStatusMonitoring();
  });
  
  return {
    videoElement,
    streamStatus,
    isLoading,
    error,
    reconnect
  };
} 