/**
 * IMU数据管理Composable
 * 处理机器狗IMU数据的订阅、解析和状态管理
 */

import { ref, reactive, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { robotDogApi } from '@/api/robotDog'
import { robotWebSocketService, type RobotControlMessage } from '@/services/robotWebSocketService'
// DataChannelType 已从 constants.js 中移除，直接使用字符串常量
const DataChannelType = {
  SUBSCRIBE: 'subscribe'
}
import * as IMUTypes from '../types/imu'
import type {
  IMUData,
  IMUDataRecord,
  IMUDataStats,
  IMUApiResponse,
  AttitudeData,
  BatteryState,
  FootForceData,
  SensorData,
  MotorState
} from '../types/imu'

// 使用别名来避免导入问题
const IMUConnectionStatus = IMUTypes.IMUConnectionStatus

export function useIMUData() {
  // 连接状态
  const connectionStatus = ref<IMUTypes.IMUConnectionStatus>(IMUConnectionStatus.DISCONNECTED)
  const isConnected = computed(() => connectionStatus.value === IMUConnectionStatus.CONNECTED)
  const isReceiving = computed(() => connectionStatus.value === IMUConnectionStatus.RECEIVING)

  // WebSocket连接和数据获取定时器
  const dataTimer = ref<number | null>(null)
  const wsConnected = ref(false)

  // 当前IMU数据
  const currentData = ref<IMUData | null>(null)

  // 数据历史记录
  const dataHistory = reactive<IMUDataRecord[]>([])
  const maxHistoryRecords = ref(1000)

  // 数据统计
  const stats = reactive<IMUDataStats>({
    totalRecords: 0,
    dataRate: 0,
    lastUpdateTime: 0,
    connectionDuration: 0,
    errorCount: 0
  })

  // 错误信息
  const lastError = ref<string | null>(null)

  // 连接开始时间
  let connectionStartTime = 0

  // 更新计时器
  let updateTimer: number | null = null

  /**
   * 弧度转角度
   */
  const radToDeg = (rad: number): number => {
    return rad * 180 / Math.PI
  }

  /**
   * 处理WebSocket返回的IMU数据
   */
  const processWebSocketIMUData = (wsData: any): void => {
    try {
      console.log('收到WebSocket IMU数据:', wsData)

      // WebSocket数据可能包装在不同的结构中，需要适配
      let imuData = wsData

      // 如果数据被包装在data字段中
      if (wsData.data) {
        imuData = wsData.data
      }

      // 处理数据
      processIMUData(imuData)

      // 重置错误计数
      if (stats.errorCount > 0) {
        stats.errorCount = Math.max(0, stats.errorCount - 1)
      }

    } catch (error) {
      console.error('处理WebSocket IMU数据失败:', error)
      stats.errorCount++
    }
  }

  /**
   * 处理IMU数据（通用处理函数）
   */
  const processIMUData = (apiData: any): void => {
    try {
      const timestamp = Date.now()

      // 解析姿态角度数据
      const attitude: AttitudeData = {
        roll: 0,
        pitch: 0,
        yaw: 0
      }

      if (apiData.imu_state?.rpy) {
        const rpy = apiData.imu_state.rpy
        attitude.roll = radToDeg(rpy[0])
        attitude.pitch = radToDeg(rpy[1])
        attitude.yaw = radToDeg(rpy[2])
      }

      // 解析电池状态
      const battery: BatteryState = {
        soc: apiData.bms_state?.soc || 0,
        current: apiData.bms_state?.current || 0,
        cycle: apiData.bms_state?.cycle || 0,
        bqTemp: apiData.bms_state?.bq_ntc || [0, 0],
        mcuTemp: apiData.bms_state?.mcu_ntc || [0, 0]
      }

      // 解析足部压力数据
      const footForce: FootForceData = {
        frontLeft: apiData.foot_force?.[0] || 0,
        frontRight: apiData.foot_force?.[1] || 0,
        rearLeft: apiData.foot_force?.[2] || 0,
        rearRight: apiData.foot_force?.[3] || 0
      }

      // 解析电机状态
      const motors: MotorState[] = []
      let maxTemp = 0
      let totalTemp = 0
      let activeMotors = 0

      if (apiData.motor_state && Array.isArray(apiData.motor_state)) {
        for (const motor of apiData.motor_state) {
          if (motor && motor.temperature > 0) {
            motors.push({
              temperature: motor.temperature
            })
            maxTemp = Math.max(maxTemp, motor.temperature)
            totalTemp += motor.temperature
            activeMotors++
          }
        }
      }

      // 解析传感器数据
      const sensors: SensorData = {
        temperatureNtc1: apiData.temperature_ntc1 || 0,
        powerVoltage: apiData.power_v || 0,
        motorMaxTemp: maxTemp,
        motorAvgTemp: activeMotors > 0 ? totalTemp / activeMotors : 0
      }

      // 创建IMU数据对象
      const imuData: IMUData = {
        timestamp,
        attitude,
        battery,
        footForce,
        sensors,
        motors
      }

      // 更新当前数据
      currentData.value = imuData

      // 添加到历史记录（确保数据格式与IMUDataService期望的一致）
      const record: IMUDataRecord = {
        id: `imu_${timestamp}`,
        timestamp,
        data: imuData, // 将IMU数据包装在data字段中
        quality: calculateDataQuality(imuData)
      }

      dataHistory.push(record)

      // 限制历史记录数量
      if (dataHistory.length > maxHistoryRecords.value) {
        dataHistory.splice(0, dataHistory.length - maxHistoryRecords.value)
      }

      // 更新统计信息
      updateStats()

      console.log('✅ IMU数据处理成功:', imuData)
    } catch (error) {
      console.error('处理IMU数据失败:', error)
      stats.errorCount++
    }
  }

  /**
   * 计算数据质量分数
   */
  const calculateDataQuality = (data: IMUData): number => {
    let score = 100

    // 检查姿态角度是否合理
    if (Math.abs(data.attitude.roll) > 45) score -= 10
    if (Math.abs(data.attitude.pitch) > 45) score -= 10

    // 检查电池状态
    if (data.battery.soc < 20) score -= 20
    if (data.battery.soc < 10) score -= 30

    // 检查传感器数据
    if (data.sensors.motorMaxTemp > 80) score -= 15
    if (data.sensors.motorMaxTemp > 90) score -= 25

    return Math.max(0, score)
  }

  /**
   * 更新统计信息
   */
  const updateStats = (): void => {
    stats.totalRecords = dataHistory.length
    stats.lastUpdateTime = Date.now()

    // 计算数据更新频率
    if (connectionStartTime > 0) {
      const duration = (Date.now() - connectionStartTime) / 1000
      stats.dataRate = stats.totalRecords / duration
      stats.connectionDuration = duration
    }
  }



  /**
   * 解析原始IMU数据（保留用于兼容性）
   */
  const parseRawIMUData = (rawMessage: RawIMUMessage): IMUData | null => {
    try {
      const { data } = rawMessage
      const timestamp = Date.now()

      // 解析姿态角度数据
      const attitude: AttitudeData = {
        roll: 0,
        pitch: 0,
        yaw: 0
      }

      if (data.imu_state?.rpy) {
        const rpy = data.imu_state.rpy
        attitude.roll = radToDeg(rpy[0])
        attitude.pitch = radToDeg(rpy[1])
        attitude.yaw = radToDeg(rpy[2])
      }

      // 解析电池状态
      const battery: BatteryState = {
        soc: data.bms_state?.soc || 0,
        current: data.bms_state?.current || 0,
        cycle: data.bms_state?.cycle || 0,
        bqTemp: data.bms_state?.bq_ntc || [0, 0],
        mcuTemp: data.bms_state?.mcu_ntc || [0, 0]
      }

      // 解析足部压力数据
      const footForce: FootForceData = {
        frontLeft: data.foot_force?.[0] || 0,
        frontRight: data.foot_force?.[1] || 0,
        rearLeft: data.foot_force?.[2] || 0,
        rearRight: data.foot_force?.[3] || 0
      }

      // 解析电机状态
      const motors: MotorState[] = []
      let maxTemp = 0
      let totalTemp = 0
      let activeMotors = 0

      if (data.motor_state && Array.isArray(data.motor_state)) {
        for (const motor of data.motor_state) {
          if (motor && motor.temperature > 0) {
            motors.push({
              temperature: motor.temperature
            })
            maxTemp = Math.max(maxTemp, motor.temperature)
            totalTemp += motor.temperature
            activeMotors++
          }
        }
      }

      // 解析传感器数据
      const sensors: SensorData = {
        temperatureNtc1: data.temperature_ntc1 || 0,
        powerVoltage: data.power_v || 0,
        motorMaxTemp: maxTemp,
        motorAvgTemp: activeMotors > 0 ? totalTemp / activeMotors : 0
      }

      return {
        timestamp,
        attitude,
        battery,
        footForce,
        sensors,
        motors
      }
    } catch (error) {
      console.error('解析IMU数据失败:', error)
      stats.errorCount++
      return null
    }
  }

  /**
   * 处理接收到的消息
   */
  const handleMessage = (message: any) => {
    try {
      // 检查是否是lowstate数据
      if (message.topic === 'rt/lf/lowstate' && message.data) {
        const parsedData = parseRawIMUData(message as RawIMUMessage)

        if (parsedData) {
          // 更新当前数据
          currentData.value = parsedData

          // 添加到历史记录
          const record: IMUDataRecord = {
            id: `imu_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: parsedData.timestamp,
            data: parsedData
          }

          dataHistory.push(record)

          // 限制历史记录数量
          if (dataHistory.length > maxHistoryRecords.value) {
            dataHistory.shift()
          }

          // 更新统计信息
          stats.totalRecords++
          stats.lastUpdateTime = parsedData.timestamp

          // 更新连接状态
          if (connectionStatus.value !== IMUConnectionStatus.RECEIVING) {
            connectionStatus.value = IMUConnectionStatus.RECEIVING
          }
        }
      }
    } catch (error) {
      console.error('处理IMU消息失败:', error)
      stats.errorCount++
      lastError.value = `消息处理错误: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }

  /**
   * 开始WebSocket实时数据获取
   */
  const startPolling = async (): Promise<boolean> => {
    try {
      connectionStatus.value = IMUConnectionStatus.CONNECTING

      // 初始化WebSocket连接
      await initWebSocketConnection()

      // 开始实时数据获取
      startRealtimeDataFetch()

      connectionStatus.value = IMUConnectionStatus.RECEIVING
      connectionStartTime = Date.now()

      // 启动数据更新计时器
      startUpdateTimer()

      console.log('✅ WebSocket实时数据获取启动成功')
      ElMessage.success('实时数据获取已启动')

      return true
    } catch (error) {
      console.error('启动WebSocket数据获取失败:', error)
      connectionStatus.value = IMUConnectionStatus.ERROR
      lastError.value = `启动失败: ${error instanceof Error ? error.message : '未知错误'}`
      stats.errorCount++
      ElMessage.error('实时数据获取启动失败')
      return false
    }
  }

  /**
   * 初始化WebSocket连接
   */
  const initWebSocketConnection = async (): Promise<void> => {
    try {
      // 设置WebSocket消息回调
      robotWebSocketService.setMessageCallback((message: RobotControlMessage) => {
        if (message.type === 'IMU_DATA' && message.data) {
          processWebSocketIMUData(message.data)
        }
      })

      // 设置连接状态回调
      robotWebSocketService.setStatusChangeCallback((status) => {
        wsConnected.value = status === 'CONNECTED'
        console.log('WebSocket连接状态:', status)
      })

      // 设置错误回调
      robotWebSocketService.setErrorCallback((error) => {
        console.error('WebSocket错误:', error)
        stats.errorCount++
        lastError.value = `WebSocket错误: ${error.message}`
      })

      // 连接WebSocket
      await robotWebSocketService.connect()

      console.log('✅ WebSocket连接初始化成功')
    } catch (error) {
      console.error('WebSocket连接初始化失败:', error)
      throw error
    }
  }

  /**
   * 开始实时数据获取
   */
  const startRealtimeDataFetch = (): void => {
    // 立即获取一次数据
    requestIMUData()

    // 设置定时器，每500ms请求一次数据（实时性更好）
    dataTimer.value = window.setInterval(() => {
      if (wsConnected.value) {
        requestIMUData()
      } else {
        console.warn('WebSocket未连接，跳过数据请求')
      }
    }, 500) // 500ms间隔，提供更好的实时性
  }

  /**
   * 请求IMU数据
   */
  const requestIMUData = async (): Promise<void> => {
    try {
      await robotWebSocketService.getIMUData()
    } catch (error) {
      console.error('请求IMU数据失败:', error)
      stats.errorCount++

      // 如果连续失败次数过多，暂停数据获取
      if (stats.errorCount > 20) {
        console.warn('IMU数据请求连续失败，暂停获取')
        await stopPolling()
        ElMessage.error('数据获取失败次数过多，已停止获取')
      }
    }
  }

  /**
   * 停止WebSocket数据获取
   */
  const stopPolling = async (): Promise<void> => {
    if (connectionStatus.value === IMUConnectionStatus.DISCONNECTED) {
      return
    }

    try {
      console.log('停止WebSocket数据获取...')

      // 停止数据获取定时器
      if (dataTimer.value) {
        clearInterval(dataTimer.value)
        dataTimer.value = null
      }

      // 断开WebSocket连接
      robotWebSocketService.disconnect()
      wsConnected.value = false

      connectionStatus.value = IMUConnectionStatus.DISCONNECTED

      // 停止更新计时器
      stopUpdateTimer()

      console.log('✅ 已停止IMU数据轮询')
      ElMessage.info('已停止IMU数据获取')

    } catch (error) {
      console.error('停止轮询失败:', error)
      lastError.value = `停止失败: ${error instanceof Error ? error.message : '未知错误'}`
      stats.errorCount++
    }
  }

  /**
   * 启动数据更新计时器
   */
  const startUpdateTimer = () => {
    stopUpdateTimer()

    updateTimer = window.setInterval(() => {
      // 计算数据更新频率
      const now = Date.now()
      if (stats.lastUpdateTime > 0) {
        const timeDiff = (now - stats.lastUpdateTime) / 1000
        if (timeDiff < 10) { // 10秒内有数据更新
          stats.dataRate = Math.round(1 / timeDiff * 10) / 10
        } else {
          stats.dataRate = 0
        }
      }

      // 计算连接持续时间
      if (connectionStartTime > 0) {
        stats.connectionDuration = Math.floor((now - connectionStartTime) / 1000)
      }

      // 检查数据超时
      if (connectionStatus.value === IMUConnectionStatus.RECEIVING) {
        const timeSinceLastUpdate = now - stats.lastUpdateTime
        if (timeSinceLastUpdate > 5000) { // 5秒无数据认为超时
          connectionStatus.value = IMUConnectionStatus.ERROR
          lastError.value = '数据接收超时'
        }
      }
    }, 1000)
  }

  /**
   * 停止数据更新计时器
   */
  const stopUpdateTimer = () => {
    if (updateTimer) {
      clearInterval(updateTimer)
      updateTimer = null
    }
  }

  /**
   * 清空历史数据
   */
  const clearHistory = () => {
    dataHistory.splice(0)
    stats.totalRecords = 0
    stats.lastUpdateTime = 0
    stats.errorCount = 0
  }

  /**
   * 重置连接状态
   */
  const resetConnection = () => {
    connectionStatus.value = IMUConnectionStatus.DISCONNECTED

    // 清理WebSocket相关资源
    if (dataTimer.value) {
      clearInterval(dataTimer.value)
      dataTimer.value = null
    }

    // 断开WebSocket连接
    robotWebSocketService.disconnect()
    wsConnected.value = false

    // 重置数据状态
    currentData.value = null
    lastError.value = null
    connectionStartTime = 0

    // 停止更新计时器
    stopUpdateTimer()
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    stopPolling()
    stopUpdateTimer()
  })

  return {
    // 状态
    connectionStatus: computed(() => connectionStatus.value),
    isConnected,
    isReceiving,
    currentData: computed(() => currentData.value),
    dataHistory: computed(() => [...dataHistory]),
    stats: computed(() => ({ ...stats })),
    lastError: computed(() => lastError.value),

    // 方法
    startPolling,
    stopPolling,
    clearHistory,
    resetConnection,
    handleMessage
  }
}
