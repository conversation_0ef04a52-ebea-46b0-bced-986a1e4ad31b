<!-- 
  CostBenefit.vue
  成本效益分析仪表盘模块
  展示农业生产的成本构成、收益来源和投资回报分析
-->
<template>
  <div class="cost-benefit">
    <!-- 页面标题 -->
    <PageHeader
      title="智慧农场成本效益分析"
      description="实时分析农业生产的成本构成、收益来源和投资回报情况"
      icon="Money"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">144.4%</span>
            <span class="summary-label">投资回报率</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">¥184,650</span>
            <span class="summary-label">净收益</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 指标卡片区域 -->
    <div class="metric-cards">
      <DataPanel title="核心经济指标">
        <template #actions>
          <el-tag type="success" effect="dark" size="small">同比增长</el-tag>
        </template>
        <div class="metrics-container">
          <div class="metric-card">
            <div class="metric-icon total-cost"></div>
            <div class="metric-details">
              <div class="metric-title">总成本</div>
              <div class="metric-value">¥127,850</div>
              <div class="metric-change negative">
                <el-icon><ArrowDown /></el-icon> 5.2%
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon total-revenue"></div>
            <div class="metric-details">
              <div class="metric-title">总收益</div>
              <div class="metric-value">¥312,500</div>
              <div class="metric-change positive">
                <el-icon><ArrowUp /></el-icon> 12.8%
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon net-profit"></div>
            <div class="metric-details">
              <div class="metric-title">净收益</div>
              <div class="metric-value">¥184,650</div>
              <div class="metric-change positive">
                <el-icon><ArrowUp /></el-icon> 18.3%
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon roi"></div>
            <div class="metric-details">
              <div class="metric-title">投资回报率</div>
              <div class="metric-value">144.4%</div>
              <div class="metric-change positive">
                <el-icon><ArrowUp /></el-icon> 23.5%
              </div>
            </div>
          </div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 分析面板区域 -->
    <div class="analysis-panels">
      <!-- 成本构成分析 -->
      <DataPanel title="成本构成分析">
        <template #actions>
          <el-select v-model="costPeriod" placeholder="选择时间段" size="small">
            <el-option label="最近30天" value="30d"></el-option>
            <el-option label="最近90天" value="90d"></el-option>
            <el-option label="最近180天" value="180d"></el-option>
            <el-option label="本年度" value="year"></el-option>
          </el-select>
        </template>
        <div class="cost-breakdown">
          <div 
            v-for="(item, index) in costItems" 
            :key="index"
            class="cost-item"
            @click="toggleCostItem(index)"
          >
            <div class="cost-label">
              <div class="color-indicator" :style="{ backgroundColor: item.color }"></div>
              <span>{{ item.name }}</span>
            </div>
            <div class="cost-bars">
              <div class="cost-value">¥{{ item.value.toLocaleString() }}</div>
              <div class="cost-bar-container">
                <div 
                  class="cost-bar" 
                  :style="{ 
                    width: `${(item.value / totalCost) * 100}%`,
                    backgroundColor: item.color,
                    opacity: item.visible ? 1 : 0.5
                  }"
                ></div>
              </div>
              <div class="cost-percentage">{{ ((item.value / totalCost) * 100).toFixed(1) }}%</div>
            </div>
          </div>
        </div>
      </DataPanel>
      
      <!-- 收益来源分析 -->
      <DataPanel title="收益来源分析">
        <template #actions>
          <el-radio-group v-model="revenueView" size="small">
            <el-radio-button label="monthly">月度</el-radio-button>
            <el-radio-button label="quarterly">季度</el-radio-button>
            <el-radio-button label="yearly">年度</el-radio-button>
          </el-radio-group>
        </template>
        <div class="revenue-analysis">
          <div class="revenue-legend">
            <div 
              v-for="(source, index) in revenueSources" 
              :key="index"
              class="legend-item"
            >
              <div class="color-indicator" :style="{ backgroundColor: source.color }"></div>
              <span>{{ source.name }}</span>
            </div>
          </div>
          <div class="revenue-chart">
            <div class="placeholder-chart">
              <h4>收益来源分布图表</h4>
              <p>此处将显示不同收益来源的分布情况，按{{ revenueViewLabel }}查看。</p>
              <div class="mock-bars">
                <div class="mock-bar" v-for="i in 5" :key="i">
                  <div class="mock-bar-segment" style="height: 60%; background-color: #3b82f6;"></div>
                  <div class="mock-bar-segment" style="height: 25%; background-color: #10b981;"></div>
                  <div class="mock-bar-segment" style="height: 15%; background-color: #f59e0b;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DataPanel>
      
      <!-- 投资回收期分析 -->
      <DataPanel title="投资回收期分析">
        <template #actions>
          <el-button-group>
            <el-button size="small" :type="paybackSortBy === 'period' ? 'primary' : ''" @click="paybackSortBy = 'period'">按周期</el-button>
            <el-button size="small" :type="paybackSortBy === 'roi' ? 'primary' : ''" @click="paybackSortBy = 'roi'">按回报率</el-button>
          </el-button-group>
        </template>
        <div class="payback-chart">
          <div 
            v-for="(measure, index) in sortedPaybackMeasures" 
            :key="index"
            class="payback-measure"
          >
            <div class="measure-name">{{ measure.name }}</div>
            <div class="payback-bar-container">
              <div 
                class="payback-bar" 
                :style="{ 
                  width: `${(measure.paybackPeriod / maxPaybackPeriod) * 100}%`,
                  backgroundColor: getPaybackColor(measure.paybackPeriod)
                }"
              >
                <span class="payback-value">{{ measure.paybackPeriod }}个月</span>
              </div>
            </div>
            <div class="measure-roi">{{ measure.roi }}%</div>
          </div>
        </div>
      </DataPanel>
      
      <!-- 成本效益对比 -->
      <DataPanel title="成本效益对比">
        <template #actions>
          <el-switch
            v-model="showTrend"
            active-text="趋势图"
            inactive-text="对比图"
            @change="toggleChartView"
          >
          </el-switch>
        </template>
        <div class="comparison-chart">
          <div class="placeholder-chart">
            <h4>{{ showTrend ? '成本效益趋势' : '防治措施对比' }}</h4>
            <p>此处将显示{{ showTrend ? '成本与收益随时间的变化趋势' : '不同防治措施的成本效益对比' }}。</p>
            <div class="mock-chart">
              <div class="mock-line" v-if="showTrend"></div>
              <div class="mock-bars" v-else>
                <div class="mock-grouped-bar" v-for="i in 4" :key="i">
                  <div class="mock-bar-pill cost"></div>
                  <div class="mock-bar-pill benefit"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="收益良好" />
        <StatusIndicator type="warning" label="成本增加" />
        <StatusIndicator type="normal" label="数据分析中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Money, 
  ArrowUp, 
  ArrowDown, 
  Refresh, 
  TrendCharts, 
  Wallet
} from '@element-plus/icons-vue';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import DataPanel from './components/DataPanel.vue';
import StatusIndicator from './components/StatusIndicator.vue';

// UI状态
const costPeriod = ref('90d');
const revenueView = ref('monthly');
const showTrend = ref(true);
const paybackSortBy = ref('period');

// 成本分析数据
const costItems = reactive([
  { name: '人力成本', value: 45800, color: '#3b82f6', visible: true },
  { name: '药剂成本', value: 32500, color: '#10b981', visible: true },
  { name: '设备成本', value: 28750, color: '#f59e0b', visible: true },
  { name: '运输成本', value: 12300, color: '#ef4444', visible: true },
  { name: '其他成本', value: 8500, color: '#8b5cf6', visible: true }
]);

// 收益来源数据
const revenueSources = reactive([
  { name: '作物增产收益', value: 185000, color: '#3b82f6' },
  { name: '损失减少收益', value: 95000, color: '#10b981' },
  { name: '绿色认证收益', value: 32500, color: '#f59e0b' }
]);

// 投资回收期数据
const paybackMeasures = reactive([
  { name: '生物防治', paybackPeriod: 8, roi: 125 },
  { name: '化学防治', paybackPeriod: 3, roi: 210 },
  { name: '物理防治', paybackPeriod: 12, roi: 85 },
  { name: '综合防治', paybackPeriod: 6, roi: 170 }
]);

// 最后更新时间
const lastUpdateTime = ref(new Date());

// 计算属性
const totalCost = computed(() => {
  return costItems.reduce((sum, item) => sum + item.value, 0);
});

const revenueViewLabel = computed(() => {
  switch(revenueView.value) {
    case 'monthly': return '月度';
    case 'quarterly': return '季度';
    case 'yearly': return '年度';
    default: return '';
  }
});

const maxPaybackPeriod = computed(() => {
  return Math.max(...paybackMeasures.map(m => m.paybackPeriod));
});

const sortedPaybackMeasures = computed(() => {
  return [...paybackMeasures].sort((a, b) => {
    if (paybackSortBy.value === 'period') {
      return a.paybackPeriod - b.paybackPeriod;
    } else {
      return b.roi - a.roi;
    }
  });
});

// 方法
const toggleCostItem = (index: number) => {
  costItems[index].visible = !costItems[index].visible;
};

const toggleChartView = () => {
  // 在实际实现中，会切换图表类型
  ElMessage.info(`已切换到${showTrend.value ? '趋势图' : '对比图'}视图`);
};

const getPaybackColor = (period: number) => {
  if (period <= 4) return '#10b981'; // 绿色
  if (period <= 8) return '#3b82f6'; // 蓝色
  if (period <= 12) return '#f59e0b'; // 琥珀色
  return '#ef4444'; // 红色
};

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

const refreshData = () => {
  // 模拟数据更新
  // 更新成本数据
  costItems.forEach(item => {
    item.value = Math.floor(item.value * (0.95 + Math.random() * 0.1));
  });
  
  // 更新收益数据
  revenueSources.forEach(source => {
    source.value = Math.floor(source.value * (0.95 + Math.random() * 0.15));
  });
  
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

// 数据更新计时器
let dataUpdateInterval: number | null = null;

onMounted(() => {
  // 启动数据自动更新
  dataUpdateInterval = window.setInterval(() => {
    // 小幅度随机波动数据
    costItems.forEach(item => {
      item.value = Math.floor(item.value * (0.98 + Math.random() * 0.04));
    });
    
    revenueSources.forEach(source => {
      source.value = Math.floor(source.value * (0.98 + Math.random() * 0.04));
    });
    
    lastUpdateTime.value = new Date();
  }, 60000); // 每60秒更新一次
});

onUnmounted(() => {
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
});
</script>

<style scoped>
.cost-benefit {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 指标卡片区域 */
.metric-cards {
  margin-bottom: 20px;
}

.metrics-container {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  flex-wrap: wrap;
}

.metric-card {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-size: 30px;
  background-position: center;
  background-repeat: no-repeat;
}

.metric-icon.total-cost {
  background-color: rgba(239, 68, 68, 0.2);
  background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="rgb(239, 68, 68)" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>');
}

.metric-icon.total-revenue {
  background-color: rgba(16, 185, 129, 0.2);
  background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="rgb(16, 185, 129)" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>');
}

.metric-icon.net-profit {
  background-color: rgba(59, 130, 246, 0.2);
  background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="rgb(59, 130, 246)" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 8l3 5m0 0l3-5m-3 5v4m-3-5h6m-6 3h6m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>');
}

.metric-icon.roi {
  background-color: rgba(245, 158, 11, 0.2);
  background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="rgb(245, 158, 11)" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>');
}

.metric-details {
  flex: 1;
}

.metric-title {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 22px;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 5px;
}

.metric-change {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.negative {
  color: #ef4444;
}

/* 分析面板区域 */
.analysis-panels {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

/* 成本构成分析 */
.cost-breakdown {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.cost-item {
  cursor: pointer;
}

.cost-label {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.cost-bars {
  display: flex;
  align-items: center;
  gap: 10px;
}

.cost-value {
  width: 80px;
  text-align: right;
  font-size: 0.9rem;
  color: #e5e7eb;
}

.cost-bar-container {
  flex: 1;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
}

.cost-bar {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s, opacity 0.3s;
}

.cost-percentage {
  width: 50px;
  font-size: 0.9rem;
  color: #9ca3af;
}

/* 收益来源分析 */
.revenue-analysis {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.revenue-legend {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
}

.revenue-chart {
  flex: 1;
}

/* 投资回收期分析 */
.payback-chart {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.payback-measure {
  display: flex;
  align-items: center;
}

.measure-name {
  width: 100px;
  color: #e5e7eb;
  font-size: 14px;
}

.payback-bar-container {
  flex: 1;
  height: 25px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-right: 10px;
}

.payback-bar {
  height: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  color: white;
  font-size: 0.9rem;
  transition: width 0.3s;
}

.measure-roi {
  width: 60px;
  font-size: 0.9rem;
  text-align: right;
  color: #e5e7eb;
}

/* 图表占位样式 */
.placeholder-chart {
  height: 100%;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  padding: 20px;
}

.placeholder-chart h4 {
  margin: 0 0 10px 0;
  color: #3b82f6;
}

.placeholder-chart p {
  margin: 0 0 20px 0;
  font-size: 0.9rem;
  color: #d1d5db;
}

.mock-bars {
  display: flex;
  justify-content: center;
  gap: 25px;
  height: 150px;
  align-items: flex-end;
}

.mock-bar {
  width: 40px;
  display: flex;
  flex-direction: column;
}

.mock-bar-segment {
  width: 100%;
  border-radius: 4px 4px 0 0;
}

.mock-chart {
  width: 100%;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mock-line {
  width: 80%;
  height: 60%;
  background: linear-gradient(90deg, transparent, #3b82f6, #10b981, transparent);
  border-radius: 50%;
  opacity: 0.5;
}

.mock-grouped-bar {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mock-bar-pill {
  height: 20px;
  width: 80px;
  border-radius: 10px;
}

.mock-bar-pill.cost {
  background-color: #ef4444;
}

.mock-bar-pill.benefit {
  background-color: #10b981;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .metrics-container {
    flex-direction: column;
  }
  
  .analysis-panels {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>

<!--
注意: 如果要实现真实的图表而不是模拟图表，需要安装echarts依赖:
npm install echarts --save

同时还需要以下SVG图标文件，如果不存在可以创建:
- 不需要外部SVG文件，已经使用内联SVG替代
--> 