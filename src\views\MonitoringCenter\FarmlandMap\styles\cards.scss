/**
 * 卡片组件通用样式
 */
@use 'variables.scss' as vars;

// 基本卡片样式
.map-card {
  @include vars.card-style-new;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: vars.$shadow-large;
  }
  
  // 卡片标题
  .card-header {
    padding: 12px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 255, 170, 0.15);
    
    .card-title {
      color: vars.$primary-color;
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      
      .title-icon {
        margin-right: 8px;
      }
    }
    
    .card-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  // 卡片内容
  .card-body {
    padding: 15px;
  }
  
  // 卡片底部
  .card-footer {
    padding: 10px 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  // 卡片变体：紧凑型
  &.card-compact {
    .card-header {
      padding: 8px 12px;
    }
    
    .card-body {
      padding: 10px 12px;
    }
    
    .card-footer {
      padding: 8px 12px;
    }
  }
  
  // 卡片变体：强调型
  &.card-accent {
    border-left: 3px solid vars.$primary-color;
    
    &.accent-warning {
      border-left-color: vars.$warning-color;
    }
    
    &.accent-danger {
      border-left-color: vars.$danger-color;
    }
    
    &.accent-success {
      border-left-color: vars.$success-color;
    }
    
    &.accent-info {
      border-left-color: vars.$info-color;
    }
  }
  
  // 卡片变体：透明型
  &.card-transparent {
    background: rgba(26, 38, 59, 0.6);
  }
}

// 数据项样式
.data-item {
  padding: 12px;
  background: rgba(0, 21, 41, 0.3);
  border-radius: 8px;
  transition: vars.$transition-normal;
  border: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 10px;
  
  &:hover {
    background: rgba(43, 255, 150, 0.08);
    transform: translateY(-2px);
    box-shadow: vars.$shadow-small;
  }
  
  .data-label {
    font-size: 13px;
    color: vars.$text-secondary;
    margin-bottom: 5px;
  }
  
  .data-value {
    font-size: 20px;
    font-weight: 600;
    color: vars.$text-light;
    
    .data-unit {
      font-size: 14px;
      font-weight: normal;
      color: vars.$text-secondary;
      margin-left: 2px;
    }
  }
}

// 指标项样式
.stat-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  transition: vars.$transition-fast;
  
  &:hover {
    background: rgba(43, 255, 150, 0.08);
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: rgba(0, 21, 41, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: vars.$primary-color;
  }
  
  .stat-content {
    flex: 1;
    min-width: 0;
    
    .stat-value {
      font-size: 18px;
      font-weight: 600;
      color: vars.$text-light;
    }
    
    .stat-label {
      font-size: 13px;
      color: vars.$text-secondary;
    }
  }
} 