/**
 * 高德地图API加载器
 * 用于动态加载高德地图API
 */

import envConfig from '@/config/env';

// 高德地图API加载状态
let isMapLoaded = false;
let loadPromise: Promise<void> | null = null;

/**
 * 加载高德地图API
 * @returns Promise 加载完成后的Promise
 */
export const loadAMapAPI = (): Promise<void> => {
  // 如果已经加载过，直接返回成功
  if (isMapLoaded && window.AMap) {
    return Promise.resolve();
  }

  // 如果正在加载中，返回正在进行的Promise
  if (loadPromise) {
    return loadPromise;
  }

  // 创建新的加载Promise
  loadPromise = new Promise<void>((resolve, reject) => {
    // 获取API密钥
    const apiKey = envConfig.amapKey;
    if (!apiKey) {
      console.warn('高德地图API密钥未配置，请在.env文件中设置VITE_AMAP_KEY');
    }

    // 创建script标签
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    // 更新API加载URL，确保包含所有必要的插件
    script.src = `https://webapi.amap.com/maps?v=2.0&key=${apiKey}&plugin=AMap.MouseTool,AMap.PolygonEditor,AMap.Scale,AMap.ToolBar`;

    // 加载成功回调
    script.onload = () => {
      console.log('高德地图API加载成功，检查插件...');
      // 验证API是否正确加载
      if (window.AMap) {
        // 安全地检查版本 - 如果存在version属性
        try {
          const version = (window.AMap as any).version;
          if (version) {
            console.log('高德地图API版本:', version);
          }
        } catch (e) {
          console.log('无法获取高德地图API版本');
        }

        isMapLoaded = true;
        loadPromise = null;
        resolve();
      } else {
        console.error('高德地图API加载异常：window.AMap未定义');
        reject(new Error('高德地图API加载异常：window.AMap未定义'));
      }
    };

    // 加载失败回调
    script.onerror = (error) => {
      console.error('高德地图API加载失败:', error);
      loadPromise = null;
      reject(new Error('高德地图API加载失败'));
    };

    // 添加到文档
    document.head.appendChild(script);
  });

  return loadPromise;
};

/**
 * 检查高德地图API是否已加载
 * @returns boolean 是否已加载
 */
export const isAMapLoaded = (): boolean => {
  return isMapLoaded && !!window.AMap;
};
