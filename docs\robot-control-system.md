# 机器狗控制系统

## 概述

机器狗控制系统是智慧农业平台的重要组成部分，提供了低延迟的实时机器狗控制功能。系统采用WebSocket技术实现前后端通信，支持键盘控制、手动控制和视频流显示。

## 功能特性

### 🎮 实时控制
- **WebSocket通信**：采用WebSocket协议，实现低延迟的实时控制
- **持续按键控制**：按住键盘时机器狗持续移动，松开立即停止
- **手动控制**：提供方向按钮进行精确控制

### 📹 视频监控
- **实时视频流**：通过HTTP流媒体显示机器狗视角画面
- **连接状态监控**：实时显示视频流连接状态
- **自动重连**：视频流断开时自动尝试重连

### ⌨️ 键盘映射
- **W**：前进 (x: +0.5)
- **A**：左移 (y: +0.5)
- **S**：后退 (x: -0.5)
- **D**：右移 (y: -0.5)
- **Q**：左转 (z: +0.5)
- **E**：右转 (z: -0.5)
- **空格**：普通停止（安全停止，不会让机器狗倒地）

### 🛡️ 安全机制
- **紧急停止**：需要用户确认的紧急停止功能（会让机器狗倒地，仅在紧急情况使用）
- **普通停止**：安全的停止移动功能（空格键、连接断开、窗口失焦时使用）
- **连接断开保护**：连接断开时自动发送普通停止命令
- **窗口失焦保护**：窗口失焦时自动停止移动（普通停止）
- **错误处理**：完善的错误处理和用户提示

## 系统架构

### 后端架构
```
┌─────────────────────────────────────────┐
│              Spring Boot                │
├─────────────────────────────────────────┤
│         RobotControlWebSocket           │
│    (/robot-control)               │
├─────────────────────────────────────────┤
│         AgriDogService                  │
│    (现有机器狗服务)                      │
├─────────────────────────────────────────┤
│         机器狗硬件接口                    │
└─────────────────────────────────────────┘
```

### 前端架构
```
┌─────────────────────────────────────────┐
│           Vue 3 + TypeScript            │
├─────────────────────────────────────────┤
│  RobotVideoPanel  │  RobotControlPanel  │
├─────────────────────────────────────────┤
│         robotWebSocketService           │
├─────────────────────────────────────────┤
│            WebSocket Client             │
└─────────────────────────────────────────┘
```

## 配置说明

### 环境变量配置

复制 `.env.example` 为 `.env.local` 并根据实际环境修改：

```bash
# 机器狗控制WebSocket路径
VITE_WEBSOCKET_ROBOT_CONTROL_PATH=/robot-control

# 机器狗IP地址
VITE_ROBOT_IP=***********

# 视频流配置
VITE_VIDEO_STREAM_HOST=***********
VITE_VIDEO_STREAM_PORT=8000

# 控制参数
VITE_ROBOT_CONTROL_MOVE_SPEED=0.5
VITE_ROBOT_CONTROL_ROTATE_SPEED=0.5
VITE_ROBOT_CONTROL_HEARTBEAT_INTERVAL=5000
VITE_ROBOT_CONTROL_KEYBOARD_ENABLED=true
```

### 后端配置

确保Spring Boot应用已启用WebSocket支持：

```java
@Configuration
@EnableWebSocket
public class WebSocketConfig {
    // WebSocket配置
}
```

## 使用指南

### 1. 启动系统

#### 后端启动
```bash
cd smart-agriculture-api
mvn spring-boot:run
```

#### 前端启动
```bash
cd smart-agriculture-h53
npm run dev
```

### 2. 访问控制界面

1. 打开浏览器访问前端地址
2. 导航到 "监控中心" -> "设备实时追踪"
3. 在右侧面板中找到机器狗控制组件

### 3. 连接机器狗

1. 点击视频面板中的"连接"按钮
2. 等待WebSocket连接建立
3. 确认视频流正常显示

### 4. 控制机器狗

#### 键盘控制
1. 启用"键盘控制"开关
2. 使用WASDQE键控制机器狗移动
3. 按住键盘时机器狗持续移动，松开立即停止

#### 手动控制
1. 使用方向按钮进行精确控制
2. 按住按钮时机器狗持续移动
3. 松开按钮时机器狗立即停止

#### 停止控制
- **普通停止**：按下空格键（安全停止，不会让机器狗倒地）
- **紧急停止**：点击"紧急停止"按钮并确认（⚠️ 危险：会让机器狗立即停止所有动作并可能倒地）

## API接口

### WebSocket消息格式

#### 请求消息
```json
{
  "type": "MOVE",
  "messageId": "msg_1234567890_abc123",
  "timestamp": 1640995200000,
  "moveParams": {
    "x": 0.5,
    "y": 0.0,
    "z": 0.0
  }
}
```

#### 响应消息
```json
{
  "type": "MOVE",
  "messageId": "msg_1234567890_abc123",
  "timestamp": 1640995200001,
  "success": true,
  "message": "移动命令执行成功",
  "data": {
    "x": 0.5,
    "y": 0.0,
    "z": 0.0
  }
}
```

### 消息类型

| 类型 | 说明 | 参数 |
|------|------|------|
| CONNECT | 连接机器狗 | 无 |
| DISCONNECT | 断开连接 | 无 |
| MOVE | 移动控制 | moveParams |
| STOP | 停止移动 | 无 |
| EMERGENCY_STOP | 紧急停止 | 无 |
| HEARTBEAT | 心跳检测 | 无 |
| STATUS | 状态查询 | 无 |
| IMU_DATA | IMU数据 | 无 |

## 故障排除

### 常见问题

#### 1. WebSocket连接失败
- 检查后端服务是否正常运行
- 确认WebSocket端点路径配置正确
- 检查防火墙设置

#### 2. 视频流无法显示
- 确认视频流服务地址配置正确
- 检查网络连接
- 验证视频流格式是否支持

#### 3. 键盘控制无响应
- 确认键盘控制开关已启用
- 检查浏览器焦点是否在控制面板上
- 验证WebSocket连接状态

#### 4. 机器狗控制延迟
- 检查网络延迟
- 确认WebSocket连接稳定
- 调整心跳间隔配置

### 日志调试

#### 前端日志
```javascript
// 在浏览器控制台查看WebSocket日志
console.log('WebSocket状态:', robotWebSocketService.connectionState)
```

#### 后端日志
```java
// 查看Spring Boot应用日志
tail -f logs/application.log | grep "RobotControl"
```

## 开发指南

### 添加新的控制命令

1. 在 `RobotControlMessageDTO.java` 中添加新的消息类型
2. 在 `RobotControlWebSocket.java` 中添加处理逻辑
3. 在前端 `robotWebSocketService.ts` 中添加对应方法
4. 在控制面板中添加UI控件

### 扩展视频功能

1. 修改 `RobotVideoPanel.vue` 组件
2. 添加新的视频控制功能
3. 更新环境配置

## 性能优化

### WebSocket优化
- 使用消息队列避免频繁发送
- 实现智能重连机制
- 优化心跳检测频率

### 视频流优化
- 使用适当的视频编码格式
- 实现自适应码率
- 添加缓冲区管理

## 安全考虑

### 访问控制
- 实现用户身份验证
- 添加操作权限检查
- 记录操作日志

### 数据安全
- 使用WSS加密WebSocket连接
- 验证控制命令合法性
- 实现命令频率限制

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础的机器狗控制功能
- 支持WebSocket实时通信
- 添加视频流显示功能
- 实现键盘和手动控制
