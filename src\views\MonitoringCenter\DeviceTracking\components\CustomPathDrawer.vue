<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleVisibleChange"
    title="自定义路径绘制"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="custom-path-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="tool-group">
          <el-button-group size="small">
            <el-button
              :type="drawMode === 'point' ? 'primary' : ''"
              @click="setDrawMode('point')"
            >
              <el-icon><Location /></el-icon>
              点击添加
            </el-button>
            <el-button
              :type="drawMode === 'draw' ? 'primary' : ''"
              @click="setDrawMode('draw')"
            >
              <el-icon><Edit /></el-icon>
              绘制路径
            </el-button>
            <el-button
              :type="drawMode === 'none' ? 'primary' : ''"
              @click="setDrawMode('none')"
            >
              <el-icon><View /></el-icon>
              查看模式
            </el-button>
          </el-button-group>
        </div>

        <div class="tool-group">
          <el-button size="small" @click="clearPath" :disabled="pathPoints.length === 0">
            <el-icon><Delete /></el-icon>
            清空路径
          </el-button>
          <el-button size="small" @click="undoLastPoint" :disabled="pathPoints.length === 0">
            <el-icon><RefreshLeft /></el-icon>
            撤销上一点
          </el-button>
        </div>

        <div class="tool-info">
          <el-tag size="small">{{ pathPoints.length }} 个路径点</el-tag>
          <el-tag size="small" type="success" v-if="totalDistance > 0">
            总距离: {{ totalDistance.toFixed(1) }}m
          </el-tag>
          <el-tag size="small" type="info" v-if="currentMouseCoords">
            鼠标位置: ({{ currentMouseCoords.x }}, {{ currentMouseCoords.y }})
          </el-tag>
        </div>
      </div>

      <!-- 绘制区域 -->
      <div class="draw-area">
        <div class="canvas-container" ref="canvasContainer">
          <svg
            ref="drawingSvg"
            :width="canvasWidth"
            :height="canvasHeight"
            class="drawing-svg"
            @click="handleCanvasClick"
            @mousemove="handleCanvasMouseMove"
            @mousedown="handleCanvasMouseDown"
            @mouseup="handleCanvasMouseUp"
            @mouseleave="handleCanvasMouseLeave"
          >
            <!-- 基站边界 -->
            <rect
              v-if="anchorBounds"
              :x="scaleX(anchorBounds.minX)"
              :y="scaleY(anchorBounds.maxY)"
              :width="scaleX(anchorBounds.maxX) - scaleX(anchorBounds.minX)"
              :height="scaleY(anchorBounds.minY) - scaleY(anchorBounds.maxY)"
              fill="rgba(64, 158, 255, 0.1)"
              stroke="rgba(64, 158, 255, 0.5)"
              stroke-width="2"
              stroke-dasharray="5,5"
            />

            <!-- 网格线 -->
            <defs>
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />

            <!-- 路径线 -->
            <polyline
              v-if="pathPoints.length > 1"
              :points="pathLinePoints"
              fill="none"
              stroke="#67c23a"
              stroke-width="3"
              stroke-linejoin="round"
              stroke-linecap="round"
            />

            <!-- 绘制中的临时线 -->
            <polyline
              v-if="isDrawing && tempPath.length > 1"
              :points="tempPathPoints"
              fill="none"
              stroke="#409eff"
              stroke-width="2"
              stroke-dasharray="5,5"
            />

            <!-- 路径点 -->
            <g v-for="(point, index) in pathPoints" :key="index">
              <circle
                :cx="scaleX(point.x)"
                :cy="scaleY(point.y)"
                :r="8"
                :fill="isPointInBounds(point) ? '#67c23a' : '#f56c6c'"
                stroke="white"
                stroke-width="2"
                class="path-point"
                @click.stop="selectPoint(index)"
                :class="{ 'selected': selectedPointIndex === index }"
              />
              <text
                :x="scaleX(point.x)"
                :y="scaleY(point.y) - 12"
                text-anchor="middle"
                font-size="12"
                fill="#333"
                font-weight="bold"
              >
                {{ index + 1 }}
              </text>
            </g>

            <!-- 机器狗当前位置 -->
            <g v-if="currentPosition && (currentPosition.x !== 0 || currentPosition.y !== 0)">
              <circle
                :cx="scaleX(currentPosition.x)"
                :cy="scaleY(currentPosition.y)"
                :r="10"
                fill="#409eff"
                stroke="white"
                stroke-width="3"
              />
              <text
                :x="scaleX(currentPosition.x)"
                :y="scaleY(currentPosition.y) + 4"
                text-anchor="middle"
                font-size="12"
                fill="white"
                font-weight="bold"
              >
                🐕
              </text>
            </g>

            <!-- 鼠标跟随的预览点 -->
            <circle
              v-if="drawMode !== 'none' && mousePosition"
              :cx="mousePosition.x"
              :cy="mousePosition.y"
              :r="6"
              fill="rgba(64, 158, 255, 0.5)"
              stroke="#409eff"
              stroke-width="2"
              stroke-dasharray="3,3"
            />
          </svg>
        </div>

        <!-- 绘制提示 -->
        <div class="draw-hints">
          <div class="hint-row">
            <div v-if="drawMode === 'point'" class="hint">
              <el-icon><InfoFilled /></el-icon>
              点击地图添加路径点
            </div>
            <div v-else-if="drawMode === 'draw'" class="hint">
              <el-icon><InfoFilled /></el-icon>
              按住鼠标拖拽绘制路径，松开完成绘制
            </div>
            <div v-else-if="drawMode === 'none'" class="hint">
              <el-icon><InfoFilled /></el-icon>
              查看模式：点击路径点可以选中和编辑
            </div>

            <div v-if="currentMouseCoords" class="coords-display">
              <el-icon><Location /></el-icon>
              坐标: ({{ currentMouseCoords.x }}, {{ currentMouseCoords.y }})
            </div>
          </div>
        </div>
      </div>

      <!-- 路径点列表 -->
      <div class="path-list">
        <div class="list-header">
          <h4>路径点列表</h4>
          <el-button size="small" @click="optimizePath" :disabled="pathPoints.length < 3">
            <el-icon><Tools /></el-icon>
            优化路径
          </el-button>
        </div>

        <div class="list-content" v-if="pathPoints.length > 0">
          <div
            v-for="(point, index) in pathPoints"
            :key="index"
            class="point-item"
            :class="{ 'selected': selectedPointIndex === index, 'invalid': !isPointInBounds(point) }"
            @click="selectPoint(index)"
          >
            <div class="point-info">
              <span class="point-index">{{ index + 1 }}</span>
              <span class="point-coords">({{ point.x.toFixed(2) }}, {{ point.y.toFixed(2) }})</span>
              <el-tag
                size="small"
                :type="isPointInBounds(point) ? 'success' : 'danger'"
              >
                {{ isPointInBounds(point) ? '安全' : '超界' }}
              </el-tag>
            </div>
            <div class="point-actions">
              <el-button size="small" text @click.stop="editPoint(index)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" text type="danger" @click.stop="removePoint(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <div v-else class="empty-list">
          <el-empty description="暂无路径点" :image-size="80" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-info">
          <el-tag v-if="pathPoints.length > 0">{{ pathPoints.length }} 个路径点</el-tag>
          <el-tag v-if="invalidPointsCount > 0" type="danger">{{ invalidPointsCount }} 个超界点</el-tag>
          <span class="distance-info" v-if="totalDistance > 0">总距离: {{ totalDistance.toFixed(1) }}m</span>
        </div>
        <div class="footer-actions">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSave" :disabled="!canSave">
            应用路径
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Location,
  Edit,
  View,
  Delete,
  RefreshLeft,
  Tools,
  InfoFilled
} from '@element-plus/icons-vue'
import type { PathPoint } from '../composables/useAutoCruise'

// Props
interface Props {
  visible: boolean
  anchors?: Array<{x: number, y: number}>
  currentPosition?: PathPoint
}

const props = withDefaults(defineProps<Props>(), {
  anchors: () => [],
  currentPosition: () => ({ x: 0, y: 0, name: '机器狗' })
})

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'save', points: PathPoint[]): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const pathPoints = ref<PathPoint[]>([])
const drawMode = ref<'point' | 'draw' | 'none'>('point')
const selectedPointIndex = ref<number>(-1)
const canvasWidth = ref(600)
const canvasHeight = ref(400)
const mousePosition = ref<{x: number, y: number} | null>(null)
const currentMouseCoords = ref<{x: string, y: string} | null>(null)

// 绘制相关状态
const isDrawing = ref(false)
const tempPath = ref<{x: number, y: number}[]>([])
const drawingSvg = ref<SVGElement>()
const canvasContainer = ref<HTMLElement>()

// 计算属性
const anchorBounds = computed(() => {
  if (!props.anchors || props.anchors.length < 4) return null

  return {
    minX: Math.min(...props.anchors.map(a => a.x)),
    maxX: Math.max(...props.anchors.map(a => a.x)),
    minY: Math.min(...props.anchors.map(a => a.y)),
    maxY: Math.max(...props.anchors.map(a => a.y))
  }
})

const pathLinePoints = computed(() => {
  return pathPoints.value
    .map(point => `${scaleX(point.x)},${scaleY(point.y)}`)
    .join(' ')
})

const tempPathPoints = computed(() => {
  return tempPath.value
    .map(point => `${point.x},${point.y}`)
    .join(' ')
})

const totalDistance = computed(() => {
  if (pathPoints.value.length < 2) return 0

  let distance = 0
  for (let i = 1; i < pathPoints.value.length; i++) {
    const prev = pathPoints.value[i - 1]
    const curr = pathPoints.value[i]
    distance += Math.sqrt(Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2))
  }
  return distance
})

const invalidPointsCount = computed(() => {
  return pathPoints.value.filter(point => !isPointInBounds(point)).length
})

const canSave = computed(() => {
  return pathPoints.value.length >= 2 && invalidPointsCount.value === 0
})

// 方法
const isPointInBounds = (point: PathPoint): boolean => {
  if (!anchorBounds.value) return true

  return point.x >= anchorBounds.value.minX &&
         point.x <= anchorBounds.value.maxX &&
         point.y >= anchorBounds.value.minY &&
         point.y <= anchorBounds.value.maxY
}

const scaleX = (x: number): number => {
  if (!anchorBounds.value) return x * 50 + 50

  const bounds = anchorBounds.value
  const padding = 40
  const width = canvasWidth.value - padding * 2
  return padding + ((x - bounds.minX) / (bounds.maxX - bounds.minX)) * width
}

const scaleY = (y: number): number => {
  if (!anchorBounds.value) return y * 50 + 50

  const bounds = anchorBounds.value
  const padding = 40
  const height = canvasHeight.value - padding * 2
  return padding + ((bounds.maxY - y) / (bounds.maxY - bounds.minY)) * height
}

const unscaleX = (x: number): number => {
  if (!anchorBounds.value) return (x - 50) / 50

  const bounds = anchorBounds.value
  const padding = 40
  const width = canvasWidth.value - padding * 2
  return bounds.minX + ((x - padding) / width) * (bounds.maxX - bounds.minX)
}

const unscaleY = (y: number): number => {
  if (!anchorBounds.value) return (y - 50) / 50

  const bounds = anchorBounds.value
  const padding = 40
  const height = canvasHeight.value - padding * 2
  return bounds.maxY - ((y - padding) / height) * (bounds.maxY - bounds.minY)
}

const setDrawMode = (mode: 'point' | 'draw' | 'none') => {
  drawMode.value = mode
  selectedPointIndex.value = -1
  isDrawing.value = false
  tempPath.value = []
}

const handleCanvasClick = (event: MouseEvent) => {
  if (drawMode.value !== 'point') return

  const rect = drawingSvg.value!.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  const worldX = unscaleX(x)
  const worldY = unscaleY(y)

  const newPoint: PathPoint = {
    x: Number(worldX.toFixed(2)),
    y: Number(worldY.toFixed(2)),
    name: `路径点${pathPoints.value.length + 1}`
  }

  pathPoints.value.push(newPoint)
  ElMessage.success(`已添加路径点 ${pathPoints.value.length}`)
}

const handleCanvasMouseMove = (event: MouseEvent) => {
  const rect = drawingSvg.value!.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  mousePosition.value = { x, y }

  // 计算并显示当前鼠标位置的世界坐标
  const worldX = unscaleX(x)
  const worldY = unscaleY(y)
  currentMouseCoords.value = {
    x: worldX.toFixed(2),
    y: worldY.toFixed(2)
  }

  if (isDrawing.value && drawMode.value === 'draw') {
    tempPath.value.push({ x, y })
  }
}

const handleCanvasMouseDown = (event: MouseEvent) => {
  if (drawMode.value !== 'draw') return

  const rect = drawingSvg.value!.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  isDrawing.value = true
  tempPath.value = [{ x, y }]
}

const handleCanvasMouseUp = () => {
  if (!isDrawing.value || drawMode.value !== 'draw') return

  // 将临时路径转换为路径点
  if (tempPath.value.length > 1) {
    const newPoints: PathPoint[] = []

    // 简化路径，每隔一定距离取一个点
    const minDistance = 20 // 像素
    let lastPoint = tempPath.value[0]

    for (const point of tempPath.value) {
      const distance = Math.sqrt(
        Math.pow(point.x - lastPoint.x, 2) +
        Math.pow(point.y - lastPoint.y, 2)
      )

      if (distance >= minDistance) {
        const worldX = unscaleX(point.x)
        const worldY = unscaleY(point.y)

        newPoints.push({
          x: Number(worldX.toFixed(2)),
          y: Number(worldY.toFixed(2)),
          name: `绘制点${pathPoints.value.length + newPoints.length + 1}`
        })

        lastPoint = point
      }
    }

    pathPoints.value.push(...newPoints)
    ElMessage.success(`已添加 ${newPoints.length} 个路径点`)
  }

  isDrawing.value = false
  tempPath.value = []
}

const handleCanvasMouseLeave = () => {
  mousePosition.value = null
  currentMouseCoords.value = null
  if (isDrawing.value) {
    handleCanvasMouseUp()
  }
}

const selectPoint = (index: number) => {
  selectedPointIndex.value = selectedPointIndex.value === index ? -1 : index
}

const editPoint = (index: number) => {
  // 这里可以打开编辑对话框，暂时简化处理
  ElMessage.info('路径点编辑功能开发中...')
}

const removePoint = (index: number) => {
  pathPoints.value.splice(index, 1)
  if (selectedPointIndex.value === index) {
    selectedPointIndex.value = -1
  } else if (selectedPointIndex.value > index) {
    selectedPointIndex.value--
  }
  ElMessage.success('已删除路径点')
}

const clearPath = () => {
  pathPoints.value = []
  selectedPointIndex.value = -1
  ElMessage.success('已清空路径')
}

const undoLastPoint = () => {
  if (pathPoints.value.length > 0) {
    pathPoints.value.pop()
    ElMessage.success('已撤销上一个路径点')
  }
}

const optimizePath = () => {
  if (pathPoints.value.length < 3) return

  // 简单的路径优化：移除过于接近的点
  const optimized: PathPoint[] = [pathPoints.value[0]]
  const minDistance = 0.5 // 最小距离0.5米

  for (let i = 1; i < pathPoints.value.length; i++) {
    const current = pathPoints.value[i]
    const last = optimized[optimized.length - 1]

    const distance = Math.sqrt(
      Math.pow(current.x - last.x, 2) +
      Math.pow(current.y - last.y, 2)
    )

    if (distance >= minDistance) {
      optimized.push(current)
    }
  }

  pathPoints.value = optimized
  ElMessage.success(`路径优化完成，保留 ${optimized.length} 个路径点`)
}

const handleVisibleChange = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSave = () => {
  if (!canSave.value) {
    ElMessage.error('路径无效，请检查路径点')
    return
  }

  emit('save', pathPoints.value)
  emit('update:visible', false)
  ElMessage.success(`已应用自定义路径，共 ${pathPoints.value.length} 个路径点`)
}

// 监听对话框打开，重置状态
watch(() => props.visible, (visible) => {
  if (visible) {
    pathPoints.value = []
    selectedPointIndex.value = -1
    setDrawMode('point')
  }
})
</script>

<style scoped lang="scss">
.custom-path-container {
  display: flex;
  flex-direction: column;
  height: 70vh;
  gap: 16px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;

  .tool-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .tool-info {
    display: flex;
    gap: 8px;
  }
}

.draw-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;

  .canvas-container {
    flex: 1;
    position: relative;

    .drawing-svg {
      width: 100%;
      height: 100%;
      cursor: crosshair;
      background: white;

      .path-point {
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          r: 10;
        }

        &.selected {
          stroke: #409eff;
          stroke-width: 4;
        }
      }
    }
  }

  .draw-hints {
    padding: 8px 12px;
    background: #f0f9ff;
    border-top: 1px solid #e4e7ed;

    .hint-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
    }

    .hint {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #409eff;
    }

    .coords-display {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #67c23a;
      font-weight: 500;
      font-family: monospace;
      background: rgba(103, 194, 58, 0.1);
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid rgba(103, 194, 58, 0.2);
    }
  }
}

.path-list {
  height: 200px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  flex-direction: column;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e4e7ed;
    background: #fafafa;

    h4 {
      margin: 0;
      font-size: 14px;
      color: #333;
    }
  }

  .list-content {
    flex: 1;
    overflow-y: auto;

    .point-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f7fa;
      }

      &.selected {
        background: #e6f7ff;
        border-left: 3px solid #409eff;
      }

      &.invalid {
        background: #fef0f0;
        border-left: 3px solid #f56c6c;
      }

      .point-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .point-index {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          background: #409eff;
          color: white;
          border-radius: 50%;
          font-size: 12px;
          font-weight: bold;
        }

        .point-coords {
          font-family: monospace;
          font-size: 12px;
          color: #666;
        }
      }

      .point-actions {
        display: flex;
        gap: 4px;
      }
    }
  }

  .empty-list {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .distance-info {
      font-size: 12px;
      color: #666;
    }
  }

  .footer-actions {
    display: flex;
    gap: 8px;
  }
}
</style>
