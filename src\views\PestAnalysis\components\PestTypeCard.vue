<!-- 
  PestTypeCard.vue
  虫害类型卡片组件，用于展示虫害基本信息
  支持图片、名称、描述和统计数据
-->
<template>
  <div class="pest-type-card" :class="{ 'danger-level': dangerLevel }">
    <div class="pest-image">
      <img v-if="imageUrl" :src="imageUrl" :alt="pestName" />
      <div v-else class="no-image">
        <el-icon><Picture /></el-icon>
      </div>
    </div>
    
    <div class="pest-info">
      <h3 class="pest-name">{{ pestName }}</h3>
      <p class="pest-type">{{ pestType }}</p>
      <div class="pest-description">{{ description }}</div>
      
      <div class="pest-stats">
        <div class="stat-item">
          <span class="stat-value">{{ count }}</span>
          <span class="stat-label">记录数</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ affectedArea }}亩</span>
          <span class="stat-label">影响面积</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ lastUpdateDays }}天前</span>
          <span class="stat-label">最近更新</span>
        </div>
      </div>
    </div>
    
    <div class="danger-indicator" :class="dangerLevel">
      <div class="indicator-dot"></div>
      <span class="indicator-label">{{ getDangerLevelText() }}</span>
    </div>
    
    <div class="card-actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Picture } from '@element-plus/icons-vue';

const props = defineProps({
  pestName: {
    type: String,
    required: true
  },
  pestType: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: '暂无描述'
  },
  imageUrl: {
    type: String,
    default: ''
  },
  count: {
    type: Number,
    default: 0
  },
  affectedArea: {
    type: Number,
    default: 0
  },
  lastUpdateDays: {
    type: Number,
    default: 0
  },
  dangerLevel: {
    type: String,
    default: 'normal', // normal, warning, serious, critical
    validator: (value: string) => {
      return ['normal', 'warning', 'serious', 'critical'].includes(value);
    }
  }
});

// 获取危害等级文本
const getDangerLevelText = () => {
  const levelTexts = {
    normal: '普通危害',
    warning: '轻度危害',
    serious: '严重危害',
    critical: '紧急危害'
  };
  return levelTexts[props.dangerLevel as keyof typeof levelTexts] || '未知';
};
</script>

<style scoped>
.pest-type-card {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  border: 1px solid #3b4863;
  padding: 20px;
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "image info"
    "image info"
    "actions actions";
  gap: 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.pest-type-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.pest-image {
  grid-area: image;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #1f2937;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pest-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4b5563;
  font-size: 32px;
}

.pest-info {
  grid-area: info;
  display: flex;
  flex-direction: column;
}

.pest-name {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #e5e7eb;
}

.pest-type {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #9ca3af;
}

.pest-description {
  margin-bottom: 15px;
  font-size: 14px;
  color: #d1d5db;
  line-height: 1.5;
  flex: 1;
}

.pest-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #3b82f6;
}

.stat-label {
  font-size: 12px;
  color: #9ca3af;
}

.danger-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.danger-indicator .indicator-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  box-shadow: 0 0 5px currentColor;
}

.danger-indicator.normal .indicator-dot {
  background-color: #10b981;
  color: #10b981;
}

.danger-indicator.warning .indicator-dot {
  background-color: #f59e0b;
  color: #f59e0b;
}

.danger-indicator.serious .indicator-dot {
  background-color: #ef4444;
  color: #ef4444;
}

.danger-indicator.critical .indicator-dot {
  background-color: #7c3aed;
  color: #7c3aed;
}

.indicator-label {
  font-size: 12px;
  color: #d1d5db;
}

.card-actions {
  grid-area: actions;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

/* 危险等级边框样式 */
.pest-type-card.danger-level.warning {
  border-color: #f59e0b;
}

.pest-type-card.danger-level.serious {
  border-color: #ef4444;
}

.pest-type-card.danger-level.critical {
  border-color: #7c3aed;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(124, 58, 237, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(124, 58, 237, 0);
  }
}
</style> 