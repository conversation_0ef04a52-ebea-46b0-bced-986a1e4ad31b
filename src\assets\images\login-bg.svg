<svg width="1000" height="1000" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景网格 -->
  <defs>
    <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0, 255, 170, 0.1)" stroke-width="0.5"/>
    </pattern>
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="url(#smallGrid)"/>
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="rgba(0, 255, 170, 0.2)" stroke-width="1"/>
    </pattern>
    
    <!-- 叶子形状 -->
    <symbol id="leaf" viewBox="0 0 100 100">
      <path d="M50,0 C70,30 90,50 50,100 C10,50 30,30 50,0" fill="rgba(0, 255, 170, 0.15)" />
    </symbol>
    
    <!-- 科技圆环 -->
    <symbol id="techCircle" viewBox="0 0 100 100">
      <circle cx="50" cy="50" r="45" fill="none" stroke="rgba(0, 255, 170, 0.2)" stroke-width="2" />
      <circle cx="50" cy="50" r="35" fill="none" stroke="rgba(0, 255, 170, 0.15)" stroke-width="1.5" />
      <path d="M50,5 L50,15 M50,85 L50,95 M5,50 L15,50 M85,50 L95,50" stroke="rgba(0, 255, 170, 0.3)" stroke-width="2" />
    </symbol>
  </defs>
  
  <!-- 背景矩形 -->
  <rect width="100%" height="100%" fill="#001041" />
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- 装饰元素 -->
  <use href="#leaf" x="100" y="100" width="80" height="80" />
  <use href="#leaf" x="800" y="200" width="100" height="100" transform="rotate(45, 850, 250)" />
  <use href="#leaf" x="200" y="700" width="70" height="70" transform="rotate(-30, 235, 735)" />
  
  <use href="#techCircle" x="700" y="600" width="150" height="150" />
  <use href="#techCircle" x="150" y="300" width="120" height="120" />
  <use href="#techCircle" x="500" y="200" width="80" height="80" />
  
  <!-- 动态线条 -->
  <path d="M0,300 Q250,250 500,400 T1000,300" fill="none" stroke="rgba(0, 255, 170, 0.3)" stroke-width="2" />
  <path d="M0,600 Q300,700 600,550 T1000,650" fill="none" stroke="rgba(0, 255, 170, 0.2)" stroke-width="1.5" />
  
  <!-- 数据点 -->
  <circle cx="250" cy="250" r="3" fill="#00ffaa" />
  <circle cx="600" cy="400" r="3" fill="#00ffaa" />
  <circle cx="800" cy="350" r="3" fill="#00ffaa" />
  <circle cx="300" cy="650" r="3" fill="#00ffaa" />
  <circle cx="700" cy="550" r="3" fill="#00ffaa" />
</svg> 