# 智慧农业前端开发环境 Docker Compose 配置
# 版本: 3.8 (支持最新的 Docker Compose 功能)

version: '3.8'

services:
  # 前端开发服务
  smart-agriculture-frontend:
    # 服务构建配置
    build:
      context: .
      dockerfile: Dockerfile
    
    # 容器名称
    container_name: smart-agriculture-h5-dev
    
    # 端口映射：宿主机端口:容器端口
    ports:
      - "5174:5174"
    
    # 卷挂载配置 - 支持热重载
    volumes:
      # 挂载源代码目录，支持实时代码更新
      - .:/app
      # 排除 node_modules，使用容器内的版本以避免平台兼容性问题
      - /app/node_modules
      # 挂载缓存目录以提高性能
      - node_modules_cache:/app/node_modules
    
    # 环境变量配置
    environment:
      # Node.js 环境
      - NODE_ENV=development
      # Vite 特定配置
      - VITE_DEV_SERVER_HOST=0.0.0.0
      - VITE_DEV_SERVER_PORT=5174
      # 启用 Vite HMR (热模块替换)
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    
    # 环境变量文件
    env_file:
      - .env.development
    
    # 网络配置
    networks:
      - smart-agriculture-network
    
    # 重启策略
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5174"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 依赖服务（如果需要等待其他服务启动）
    # depends_on:
    #   - backend-api
    
    # 标签（用于管理和识别）
    labels:
      - "project=smart-agriculture"
      - "service=frontend"
      - "environment=development"

# 网络配置
networks:
  smart-agriculture-network:
    driver: bridge
    name: smart-agriculture-dev-network

# 卷配置
volumes:
  # Node.js 模块缓存卷
  node_modules_cache:
    name: smart-agriculture-node-modules
