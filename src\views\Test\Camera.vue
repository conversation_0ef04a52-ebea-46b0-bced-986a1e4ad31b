<template>
  <div class="camera-container">
    <div class="camera-header">
      <h2>摄像头监控</h2>
      <div class="camera-controls">
        <el-select
          v-model="selectedCamera"
          placeholder="请选择摄像头"
          @change="handleCameraChange"
          :loading="isLoading"
          style="width: 200px;"
        >
          <el-option
            v-for="item in cameraOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" @click="connectCamera" :disabled="!selectedCamera || isConnected">
          连接
        </el-button>
        <el-button type="success" @click="takePhoto" :disabled="!isConnected">
          拍照
        </el-button>
        <el-button type="danger" @click="disconnectCamera" :disabled="!isConnected">
          断开
        </el-button>
        <el-button @click="fetchCameras" :loading="isLoading" icon="Refresh" circle></el-button>
      </div>
    </div>

    <div class="camera-view">
      <div v-if="isConnecting" class="camera-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>{{ connectingMessage }}</span>
      </div>
      <div v-else-if="!isConnected" class="camera-placeholder">
        <el-empty description="请选择并连接摄像头" />
      </div>
      <div v-else class="camera-stream">
        <img v-if="imageUrl" :src="imageUrl" alt="摄像头画面" :width="cameraWidth" :height="cameraHeight" />
        <canvas ref="videoCanvas" :width="cameraWidth" :height="cameraHeight"></canvas>
      </div>

      <div class="camera-status">
        <el-tag :type="statusType">{{ statusMessage }}</el-tag>
        <span v-if="isConnected" class="camera-info">
          已连接: {{ selectedCameraName }} | 分辨率: {{ resolution }} | 帧率: {{ frameRate }}fps
        </span>
      </div>

      <!-- 检测结果展示区域 -->
      <div v-if="selectedCamera === 'ai-detection' " class="detection-results">
        <h3>检测到的物品：</h3>
        <ul class="detection-list">
          <li v-for="(item, index) in detectionResults" :key="index" class="detection-item">
            <div class="detection-name">{{ item.name }}</div>
            <div class="detection-confidence">置信度: {{ (item.confidence * 100).toFixed(1) }}%</div>
          </li>
        </ul>
      </div>

      <!-- 故障排除提示 -->
      <div v-if="showTroubleshootingTips" class="troubleshooting-tips">
        <h3>连接故障排除：</h3>
        <ul>
          <li>确保Python服务器已在***********:8000运行</li>
          <li>确保WebSocket路径正确配置为 /ws/detect</li>
          <li>检查CORS设置是否允许前端域名</li>
          <li>检查网络连接和防火墙设置</li>
          <li>检查控制台是否有具体错误信息</li>
        </ul>
        <div class="troubleshooting-actions">
          <el-button size="small" type="primary" @click="testWebSocketServer">测试WebSocket服务器</el-button>
          <el-button size="small" type="info" @click="showTroubleshootingTips = false">关闭提示</el-button>
        </div>

        <!-- WebSocket服务器测试结果 -->
        <div v-if="wsTestResults.length > 0" class="test-results">
          <h4>WebSocket测试结果：</h4>
          <ul>
            <li v-for="(result, index) in wsTestResults" :key="index"
                :class="{'success': result.success, 'error': !result.success}">
              {{ result.url }}: {{ result.message }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import axios from 'axios'
import envConfig, { WEBSOCKET_URLS } from '@/config/env'

// 配置axios基础URL，使用环境配置
const apiClient = axios.create({
  baseURL: envConfig.mainApiProxyTarget,
  timeout: envConfig.apiTimeout
})

// 定义摄像头对象类型
interface CameraOption {
  id: string
  name: string
  resolution: {
    width: number
    height: number
  }
  frameRate: number
  status: string
  open: boolean
}

// 摄像头选项数据
const cameraOptions = ref<{ label: string; value: string }[]>([])
const isLoading = ref(false)

// 状态变量
const selectedCamera = ref('')
const isConnected = ref(false)
const isConnecting = ref(false)
const statusMessage = ref('未连接')
const connectingMessage = ref('正在连接摄像头...')
const imageUrl = ref('')
const resolution = ref('640x480')
const frameRate = ref(24)
const videoCanvas = ref<HTMLCanvasElement | null>(null)
const websocket = ref<WebSocket | null>(null)
const showTroubleshootingTips = ref(false)
const connectionAttempts = ref(0)
const wsTestResults = ref<Array<{url: string, success: boolean, message: string}>>([])

// 添加摄像头动态尺寸变量
const cameraWidth = ref(640)
const cameraHeight = ref(480)

// 添加检测结果变量
const detectionResults = ref<Array<{name: string, confidence: number}>>([])

// 计算属性
const selectedCameraName = computed(() => {
  if (selectedCamera.value === 'ai-detection') {
    return 'AI监测物品'
  }
  const selected = cameraOptions.value.find(item => item.value === selectedCamera.value)
  return selected ? selected.label : ''
})

const statusType = computed(() => {
  if (isConnected.value) return 'success'
  if (isConnecting.value) return 'warning'
  return 'info'
})

// 获取所有摄像头列表
const fetchCameras = async () => {
  isLoading.value = true
  try {
    const response = await apiClient.get('/camera/getAllCameras')
    if (response.data.code === 200 && response.data.data) {
      const cameras: CameraOption[] = response.data.data
      cameraOptions.value = cameras.map(camera => ({
        label: camera.name,
        value: camera.id
      }))

      // 添加AI监测物品选项
      cameraOptions.value.push({
        label: "AI监测物品",
        value: "ai-detection"
      })

      if (cameras.length === 0) {
        ElMessage.warning('未检测到可用摄像头')
      }
    } else {
      ElMessage.error('获取摄像头列表失败')
    }
  } catch (error) {
    console.error('获取摄像头列表失败:', error)
    ElMessage.error('获取摄像头列表失败，请检查网络连接')
  } finally {
    isLoading.value = false
  }
}

// 方法
const handleCameraChange = () => {
  if (isConnected.value) {
    disconnectCamera()
  }
}

const takePhoto = () => {
  // 拍照功能 - 暂未实现
  ElMessage.success('拍照功能待实现')
}

// 添加AI监测物品处理函数
const handleAIDetection = () => {
  isConnecting.value = true
  statusMessage.value = '正在连接AI监测服务...'

  // 设置摄像头尺寸
  cameraWidth.value = 640
  cameraHeight.value = 480
  resolution.value = '640x480'
  frameRate.value = 24

  // 先加载视频流
  loadVideoStream()

  // 尝试连接WebSocket
  tryConnectWebSocket()
}

// 尝试不同的WebSocket连接方式
const tryConnectWebSocket = () => {
  // 尝试的WebSocket URL列表，优先使用非加密的ws协议
  const aiServerHost = envConfig.aiApiProxyTarget.replace(/^https?:\/\//, '')
  const wsUrls = [
    `ws://${aiServerHost}/ws/detect`,
    `ws://${aiServerHost}/detect`,
    `ws://${aiServerHost}/api/ws/detect`,
    `ws://${aiServerHost}/api/detect`
  ]

  let currentUrlIndex = 0
  connectionAttempts.value = 0

  const tryNextUrl = () => {
    if (currentUrlIndex >= wsUrls.length) {
      ElMessage.error('所有WebSocket连接尝试均失败')
      connectingMessage.value = '连接失败，请检查服务器设置'
      // 显示故障排除提示
      showTroubleshootingTips.value = true

      // 如果视频流已连接，保持视频流连接
      // if (imageUrl.value && imageUrl.value.includes('***********:8000/camera/stream')) {
      //   isConnected.value = true
      //   isConnecting.value = false
      //   statusMessage.value = '仅视频流已连接，AI检测未连接'
      //   ElMessage.warning('已连接视频流，但AI检测服务连接失败')
      // } else {
      //   resetConnection()
      // }
      // return

      if (imageUrl.value && imageUrl.value.includes('***********:5000/video')) {
        isConnected.value = true
        isConnecting.value = false
        statusMessage.value = '仅视频流已连接，AI检测未连接'
        ElMessage.warning('已连接视频流，但AI检测服务连接失败')
      } else {
        resetConnection()
      }
      return
    }

    connectionAttempts.value++
    const wsUrl = wsUrls[currentUrlIndex]
    console.log(`尝试连接WebSocket (${currentUrlIndex + 1}/${wsUrls.length}):`, wsUrl)
    connectingMessage.value = `正在连接AI检测服务 (${currentUrlIndex + 1}/${wsUrls.length})...`

    try {
      websocket.value = new WebSocket(wsUrl)

      // 添加连接超时处理
      const connectionTimeout = setTimeout(() => {
        if (websocket.value && websocket.value.readyState !== WebSocket.OPEN) {
          console.error(`WebSocket连接超时: ${wsUrl}`)

          // 关闭当前连接
          if (websocket.value) {
            websocket.value.close()
            websocket.value = null
          }

          // 尝试下一个URL
          currentUrlIndex++
          tryNextUrl()
        }
      }, 3000) // 3秒超时

      websocket.value.onopen = () => {
        clearTimeout(connectionTimeout)
        console.log('AI检测WebSocket已连接:', wsUrl)

        isConnected.value = true
        isConnecting.value = false
        statusMessage.value = 'AI监测已连接'
        ElMessage.success('AI监测服务已连接')
        showTroubleshootingTips.value = false
      }

      websocket.value.onmessage = (event) => {
        try {
          // 解析接收到的检测框数据
          const detectionData = JSON.parse(event.data)
          console.log('收到检测数据:', detectionData.boxes.length, '个目标')
          // 在画面上绘制检测框
          drawDetectionBoxes(detectionData)
        } catch (e) {
          console.error('无法解析检测数据:', e)
        }
      }

      websocket.value.onerror = (error) => {
        clearTimeout(connectionTimeout)
        // 显示更详细的WebSocket错误信息
        console.error(`WebSocket连接错误 (${wsUrl}):`, error)
        console.log('WebSocket状态:', websocket.value ? websocket.value.readyState : 'undefined')

        // 尝试记录WebSocket对象的所有状态
        if (websocket.value) {
          console.log('WebSocket详细信息:', {
            readyState: websocket.value.readyState,
            url: websocket.value.url,
            protocol: websocket.value.protocol,
            extensions: websocket.value.extensions,
            bufferedAmount: websocket.value.bufferedAmount
          })
        }

        // 关闭当前连接
        if (websocket.value) {
          websocket.value.close()
          websocket.value = null
        }

        // 尝试下一个URL
        currentUrlIndex++
        tryNextUrl()
      }

      websocket.value.onclose = (event) => {
        clearTimeout(connectionTimeout)
        // 显示更详细的关闭原因和代码
        console.log('AI检测WebSocket已关闭:', {
          code: event.code,
          reason: event.reason || '无关闭原因',
          wasClean: event.wasClean ? '正常关闭' : '异常关闭',
          timestamp: new Date().toISOString()
        })

        if (isConnected.value) {
          const closeReasonText = event.code === 1006 ? '（连接异常关闭）' :
                                 event.code === 1000 ? '（正常关闭）' :
                                 `（代码: ${event.code}）`;
          ElMessage.info(`AI监测服务已断开 ${closeReasonText}`)
          resetConnection()
        }
      }
    } catch (error) {
      console.error(`创建WebSocket连接失败 (${wsUrl}):`, error)
      // 尝试下一个URL
      currentUrlIndex++
      setTimeout(tryNextUrl, 500)
    }
  }

  // 开始尝试第一个URL
  tryNextUrl()
}

// 加载网络摄像头视频流
const loadVideoStream = () => {
  // 使用环境配置的视频流地址
  const videoUrl = `http://${envConfig.videoStreamHost}:${envConfig.videoStreamPort}/video`
  console.log('尝试加载视频流:', videoUrl)

  // 创建图像元素进行预加载检查
  const testImg = new Image()
  testImg.onload = () => {
    // 图像可以加载，设置URL
    imageUrl.value = videoUrl
    prepareCanvas()
    ElMessage.success('视频流连接成功')
  }

  testImg.onerror = (error) => {
    // 图像无法加载，显示错误
    console.error('视频流加载失败:', error)
    ElMessage.error('无法连接到视频流，请检查网络或服务器状态')
    resetConnection()
  }

  // 开始尝试加载图像
  testImg.src = videoUrl
}

// 准备Canvas用于绘制检测框
const prepareCanvas = () => {
  // 延迟执行以确保图像已加载
  setTimeout(() => {
    const canvas = videoCanvas.value
    if (canvas) {
      // 显示Canvas并设置在图像上方
      canvas.style.display = 'block'
      canvas.style.position = 'absolute'
      canvas.style.top = '0'
      canvas.style.left = '0'

      // 确保Canvas与图像尺寸一致
      canvas.width = cameraWidth.value
      canvas.height = cameraHeight.value

      // 绘制初始透明层
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // 绘制加载提示
        ctx.font = '16px Arial'
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'
        ctx.fillText('等待检测数据...', 20, 30)
      }
    }
  }, 500)
}

// 在画面上绘制检测框
const drawDetectionBoxes = (detectionData: any) => {
  const canvas = videoCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清除上一帧的绘制内容
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 确保有检测框数据
  if (!detectionData.boxes || !Array.isArray(detectionData.boxes)) {
    // 如果没有检测到物品，显示提示
    ctx.font = '16px Arial'
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'
    ctx.fillText('未检测到物品', 20, 30)

    // 清空检测结果列表
    detectionResults.value = []
    return
  }

  // 设置画布尺寸，如果检测数据中包含宽高信息
  if (detectionData.width && detectionData.height) {
    // 这里要考虑视频流的实际尺寸与显示尺寸的比例
    const scaleX = canvas.width / detectionData.width
    const scaleY = canvas.height / detectionData.height

    // 更新检测结果列表，并按置信度排序
    detectionResults.value = detectionData.boxes
      .map((box: any) => ({
        name: box.name,
        confidence: box.confidence || 0
      }))
      .sort((a: any, b: any) => b.confidence - a.confidence)

    // 遍历所有检测框
    detectionData.boxes.forEach((box: any) => {
      if (!box.bbox || box.bbox.length !== 4) return

      const [x1, y1, x2, y2] = box.bbox
      const boxWidth = (x2 - x1) * scaleX
      const boxHeight = (y2 - y1) * scaleY

      // 根据置信度设置颜色
      const confidence = box.confidence || 0
      const hue = (1 - confidence) * 60 // 60度是黄色，0度是红色
      ctx.strokeStyle = `hsl(${hue}, 100%, 50%)`
      ctx.lineWidth = 2

      // 绘制矩形框
      ctx.beginPath()
      ctx.rect(x1 * scaleX, y1 * scaleY, boxWidth, boxHeight)
      ctx.stroke()

      // 绘制类别标签和置信度
      if (box.name) {
        ctx.font = '14px Arial'
        ctx.fillStyle = `hsl(${hue}, 100%, 50%)`
        const label = `${box.name} ${(confidence * 100).toFixed(1)}%`
        const textWidth = ctx.measureText(label).width

        // 绘制标签背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
        ctx.fillRect(x1 * scaleX, y1 * scaleY - 20, textWidth + 10, 20)

        // 绘制文本
        ctx.fillStyle = 'white'
        ctx.fillText(label, x1 * scaleX + 5, y1 * scaleY - 5)
      }
    })
  }
}

const connectCamera = () => {
  if (!selectedCamera.value) {
    ElMessage.warning('请先选择摄像头')
    return
  }

  // 处理AI监测物品选项
  if (selectedCamera.value === 'ai-detection') {
    handleAIDetection()
    return
  }

  isConnecting.value = true
  statusMessage.value = '正在连接...'

  // 修改WebSocket URL以匹配后端的ServerEndpoint路径，使用配置化地址
  const wsUrl = WEBSOCKET_URLS.CAMERA(selectedCamera.value)

  try {
    websocket.value = new WebSocket(wsUrl)

    websocket.value.onopen = () => {
      isConnected.value = true
      isConnecting.value = false
      statusMessage.value = '已连接'
      ElMessage.success('摄像头连接成功')

      // 查找选中的摄像头信息，更新分辨率和帧率
      const selectedCameraObj = cameraOptions.value.find(item => item.value === selectedCamera.value)
      if (selectedCameraObj) {
        // 获取摄像头信息
        apiClient.get(`/camera/getCameraInfo/${selectedCamera.value}`)
          .then(response => {
            if (response.data.code === 200 && response.data.data) {
              const cameraInfo = response.data.data
              if (cameraInfo.resolution) {
                // 更新分辨率
                const width = cameraInfo.resolution.width
                const height = cameraInfo.resolution.height
                cameraWidth.value = width
                cameraHeight.value = height
                resolution.value = `${width}x${height}`
              }
              if (cameraInfo.frameRate) {
                frameRate.value = cameraInfo.frameRate
              }
            }
          })
          .catch(error => {
            console.error('获取摄像头信息失败:', error)
          })
      }
    }

    websocket.value.onmessage = (event) => {
      // 处理接收到的摄像头数据
      // 如果接收的是二进制数据（如MJPEG流）
      if (event.data instanceof Blob) {
        const url = URL.createObjectURL(event.data)
        imageUrl.value = url
      }
      // 如果接收的是Base64编码的图像（没有前缀）
      else if (typeof event.data === 'string' && !event.data.startsWith('data:')) {
        imageUrl.value = `data:image/jpeg;base64,${event.data}`
      }
      // 如果接收的是已经带前缀的Base64图像
      else if (typeof event.data === 'string' && event.data.startsWith('data:image')) {
        imageUrl.value = event.data
      }
      // 如果接收的是JSON数据（包含图像URL和其他信息）
      else {
        try {
          const data = JSON.parse(event.data)
          if (data.imageData) {
            imageUrl.value = data.imageData
          }
          if (data.resolution) {
            // 如果服务器发送了分辨率信息，更新分辨率
            if (typeof data.resolution === 'string') {
              resolution.value = data.resolution
              const [width, height] = data.resolution.split('x').map(Number)
              if (!isNaN(width) && !isNaN(height)) {
                cameraWidth.value = width
                cameraHeight.value = height
              }
            } else if (data.resolution.width && data.resolution.height) {
              const width = data.resolution.width
              const height = data.resolution.height
              cameraWidth.value = width
              cameraHeight.value = height
              resolution.value = `${width}x${height}`
            }
          }
          if (data.frameRate) {
            frameRate.value = data.frameRate
          }
        } catch (e) {
          console.error('无法解析摄像头数据', e)
        }
      }
    }

    websocket.value.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      ElMessage.error('连接错误，请检查网络或服务器状态')
      resetConnection()
    }

    websocket.value.onclose = () => {
      if (isConnected.value) {
        ElMessage.info('摄像头连接已断开')
      }
      resetConnection()
    }
  } catch (error) {
    console.error('创建WebSocket连接失败:', error)
    ElMessage.error('无法连接到摄像头服务')
    resetConnection()
  }
}

const disconnectCamera = () => {
  if (websocket.value) {
    websocket.value.close()
  }
  resetConnection()
}

const resetConnection = () => {
  isConnected.value = false
  isConnecting.value = false
  statusMessage.value = '未连接'
  imageUrl.value = ''

  // 重置摄像头尺寸
  cameraWidth.value = 640
  cameraHeight.value = 480
  resolution.value = '640x480'

  // 清空检测结果
  detectionResults.value = []

  if (websocket.value) {
    websocket.value.onopen = null
    websocket.value.onmessage = null
    websocket.value.onerror = null
    websocket.value.onclose = null
    websocket.value = null
  }
}

// WebSocket服务器测试功能
const testWebSocketServer = () => {
  wsTestResults.value = []

  // 要测试的URL列表，使用环境配置
  const aiServerHost = envConfig.aiApiProxyTarget.replace(/^https?:\/\//, '')
  const testUrls = [
    `ws://${aiServerHost}/ws/detect`,
    `ws://${aiServerHost}/detect`,
    `ws://${aiServerHost}/api/ws/detect`,
    WEBSOCKET_URLS.CAMERA('test'),
    WEBSOCKET_URLS.ROBOT_LOCATION,
    WEBSOCKET_URLS.JIZHAN
  ]

  // 测试每个URL
  testUrls.forEach(url => {
    try {
      const testWs = new WebSocket(url)
      const timeoutId = setTimeout(() => {
        if (testWs.readyState !== WebSocket.OPEN) {
          wsTestResults.value.push({
            url,
            success: false,
            message: '连接超时'
          })
          testWs.close()
        }
      }, 3000)

      testWs.onopen = () => {
        clearTimeout(timeoutId)
        wsTestResults.value.push({
          url,
          success: true,
          message: '连接成功'
        })
        // 成功连接后立即关闭
        testWs.close()
      }

      testWs.onerror = () => {
        clearTimeout(timeoutId)
        wsTestResults.value.push({
          url,
          success: false,
          message: '连接失败'
        })
      }
    } catch (error) {
      wsTestResults.value.push({
        url,
        success: false,
        message: `错误: ${error}`
      })
    }
  })

  // 同时测试API服务器是否可访问
  axios.get('http://***********:8000/health')
    .then(response => {
      wsTestResults.value.push({
        url: 'HTTP: http://***********:8000/health',
        success: true,
        message: `API服务器可访问: ${JSON.stringify(response.data)}`
      })
    })
    .catch(error => {
      wsTestResults.value.push({
        url: 'HTTP: http://***********:8000/health',
        success: false,
        message: `API服务器不可访问: ${error.message}`
      })
    })
}

// 生命周期钩子
onMounted(() => {
  // 组件加载时自动获取摄像头列表
  fetchCameras()
})

onUnmounted(() => {
  // 在组件销毁前断开连接
  disconnectCamera()
})
</script>

<style scoped>
.camera-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.camera-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.camera-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.camera-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f7fa;
}

.camera-stream {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  position: relative;
  min-height: 240px;
  overflow: hidden;
}

.camera-stream img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
  z-index: 1;
}

.camera-stream canvas {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 2;
  pointer-events: none; /* 允许点击穿透到下方的图像 */
}

.camera-placeholder, .camera-loading {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 240px;
  color: #909399;
  gap: 10px;
}

.camera-status {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f7fa;
  border-top: 1px solid #ebeef5;
}

.camera-info {
  font-size: 14px;
  color: #606266;
}

/* 检测结果样式 */
.detection-results {
  padding: 10px;
  background-color: #f5f7fa;
  border-top: 1px solid #ebeef5;
  max-height: 150px;
  overflow-y: auto;
}

.detection-results h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #303133;
}

.detection-list {
  margin: 0;
  padding: 0;
  height: 200px;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.detection-item {
  padding: 5px 10px;
  background-color: #ecf5ff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  min-width: 100px;
}

.detection-name {
  font-weight: bold;
  color: #409eff;
}

.detection-confidence {
  font-size: 12px;
  color: #606266;
}

/* 故障排除提示样式 */
.troubleshooting-tips {
  padding: 15px;
  background-color: #fcf6f0;
  border-top: 1px solid #f3e2cf;
}

.troubleshooting-tips h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #e6a23c;
}

.troubleshooting-tips ul {
  margin: 0 0 10px 20px;
  padding: 0;
}

.troubleshooting-tips li {
  margin-bottom: 5px;
  color: #666;
}

.troubleshooting-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

/* 测试结果样式 */
.test-results {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.test-results h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.test-results ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.test-results li {
  padding: 5px;
  margin-bottom: 5px;
  border-radius: 3px;
}

.test-results li.success {
  background-color: #f0f9eb;
  color: #67c23a;
}

.test-results li.error {
  background-color: #fef0f0;
  color: #f56c6c;
}
</style>
