<!-- 
  SmartRecommendation.vue
  智能用药推荐系统模块
  基于虫害种类、作物类型和环境条件智能推荐农药使用方案
-->
<template>
  <div class="smart-recommendation">
    <!-- 页面标题 -->
    <PageHeader
      title="智能用药推荐系统"
      description="基于虫害种类、作物类型和环境条件智能推荐农药使用方案"
      icon="DataAnalysis"
    >
      <template #actions>
        <div class="status-indicator-wrapper">
          <StatusIndicator type="success" label="AI分析系统在线" size="large" />
        </div>
      </template>
    </PageHeader>
    
    <!-- 筛选条件面板 -->
    <DataPanel title="推荐条件设置" dark>
      <template #actions>
        <el-tag type="info" effect="dark" size="small">根据条件生成精准推荐方案</el-tag>
      </template>
      <div class="filter-form">
        <el-form label-position="top" :model="filterData" class="filter-form-inner">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="虫害类型">
                <el-select 
                  v-model="filterData.pestType" 
                  placeholder="请选择虫害类型" 
                  class="full-width"
                  popper-class="dark-select-dropdown"
                >
                  <el-option
                    v-for="pest in pestOptions"
                    :key="pest.value"
                    :value="pest.value"
                    :label="pest.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="作物类型">
                <el-select 
                  v-model="filterData.cropType" 
                  placeholder="请选择作物类型" 
                  class="full-width"
                  popper-class="dark-select-dropdown"
                >
                  <el-option
                    v-for="crop in cropOptions"
                    :key="crop.value"
                    :value="crop.value"
                    :label="crop.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="环境条件">
                <el-select
                  v-model="filterData.environmentalFactors"
                  multiple
                  placeholder="选择环境条件(可多选)"
                  class="full-width"
                  popper-class="dark-select-dropdown"
                >
                  <el-option
                    v-for="env in environmentOptions"
                    :key="env.value"
                    :value="env.value"
                    :label="env.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="8">
              <el-form-item label="当前虫害严重度">
                <el-slider
                  v-model="filterData.infestationLevel"
                  :marks="severityMarks"
                  :min="1"
                  :max="5"
                  :show-tooltip="true"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="预算限制">
                <div class="budget-input">
                  <el-input-number
                    v-model="filterData.budgetLimit"
                    :min="0"
                    :max="10000"
                    :controls-position="'right'"
                    class="full-width"
                  />
                  <span class="unit">元/亩</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="偏好选项">
                <div class="preference-options">
                  <el-checkbox-group v-model="filterData.preferences">
                    <div class="preference-option-item">
                      <el-checkbox label="eco">
                        <div class="preference-option">
                          <el-icon><Briefcase /></el-icon>
                          <span>优先环保</span>
                        </div>
                      </el-checkbox>
                    </div>
                    <div class="preference-option-item">
                      <el-checkbox label="cost">
                        <div class="preference-option">
                          <el-icon><Money /></el-icon>
                          <span>优先成本</span>
                        </div>
                      </el-checkbox>
                    </div>
                    <div class="preference-option-item">
                      <el-checkbox label="effect">
                        <div class="preference-option">
                          <el-icon><Aim /></el-icon>
                          <span>优先效果</span>
                        </div>
                      </el-checkbox>
                    </div>
                  </el-checkbox-group>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        
        <div class="filter-actions">
          <el-button type="primary" @click="generateRecommendations">
            <el-icon><Search /></el-icon>
            生成推荐方案
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
        </div>
      </div>
    </DataPanel>
    
    <!-- 推荐结果区域 -->
    <div class="recommendation-results">
      <DataPanel v-if="showResults" title="推荐方案结果" dark>
        <template #actions>
          <el-tag type="success" effect="dark" size="small">已生成 {{ recommendationPlans.length }} 条推荐</el-tag>
        </template>
        <el-tabs v-model="activeTabKey" class="dark-tabs">
          <!-- 推荐方案标签页 -->
          <el-tab-pane label="推荐方案" name="topRecommendations">
            <div class="recommendation-cards">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="24" :md="8" v-for="(plan, index) in recommendationPlans" :key="index">
                  <RecommendationCard
                    :plan="plan"
                    :is-top="index === 0"
                    :tag-text="getTagText(index)"
                    :tag-type="getTagType(index)"
                  >
                    <template #actions>
                      <el-button type="primary" size="small" @click="addToPlan(plan)">
                        <el-icon><Plus /></el-icon>
                        添加到方案
                      </el-button>
                      <el-button size="small" @click="viewDetails(plan)">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-button>
                    </template>
                  </RecommendationCard>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          
          <!-- 替代防治方法标签页 -->
          <el-tab-pane label="替代防治方法" name="alternativeMethods">
            <el-table 
              :data="alternativeMethods" 
              style="width: 100%" 
              :header-cell-style="{ background: 'rgba(31, 41, 55, 0.7)', color: '#e5e7eb' }"
              :row-style="{ background: 'rgba(31, 41, 55, 0.3)' }"
              :cell-style="{ color: '#e5e7eb' }"
            >
              <el-table-column label="防治方法" width="180">
                <template #default="scope">
                  <div class="method-name">
                    <el-avatar :icon="getMethodIcon(scope.row.type)" />
                    <span>{{ scope.row.title }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" width="220" />
              <el-table-column prop="content" label="详细内容" />
              <el-table-column label="指标" width="250">
                <template #default="scope">
                  <div class="method-metrics">
                    <div class="metric-item">
                      <strong>成本:</strong> 
                      <span class="metric-value">{{ scope.row.cost }}元/亩</span>
                    </div>
                    <div class="metric-item">
                      <strong>难度:</strong> 
                      <el-progress 
                        :percentage="scope.row.difficulty * 10" 
                        :stroke-width="8"
                        :show-text="false"
                        class="small-progress"
                      />
                      <span class="metric-value">{{ scope.row.difficulty }}/10</span>
                    </div>
                    <div class="metric-item">
                      <strong>环保:</strong> 
                      <el-progress 
                        :percentage="scope.row.eco_friendly * 10" 
                        :stroke-width="8"
                        :show-text="false"
                        :color="'#67C23A'"
                        class="small-progress"
                      />
                      <span class="metric-value">{{ scope.row.eco_friendly }}/10</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <!-- 综合防治方案标签页 -->
          <el-tab-pane label="综合防治方案" name="integratedPlan">
            <div class="integrated-plan-container">
              <el-timeline>
                <el-timeline-item
                  v-for="(step, index) in integratedPlan"
                  :key="index"
                  :timestamp="step.timing"
                  :color="step.color"
                  :hollow="true"
                >
                  <div class="timeline-content">
                    <h4 class="timeline-title">{{ step.title }}</h4>
                    <p class="timeline-description">{{ step.description }}</p>
                    <div class="timeline-details">
                      <el-tag type="info" effect="dark">{{ step.method }}</el-tag>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
              <div class="integrated-plan-actions">
                <el-button type="primary">
                  <el-icon><Download /></el-icon>
                  导出防治方案
                </el-button>
                <el-button>
                  <el-icon><Star /></el-icon>
                  保存至我的方案
                </el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </DataPanel>
      
      <!-- 无结果显示 -->
      <DataPanel v-if="!showResults" title="推荐方案" dark>
        <div class="empty-state">
          <el-empty description="请填写条件并生成推荐方案" :image-size="200">
            <el-button type="primary" @click="generateRecommendations">生成推荐方案</el-button>
          </el-empty>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="AI模型已加载" />
        <StatusIndicator type="normal" label="数据分析就绪" />
        <StatusIndicator type="warning" label="虫害数据库更新中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  DataAnalysis, 
  Refresh, 
  View, 
  Plus, 
  Search, 
  RefreshRight,
  Briefcase,
  Money,
  Aim,
  Download,
  Star,
  Chicken, 
  MapLocation 
} from '@element-plus/icons-vue';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import StatusIndicator from './components/StatusIndicator.vue';
import DataPanel from './components/DataPanel.vue';
import RecommendationCard from './components/RecommendationCard.vue';

// 定义类型
interface PestOption {
  value: string;
  label: string;
}

interface CropOption {
  value: string;
  label: string;
}

interface EnvironmentOption {
  value: string;
  label: string;
}

interface RecommendationPlan {
  title: string;
  product: string;
  dosage: string;
  cost: number;
  timing: string;
  effectivenessRating: number;
  instructions: string;
  environmentalImpact: number;
}

interface AlternativeMethod {
  title: string;
  description: string;
  type: 'biological' | 'physical' | 'cultural';
  content: string;
  image: string;
  tags: string[];
  cost: number;
  difficulty: number;
  eco_friendly: number;
}

interface IntegratedPlanStep {
  title: string;
  description: string;
  timing: string;
  method: string;
  color: string;
}

// 虫害类型选项
const pestOptions = ref<PestOption[]>([
  { value: 'aphid', label: '蚜虫' },
  { value: 'beetle', label: '甲虫' },
  { value: 'moth', label: '蛾类' },
  { value: 'mite', label: '螨虫' },
  { value: 'nematode', label: '线虫' },
]);

// 作物类型选项
const cropOptions = ref<CropOption[]>([
  { value: 'rice', label: '水稻' },
  { value: 'wheat', label: '小麦' },
  { value: 'corn', label: '玉米' },
  { value: 'cotton', label: '棉花' },
  { value: 'vegetable', label: '蔬菜' },
  { value: 'fruit', label: '果树' },
]);

// 环境条件选项
const environmentOptions = ref<EnvironmentOption[]>([
  { value: 'high_temp', label: '高温' },
  { value: 'high_humid', label: '高湿' },
  { value: 'low_temp', label: '低温' },
  { value: 'drought', label: '干旱' },
  { value: 'rainy', label: '多雨' },
  { value: 'windy', label: '多风' },
]);

// 虫害严重程度标记
const severityMarks = {
  1: '轻微',
  2: '较轻',
  3: '中等',
  4: '严重',
  5: '极重',
};

// 筛选条件数据
const filterData = reactive({
  pestType: '',
  cropType: '',
  environmentalFactors: [] as string[],
  infestationLevel: 3,
  budgetLimit: 500,
  preferences: ['effect'] as string[]
});

// UI状态
const activeTabKey = ref('topRecommendations');
const showResults = ref(false);
const lastUpdateTime = ref(new Date());

// 推荐结果数据
const recommendationPlans = ref<RecommendationPlan[]>([]);
const alternativeMethods = ref<AlternativeMethod[]>([]);
const integratedPlan = ref<IntegratedPlanStep[]>([]);

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 生成推荐方案
const generateRecommendations = () => {
  // 验证必填项
  if (!filterData.pestType || !filterData.cropType) {
    ElMessage.warning('请至少选择虫害类型和作物类型');
    return;
  }
  
  // 在实际应用中，这里会调用API获取推荐
  // 现在使用模拟数据
  generateMockData();
  showResults.value = true;
  lastUpdateTime.value = new Date();
  ElMessage.success('已生成推荐方案');
};

// 重置筛选条件
const resetFilters = () => {
  filterData.pestType = '';
  filterData.cropType = '';
  filterData.environmentalFactors = [];
  filterData.infestationLevel = 3;
  filterData.budgetLimit = 500;
  filterData.preferences = ['effect'];
  showResults.value = false;
  ElMessage.info('已重置所有条件');
};

// 刷新数据
const refreshData = () => {
  if (showResults.value) {
    generateMockData();
    lastUpdateTime.value = new Date();
    ElMessage.success('数据已更新');
  } else {
    ElMessage.info('请先生成推荐方案');
  }
};

// 获取标签文本
const getTagText = (index: number): string => {
  if (index === 0) return '最佳推荐';
  if (index === 1) return '经济之选';
  if (index === 2) return '生态之选';
  return '推荐方案';
};

// 获取标签类型
const getTagType = (index: number): string => {
  if (index === 0) return 'danger';
  if (index === 1) return 'primary';
  if (index === 2) return 'success';
  return 'info';
};

// 获取方法图标
const getMethodIcon = (type: string) => {
  if (type === 'biological') return Chicken;
  if (type === 'physical') return DataAnalysis;
  return MapLocation;
};

// 添加到方案
const addToPlan = (plan: RecommendationPlan) => {
  ElMessage.success(`已添加 ${plan.title} 到防治方案`);
};

// 查看详情
const viewDetails = (plan: RecommendationPlan) => {
  ElMessage.info(`查看 ${plan.title} 详情`);
};

// 生成模拟数据
const generateMockData = () => {
  // 根据用户偏好调整推荐方案顺序
  const plans = [
    {
      title: '高效低残留方案',
      product: '氯虫苯甲酰胺 5% 悬浮剂',
      dosage: '40-60毫升/亩',
      cost: 120,
      timing: '初见虫害时',
      effectivenessRating: 4.5,
      instructions: '喷施时注意均匀覆盖植物表面，避开中午高温时段施药',
      environmentalImpact: 35
    },
    {
      title: '经济实用方案',
      product: '吡虫啉 25% 可湿性粉剂',
      dosage: '10-15克/亩',
      cost: 65,
      timing: '虫口密度增大时',
      effectivenessRating: 3.5,
      instructions: '稀释后均匀喷洒，7-10天一次，连续2-3次',
      environmentalImpact: 65
    },
    {
      title: '生物防治方案',
      product: '苏云金杆菌 BT 可湿性粉剂',
      dosage: '100-150克/亩',
      cost: 95,
      timing: '幼虫期施用',
      effectivenessRating: 3,
      instructions: '早晚喷施效果最佳，雨后需要重新施用',
      environmentalImpact: 15
    }
  ];
  
  // 根据用户偏好调整顺序
  if (filterData.preferences.includes('eco')) {
    plans.sort((a, b) => a.environmentalImpact - b.environmentalImpact);
  } else if (filterData.preferences.includes('cost')) {
    plans.sort((a, b) => a.cost - b.cost);
  } else if (filterData.preferences.includes('effect')) {
    plans.sort((a, b) => b.effectivenessRating - a.effectivenessRating);
  }
  
  recommendationPlans.value = plans;
  
  // 生成替代方法
  alternativeMethods.value = [
    {
      title: '天敌释放',
      description: '释放捕食性天敌控制害虫种群',
      type: 'biological',
      content: '根据田间调查，释放瓢虫、草蛉等天敌，每亩释放量为3000-5000头，视虫情可多次释放。',
      image: 'https://via.placeholder.com/300x180',
      tags: ['生物防治', '零残留', '持续性好'],
      cost: 320,
      difficulty: 7,
      eco_friendly: 10
    },
    {
      title: '色板诱捕',
      description: '利用害虫趋色性进行物理防治',
      type: 'physical',
      content: '黄板对蚜虫、粉虱等有较好的诱集效果，蓝板对蓟马有较好的诱集效果。每亩悬挂40-60张，距离作物顶端20-30厘米。',
      image: 'https://via.placeholder.com/300x180',
      tags: ['物理防治', '简单易行', '无污染'],
      cost: 85,
      difficulty: 3,
      eco_friendly: 9
    },
    {
      title: '轮作倒茬',
      description: '通过改变种植作物打破虫害生活周期',
      type: 'cultural',
      content: '采用不同科属作物进行轮作，可有效降低土传病虫害的发生，通常2-3年一个轮作周期最为有效。',
      image: 'https://via.placeholder.com/300x180',
      tags: ['农业措施', '长效管理', '生态友好'],
      cost: 0,
      difficulty: 5,
      eco_friendly: 10
    }
  ];
  
  // 生成综合防治方案
  integratedPlan.value = [
    {
      title: '监测预警',
      description: '定期田间调查，设置黄板，监测虫情动态，达到预警指标时及时采取措施',
      timing: '全生育期',
      method: '农业措施',
      color: '#87d068'
    },
    {
      title: '前期防控',
      description: '作物幼苗期，释放天敌或使用生物农药，控制害虫初期种群',
      timing: '苗期至生长初期',
      method: '生物防治',
      color: '#2db7f5'
    },
    {
      title: '中期治理',
      description: '害虫种群达到经济阈值时，使用推荐的高效低毒农药进行喷施',
      timing: '生长中期',
      method: '化学防治',
      color: '#f50'
    },
    {
      title: '后期防护',
      description: '作物接近成熟期，使用植物源农药或物理方法，减少农药残留',
      timing: '生长后期',
      method: '综合防治',
      color: '#722ed1'
    }
  ];
};

onMounted(() => {
  // 页面加载时的初始化操作
});
</script>

<style>
/* 全局样式：修复 Element Plus 下拉菜单在深色主题下的样式 */
:deep(.dark-select-dropdown) {
  background-color: #1f2937 !important;
  border-color: #3b4863 !important;
}

:deep(.dark-select-dropdown .el-select-dropdown__item) {
  color: #e5e7eb !important;
}

:deep(.dark-select-dropdown .el-select-dropdown__item.hover),
:deep(.dark-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #374151 !important;
}

:deep(.dark-select-dropdown .el-select-dropdown__item.selected) {
  color: #3b82f6 !important;
  font-weight: bold;
}

:deep(.el-popper.is-dark) {
  background-color: #1f2937 !important;
  border-color: #3b4863 !important;
}
</style>

<style scoped>
.smart-recommendation {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.status-indicator-wrapper {
  display: flex;
  align-items: center;
}

.filter-form {
  display: flex;
  flex-direction: column;
  width: 100%;
  color: #e5e7eb;
}

.filter-form-inner {
  margin-bottom: 20px;
  width: 100%;
}

.filter-form :deep(.el-form-item__label) {
  color: #e5e7eb;
  font-weight: 500;
  padding-bottom: 4px;
}

.filter-form :deep(.el-input__wrapper),
.filter-form :deep(.el-select__wrapper),
.filter-form :deep(.el-input-number__wrapper) {
  background-color: rgba(31, 41, 55, 0.5);
  border-color: #3b4863;
}

.filter-form :deep(.el-input__inner),
.filter-form :deep(.el-select__inner),
.filter-form :deep(.el-input-number__inner) {
  color: #e5e7eb;
}

.filter-form :deep(.el-slider__runway) {
  margin: 16px 0;
}

.filter-form :deep(.el-slider__bar) {
  background-color: #3b82f6;
}

.filter-form :deep(.el-slider__button) {
  border-color: #3b82f6;
}

.filter-form :deep(.el-checkbox__label) {
  color: #e5e7eb;
}

.filter-form :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 10px;
}

.full-width {
  width: 100%;
}

.budget-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit {
  color: #9ca3af;
  font-size: 14px;
}

.preference-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.preference-option-item {
  margin-bottom: 8px;
}

.preference-option {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #e5e7eb;
}

.recommendation-results {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.recommendation-cards {
  margin-top: 16px;
}

.dark-tabs :deep(.el-tabs__item) {
  color: #9ca3af;
}

.dark-tabs :deep(.el-tabs__item.is-active) {
  color: #3b82f6;
}

.dark-tabs :deep(.el-tabs__active-bar) {
  background-color: #3b82f6;
}

.method-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.method-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-value {
  margin-left: 5px;
}

.small-progress {
  width: 100px;
  margin: 0 8px;
}

.integrated-plan-container {
  padding: 16px;
}

.timeline-content {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 16px;
  color: #e5e7eb;
}

.timeline-title {
  margin: 0 0 10px 0;
  color: #3b82f6;
  font-size: 16px;
}

.timeline-description {
  margin: 0 0 12px 0;
  color: #d1d5db;
  font-size: 14px;
  line-height: 1.5;
}

.timeline-details {
  margin-top: 10px;
}

.integrated-plan-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .recommendation-cards el-row {
    display: block;
  }
  
  .recommendation-cards .el-col {
    margin-bottom: 16px;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .preference-options {
    flex-direction: column;
    gap: 8px;
  }
}
</style>

<!--
注意: 此组件需要以下图标:
- @element-plus/icons-vue 中的各种图标
-->