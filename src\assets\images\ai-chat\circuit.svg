<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="circuitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00e676" />
      <stop offset="100%" stop-color="#00c853" />
    </linearGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="1" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- 主电路线 -->
  <path d="M10,50 L30,50 L30,30 L50,30 L50,10" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <path d="M10,70 L40,70 L40,50 L60,50 L60,30 L70,30 L70,10" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <path d="M30,50 L30,90" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <path d="M70,30 L90,30" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <path d="M50,30 L60,30" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  
  <!-- 电路节点 -->
  <circle cx="30" cy="50" r="3" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <circle cx="30" cy="30" r="3" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <circle cx="50" cy="30" r="3" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <circle cx="70" cy="30" r="3" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <circle cx="40" cy="70" r="3" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <circle cx="60" cy="50" r="3" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  
  <!-- 数据点 -->
  <circle cx="30" cy="50" r="1.5" fill="#00e676" filter="url(#glow)" />
  <circle cx="30" cy="30" r="1.5" fill="#00e676" filter="url(#glow)" />
  <circle cx="50" cy="30" r="1.5" fill="#00e676" filter="url(#glow)" />
  <circle cx="70" cy="30" r="1.5" fill="#00e676" filter="url(#glow)" />
  <circle cx="40" cy="70" r="1.5" fill="#00e676" filter="url(#glow)" />
  <circle cx="60" cy="50" r="1.5" fill="#00e676" filter="url(#glow)" />
  
  <!-- 芯片/处理器 -->
  <rect x="65" y="60" width="20" height="20" rx="2" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <line x1="65" y1="65" x2="85" y2="65" stroke="url(#circuitGradient)" stroke-width="0.8" />
  <line x1="65" y1="70" x2="85" y2="70" stroke="url(#circuitGradient)" stroke-width="0.8" />
  <line x1="65" y1="75" x2="85" y2="75" stroke="url(#circuitGradient)" stroke-width="0.8" />
  <line x1="70" y1="60" x2="70" y2="80" stroke="url(#circuitGradient)" stroke-width="0.8" />
  <line x1="75" y1="60" x2="75" y2="80" stroke="url(#circuitGradient)" stroke-width="0.8" />
  <line x1="80" y1="60" x2="80" y2="80" stroke="url(#circuitGradient)" stroke-width="0.8" />
  
  <!-- 连接芯片的线 -->
  <path d="M60,50 L65,70" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  <path d="M40,70 L65,75" fill="none" stroke="url(#circuitGradient)" stroke-width="1.5" />
  
  <!-- 装饰线 -->
  <path d="M10,10 L20,10 L20,20" fill="none" stroke="url(#circuitGradient)" stroke-width="0.8" stroke-dasharray="2,1" />
  <path d="M90,90 L80,90 L80,80" fill="none" stroke="url(#circuitGradient)" stroke-width="0.8" stroke-dasharray="2,1" />
  <path d="M10,90 L20,90 L20,80" fill="none" stroke="url(#circuitGradient)" stroke-width="0.8" stroke-dasharray="2,1" />
  <path d="M90,10 L80,10 L80,20" fill="none" stroke="url(#circuitGradient)" stroke-width="0.8" stroke-dasharray="2,1" />
</svg> 