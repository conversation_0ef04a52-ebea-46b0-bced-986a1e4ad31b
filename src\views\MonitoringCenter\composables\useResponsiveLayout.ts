/**
 * 响应式布局管理 Composable Hook
 * 提供屏幕尺寸监听、设备类型检测和布局模式管理
 */

import { ref, computed, onMounted, onUnmounted } from 'vue';

// 屏幕尺寸类型
export type ScreenSize = 'mobile' | 'tablet' | 'desktop' | 'large';

// 布局模式类型
export type LayoutMode = 'compact' | 'normal' | 'expanded';

// 配置接口
export interface ResponsiveLayoutConfig {
  mobileBreakpoint?: number;
  tabletBreakpoint?: number;
  desktopBreakpoint?: number;
  debounceDelay?: number;
}

// 默认配置
const DEFAULT_CONFIG: Required<ResponsiveLayoutConfig> = {
  mobileBreakpoint: 768,
  tabletBreakpoint: 1024,
  desktopBreakpoint: 1440,
  debounceDelay: 150
};

/**
 * 响应式布局管理Hook
 */
export function useResponsiveLayout(config: ResponsiveLayoutConfig = {}) {
  // 合并配置
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // 响应式状态
  const windowWidth = ref(0);
  const windowHeight = ref(0);
  const orientation = ref<'portrait' | 'landscape'>('landscape');
  
  // 计算屏幕尺寸类型
  const screenSize = computed<ScreenSize>(() => {
    const width = windowWidth.value;
    
    if (width < finalConfig.mobileBreakpoint) {
      return 'mobile';
    } else if (width < finalConfig.tabletBreakpoint) {
      return 'tablet';
    } else if (width < finalConfig.desktopBreakpoint) {
      return 'desktop';
    } else {
      return 'large';
    }
  });
  
  // 设备类型检测
  const isMobile = computed(() => screenSize.value === 'mobile');
  const isTablet = computed(() => screenSize.value === 'tablet');
  const isDesktop = computed(() => screenSize.value === 'desktop' || screenSize.value === 'large');
  const isLargeScreen = computed(() => screenSize.value === 'large');
  
  // 布局模式计算
  const layoutMode = computed<LayoutMode>(() => {
    if (isMobile.value) {
      return 'compact';
    } else if (isTablet.value) {
      return 'normal';
    } else {
      return 'expanded';
    }
  });
  
  // 屏幕方向检测
  const isPortrait = computed(() => orientation.value === 'portrait');
  const isLandscape = computed(() => orientation.value === 'landscape');
  
  // 触摸设备检测
  const isTouchDevice = computed(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  });
  
  // 高DPI屏幕检测
  const isHighDPI = computed(() => {
    return window.devicePixelRatio > 1;
  });
  
  // 可用空间计算
  const availableSpace = computed(() => ({
    width: windowWidth.value,
    height: windowHeight.value,
    aspectRatio: windowWidth.value / windowHeight.value
  }));
  
  // 防抖定时器
  let resizeTimer: number | null = null;
  
  // 更新窗口尺寸
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth;
    windowHeight.value = window.innerHeight;
    orientation.value = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
  };
  
  // 防抖的resize处理
  const handleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }
    
    resizeTimer = window.setTimeout(() => {
      updateWindowSize();
    }, finalConfig.debounceDelay);
  };
  
  // 方向变化处理
  const handleOrientationChange = () => {
    // 延迟更新以确保获取正确的尺寸
    setTimeout(() => {
      updateWindowSize();
    }, 100);
  };
  
  // 获取断点信息
  const getBreakpoints = () => ({
    mobile: finalConfig.mobileBreakpoint,
    tablet: finalConfig.tabletBreakpoint,
    desktop: finalConfig.desktopBreakpoint
  });
  
  // 检查是否匹配特定断点
  const matchesBreakpoint = (breakpoint: keyof typeof finalConfig | number) => {
    const width = windowWidth.value;
    
    if (typeof breakpoint === 'number') {
      return width >= breakpoint;
    }
    
    switch (breakpoint) {
      case 'mobileBreakpoint':
        return width >= finalConfig.mobileBreakpoint;
      case 'tabletBreakpoint':
        return width >= finalConfig.tabletBreakpoint;
      case 'desktopBreakpoint':
        return width >= finalConfig.desktopBreakpoint;
      default:
        return false;
    }
  };
  
  // 获取推荐的列数
  const getRecommendedColumns = () => {
    switch (screenSize.value) {
      case 'mobile':
        return 1;
      case 'tablet':
        return 2;
      case 'desktop':
        return 3;
      case 'large':
        return 4;
      default:
        return 2;
    }
  };
  
  // 获取推荐的间距
  const getRecommendedSpacing = () => {
    switch (screenSize.value) {
      case 'mobile':
        return { padding: '10px', gap: '10px' };
      case 'tablet':
        return { padding: '15px', gap: '15px' };
      case 'desktop':
        return { padding: '20px', gap: '20px' };
      case 'large':
        return { padding: '30px', gap: '30px' };
      default:
        return { padding: '15px', gap: '15px' };
    }
  };
  
  // 生命周期管理
  onMounted(() => {
    // 初始化窗口尺寸
    updateWindowSize();
    
    // 添加事件监听器
    window.addEventListener('resize', handleResize, { passive: true });
    window.addEventListener('orientationchange', handleOrientationChange, { passive: true });
    
    // 监听媒体查询变化（可选的额外支持）
    if (window.matchMedia) {
      const mobileQuery = window.matchMedia(`(max-width: ${finalConfig.mobileBreakpoint - 1}px)`);
      const tabletQuery = window.matchMedia(`(max-width: ${finalConfig.tabletBreakpoint - 1}px)`);
      
      // 可以在这里添加更精确的媒体查询监听
    }
  });
  
  onUnmounted(() => {
    // 清理定时器
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }
    
    // 移除事件监听器
    window.removeEventListener('resize', handleResize);
    window.removeEventListener('orientationchange', handleOrientationChange);
  });
  
  return {
    // 基础状态
    windowWidth,
    windowHeight,
    orientation,
    
    // 屏幕类型
    screenSize,
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    
    // 布局模式
    layoutMode,
    isPortrait,
    isLandscape,
    
    // 设备特性
    isTouchDevice,
    isHighDPI,
    
    // 计算属性
    availableSpace,
    
    // 工具方法
    getBreakpoints,
    matchesBreakpoint,
    getRecommendedColumns,
    getRecommendedSpacing,
    
    // 手动更新方法
    updateWindowSize
  };
}
