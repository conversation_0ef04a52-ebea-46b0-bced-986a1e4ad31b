<!-- 
  PestDashboard.vue
  虫害大数据分析平台概览页面
  展示虫害监测数据、趋势分析和预警信息
-->
<template>
  <div class="pest-dashboard">
    <!-- 页面标题 -->
    <PageHeader
      title="虫害大数据分析平台"
      description="全面监测虫害数据，分析趋势变化，提供智能预警和防治建议"
      icon="DataAnalysis"
    >
      <template #actions>
        <div class="date-selector">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleDateChange"
          />
        </div>
      </template>
    </PageHeader>
    
    <!-- 数据概览卡片 -->
    <div class="data-cards">
      <PestDataCard
        v-for="(metric, index) in summaryMetrics"
        :key="index"
        :title="metric.title"
        :value="metric.value"
        :trend="metric.trend"
        :trend-text="metric.trendText"
        :type="metric.type"
        :icon="metric.icon"
      />
    </div>
    
    <!-- 图表与数据区域 -->
    <div class="data-panels">
      <!-- 左侧：趋势图表 -->
      <DataPanel title="虫害趋势分析">
        <template #actions>
          <el-radio-group v-model="trendTimeRange" size="small" @change="handleTrendRangeChange">
            <el-radio-button label="week">周</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="quarter">季度</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>
        </template>
        <div ref="trendChartRef" class="trend-chart"></div>
      </DataPanel>
      
      <!-- 右侧面板组 -->
      <div class="right-panels">
        <!-- 最近爆发记录 -->
        <DataPanel title="最近虫害爆发">
          <template #actions>
            <el-button type="primary" size="small" plain @click="refreshOutbreaks">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </template>
          <div class="outbreak-list">
            <el-empty v-if="outbreaks.length === 0" description="暂无爆发记录" />
            <OutbreakItem
              v-else
              v-for="(outbreak, index) in outbreaks"
              :key="index"
              :pest-name="outbreak.pestName"
              :location="outbreak.location"
              :date="outbreak.date"
              :area="outbreak.area"
              :severity-text="outbreak.severityText"
              :severity-class="outbreak.severityClass"
            />
          </div>
        </DataPanel>
        
        <!-- 虫害预警 -->
        <DataPanel title="虫害预警">
          <template #actions>
            <el-tag size="small" type="danger" effect="dark">高风险</el-tag>
          </template>
          <div class="forecast-content">
            <div class="forecast-map" ref="forecastMapRef"></div>
            <div class="forecast-metrics">
              <div v-for="(item, idx) in forecastMetrics" :key="idx" class="forecast-metric">
                <div class="metric-label">{{ item.label }}</div>
                <div class="metric-value">{{ item.value }}</div>
              </div>
            </div>
          </div>
        </DataPanel>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="数据采集正常" />
        <StatusIndicator type="warning" label="预警监测中" />
        <StatusIndicator type="normal" label="AI分析进行中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  DataAnalysis,
  User,
  Bell,
  Odometer,
  Refresh,
  ArrowUp,
  ArrowDown,
  ArrowRight
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import DataPanel from './components/DataPanel.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import PestDataCard from './components/PestDataCard.vue'
import OutbreakItem from './components/OutbreakItem.vue'

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 趋势图相关
const trendTimeRange = ref('month')
const trendChartRef = ref<HTMLElement | null>(null)
let trendChart: echarts.ECharts | null = null

// 预警地图
const forecastMapRef = ref<HTMLElement | null>(null)
let forecastMap: echarts.ECharts | null = null

// 最后更新时间
const lastUpdateTime = ref(new Date())

// 数据概览
const summaryMetrics = ref([
  {
    title: '监测虫害总数',
    value: '8,532',
    trend: 'up',
    trendText: '环比增长 12%',
    type: 'primary',
    icon: 'User'
  },
  {
    title: '本月爆发次数',
    value: '26',
    trend: 'up',
    trendText: '同比增长 8%',
    type: 'warning',
    icon: 'Bell'
  },
  {
    title: '平均防治效率',
    value: '89%',
    trend: 'up',
    trendText: '提升 5%',
    type: 'success',
    icon: 'Odometer'
  },
  {
    title: '数据分析覆盖率',
    value: '96%',
    trend: 'stable',
    trendText: '持平',
    type: 'info',
    icon: 'DataAnalysis'
  }
])

// 最近爆发记录
const outbreaks = ref([
  {
    pestName: '稻飞虱',
    location: '东部农田区',
    date: '2023-06-23',
    area: '约 120 亩',
    severityText: '中度',
    severityClass: 'medium'
  },
  {
    pestName: '粘虫',
    location: '南部农田区',
    date: '2023-06-20',
    area: '约 85 亩',
    severityText: '轻微',
    severityClass: 'low'
  },
  {
    pestName: '稻纵卷叶螟',
    location: '中部农田区',
    date: '2023-06-18',
    area: '约 210 亩',
    severityText: '严重',
    severityClass: 'high'
  },
  {
    pestName: '蚜虫',
    location: '西部农田区',
    date: '2023-06-15',
    area: '约 160 亩',
    severityText: '中度',
    severityClass: 'medium'
  }
])

// 预警指标
const forecastMetrics = ref([
  { label: '高风险区域', value: '东部、南部' },
  { label: '主要预警虫害', value: '稻飞虱、粘虫' },
  { label: '预计爆发时间', value: '7-10天内' },
  { label: '建议防治措施', value: '喷洒农药、生物防治' }
])

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 处理日期变化
const handleDateChange = () => {
  // 根据日期范围重新获取数据
  renderTrendChart()
}

// 处理趋势图时间范围变化
const handleTrendRangeChange = () => {
  renderTrendChart()
}

// 刷新爆发记录
const refreshOutbreaks = () => {
  // 模拟刷新数据
  outbreaks.value = [
    {
      pestName: '蝗虫',
      location: '北部农田区',
      date: '2023-06-24',
      area: '约 75 亩',
      severityText: '轻微',
      severityClass: 'low'
    },
    ...outbreaks.value.slice(0, 3)
  ]
  ElMessage.success('爆发记录已更新')
}

// 刷新所有数据
const refreshData = () => {
  renderTrendChart()
  renderForecastMap()
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
}

// 渲染趋势图表
const renderTrendChart = () => {
  if (!trendChart) return
  
  const pestTypes = ['蚜虫', '飞虱', '螟虫', '粘虫', '蝗虫']
  const xAxisData = generateTimeData()
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    legend: {
      data: pestTypes,
      bottom: 0,
      textStyle: {
        color: '#9ca3af'
      },
      icon: 'roundRect'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: '#4b5563'
          }
        },
        axisLabel: {
          color: '#9ca3af',
          fontSize: 10
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        axisLabel: {
          formatter: '{value}',
          color: '#9ca3af'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#4b5563'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(75, 85, 99, 0.1)'
          }
        }
      }
    ],
    series: pestTypes.map((type, index) => {
      const colors = ['#3b82f6', '#f59e0b', '#10b981', '#8b5cf6', '#ec4899']
      return {
        name: type,
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3,
          color: colors[index % colors.length],
          shadowColor: `rgba(${colors[index % colors.length]}, 0.3)`,
          shadowBlur: 10
        },
        symbol: 'emptyCircle',
        symbolSize: 8,
        itemStyle: {
          color: colors[index % colors.length],
          borderColor: '#1f2937',
          borderWidth: 2
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: `${colors[index % colors.length]}50` },
            { offset: 1, color: `${colors[index % colors.length]}10` }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: generateRandomData(xAxisData.length, 50 + index * 20, 150 + index * 30)
      }
    })
  }
  
  trendChart.setOption(option)
}

// 渲染预警地图
const renderForecastMap = () => {
  if (!forecastMap) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}',
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#10b981', '#f59e0b', '#ef4444']
      },
      textStyle: {
        color: '#9ca3af'
      }
    },
    series: [
      {
        name: '虫害风险',
        type: 'map',
        map: 'farmland',
        zoom: 1.2,
        selectedMode: false,
        label: {
          show: true,
          formatter: '{b}',
          fontSize: 8,
          color: '#e5e7eb'
        },
        itemStyle: {
          areaColor: '#1f2937',
          borderColor: '#374151'
        },
        emphasis: {
          itemStyle: {
            areaColor: '#3b82f6'
          },
          label: {
            color: '#ffffff'
          }
        },
        data: [
          {name: '东部农田区', value: 87},
          {name: '西部农田区', value: 42},
          {name: '南部农田区', value: 76},
          {name: '北部农田区', value: 31},
          {name: '中部农田区', value: 65}
        ]
      }
    ]
  }
  
  // 注册自定义地图数据
  registerFarmlandMap()
  
  forecastMap.setOption(option)
}

// 生成随机数据
const generateRandomData = (length: number, min: number, max: number) => {
  const result = []
  for (let i = 0; i < length; i++) {
    result.push(Math.floor(Math.random() * (max - min + 1)) + min)
  }
  return result
}

// 生成时间数据
const generateTimeData = () => {
  const result = []
  const now = new Date()
  const count = trendTimeRange.value === 'week' ? 7 
    : trendTimeRange.value === 'month' ? 30 
    : trendTimeRange.value === 'quarter' ? 12 
    : 12
  
  for (let i = 0; i < count; i++) {
    const date = new Date()
    if (trendTimeRange.value === 'week') {
      date.setDate(now.getDate() - (count - i - 1))
      result.push(`${date.getMonth() + 1}-${date.getDate()}`)
    } else if (trendTimeRange.value === 'month') {
      date.setDate(now.getDate() - (count - i - 1))
      result.push(`${date.getMonth() + 1}-${date.getDate()}`)
    } else if (trendTimeRange.value === 'quarter') {
      date.setMonth(now.getMonth() - (count - i - 1))
      result.push(`${date.getMonth() + 1}月`)
    } else {
      date.setMonth(now.getMonth() - (count - i - 1))
      result.push(`${date.getFullYear()}-${date.getMonth() + 1}`)
    }
  }
  
  return result
}

// 注册自定义农田地图
const registerFarmlandMap = () => {
  // 简单的自定义地图数据
  const farmlandJson = {
    "type": "FeatureCollection" as const,
    "features": [
      {
        "type": "Feature",
        "id": "eastern",
        "properties": { "name": "东部农田区" },
        "geometry": {
          "type": "Polygon",
          "coordinates": [[[108, 30], [110, 30], [110, 32], [108, 32], [108, 30]]]
        }
      },
      {
        "type": "Feature",
        "id": "western",
        "properties": { "name": "西部农田区" },
        "geometry": {
          "type": "Polygon",
          "coordinates": [[[102, 30], [104, 30], [104, 32], [102, 32], [102, 30]]]
        }
      },
      {
        "type": "Feature",
        "id": "southern",
        "properties": { "name": "南部农田区" },
        "geometry": {
          "type": "Polygon",
          "coordinates": [[[105, 28], [107, 28], [107, 30], [105, 30], [105, 28]]]
        }
      },
      {
        "type": "Feature",
        "id": "northern",
        "properties": { "name": "北部农田区" },
        "geometry": {
          "type": "Polygon",
          "coordinates": [[[105, 32], [107, 32], [107, 34], [105, 34], [105, 32]]]
        }
      },
      {
        "type": "Feature",
        "id": "central",
        "properties": { "name": "中部农田区" },
        "geometry": {
          "type": "Polygon",
          "coordinates": [[[105, 30], [107, 30], [107, 32], [105, 32], [105, 30]]]
        }
      }
    ]
  }
  
  // 注册地图
  echarts.registerMap('farmland', farmlandJson as any)
}

// 初始化图表
const initCharts = () => {
  // 初始化趋势图
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value as HTMLElement)
    renderTrendChart()
  }
  
  // 初始化预警地图
  if (forecastMapRef.value) {
    forecastMap = echarts.init(forecastMapRef.value as HTMLElement)
    renderForecastMap()
  }
  
  // 添加窗口大小改变时的响应式处理
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  trendChart?.resize()
  forecastMap?.resize()
}

// 组件挂载后初始化
onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})

// 组件卸载前清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  
  if (forecastMap) {
    forecastMap.dispose()
    forecastMap = null
  }
})
</script>

<style scoped>
.pest-dashboard {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.date-selector {
  display: flex;
  align-items: center;
}

/* 数据卡片网格 */
.data-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* 数据面板布局 */
.data-panels {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
  min-height: 0;
}

.right-panels {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.right-panels > * {
  flex: 1;
  min-height: 0;
}

/* 趋势图表 */
.trend-chart {
  height: 100%;
  width: 100%;
}

/* 爆发列表 */
.outbreak-list {
  height: 100%;
  overflow-y: auto;
  padding-right: 5px;
}

/* 预警内容 */
.forecast-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.forecast-map {
  height: 60%;
  width: 100%;
}

.forecast-metrics {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
}

.forecast-metric {
  width: 50%;
  padding: 5px 10px;
}

.metric-label {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 14px;
  color: #e5e7eb;
  font-weight: 500;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .data-panels {
    grid-template-columns: 1fr;
  }
  
  .right-panels {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    display: grid;
  }
}

@media (max-width: 768px) {
  .data-cards {
    grid-template-columns: 1fr;
  }
  
  .right-panels {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style> 