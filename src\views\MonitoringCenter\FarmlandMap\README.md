# 农田地图 3D 实现

## 概述

本模块实现了智慧农场的3D地图监控功能，使用Three.js加载和渲染GLB模型，并在3D空间中显示设备位置和状态。

## 文件结构

- `FarmlandMap.vue` - 主页面组件
- `components/`
  - `MapCanvas.vue` - 地图画布容器组件
  - `ThreeMapCanvas.vue` - Three.js 3D地图渲染组件
  - `ThreeViewControls.vue` - 3D视角控制组件
  - `MapControls.vue` - 地图控制组件
  - `DevicePanel.vue` - 设备详情面板
  - `AlertList.vue` - 警报列表组件
  - `DeviceLegend.vue` - 设备图例组件
  - `PerspectiveControl.vue` - 视角控制组件
- `services/`
  - `threeService.ts` - Three.js场景管理服务
  - `mapService.ts` - 地图服务（旧版，已被threeService替代）
  - `deviceService.ts` - 设备数据服务
- `composables/`
  - `useDeviceData.ts` - 设备数据管理Hook
  - `useMapInteraction.ts` - 地图交互管理Hook（旧版）
  - `useWeatherData.ts` - 天气数据管理Hook
  - `useAlertData.ts` - 警报数据管理Hook
- `styles/` - 样式文件
- `types/` - 类型定义

## 功能特性

- 3D农田模型加载和渲染
- 设备在3D空间中的定位和显示
- 设备状态实时更新
- 设备选择和聚焦
- 视角控制和切换
- 图层控制
- 响应式设计

## 技术栈

- Vue 3 + TypeScript
- Three.js - 3D渲染
- Element Plus - UI组件

## 使用的模型

- `farmland.glb` - 农田3D模型

## 实现细节

### Three.js集成

通过`threeService`单例服务管理Three.js场景，包括：
- 场景初始化
- 模型加载
- 设备标记创建
- 相机控制
- 事件处理

### 设备数据流

1. `deviceService`提供设备数据
2. `useDeviceData` composable管理设备状态
3. `ThreeMapCanvas`组件监听设备数据变化
4. `threeService`更新3D场景中的设备位置和状态

### 交互流程

1. 用户点击3D场景中的设备
2. `threeService`检测点击并触发回调
3. `ThreeMapCanvas`组件接收回调并调用`selectDevice`
4. `useDeviceData`更新选中设备状态
5. `DevicePanel`组件显示选中设备的详情

## 未来改进

- 添加更多设备类型和模型
- 实现地形编辑功能
- 添加环境效果（天气、时间等）
- 优化性能，支持更多设备同时显示
- 添加动画路径规划 