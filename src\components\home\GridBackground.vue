<template>
  <div class="grid-background" ref="gridContainer">
    <div class="grid-overlay"></div>
    <canvas ref="gridCanvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  /** 网格颜色，默认为 #00ffaa */
  gridColor?: string
  /** 网格线宽度，默认为 0.3 */
  lineWidth?: number
  /** 网格大小，默认为 50 */
  gridSize?: number
  /** 是否启用脉冲效果，默认为 true */
  enablePulse?: boolean
  /** 是否启用视差效果，默认为 true */
  enableParallax?: boolean
  /** 视差强度，默认为 20 */
  parallaxStrength?: number
}

const props = withDefaults(defineProps<Props>(), {
  gridColor: '#00ffaa',
  lineWidth: 0.3,
  gridSize: 50,
  enablePulse: true,
  enableParallax: true,
  parallaxStrength: 20
})

const gridContainer = ref<HTMLElement | null>(null)
const gridCanvas = ref<HTMLCanvasElement | null>(null)
let ctx: CanvasRenderingContext2D | null = null
let animationFrameId: number | null = null
let mouseX = 0
let mouseY = 0

// 初始化网格
const initGrid = () => {
  if (!gridCanvas.value) return
  
  const canvas = gridCanvas.value
  ctx = canvas.getContext('2d')
  
  if (!ctx) return
  
  // 设置canvas尺寸为窗口大小
  resizeCanvas()
  
  // 开始动画循环
  animate()
  
  // 如果启用视差效果，添加鼠标移动监听
  if (props.enableParallax) {
    window.addEventListener('mousemove', handleMouseMove)
  }
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', resizeCanvas)
}

// 调整canvas尺寸
const resizeCanvas = () => {
  if (!gridCanvas.value || !gridContainer.value) return
  
  const canvas = gridCanvas.value
  const container = gridContainer.value
  
  canvas.width = container.offsetWidth
  canvas.height = container.offsetHeight
}

// 处理鼠标移动
const handleMouseMove = (e: MouseEvent) => {
  if (!gridContainer.value) return
  
  const rect = gridContainer.value.getBoundingClientRect()
  mouseX = e.clientX - rect.left
  mouseY = e.clientY - rect.top
}

// 绘制网格
const drawGrid = (time: number) => {
  if (!ctx || !gridCanvas.value) return
  
  const { width, height } = gridCanvas.value
  
  // 清除画布
  ctx.clearRect(0, 0, width, height)
  
  // 计算网格偏移（视差效果）
  let offsetX = 0
  let offsetY = 0
  
  if (props.enableParallax) {
    const centerX = width / 2
    const centerY = height / 2
    offsetX = (mouseX - centerX) / props.parallaxStrength
    offsetY = (mouseY - centerY) / props.parallaxStrength
  }
  
  // 设置线条样式
  ctx.strokeStyle = props.gridColor
  ctx.lineWidth = props.lineWidth
  
  // 计算脉冲效果
  let pulseScale = 1
  if (props.enablePulse) {
    pulseScale = 1 + 0.05 * Math.sin(time / 1000)
  }
  
  const gridSize = props.gridSize * pulseScale
  
  // 绘制垂直线
  for (let x = offsetX % gridSize; x < width; x += gridSize) {
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, height)
    
    // 根据距离中心的远近调整透明度
    const distanceX = Math.abs(x - width / 2)
    const alphaX = 1 - Math.min(distanceX / (width / 2), 0.9)
    ctx.globalAlpha = alphaX * 0.3
    
    ctx.stroke()
  }
  
  // 绘制水平线
  for (let y = offsetY % gridSize; y < height; y += gridSize) {
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    
    // 根据距离中心的远近调整透明度
    const distanceY = Math.abs(y - height / 2)
    const alphaY = 1 - Math.min(distanceY / (height / 2), 0.9)
    ctx.globalAlpha = alphaY * 0.3
    
    ctx.stroke()
  }
  
  // 重置透明度
  ctx.globalAlpha = 1.0
}

// 动画循环
const animate = (time = 0) => {
  drawGrid(time)
  animationFrameId = requestAnimationFrame(animate)
}

// 监听属性变化
watch([
  () => props.gridColor,
  () => props.lineWidth,
  () => props.gridSize,
  () => props.enablePulse,
  () => props.enableParallax,
  () => props.parallaxStrength
], () => {
  // 属性变化时，如果启用/禁用视差效果，需要添加/移除事件监听
  if (props.enableParallax) {
    window.addEventListener('mousemove', handleMouseMove)
  } else {
    window.removeEventListener('mousemove', handleMouseMove)
  }
})

onMounted(() => {
  initGrid()
})

onUnmounted(() => {
  // 清理资源
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId)
  }
  
  window.removeEventListener('mousemove', handleMouseMove)
  window.removeEventListener('resize', resizeCanvas)
})
</script>

<style lang="scss" scoped>
.grid-background {
  position: absolute;
  inset: 0;
  z-index: 2;
  overflow: hidden;
  pointer-events: none;
  
  canvas {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
    perspective: 1000px;
    transform-style: preserve-3d;
  }
  
  .grid-overlay {
    position: absolute;
    inset: 0;
    background: radial-gradient(
      circle at 50% 50%,
      rgba(0, 40, 100, 0) 0%,
      rgba(0, 20, 60, 0.2) 70%,
      rgba(0, 10, 30, 0.4) 100%
    );
    z-index: 2;
    pointer-events: none;
  }
}

// 添加一个淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.grid-background {
  animation: fadeIn 2s ease-in-out;
}
</style> 