<!-- 
  ControlPanel.vue
  通用控制面板组件，用于设备控制操作
  支持各种控制按钮、开关、滑块等
-->
<template>
  <div class="control-panel" :class="{ 'is-dark': dark }">
    <div class="panel-header">
      <h3 class="panel-title">{{ title }}</h3>
      <div class="panel-status" v-if="status">
        <span class="status-dot" :class="statusType"></span>
        <span class="status-text">{{ status }}</span>
      </div>
    </div>
    
    <div class="panel-content">
      <slot></slot>
    </div>
    
    <div class="panel-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true
  },
  status: {
    type: String,
    default: ''
  },
  statusType: {
    type: String,
    default: 'normal' // normal, success, warning, error
  },
  dark: {
    type: Boolean,
    default: true
  }
});
</script>

<style scoped>
.control-panel {
  background: linear-gradient(145deg, #ffffff, #f3f4f6);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.control-panel.is-dark {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  border: 1px solid #3b4863;
}

.control-panel:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.control-panel.is-dark:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.panel-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.control-panel.is-dark .panel-header {
  border-bottom: 1px solid #3b4863;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.control-panel.is-dark .panel-title {
  color: #e5e7eb;
}

.panel-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.normal {
  background-color: #60a5fa;
}

.status-dot.success {
  background-color: #10b981;
}

.status-dot.warning {
  background-color: #f59e0b;
}

.status-dot.error {
  background-color: #ef4444;
}

.status-text {
  font-size: 14px;
  color: #6b7280;
}

.control-panel.is-dark .status-text {
  color: #9ca3af;
}

.panel-content {
  padding: 20px;
  flex: 1;
  overflow: auto;
}

.panel-footer {
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.control-panel.is-dark .panel-footer {
  border-top: 1px solid #3b4863;
  background-color: rgba(31, 41, 55, 0.7);
}

/* 控制元素通用样式 */
:deep(.control-group) {
  margin-bottom: 20px;
}

:deep(.control-label) {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #4b5563;
}

.control-panel.is-dark :deep(.control-label) {
  color: #9ca3af;
}

:deep(.control-actions) {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

:deep(.control-value) {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.control-panel.is-dark :deep(.control-value) {
  color: #e5e7eb;
}
</style> 