<!--
  UltrasonicControl.vue
  超声波强度实时调节面板
  调节超声波装置的强度参数，满足不同场景下的病虫防治需求
-->
<template>
  <div class="ultrasonic-control">
    <!-- 页面标题 -->
    <PageHeader
      title="超声波强度实时调节面板"
      description="调节超声波装置的强度参数，满足不同场景下的病虫防治需求"
      icon="Setting"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ getActiveDevicesCount() }}</span>
            <span class="summary-label">活跃设备</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ getAverageIntensity() }}dB</span>
            <span class="summary-label">平均强度</span>
          </div>
        </div>
      </template>
    </PageHeader>

    <div class="control-container">
      <div class="control-panel">
        <div class="panel-header">
          <div class="device-info">
            <h3 class="device-name">超声波装置 {{ currentDevice.name }}</h3>
            <div class="device-location">位置: {{ currentDevice.location }}</div>
          </div>
          <div class="current-intensity">{{ currentDevice.intensity }}dB</div>
        </div>

        <div class="slider-area">
          <div class="slider-wrapper">
            <div class="slider-track">
              <div class="slider-fill" :style="{ width: `${getIntensityPercentage(currentDevice.intensity)}%` }"></div>
            </div>
            <div class="slider-knob" :style="{ left: `calc(${getIntensityPercentage(currentDevice.intensity)}% - 12px)` }"></div>
            <div class="sonar-effect" :style="{ left: `calc(${getIntensityPercentage(currentDevice.intensity)}% - 25px)` }"></div>

            <el-slider
              v-model="currentDevice.intensity"
              :min="minIntensity"
              :max="maxIntensity"
              :step="1"
              class="intensity-slider"
              @change="handleSliderChange"
            />

            <div class="slider-ticks">
              <div class="tick" v-for="tick in sliderTicks" :key="tick" :style="{ left: `${getIntensityPercentage(tick)}%` }">
                <div class="tick-line"></div>
                <div class="tick-value">{{ tick }}dB</div>
              </div>
            </div>
          </div>
        </div>

        <div class="intensity-value-display">
          <div class="intensity-value">
            <span class="value">{{ currentDevice.intensity }}</span>
            <span class="unit">dB</span>
          </div>
          <div class="intensity-input">
            <el-input-number
              v-model="currentDevice.intensity"
              :min="minIntensity"
              :max="maxIntensity"
              :step="1"
              size="large"
              controls-position="right"
              @change="handleInputChange"
            />
          </div>
        </div>

        <div class="target-scenario">
          <h4 class="scenario-title">目标防治场景</h4>
          <div class="scenario-selection">
            <div class="scenario"
                 v-for="scenario in targetScenarios"
                 :key="scenario.id"
                 :class="{ active: currentScenario === scenario.id }"
                 @click="selectScenario(scenario.id)">
              <div class="scenario-icon" :class="scenario.icon"></div>
              <div class="scenario-info">
                <div class="scenario-name">{{ scenario.name }}</div>
                <div class="scenario-desc">{{ scenario.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="preset-intensities">
          <h4 class="preset-title">预设强度</h4>
          <div class="preset-buttons">
            <div v-for="preset in presetIntensities" :key="preset.value"
              class="preset-button"
              :class="{ active: currentDevice.intensity === preset.value }"
              @click="applyPreset(preset.value)"
            >
              <div class="preset-icon" :class="preset.level"></div>
              <div class="preset-label">{{ preset.name }}</div>
              <div class="preset-value">{{ preset.value }}dB</div>
            </div>
          </div>
        </div>

        <div class="action-buttons">
          <el-button type="primary" @click="applySettings">应用</el-button>
          <el-button @click="resetSettings">重置</el-button>
        </div>
      </div>

      <div class="device-selector">
        <h3 class="selector-title">超声波装置列表</h3>
        <div class="device-list">
          <div v-for="device in devices" :key="device.id"
            class="device-item"
            :class="{ active: device.id === currentDevice.id }"
            @click="selectDevice(device)"
          >
            <div class="device-icon" :class="getStatusClass(device.status)"></div>
            <div class="device-details">
              <div class="device-name">{{ device.name }}</div>
              <div class="device-location">{{ device.location }}</div>
            </div>
            <div class="device-intensity">{{ device.intensity }}dB</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 应用设置成功提示 -->
    <el-dialog v-model="successDialogVisible" width="300px" class="success-dialog">
      <div class="success-content">
        <div class="success-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="40" height="40">
            <path d="M20 6L9 17l-5-5" stroke="white" stroke-width="2" fill="none" />
          </svg>
        </div>
        <div class="success-message">超声波强度设置已成功应用</div>
        <div class="device-details">
          <div class="detail-item">
            <span class="detail-label">设备</span>
            <span class="detail-value">{{ currentDevice.name }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">强度</span>
            <span class="detail-value">{{ currentDevice.intensity }}dB</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">场景</span>
            <span class="detail-value">{{ getScenarioName(currentScenario) }}</span>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="超声波防护" />
        <StatusIndicator type="normal" label="生态友好" />
        <StatusIndicator type="warning" label="智能控制" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Setting,
  Refresh,
  View,
  Switch,
  HomeFilled
} from '@element-plus/icons-vue';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import StatusIndicator from './components/StatusIndicator.vue';
import DeviceCard from './components/DeviceCard.vue';

// 强度范围
const minIntensity = 20;
const maxIntensity = 80;

// 设备列表
const devices = reactive([
  { id: 1, name: 'US-101', location: '东区果园A7', status: 'active', intensity: 50, originalIntensity: 50 },
  { id: 2, name: 'US-102', location: '西区蔬菜大棚B3', status: 'active', intensity: 35, originalIntensity: 35 },
  { id: 3, name: 'US-103', location: '南区稻田C2', status: 'inactive', intensity: 65, originalIntensity: 65 },
  { id: 4, name: 'US-104', location: '北区温室D1', status: 'active', intensity: 40, originalIntensity: 40 },
  { id: 5, name: 'US-105', location: '中心区试验田E5', status: 'maintenance', intensity: 55, originalIntensity: 55 }
]);

// 当前选中的设备
const currentDevice = reactive(JSON.parse(JSON.stringify(devices[0])));

// 目标场景
const targetScenarios = [
  {
    id: 'birds',
    name: '鸟类驱离',
    icon: 'high',
    description: '防止鸟类啄食作物'
  },
  {
    id: 'rodents',
    name: '啮齿类驱离',
    icon: 'medium',
    description: '防止鼠类破坏农田'
  },
  {
    id: 'insects',
    name: '昆虫驱离',
    icon: 'low',
    description: '限制害虫活动范围'
  }
];

// 当前选中的场景
const currentScenario = ref('birds');

// 预设强度选项
const presetIntensities = [
  { name: '低强度', value: 30, level: 'low' },
  { name: '中强度', value: 50, level: 'medium' },
  { name: '高强度', value: 70, level: 'high' }
];

// 滑块刻度值
const sliderTicks = [20, 35, 50, 65, 80];

// 成功对话框可见性
const successDialogVisible = ref(false);

// 最后更新时间
const lastUpdateTime = ref(new Date());

// 计算活跃设备数量
const getActiveDevicesCount = () => {
  return devices.filter(device => device.status === 'active').length;
};

// 计算平均强度
const getAverageIntensity = () => {
  const sum = devices.reduce((total, device) => total + device.intensity, 0);
  return Math.round(sum / devices.length);
};

// 计算强度百分比
const getIntensityPercentage = (intensity: number) => {
  return ((intensity - minIntensity) / (maxIntensity - minIntensity)) * 100;
};

// 获取设备状态样式类
const getStatusClass = (status: string) => {
  return `status-${status}`;
};

// 根据场景ID获取场景名称
const getScenarioName = (scenarioId: string) => {
  const scenario = targetScenarios.find(s => s.id === scenarioId);
  return scenario ? scenario.name : '';
};

// 处理滑块变化
const handleSliderChange = (value: number) => {
  currentDevice.intensity = value;
};

// 处理输入框变化
const handleInputChange = (value: number) => {
  currentDevice.intensity = value;
};

// 应用预设强度
const applyPreset = (value: number) => {
  currentDevice.intensity = value;
  // 添加触觉反馈效果
  if (window.navigator.vibrate) {
    window.navigator.vibrate(100);
  }
};

// 选择应用场景
const selectScenario = (scenarioId: string) => {
  currentScenario.value = scenarioId;

  // 根据场景自动推荐强度设置
  if (scenarioId === 'birds') {
    applyPreset(70); // 高强度适合驱鸟
  } else if (scenarioId === 'rodents') {
    applyPreset(50); // 中强度适合驱鼠
  } else if (scenarioId === 'insects') {
    applyPreset(30); // 低强度适合驱虫
  }
};

// 应用设置
const applySettings = () => {
  // 更新设备列表中的强度值
  const deviceIndex = devices.findIndex(d => d.id === currentDevice.id);
  if (deviceIndex !== -1) {
    devices[deviceIndex].intensity = currentDevice.intensity;
    devices[deviceIndex].originalIntensity = currentDevice.intensity;

    // 显示成功对话框
    successDialogVisible.value = true;
    setTimeout(() => {
      successDialogVisible.value = false;
    }, 2000);

    // 发送请求到后端
    // 实际项目中这里会使用 axios 向后端发送设置请求
    // axios.post('/api/ultrasonic/setIntensity', {
    //   deviceId: currentDevice.id,
    //   intensity: currentDevice.intensity,
    //   scenario: currentScenario.value
    // })
  }
};

// 重置设置
const resetSettings = () => {
  const deviceIndex = devices.findIndex(d => d.id === currentDevice.id);
  if (deviceIndex !== -1) {
    currentDevice.intensity = devices[deviceIndex].originalIntensity;
    ElMessage({
      type: 'info',
      message: '已重置为原始强度值'
    });
  }
};

// 选择设备
const selectDevice = (device: any) => {
  Object.assign(currentDevice, JSON.parse(JSON.stringify(device)));
};

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 刷新数据
const refreshData = () => {
  // 模拟数据更新
  devices.forEach(device => {
    device.intensity = Math.max(minIntensity, Math.min(maxIntensity, device.intensity + Math.floor(Math.random() * 10) - 5));
  });

  // 如果当前选中的设备在列表中，更新当前设备的强度
  const deviceIndex = devices.findIndex(d => d.id === currentDevice.id);
  if (deviceIndex !== -1) {
    currentDevice.intensity = devices[deviceIndex].intensity;
    currentDevice.originalIntensity = devices[deviceIndex].intensity;
  }

  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

// 数据更新计时器
let dataUpdateInterval: number | null = null;

onMounted(() => {
  // 启动数据自动更新
  dataUpdateInterval = window.setInterval(() => {
    // 小幅度随机波动数据
    devices.forEach(device => {
      if (device.status === 'active') {
        device.intensity = Math.max(minIntensity, Math.min(maxIntensity, device.intensity + Math.floor(Math.random() * 4) - 2));
      }
    });

    // 如果当前选中的设备在列表中，更新当前设备的强度
    const deviceIndex = devices.findIndex(d => d.id === currentDevice.id);
    if (deviceIndex !== -1 && devices[deviceIndex].status === 'active') {
      currentDevice.intensity = devices[deviceIndex].intensity;
      currentDevice.originalIntensity = devices[deviceIndex].intensity;
    }

    lastUpdateTime.value = new Date();
  }, 30000); // 每30秒更新一次
});

onUnmounted(() => {
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
});
</script>

<style scoped>
.ultrasonic-control {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 控制容器 */
.control-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 15px;
  flex: 1;
  margin-bottom: 20px;
}

@media (max-width: 1024px) {
  .control-container {
    grid-template-columns: 1fr;
  }
}

/* 控制面板 */
.control-panel {
  background: linear-gradient(135deg, #1e293b, #0f172a);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 3px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.device-info .device-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 3px 0;
  display: flex;
  align-items: center;
}

.device-info .device-location {
  font-size: 0.8rem;
  color: #a1a1aa;
  margin-left: 26px;
}

.current-intensity {
  font-size: 1.6rem;
  font-weight: 700;
  color: #3b82f6;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* 滑块区域 */
.slider-area {
  margin-bottom: 25px;
}

.slider-wrapper {
  position: relative;
  height: 70px;
}

.slider-track {
  position: absolute;
  top: 30px;
  left: 0;
  right: 0;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.slider-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.slider-knob {
  position: absolute;
  top: 26px;
  width: 16px;
  height: 16px;
  background-color: #3b82f6;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  transition: left 0.3s ease;
  z-index: 3;
}

.sonar-effect {
  position: absolute;
  top: 19px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(59, 130, 246, 0.2);
  animation: sonar 2s infinite;
  z-index: 2;
}

.intensity-slider {
  position: absolute;
  top: 30px;
  left: 0;
  right: 0;
  margin: 0;
  z-index: 4;
}

.intensity-slider :deep(.el-slider__runway) {
  height: 8px;
  background: transparent;
}

.intensity-slider :deep(.el-slider__bar) {
  display: none;
}

.intensity-slider :deep(.el-slider__button-wrapper) {
  top: -3px;
}

.intensity-slider :deep(.el-slider__button) {
  border: none;
  background-color: transparent;
  box-shadow: none;
}

.slider-ticks {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  height: 30px;
}

.tick {
  position: absolute;
  transform: translateX(-50%);
}

.tick-line {
  width: 1px;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.5);
  margin: 0 auto;
}

.tick-value {
  margin-top: 4px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.7rem;
  white-space: nowrap;
}

/* 强度值显示 */
.intensity-value-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.intensity-value .value {
  font-size: 2.2rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
}

.intensity-value .unit {
  font-size: 1rem;
  color: #a1a1aa;
  margin-left: 3px;
}

.intensity-input :deep(.el-input-number) {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  width: 120px;
  overflow: hidden;
}

.intensity-input :deep(.el-input__wrapper) {
  background: transparent;
  box-shadow: none !important;
}

.intensity-input :deep(.el-input__inner) {
  color: white;
  font-size: 1rem;
}

.intensity-input :deep(.el-input-number__decrease),
.intensity-input :deep(.el-input-number__increase) {
  color: rgba(255, 255, 255, 0.7);
  background-color: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.intensity-input :deep(.el-input-number__decrease:hover),
.intensity-input :deep(.el-input-number__increase:hover) {
  color: white;
  background-color: rgba(59, 130, 246, 0.4);
}

/* 目标场景 */
.target-scenario {
  margin-bottom: 15px;
}

.scenario-title {
  font-size: 0.9rem;
  color: white;
  margin-bottom: 10px;
}

.scenario-selection {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.scenario {
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.scenario.active {
  background-color: rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.scenario:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.scenario-icon {
  width: 25px;
  height: 25px;
  margin-bottom: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.scenario-name {
  font-size: 0.85rem;
  font-weight: 600;
  color: white;
  margin-bottom: 2px;
}

.scenario-desc {
  font-size: 0.7rem;
  color: #a1a1aa;
}

/* 预设强度 */
.preset-intensities {
  margin-bottom: 15px;
}

.preset-title {
  font-size: 0.9rem;
  color: white;
  margin-bottom: 10px;
}

.preset-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.preset-button {
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preset-button.active {
  background-color: rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.preset-button:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.preset-icon {
  width: 25px;
  height: 25px;
  margin: 0 auto 6px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.preset-label {
  font-size: 0.85rem;
  color: white;
  margin-bottom: 2px;
}

.preset-value {
  font-size: 0.7rem;
  color: #a1a1aa;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 10px;
}

.action-buttons .el-button {
  flex: 1;
  height: 38px;
  transition: all 0.3s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.action-buttons .el-button--primary {
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-color: #3b82f6;
}

/* 设备选择器 */
.device-selector {
  background-color: #1f2937;
  border-radius: 16px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  max-height: 62vh;
  overflow-y: auto;
}

.selector-title {
  font-size: 1.1rem;
  color: #e5e7eb;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #374151;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.device-item:hover {
  background-color: rgba(55, 65, 81, 0.5);
}

.device-item.active {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #3b82f6;
}

.device-icon {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 12px;
}

.device-icon.status-active {
  background-color: #10b981;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.device-icon.status-inactive {
  background-color: #6b7280;
}

.device-icon.status-maintenance {
  background-color: #f59e0b;
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.device-details {
  flex: 1;
}

.device-details .device-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 2px;
}

.device-details .device-location {
  font-size: 0.75rem;
  color: #9ca3af;
}

.device-intensity {
  font-size: 0.9rem;
  font-weight: 600;
  color: #3b82f6;
}

/* 成功对话框 */
.success-dialog :deep(.el-dialog__header) {
  display: none;
}

.success-dialog :deep(.el-dialog__body) {
  padding: 20px 15px;
}

.success-content {
  text-align: center;
}

.success-icon {
  width: 50px;
  height: 50px;
  background-color: #10b981;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 25px;
}

.success-message {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 15px;
}

.success-dialog .device-details {
  background-color: #f3f4f6;
  border-radius: 10px;
  padding: 12px;
}

.success-dialog .detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.success-dialog .detail-item:last-child {
  margin-bottom: 0;
}

.success-dialog .detail-label {
  color: #6b7280;
}

.success-dialog .detail-value {
  font-weight: 600;
  color: #1f2937;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .scenario-selection,
  .preset-buttons {
    grid-template-columns: repeat(3, 1fr);
  }

  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }

  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 动画 */
@keyframes sonar {
  0% {
    opacity: 0.8;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(1.4);
  }
}
</style>

<!--
注意: 此组件需要以下SVG图标文件:
- @/assets/icons/ultrasonic.svg

如果此文件不存在，请创建相应的SVG图标文件。
-->
