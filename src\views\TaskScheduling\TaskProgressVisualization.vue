<!--
  TaskProgressVisualization.vue
  任务执行进度可视化模块
  实时监控任务执行进度，提供直观的可视化界面，帮助管理人员掌握任务状态
-->

<template>
  <div class="task-progress-container">
    <!-- 页面标题 -->
    <PageHeader
      title="任务执行进度可视化"
      description="实时监控任务执行进度，提供直观的可视化界面，帮助管理人员掌握任务状态"
      icon="Connection"
    >
      <template #actions>
        <div class="header-filters">
          <el-select v-model="selectedTaskType" placeholder="任务类型" class="filter-item">
            <el-option label="全部类型" value="" />
            <el-option label="喷洒任务" value="spray" />
            <el-option label="巡检任务" value="patrol" />
            <el-option label="消杀任务" value="disinfection" />
            <el-option label="采样任务" value="sampling" />
          </el-select>
          
          <el-select v-model="selectedDeviceType" placeholder="设备类型" class="filter-item">
            <el-option label="全部设备" value="" />
            <el-option label="无人机" value="drone" />
            <el-option label="机器狗" value="robotDog" />
            <el-option label="履带机器人" value="other" />
          </el-select>
          
          <el-select v-model="selectedTimeRange" placeholder="时间范围" class="filter-item">
            <el-option label="今天" value="today" />
            <el-option label="昨天" value="yesterday" />
            <el-option label="本周" value="thisWeek" />
            <el-option label="本月" value="thisMonth" />
            <el-option label="自定义" value="custom" />
          </el-select>
          
          <el-date-picker
            v-if="selectedTimeRange === 'custom'"
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="filter-item date-picker"
          />
        </div>
      </template>
    </PageHeader>

    <!-- 关键指标卡片 -->
    <div class="metrics-grid">
      <div 
        v-for="metric in metrics" 
        :key="metric.id" 
        class="metric-card"
        :class="metric.type"
      >
        <div class="metric-icon">
          <el-icon>
            <component :is="metric.icon"></component>
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-label">{{ metric.label }}</div>
        </div>
        <div class="metric-trend">
          <span :class="metric.trend > 0 ? 'up' : 'down'">
            {{ Math.abs(metric.trend) }}% 
            <el-icon>
              <component :is="metric.trend > 0 ? 'ArrowUp' : 'ArrowDown'"></component>
            </el-icon>
          </span>
          与上周相比
        </div>
      </div>
    </div>

    <div class="main-content">
      <!-- 左侧进行中任务列表 -->
      <div class="task-list-panel">
        <div class="panel-header">
          <h3>进行中任务</h3>
          <el-badge :value="runningTasks.length" type="primary" />
        </div>
        
        <el-input
          v-model="searchKeyword"
          placeholder="搜索任务"
          prefix-icon="Search"
          clearable
          class="search-input"
        />
        
        <div class="task-list">
          <div 
            v-for="task in filteredTasks" 
            :key="task.id"
            class="task-item"
            :class="{ 'selected': selectedTaskId === task.id }"
            @click="selectTask(task.id)"
          >
            <div class="task-icon" :class="task.type">
              <el-icon><component :is="getTaskIcon(task.type)"></component></el-icon>
            </div>
            <div class="task-info">
              <div class="task-name">{{ task.name }}</div>
              <div class="task-meta">
                <span class="task-device">{{ getDeviceNames(task.deviceIds) }}</span>
                <span class="task-time">{{ formatTime(task.startTime) }}</span>
              </div>
            </div>
            <div class="task-progress">
              <el-progress 
                type="circle" 
                :percentage="task.progress" 
                :width="40"
                :stroke-width="4"
                :color="getProgressColor(task.progress)"
              />
            </div>
          </div>
          
          <div class="empty-tasks" v-if="filteredTasks.length === 0">
            <el-empty description="没有进行中的任务" :image-size="60" />
          </div>
        </div>
      </div>
      
      <!-- 右侧任务详情与图表 -->
      <div class="task-detail-panel" v-if="currentTask">
        <div class="task-header">
          <div class="task-title">
            <h3>{{ currentTask.name }}</h3>
            <el-tag :type="getTaskStatusType(currentTask.status)">
              {{ getTaskStatusLabel(currentTask.status) }}
            </el-tag>
          </div>
          
          <div class="task-actions">
            <el-button size="small" @click="refreshTaskData">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button size="small" type="primary" @click="viewTaskDetail">
              查看详情
            </el-button>
          </div>
        </div>
        
        <el-descriptions :column="3" border size="small" class="task-brief">
          <el-descriptions-item label="任务类型">{{ getTaskTypeLabel(currentTask.type) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDateTime(currentTask.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="预计结束">{{ formatDateTime(currentTask.estimatedEndTime) }}</el-descriptions-item>
          <el-descriptions-item label="执行设备">{{ getDeviceNames(currentTask.deviceIds) }}</el-descriptions-item>
          <el-descriptions-item label="工作区域">{{ currentTask.workArea }}</el-descriptions-item>
          <el-descriptions-item label="已完成">{{ currentTask.progress }}%</el-descriptions-item>
        </el-descriptions>
        
        <div class="chart-container">
          <el-tabs v-model="activeChartTab" class="progress-tabs">
            <el-tab-pane label="进度趋势" name="progressTrend">
              <div class="chart-wrapper progress-trend-chart">
                <ProgressTrendChart 
                  :taskData="currentTask" 
                  :progressHistory="currentTaskProgressHistory" 
                />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="资源消耗" name="resourceUsage">
              <div class="chart-wrapper resource-usage-chart">
                <ResourceUsageChart :taskData="currentTask" />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="路径轨迹" name="pathTrajectory">
              <div class="chart-wrapper map-trajectory">
                <PathTrajectoryMap 
                  :deviceList="taskDevices" 
                  :devicePaths="currentTaskDevicePaths" 
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <div class="key-events">
          <h4>关键事件</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(event, index) in currentTaskEvents"
              :key="index"
              :timestamp="formatDateTime(event.timestamp)"
              :type="getEventType(event.eventType)"
              :color="getEventColor(event.eventType)"
              size="small"
            >
              <div class="event-content">
                {{ event.description || getDefaultEventDescription(event) }}
                <div class="event-device" v-if="event.deviceId">
                  设备：{{ getDeviceName(event.deviceId) }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <div class="select-task-placeholder" v-else>
        <el-empty 
          description="请从左侧选择任务查看详情" 
          :image-size="120"
        >
          <template #image>
            <el-icon :size="60"><Select /></el-icon>
          </template>
        </el-empty>
      </div>
    </div>
    
    <!-- 任务详情抽屉 -->
    <el-drawer
      v-model="showTaskDetailDrawer"
      title="任务详细信息"
      direction="rtl"
      size="50%"
    >
      <div v-if="currentTask" class="task-detail-content">
        <el-descriptions :column="2" border direction="vertical">
          <el-descriptions-item label="任务ID">{{ currentTask.id }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ currentTask.name }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ getTaskTypeLabel(currentTask.type) }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getTaskStatusType(currentTask.status)">
              {{ getTaskStatusLabel(currentTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="工作区域">{{ currentTask.workArea }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-rate v-model="currentTask.priority" disabled show-score :colors="priorityColors" />
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDateTime(currentTask.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="预计结束时间">{{ formatDateTime(currentTask.estimatedEndTime) }}</el-descriptions-item>
          <el-descriptions-item label="预计工作时间">{{ currentTask.estimatedDuration }} 分钟</el-descriptions-item>
          <el-descriptions-item label="已执行时间">{{ getExecutionTime(currentTask) }} 分钟</el-descriptions-item>
          <el-descriptions-item label="执行进度">
            <el-progress :percentage="currentTask.progress" :status="getProgressStatus(currentTask.progress)" />
          </el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">
            {{ currentTask.description || '暂无描述' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-section">
          <h3>执行设备</h3>
          <el-table :data="taskDevices" border style="width: 100%">
            <el-table-column prop="name" label="设备名称" />
            <el-table-column prop="type" label="设备类型">
              <template #default="scope">
                {{ getDeviceTypeLabel(scope.row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="当前状态">
              <template #default="scope">
                <el-tag :type="getDeviceStatusType(scope.row.status)">
                  {{ getDeviceStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="battery" label="电量">
              <template #default="scope">
                <el-progress 
                  :percentage="scope.row.battery" 
                  :color="getBatteryColor(scope.row.battery)" 
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div class="detail-section">
          <h3>资源消耗</h3>
          <el-row :gutter="12">
            <el-col :span="8" v-for="(resource, index) in currentTask.resources" :key="index">
              <div class="resource-card">
                <div class="resource-name">{{ resource.name }}</div>
                <div class="resource-value">{{ resource.value }}{{ resource.unit }}</div>
                <el-progress 
                  :percentage="(resource.value / resource.total) * 100" 
                  :format="() => `${Math.round((resource.value / resource.total) * 100)}%`" 
                  :color="getResourceColor(resource.type)"
                />
              </div>
            </el-col>
          </el-row>
        </div>
        
        <div class="detail-section">
          <h3>完整事件日志</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(event, index) in allTaskEvents"
              :key="index"
              :timestamp="formatDateTime(event.timestamp)"
              :type="getEventType(event.eventType)"
              :color="getEventColor(event.eventType)"
            >
              <div class="event-content">
                {{ event.description || getDefaultEventDescription(event) }}
                <div class="event-device" v-if="event.deviceId">
                  设备：{{ getDeviceName(event.deviceId) }}
                </div>
                <div class="event-data" v-if="event.data">
                  <el-button size="small" text @click="showEventData(event)">
                    查看详细数据
                  </el-button>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>
    
    <!-- 事件数据对话框 -->
    <el-dialog
      v-model="showEventDataDialog"
      title="事件详细数据"
      width="600px"
    >
      <pre class="event-data-json" v-if="selectedEvent">{{ JSON.stringify(selectedEvent.data, null, 2) }}</pre>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Search,
  Refresh,
  Select,
  Connection,
  Clock,
  Finished,
  Odometer,
  TakeawayBox,
  WarningFilled,
  ArrowUp,
  ArrowDown,
  View,
  HomeFilled,
  Monitor,
  Setting
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import type { TaskProgressInfo, TaskEvent, DeviceInfo } from '@/types/taskScheduling'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import ProgressTrendChart from './components/charts/ProgressTrendChart.vue'
import ResourceUsageChart from './components/charts/ResourceUsageChart.vue'
import PathTrajectoryMap from './components/charts/PathTrajectoryMap.vue'

// 状态数据
const selectedTaskType = ref('')
const selectedDeviceType = ref('')
const selectedTimeRange = ref('today')
const dateRange = ref([])
const searchKeyword = ref('')
const selectedTaskId = ref('')
const showTaskDetailDrawer = ref(false)
const showEventDataDialog = ref(false)
const activeChartTab = ref('progressTrend')
const selectedEvent = ref<TaskEvent | null>(null)

// 生成随机进度历史数据
const generateProgressHistory = (taskId: string) => {
  const now = new Date();
  const result = [];
  let progress = 0;
  
  for (let i = 0; i < 20; i++) {
    progress += Math.random() * 5;
    progress = Math.min(progress, 100);
    
    const time = new Date(now.getTime() - (19 - i) * 15 * 60 * 1000).toISOString(); // 每15分钟一个点
    result.push({
      time,
      progress: Math.round(progress)
    });
  }
  
  return result;
};

// 任务和事件数据
const runningTasks = ref<TaskProgressInfo[]>([
  {
    id: 'task-001',
    name: '南区农田喷洒任务',
    type: 'spray',
    status: 'running',
    priority: 3,
    workArea: '南区农田',
    deviceIds: ['device-001', 'device-002'],
    progress: 65,
    startTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
    estimatedEndTime: new Date(Date.now() + 1 * 60 * 60 * 1000).toISOString(), // 1小时后
    estimatedDuration: 180, // 预计总共180分钟
    description: '对南区农田进行全面喷洒作业，重点关注病虫害高发区',
    resources: [
      { name: '农药', value: 32, total: 50, unit: 'L', type: 'pesticide' },
      { name: '电量', value: 65, total: 100, unit: '%', type: 'battery' },
      { name: '覆盖面积', value: 3200, total: 5000, unit: 'm²', type: 'area' }
    ]
  },
  {
    id: 'task-002',
    name: '东区农田巡检任务',
    type: 'patrol',
    status: 'running',
    priority: 2,
    workArea: '东区农田',
    deviceIds: ['device-003'],
    progress: 48,
    startTime: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45分钟前
    estimatedEndTime: new Date(Date.now() + 50 * 60 * 1000).toISOString(), // 50分钟后
    estimatedDuration: 95, // 预计总共95分钟
    description: '对东区农田进行例行巡检，检查作物生长状况并记录数据',
    resources: [
      { name: '电量', value: 58, total: 100, unit: '%', type: 'battery' },
      { name: '巡检面积', value: 1500, total: 3000, unit: 'm²', type: 'area' }
    ]
  },
  {
    id: 'task-003',
    name: '北区紧急消杀任务',
    type: 'disinfection',
    status: 'running',
    priority: 5,
    workArea: '北区农田',
    deviceIds: ['device-004', 'device-005'],
    progress: 82,
    startTime: new Date(Date.now() - 80 * 60 * 1000).toISOString(), // 80分钟前
    estimatedEndTime: new Date(Date.now() + 20 * 60 * 1000).toISOString(), // 20分钟后
    estimatedDuration: 100, // 预计总共100分钟
    description: '北区发现虫害爆发，紧急进行针对性消杀处理',
    resources: [
      { name: '杀虫剂', value: 22, total: 25, unit: 'L', type: 'pesticide' },
      { name: '电量', value: 35, total: 100, unit: '%', type: 'battery' },
      { name: '消杀面积', value: 2200, total: 2500, unit: 'm²', type: 'area' }
    ]
  }
])

// 进度历史数据
const progressHistoryData = ref<Record<string, {time: string, progress: number}[]>>({
  'task-001': generateProgressHistory('task-001'),
  'task-002': generateProgressHistory('task-002'),
  'task-003': generateProgressHistory('task-003')
})

// 设备路径数据
const devicePathsData = ref<Record<string, {
  deviceId: string,
  path: Array<{
    x: number,
    y: number,
    timestamp: string,
    type?: 'normal' | 'key' | 'warning'
  }>
}[]>>({
  'task-001': [
    {
      deviceId: 'device-001',
      path: Array(30).fill(0).map((_, i) => ({
        x: 100 + i * 10 + Math.random() * 20,
        y: 100 + Math.sin(i * 0.3) * 50 + Math.random() * 20,
        timestamp: new Date(Date.now() - (30 - i) * 4 * 60 * 1000).toISOString(),
        type: i % 10 === 5 ? 'key' : (i % 15 === 10 ? 'warning' : 'normal')
      }))
    },
    {
      deviceId: 'device-002',
      path: Array(25).fill(0).map((_, i) => ({
        x: 100 + i * 12 + Math.random() * 20,
        y: 200 + Math.cos(i * 0.3) * 50 + Math.random() * 20,
        timestamp: new Date(Date.now() - (25 - i) * 4 * 60 * 1000).toISOString(),
        type: i % 8 === 4 ? 'key' : (i % 12 === 8 ? 'warning' : 'normal')
      }))
    }
  ],
  'task-002': [
    {
      deviceId: 'device-003',
      path: Array(20).fill(0).map((_, i) => ({
        x: 200 + i * 15 + Math.random() * 20,
        y: 150 + Math.sin(i * 0.5) * 40 + Math.random() * 20,
        timestamp: new Date(Date.now() - (20 - i) * 2 * 60 * 1000).toISOString(),
        type: i % 5 === 2 ? 'key' : (i % 10 === 5 ? 'warning' : 'normal')
      }))
    }
  ],
  'task-003': [
    {
      deviceId: 'device-004',
      path: Array(35).fill(0).map((_, i) => ({
        x: 150 + i * 8 + Math.random() * 15,
        y: 250 + Math.sin(i * 0.4) * 30 + Math.random() * 15,
        timestamp: new Date(Date.now() - (35 - i) * 2 * 60 * 1000).toISOString(),
        type: i % 7 === 3 ? 'key' : (i % 14 === 7 ? 'warning' : 'normal')
      }))
    },
    {
      deviceId: 'device-005',
      path: Array(30).fill(0).map((_, i) => ({
        x: 180 + i * 9 + Math.random() * 15,
        y: 300 + Math.cos(i * 0.4) * 30 + Math.random() * 15,
        timestamp: new Date(Date.now() - (30 - i) * 2 * 60 * 1000).toISOString(),
        type: i % 6 === 2 ? 'key' : (i % 12 === 6 ? 'warning' : 'normal')
      }))
    }
  ]
})

// 关键指标数据
const metrics = ref([
  {
    id: 'metric-1',
    label: '进行中任务',
    value: 3,
    icon: 'Connection',
    trend: 20,
    type: 'primary'
  },
  {
    id: 'metric-2',
    label: '已完成任务',
    value: 26,
    icon: 'Finished',
    trend: 15,
    type: 'success'
  },
  {
    id: 'metric-3',
    label: '工作时长',
    value: '126h',
    icon: 'Clock',
    trend: 8,
    type: 'info'
  },
  {
    id: 'metric-4',
    label: '设备告警',
    value: 2,
    icon: 'WarningFilled',
    trend: -30,
    type: 'warning'
  }
])

// 模拟设备数据
const devices = ref<DeviceInfo[]>([
  {
    id: 'device-001',
    name: '无人机-01',
    type: 'drone',
    status: 'busy',
    battery: 65,
    position: { x: 120, y: 150, z: 30 },
    lastActiveTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    capabilities: ['spray', 'camera']
  },
  {
    id: 'device-002',
    name: '无人机-02',
    type: 'drone',
    status: 'busy',
    battery: 78,
    position: { x: 150, y: 180, z: 25 },
    lastActiveTime: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
    capabilities: ['spray', 'camera']
  },
  {
    id: 'device-003',
    name: '机器狗-01',
    type: 'robotDog',
    status: 'busy',
    battery: 58,
    position: { x: 200, y: 220, z: 0 },
    lastActiveTime: new Date(Date.now() - 3 * 60 * 1000).toISOString(),
    capabilities: ['patrol', 'camera', 'sensor']
  },
  {
    id: 'device-004',
    name: '机器狗-02',
    type: 'robotDog',
    status: 'busy',
    battery: 35,
    position: { x: 220, y: 280, z: 0 },
    lastActiveTime: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
    capabilities: ['patrol', 'spray', 'sensor']
  },
  {
    id: 'device-005',
    name: '履带机器人-01',
    type: 'other',
    status: 'busy',
    battery: 42,
    position: { x: 250, y: 200, z: 0 },
    lastActiveTime: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
    capabilities: ['spray', 'heavy']
  }
])

// 模拟任务事件数据
const taskEvents = ref<Record<string, TaskEvent[]>>({
  'task-001': [
    {
      id: 'event-001-1',
      taskId: 'task-001',
      deviceId: 'device-001',
      eventType: 'start',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      data: { location: { x: 100, y: 120 } },
      importance: 'normal',
      description: '任务开始执行'
    },
    {
      id: 'event-001-2',
      taskId: 'task-001',
      deviceId: 'device-001',
      eventType: 'checkpoint',
      timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(),
      data: { location: { x: 150, y: 130 }, areaCompleted: 1000 },
      importance: 'normal',
      description: '完成A区块喷洒'
    },
    {
      id: 'event-001-3',
      taskId: 'task-001',
      deviceId: 'device-002',
      eventType: 'warning',
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      data: { batterLevel: 70, location: { x: 180, y: 140 } },
      importance: 'important',
      description: '设备电量低于70%'
    },
    {
      id: 'event-001-4',
      taskId: 'task-001',
      deviceId: 'device-001',
      eventType: 'checkpoint',
      timestamp: new Date(Date.now() - 0.5 * 60 * 60 * 1000).toISOString(),
      data: { location: { x: 200, y: 150 }, areaCompleted: 2500 },
      importance: 'normal',
      description: '完成B区块喷洒'
    }
  ],
  'task-002': [
    {
      id: 'event-002-1',
      taskId: 'task-002',
      deviceId: 'device-003',
      eventType: 'start',
      timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
      data: { location: { x: 180, y: 200 } },
      importance: 'normal',
      description: '巡检任务开始执行'
    },
    {
      id: 'event-002-2',
      taskId: 'task-002',
      deviceId: 'device-003',
      eventType: 'data',
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      data: { 
        location: { x: 200, y: 210 }, 
        sensorData: {
          temperature: 28,
          humidity: 65,
          soilMoisture: 42
        }
      },
      importance: 'normal',
      description: '采集环境数据点#1'
    },
    {
      id: 'event-002-3',
      taskId: 'task-002',
      deviceId: 'device-003',
      eventType: 'checkpoint',
      timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
      data: { location: { x: 210, y: 230 }, areaCompleted: 1200 },
      importance: 'normal',
      description: '完成C2区块巡检'
    }
  ],
  'task-003': [
    {
      id: 'event-003-1',
      taskId: 'task-003',
      deviceId: 'device-004',
      eventType: 'start',
      timestamp: new Date(Date.now() - 80 * 60 * 1000).toISOString(),
      data: { location: { x: 220, y: 260 } },
      importance: 'important',
      description: '紧急消杀任务开始执行'
    },
    {
      id: 'event-003-2',
      taskId: 'task-003',
      deviceId: 'device-005',
      eventType: 'start',
      timestamp: new Date(Date.now() - 79 * 60 * 1000).toISOString(),
      data: { location: { x: 240, y: 190 } },
      importance: 'normal',
      description: '辅助设备加入消杀任务'
    },
    {
      id: 'event-003-3',
      taskId: 'task-003',
      deviceId: 'device-004',
      eventType: 'warning',
      timestamp: new Date(Date.now() - 50 * 60 * 1000).toISOString(),
      data: { pesticideLevel: 50, location: { x: 230, y: 270 } },
      importance: 'important',
      description: '杀虫剂用量过半'
    },
    {
      id: 'event-003-4',
      taskId: 'task-003',
      deviceId: 'device-004',
      eventType: 'checkpoint',
      timestamp: new Date(Date.now() - 40 * 60 * 1000).toISOString(),
      data: { location: { x: 235, y: 280 }, areaCompleted: 1800 },
      importance: 'normal',
      description: '完成N1区块消杀'
    },
    {
      id: 'event-003-5',
      taskId: 'task-003',
      deviceId: 'device-005',
      eventType: 'checkpoint',
      timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
      data: { location: { x: 245, y: 200 }, areaCompleted: 2000 },
      importance: 'normal',
      description: '完成N2区块消杀'
    }
  ]
})

// 优先级评分颜色
const priorityColors = ['#909399', '#e6a23c', '#409eff', '#67c23a', '#f56c6c']

// 计算属性 - 过滤后的任务
const filteredTasks = computed(() => {
  return runningTasks.value.filter(task => {
    const matchesKeyword = task.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    const matchesTaskType = selectedTaskType.value ? task.type === selectedTaskType.value : true
    
    // 设备类型筛选需要根据设备ID查找对应设备然后判断类型
    const matchesDeviceType = selectedDeviceType.value 
      ? task.deviceIds.some(id => {
          const device = devices.value.find(d => d.id === id)
          return device && device.type === selectedDeviceType.value
        })
      : true
    
    return matchesKeyword && matchesTaskType && matchesDeviceType
  })
})

// 计算属性 - 当前选中的任务
const currentTask = computed(() => {
  return runningTasks.value.find(task => task.id === selectedTaskId.value)
})

// 计算属性 - 当前任务的设备
const taskDevices = computed(() => {
  if (!currentTask.value) return []
  return devices.value.filter(device => 
    currentTask.value?.deviceIds.includes(device.id)
  )
})

// 计算属性 - 当前任务的事件
const currentTaskEvents = computed(() => {
  if (!currentTask.value) return []
  return (taskEvents.value[currentTask.value.id] || []).slice(0, 5) // 取最近5条事件
})

// 计算属性 - 当前任务的所有事件
const allTaskEvents = computed(() => {
  if (!currentTask.value) return []
  return taskEvents.value[currentTask.value.id] || []
})

// 计算属性 - 当前任务的进度历史
const currentTaskProgressHistory = computed(() => {
  if (!currentTask.value) return []
  return progressHistoryData.value[currentTask.value.id] || []
})

// 计算属性 - 当前任务的设备路径
const currentTaskDevicePaths = computed(() => {
  if (!currentTask.value) return []
  return devicePathsData.value[currentTask.value.id] || []
})

// 生命周期钩子
onMounted(() => {
  // 模拟API调用，获取数据
  if (runningTasks.value.length > 0) {
    // 默认选中第一个任务
    selectedTaskId.value = runningTasks.value[0].id
  }
})

// 方法 - 选择任务
const selectTask = (taskId: string) => {
  selectedTaskId.value = taskId
}

// 方法 - 刷新任务数据
const refreshTaskData = () => {
  // 模拟刷新数据
  if (currentTask.value) {
    // 更新进度
    const currentProgress = currentTask.value.progress
    const newProgress = Math.min(100, currentProgress + Math.floor(Math.random() * 5) + 1)
    currentTask.value.progress = newProgress
    
    // 更新资源使用
    currentTask.value.resources.forEach(resource => {
      if (resource.value < resource.total) {
        resource.value = Math.min(resource.total, resource.value + Math.random() * 5)
      }
    })
    
    // 更新进度历史
    const progressHistory = progressHistoryData.value[currentTask.value.id] || []
    progressHistory.push({
      time: new Date().toISOString(),
      progress: newProgress
    })
    if (progressHistory.length > 20) {
      progressHistory.shift()
    }
    progressHistoryData.value[currentTask.value.id] = progressHistory
    
    // 更新设备位置
    const devicePaths = devicePathsData.value[currentTask.value.id] || []
    devicePaths.forEach(devicePath => {
      const path = devicePath.path
      if (path.length > 0) {
        const lastPoint = path[path.length - 1]
        const newX = lastPoint.x + (Math.random() * 10 - 5)
        const newY = lastPoint.y + (Math.random() * 10 - 5)
        path.push({
          x: newX,
          y: newY,
          timestamp: new Date().toISOString(),
          type: Math.random() > 0.9 ? 'key' : 'normal'
        })
        if (path.length > 40) {
          path.shift()
        }
      }
    })
  }
  
  ElMessage.success('数据已更新')
}

// 方法 - 查看任务详情
const viewTaskDetail = () => {
  showTaskDetailDrawer.value = true
}

// 方法 - 获取任务图标
const getTaskIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'spray': 'TakeawayBox',
    'patrol': 'Connection',
    'disinfection': 'WarningFilled',
    'sampling': 'Odometer'
  }
  return iconMap[type] || 'Connection'
}

// 方法 - 获取设备类型标签
const getDeviceTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'drone': '无人机',
    'robotDog': '机器狗',
    'other': '履带机器人'
  }
  return typeMap[type] || '未知设备'
}

// 方法 - 获取任务类型标签
const getTaskTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'spray': '喷洒任务',
    'patrol': '巡检任务',
    'disinfection': '消杀任务',
    'sampling': '采样任务'
  }
  return typeMap[type] || '未知任务'
}

// 方法 - 获取任务状态标签
const getTaskStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '等待中',
    'running': '执行中',
    'paused': '已暂停',
    'completed': '已完成',
    'failed': '已失败'
  }
  return statusMap[status] || '未知状态'
}

// 方法 - 获取任务状态类型
const getTaskStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'info',
    'running': 'success',
    'paused': 'warning',
    'completed': '',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

// 方法 - 获取设备状态标签
const getDeviceStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'online': '在线',
    'offline': '离线',
    'busy': '工作中',
    'charging': '充电中',
    'error': '故障'
  }
  return statusMap[status] || '未知状态'
}

// 方法 - 获取设备状态类型
const getDeviceStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'online': 'success',
    'offline': 'info',
    'busy': 'warning',
    'charging': 'primary',
    'error': 'danger'
  }
  return statusMap[status] || 'info'
}

// 方法 - 获取设备名称
const getDeviceName = (deviceId: string) => {
  const device = devices.value.find(d => d.id === deviceId)
  return device ? device.name : '未知设备'
}

// 方法 - 获取设备名称列表
const getDeviceNames = (deviceIds: string[]) => {
  if (!deviceIds.length) return '无'
  
  const names = deviceIds.map(id => getDeviceName(id))
  if (names.length <= 2) return names.join('、')
  return `${names.slice(0, 2).join('、')} 等${names.length}个设备`
}

// 方法 - 获取进度颜色
const getProgressColor = (progress: number) => {
  if (progress < 30) return '#909399'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

// 方法 - 获取进度状态
const getProgressStatus = (progress: number) => {
  if (progress >= 100) return 'success'
  return ''
}

// 方法 - 获取电量颜色
const getBatteryColor = (battery: number) => {
  if (battery <= 20) return '#f56c6c'
  if (battery <= 50) return '#e6a23c'
  return '#67c23a'
}

// 方法 - 获取资源颜色
const getResourceColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'pesticide': '#f56c6c',
    'battery': '#e6a23c',
    'area': '#67c23a',
    'water': '#409eff'
  }
  return colorMap[type] || '#909399'
}

// 方法 - 获取事件类型
const getEventType = (eventType: string) => {
  const typeMap: Record<string, string> = {
    'start': 'success',
    'end': 'info',
    'checkpoint': 'primary',
    'warning': 'warning',
    'error': 'danger',
    'data': 'info'
  }
  return typeMap[eventType] || 'info'
}

// 方法 - 获取事件颜色
const getEventColor = (eventType: string) => {
  const colorMap: Record<string, string> = {
    'start': '#67c23a',
    'end': '#909399',
    'checkpoint': '#409eff',
    'warning': '#e6a23c',
    'error': '#f56c6c',
    'data': '#909399'
  }
  return colorMap[eventType] || '#909399'
}

// 方法 - 获取事件默认描述
const getDefaultEventDescription = (event: TaskEvent) => {
  const descMap: Record<string, string> = {
    'start': '任务开始执行',
    'end': '任务执行完成',
    'checkpoint': '达到任务检查点',
    'warning': '设备发出警告',
    'error': '设备发生错误',
    'data': '采集数据点'
  }
  return descMap[event.eventType] || '未知事件'
}

// 方法 - 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 方法 - 格式化时间（只显示时:分）
const formatTime = (dateString: string) => {
  if (!dateString) return '-'
  
  const date = new Date(dateString)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 方法 - 计算已执行时间
const getExecutionTime = (task: TaskProgressInfo) => {
  const startTime = new Date(task.startTime).getTime()
  const now = Date.now()
  const diffMinutes = Math.floor((now - startTime) / (60 * 1000))
  return diffMinutes
}

// 方法 - 显示事件数据
const showEventData = (event: TaskEvent) => {
  selectedEvent.value = event
  showEventDataDialog.value = true
}
</script>

<style scoped lang="scss">
.task-progress-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  
  .header-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    
    .filter-item {
      min-width: 120px;
    }
    
    .date-picker {
      width: 300px;
    }
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 15px;
    margin-bottom: 20px;
  }
  
  .metric-card {
    background-color: rgba(31, 41, 55, 0.5);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    
    &.primary {
      border-left: 4px solid #409eff;
      .metric-icon {
        color: #409eff;
        background-color: rgba(64, 158, 255, 0.1);
      }
    }
    
    &.success {
      border-left: 4px solid #67c23a;
      .metric-icon {
        color: #67c23a;
        background-color: rgba(103, 194, 58, 0.1);
      }
    }
    
    &.warning {
      border-left: 4px solid #e6a23c;
      .metric-icon {
        color: #e6a23c;
        background-color: rgba(230, 162, 60, 0.1);
      }
    }
    
    &.info {
      border-left: 4px solid #909399;
      .metric-icon {
        color: #909399;
        background-color: rgba(144, 147, 153, 0.1);
      }
    }
    
    .metric-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      .el-icon {
        font-size: 24px;
      }
    }
    
    .metric-content {
      flex: 1;
      
      .metric-value {
        font-size: 24px;
        font-weight: bold;
        color: #e5e7eb;
        margin-bottom: 4px;
      }
      
      .metric-label {
        font-size: 14px;
        color: #9ca3af;
      }
    }
    
    .metric-trend {
      font-size: 12px;
      color: #9ca3af;
      
      span {
        display: inline-flex;
        align-items: center;
        font-weight: bold;
        
        &.up {
          color: #67c23a;
        }
        
        &.down {
          color: #f56c6c;
        }
        
        .el-icon {
          margin-left: 2px;
        }
      }
    }
  }
  
  .main-content {
    display: flex;
    gap: 20px;
    flex: 1;
    overflow: hidden;
  }
  
  .task-list-panel {
    width: 350px;
    background-color: rgba(31, 41, 55, 0.5);
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        font-size: 18px;
        font-weight: bold;
        color: #e5e7eb;
        margin: 0;
      }
    }
    
    .search-input {
      margin-bottom: 16px;
    }
    
    .task-list {
      flex: 1;
      overflow-y: auto;
      
      .task-item {
        display: flex;
        align-items: center;
        padding: 12px;
        margin-bottom: 8px;
        border-radius: 8px;
        border: 1px solid rgba(75, 85, 99, 0.3);
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background-color: rgba(31, 41, 55, 0.7);
        }
        
        &.selected {
          background-color: rgba(59, 130, 246, 0.2);
          border-color: #409eff;
        }
        
        .task-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          
          &.spray {
            background-color: rgba(64, 158, 255, 0.1);
            color: #409eff;
          }
          
          &.patrol {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67c23a;
          }
          
          &.disinfection {
            background-color: rgba(245, 108, 108, 0.1);
            color: #f56c6c;
          }
          
          &.sampling {
            background-color: rgba(144, 147, 153, 0.1);
            color: #909399;
          }
        }
        
        .task-info {
          flex: 1;
          
          .task-name {
            font-weight: bold;
            color: #e5e7eb;
            margin-bottom: 4px;
          }
          
          .task-meta {
            font-size: 12px;
            color: #9ca3af;
            display: flex;
            
            .task-device {
              margin-right: 12px;
            }
          }
        }
      }
      
      .empty-tasks {
        padding: 40px 0;
        display: flex;
        justify-content: center;
      }
    }
  }
  
  .task-detail-panel {
    flex: 1;
    background-color: rgba(31, 41, 55, 0.5);
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    
    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .task-title {
        display: flex;
        align-items: center;
        
        h3 {
          font-size: 18px;
          font-weight: bold;
          color: #e5e7eb;
          margin: 0 12px 0 0;
        }
      }
      
      .task-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .task-brief {
      margin-bottom: 20px;
    }
    
    .chart-container {
      flex: 1;
      margin-bottom: 20px;
      
      .chart-wrapper {
        height: 350px;
        margin-top: 16px;
      }
    }
    
    .key-events {
      h4 {
        font-size: 16px;
        font-weight: bold;
        color: #e5e7eb;
        margin-bottom: 16px;
      }
      
      .event-content {
        color: #e5e7eb;
        
        .event-device {
          font-size: 12px;
          color: #9ca3af;
          margin-top: 4px;
        }
      }
    }
  }
  
  .select-task-placeholder {
    flex: 1;
    background-color: rgba(31, 41, 55, 0.5);
    border-radius: 8px;
    padding: 40px 20px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .task-detail-content {
    .detail-section {
      margin-top: 24px;
      
      h3 {
        font-size: 16px;
        font-weight: bold;
        color: #e5e7eb;
        margin-bottom: 16px;
      }
      
      .resource-card {
        background-color: rgba(31, 41, 55, 0.3);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        
        .resource-name {
          font-size: 14px;
          color: #9ca3af;
          margin-bottom: 4px;
        }
        
        .resource-value {
          font-size: 20px;
          font-weight: bold;
          color: #e5e7eb;
          margin-bottom: 8px;
        }
      }
      
      .event-content {
        color: #e5e7eb;
        
        .event-device, .event-data {
          font-size: 12px;
          color: #9ca3af;
          margin-top: 4px;
        }
      }
    }
  }
  
  .event-data-json {
    background-color: rgba(31, 41, 55, 0.3);
    border-radius: 8px;
    padding: 16px;
    font-family: monospace;
    font-size: 14px;
    color: #e5e7eb;
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
  }
  
  @media (max-width: 1200px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .main-content {
      flex-direction: column;
    }
    
    .task-list-panel {
      width: 100%;
      height: 300px;
    }
  }
}
</style> 