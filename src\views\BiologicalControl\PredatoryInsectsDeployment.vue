<!-- 
  PredatoryInsectsDeployment.vue
  天敌昆虫投放管理模块
  管理和监控天敌昆虫在农田中的投放位置、效果评估及历史记录
-->
<template>
  <div class="predatory-insects-deployment">
    <!-- 页面标题 -->
    <PageHeader
      title="天敌昆虫投放管理"
      description="管理和监控天敌昆虫在农田中的投放位置、效果评估及历史记录"
      icon="MapLocation"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ deploymentData.length }}</span>
            <span class="summary-label">投放总数</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ overallSuccessRate }}%</span>
            <span class="summary-label">成功率</span>
          </div>
        </div>
      </template>
    </PageHeader>

    <!-- 地图与数据面板双分屏布局 -->
    <div class="deployment-layout">
      <!-- 左侧：投放地图 -->
      <div class="deployment-map-container">
        <div class="map-filtering-toolbar">
          <div class="toolbar-title">投放筛选工具</div>
          <div class="toolbar-content">
            <el-form :model="filterForm" label-position="top" size="small">
              <el-form-item label="投放时间范围">
                <el-date-picker
                  v-model="filterForm.timeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item label="昆虫种类">
                <el-select 
                  v-model="filterForm.insectTypes" 
                  multiple 
                  placeholder="请选择昆虫种类"
                  collapse-tags
                >
                  <el-option 
                    v-for="item in insectTypeOptions" 
                    :key="item.value" 
                    :label="item.label" 
                    :value="item.value" 
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="投放效果">
                <el-select v-model="filterForm.effectiveness" placeholder="请选择投放效果">
                  <el-option 
                    v-for="item in effectivenessOptions" 
                    :key="item.value" 
                    :label="item.label" 
                    :value="item.value" 
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="resetFilters">重置筛选</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
        
        <div class="map-layer-control">
          <div class="layer-title">图层控制</div>
          <div class="layer-options">
            <el-checkbox 
              v-model="activeMapLayers" 
              label="recent"
            >
              近期投放(7天内)
            </el-checkbox>
            <el-checkbox 
              v-model="activeMapLayers" 
              label="early"
            >
              较早投放(30天内)
            </el-checkbox>
            <el-checkbox 
              v-model="activeMapLayers" 
              label="historical"
            >
              历史投放(30天前)
            </el-checkbox>
          </div>
        </div>
        
        <div 
          ref="mapRef"
          class="deployment-map"
          :class="{ 'map-loading': !mapLoaded }"
        >
          <div v-if="!mapLoaded" class="map-loading-indicator">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <span>地图加载中...</span>
          </div>
          <div v-else class="map-placeholder">
            <!-- 地图将在这里加载 -->
            <div class="map-markers">
              <div 
                v-for="item in filteredDeploymentData" 
                :key="item.id"
                class="map-marker"
                :class="[
                  getDeploymentTimeClass(item.deploymentTime),
                  getStatusClass(item.status)
                ]"
                v-show="activeMapLayers.includes(getDeploymentTimeClass(item.deploymentTime))"
                :style="{
                  left: `${(item.coordinates[0] - 116.2) * 500}px`,
                  top: `${(40 - item.coordinates[1]) * 500}px`
                }"
              >
                <el-tooltip 
                  effect="dark" 
                  :content="`${item.insectType} - ${item.location} - ${getStatusText(item.status)}`"
                  placement="top"
                >
                  <div class="marker-icon">
                    <i class="marker-dot"></i>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧：投放记录与评估数据面板 -->
      <div class="deployment-data-panel">
        <DataPanel title="投放记录">
          <template #actions>
            <el-input 
              placeholder="搜索记录..."
              prefix-icon="Search"
              size="small"
              style="width: 200px"
            />
          </template>
          
          <el-table 
            :data="filteredDeploymentData" 
            style="width: 100%"
            border
            stripe
            size="small"
          >
            <el-table-column prop="insectType" label="昆虫种类" width="100" />
            <el-table-column prop="quantity" label="投放数量" width="100" />
            <el-table-column prop="location" label="投放地点" width="120" />
            <el-table-column prop="deploymentTime" label="投放时间" width="160" />
            <el-table-column label="投放效果" width="100">
              <template #default="scope">
                <el-tag 
                  :type="
                    scope.row.status === 'success' ? 'success' :
                    scope.row.status === 'partial' ? 'warning' : 'danger'
                  "
                  size="small"
                >
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default>
                <el-button link type="primary" size="small">查看</el-button>
                <el-button link type="primary" size="small">编辑</el-button>
                <el-button link type="danger" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </DataPanel>
        
        <DataPanel title="投放效果评估摘要">
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-label">总体成功率</div>
              <div class="metric-value">{{ overallSuccessRate }}%</div>
              <div class="mini-chart success-rate-chart">
                <div class="chart-bar" :style="{ width: `${overallSuccessRate}%` }"></div>
              </div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">平均效益提升</div>
              <div class="metric-value">{{ averageBenefitIncrease }}%</div>
              <div class="mini-chart benefit-chart">
                <div class="chart-bar" :style="{ width: `${averageBenefitIncrease}%` }"></div>
              </div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">投放记录总数</div>
              <div class="metric-value">{{ deploymentData.length }}</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">成功投放次数</div>
              <div class="metric-value">
                {{ deploymentData.filter(item => item.status === 'success').length }}
              </div>
            </div>
          </div>
        </DataPanel>
        
        <DataPanel title="评估报告生成">
          <div class="report-actions">
            <el-button type="primary" @click="generateReport">
              <el-icon><DocumentAdd /></el-icon>
              生成详细报告
            </el-button>
            
            <el-dropdown @command="exportData">
              <el-button type="info">
                <el-icon><Download /></el-icon>
                导出数据
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="Excel">Excel 格式</el-dropdown-item>
                  <el-dropdown-item command="PDF">PDF 格式</el-dropdown-item>
                  <el-dropdown-item command="CSV">CSV 格式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </DataPanel>
      </div>
    </div>

    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="投放成功" />
        <StatusIndicator type="warning" label="部分成功" />
        <StatusIndicator type="error" label="投放失败" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(new Date()) }}</span>
        <el-button type="primary" size="small" plain @click="initMap">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  MapLocation,
  Loading,
  Search,
  DocumentAdd,
  Download,
  ArrowDown,
  Refresh
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'

// 定义类型
interface DeploymentData {
  id: number
  insectType: string
  quantity: number
  location: string
  coordinates: [number, number]
  deploymentTime: string
  status: 'success' | 'partial' | 'failed'
  effectivenessRate: number
  notes: string
}

interface FilterForm {
  timeRange: string[]
  insectTypes: string[]
  effectiveness: string
}

// 地图相关数据
const mapRef = ref<HTMLElement | null>(null)
const mapLoaded = ref(false)
const activeMapLayers = ref<string[]>(['recent', 'early', 'historical'])

// 投放数据
const deploymentData = ref<DeploymentData[]>([
  {
    id: 1,
    insectType: '瓢虫',
    quantity: 500,
    location: '西北区域',
    coordinates: [116.3, 39.9],
    deploymentTime: '2023-05-10 09:30:00',
    status: 'success',
    effectivenessRate: 85,
    notes: '在西北玉米区域释放，天气晴朗，24小时后观察到明显的蚜虫减少'
  },
  {
    id: 2,
    insectType: '螳螂',
    quantity: 200,
    location: '东南区域',
    coordinates: [116.35, 39.85],
    deploymentTime: '2023-05-01 14:20:00',
    status: 'partial',
    effectivenessRate: 65,
    notes: '在果园区域释放，部分区域效果明显，部分区域效果不佳'
  },
  {
    id: 3,
    insectType: '草蛉',
    quantity: 300,
    location: '中心区域',
    coordinates: [116.32, 39.88],
    deploymentTime: '2023-04-20 10:45:00',
    status: 'failed',
    effectivenessRate: 30,
    notes: '释放后遇到降雨，效果不佳'
  },
  {
    id: 4,
    insectType: '寄生蜂',
    quantity: 400,
    location: '西南区域',
    coordinates: [116.28, 39.86],
    deploymentTime: '2023-04-15 16:30:00',
    status: 'success',
    effectivenessRate: 90,
    notes: '针对小麦蚜虫释放，效果显著'
  }
])

// 筛选条件
const filterForm = reactive<FilterForm>({
  timeRange: [],
  insectTypes: [],
  effectiveness: '',
})

// 昆虫种类选项
const insectTypeOptions = [
  { label: '瓢虫', value: '瓢虫' },
  { label: '螳螂', value: '螳螂' },
  { label: '草蛉', value: '草蛉' },
  { label: '寄生蜂', value: '寄生蜂' }
]

// 效果评估选项
const effectivenessOptions = [
  { label: '全部', value: '' },
  { label: '成功', value: 'success' },
  { label: '部分成功', value: 'partial' },
  { label: '失败', value: 'failed' }
]

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 时间分类函数
const getDeploymentTimeClass = (deploymentTime: string) => {
  const now = new Date()
  const deploymentDate = new Date(deploymentTime)
  const diffDays = Math.floor((now.getTime() - deploymentDate.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 7) {
    return 'recent'
  } else if (diffDays < 30) {
    return 'early'
  } else {
    return 'historical'
  }
}

// 获取状态类名
const getStatusClass = (status: string) => {
  switch (status) {
    case 'success':
      return 'status-success'
    case 'partial':
      return 'status-partial'
    case 'failed':
      return 'status-failed'
    default:
      return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'success':
      return '成功'
    case 'partial':
      return '部分成功'
    case 'failed':
      return '失败'
    default:
      return '未知'
  }
}

// 筛选数据
const filteredDeploymentData = computed(() => {
  return deploymentData.value.filter(item => {
    // 时间范围筛选
    if (filterForm.timeRange && filterForm.timeRange.length === 2) {
      const deploymentDate = new Date(item.deploymentTime)
      const startDate = new Date(filterForm.timeRange[0])
      const endDate = new Date(filterForm.timeRange[1])
      if (deploymentDate < startDate || deploymentDate > endDate) {
        return false
      }
    }
    
    // 昆虫种类筛选
    if (filterForm.insectTypes && filterForm.insectTypes.length > 0) {
      if (!filterForm.insectTypes.includes(item.insectType)) {
        return false
      }
    }
    
    // 效果评估筛选
    if (filterForm.effectiveness && item.status !== filterForm.effectiveness) {
      return false
    }
    
    return true
  })
})

// 计算总体成功率
const overallSuccessRate = computed(() => {
  const total = deploymentData.value.length
  if (total === 0) return 0
  
  const successful = deploymentData.value.filter(item => item.status === 'success').length
  return Math.round((successful / total) * 100)
})

// 计算平均效益提升
const averageBenefitIncrease = computed(() => {
  const total = deploymentData.value.length
  if (total === 0) return 0
  
  const sum = deploymentData.value.reduce((acc, item) => acc + item.effectivenessRate, 0)
  return Math.round(sum / total)
})

// 初始化地图
const initMap = () => {
  mapLoaded.value = false
  // 模拟地图加载
  setTimeout(() => {
    mapLoaded.value = true
    ElMessage.success('地图加载成功')
  }, 1000)
}

// 重置筛选条件
const resetFilters = () => {
  filterForm.timeRange = []
  filterForm.insectTypes = []
  filterForm.effectiveness = ''
}

// 生成报告
const generateReport = () => {
  ElMessageBox.confirm('确定要生成详细报告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '报告生成成功，请在下方预览'
    })
  }).catch(() => {})
}

// 导出数据
const exportData = (type: string) => {
  ElMessage({
    type: 'success',
    message: `数据已导出为${type}格式`
  })
}

onMounted(() => {
  initMap()
})
</script>

<style scoped>
.predatory-insects-deployment {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

.deployment-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  flex: 1;
  margin-bottom: 20px;
  overflow: hidden;
}

/* 地图区域样式 */
.deployment-map-container {
  position: relative;
  background-color: #111827;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.deployment-map {
  width: 100%;
  height: 100%;
  min-height: 500px;
  background-color: #1a202c;
  position: relative;
}

.map-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #d1d5db;
}

.loading-icon {
  font-size: 24px;
  animation: spin 1s linear infinite;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23111827"/><path d="M0,0 L100,100 M0,100 L100,0" stroke="%23374151" stroke-width="0.5"/></svg>');
  position: relative;
}

.map-filtering-toolbar {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 250px;
  background-color: rgba(31, 41, 55, 0.9);
  border-radius: 8px;
  padding: 10px;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.toolbar-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #ffffff;
  border-bottom: 1px solid #4b5563;
  padding-bottom: 5px;
}

.map-layer-control {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 200px;
  background-color: rgba(31, 41, 55, 0.9);
  border-radius: 8px;
  padding: 10px;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.layer-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #ffffff;
  border-bottom: 1px solid #4b5563;
  padding-bottom: 5px;
}

.layer-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 地图标记样式 */
.map-markers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.map-marker {
  position: absolute;
  width: 20px;
  height: 20px;
  cursor: pointer;
  transform: translate(-50%, -50%);
}

.marker-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.marker-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: block;
}

/* 投放时间分类颜色 */
.map-marker.recent .marker-dot {
  background-color: #3b82f6; /* 蓝色 - 近期投放 */
  box-shadow: 0 0 8px #3b82f6;
}

.map-marker.early .marker-dot {
  background-color: #10b981; /* 绿色 - 较早投放 */
  box-shadow: 0 0 8px #10b981;
}

.map-marker.historical .marker-dot {
  background-color: #f59e0b; /* 黄色 - 历史投放 */
  box-shadow: 0 0 8px #f59e0b;
}

/* 效果状态样式 */
.map-marker.status-success {
  animation: pulse 2s infinite;
}

.map-marker.status-partial {
  animation: pulse 3s infinite;
}

.map-marker.status-failed {
  opacity: 0.6;
}

/* 数据面板样式 */
.deployment-data-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  height: 100%;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.metric-item {
  background-color: rgba(31, 41, 55, 0.7);
  border-radius: 6px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.metric-label {
  font-size: 0.9rem;
  color: #d1d5db;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #f9fafb;
}

.mini-chart {
  height: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  margin-top: 8px;
  overflow: hidden;
}

.chart-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}

.report-actions {
  display: flex;
  gap: 15px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.1); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .deployment-layout {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style> 