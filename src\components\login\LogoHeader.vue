<template>
  <div class="logo-header">
    <div class="logo-container">
      <img src="@/assets/logo.svg" alt="Logo" class="logo-image" />
      <div class="logo-glow"></div>
    </div>
    <h1 class="system-title">智慧农业病虫防治系统</h1>
    <p class="system-subtitle">科技创新 · 智慧农业 · 绿色防控</p>
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style lang="scss" scoped>
@use '@/styles/login.scss' as login;

.logo-header {
  text-align: center;
  margin-bottom: 2.5rem;
  animation: fadeInDown 1s ease-out;
  position: relative;
  
  .logo-container {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 1.5rem;
    
    .logo-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      filter: drop-shadow(0 4px 8px login.$shadow-color);
      transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      z-index: 2;
      position: relative;
      
      &:hover {
        transform: scale(1.08) rotate(5deg);
        filter: drop-shadow(0 6px 12px login.$shadow-color) brightness(1.1);
        
        & + .logo-glow {
          opacity: 0.8;
          transform: scale(1.2);
        }
      }
    }
    
    .logo-glow {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 90%;
      height: 90%;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(0, 255, 170, 0.4) 0%, rgba(0, 255, 170, 0) 70%);
      transform: translate(-50%, -50%);
      opacity: 0.5;
      z-index: 1;
      transition: all 0.5s ease;
      animation: pulse 3s infinite alternate;
    }
  }
  
  .system-title {
    font-size: 2rem;
    color: login.$text-light;
    margin: 0 0 0.5rem;
    font-weight: 600;
    text-shadow: 0 2px 10px login.$shadow-color;
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60%;
      height: 2px;
      background: linear-gradient(90deg, rgba(0, 255, 170, 0), rgba(0, 255, 170, 0.8), rgba(0, 255, 170, 0));
    }
  }
  
  .system-subtitle {
    font-size: 1rem;
    color: login.$text-secondary;
    margin: 1rem 0 0;
    font-weight: 300;
    letter-spacing: 2px;
    animation: fadeIn 1.5s ease-out 0.5s both;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.9);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@media (max-width: 480px) {
  .logo-header {
    margin-bottom: 2rem;
    
    .logo-container {
      width: 80px;
      height: 80px;
      margin-bottom: 1rem;
    }
    
    .system-title {
      font-size: 1.5rem;
    }
    
    .system-subtitle {
      font-size: 0.85rem;
      letter-spacing: 1px;
    }
  }
}
</style> 