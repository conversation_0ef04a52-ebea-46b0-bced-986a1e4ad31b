<!-- 
  UAVSprayingConfig.vue
  无人机喷洒参数配置模块
  配置无人机喷洒农药的各项参数，包括飞行高度、速度、喷洒量等
-->
<template>
  <div class="uav-spraying-config">
    <!-- 页面标题 -->
    <PageHeader
      title="无人机喷洒参数配置"
      description="配置无人机喷洒农药的各项参数，包括飞行高度、速度、喷洒量等"
      icon="Crop"
    >
      <template #actions>
        <div class="step-indicator">
          <span>步骤 {{ currentStep }}/4: {{ stepTitles[currentStep-1] }}</span>
        </div>
      </template>
    </PageHeader>

    <!-- 步骤指示器 -->
    <div class="step-progress">
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step v-for="(title, index) in stepTitles" :key="index" :title="title" />
      </el-steps>
    </div>

    <!-- 表单内容区域 -->
    <div class="config-content">
      <div class="config-form">
        <!-- 步骤1: 基础参数设置 -->
        <div v-if="currentStep === 1" class="step-container">
          <el-form :model="configForm" label-position="top">
            <el-form-item label="无人机型号">
              <div class="uav-model-selector">
                <div 
                  v-for="model in uavModels" 
                  :key="model.value"
                  :class="['uav-model-item', { active: configForm.uavModel === model.value }]"
                  @click="configForm.uavModel = model.value"
                >
                  <el-icon class="model-icon"><Aim /></el-icon>
                  <span class="model-name">{{ model.label }}</span>
                </div>
              </div>
            </el-form-item>
            
            <el-form-item label="飞行模式">
              <el-radio-group v-model="configForm.flightMode">
                <el-radio label="manual">手动模式</el-radio>
                <el-radio label="semi_auto">半自动模式</el-radio>
                <el-radio label="full_auto">全自动模式</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="农田区域">
              <el-select
                v-model="configForm.farmAreaId"
                filterable
                placeholder="选择农田区域"
                style="width: 100%"
              >
                <el-option
                  v-for="area in farmAreas"
                  :key="area.id"
                  :label="area.name"
                  :value="area.id"
                >
                  <span>{{ area.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ area.area }}亩
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          
          <div class="step-actions">
            <el-button type="primary" @click="nextStep">下一步</el-button>
          </div>
        </div>
        
        <!-- 步骤2: 喷洒参数配置 -->
        <div v-if="currentStep === 2" class="step-container">
          <el-form :model="configForm" label-position="top">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="飞行高度 (米)">
                  <el-input-number
                    v-model="configForm.flightHeight"
                    :min="0.5"
                    :max="10"
                    :step="0.1"
                    :precision="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="飞行速度 (米/秒)">
                  <el-input-number
                    v-model="configForm.flightSpeed"
                    :min="0.5"
                    :max="10"
                    :step="0.1"
                    :precision="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="喷洒量 (升/亩)">
                  <el-input-number
                    v-model="configForm.sprayingQuantity"
                    :min="1"
                    :max="100"
                    :step="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="喷雾粒径">
                  <el-select
                    v-model="configForm.dropletSize"
                    placeholder="选择喷雾粒径"
                    style="width: 100%"
                  >
                    <el-option label="细雾" value="fine" />
                    <el-option label="中雾" value="medium" />
                    <el-option label="粗雾" value="coarse" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" @click="nextStep">下一步</el-button>
          </div>
        </div>
        
        <!-- 步骤3: 环境与安全设置 -->
        <div v-if="currentStep === 3" class="step-container">
          <el-form :model="configForm" label-position="top">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="风速 (米/秒)">
                  <el-input-number
                    v-model="configForm.windSpeed"
                    :min="0"
                    :max="20"
                    :step="0.1"
                    :precision="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="风向">
                  <el-select
                    v-model="configForm.windDirection"
                    placeholder="选择风向"
                    style="width: 100%"
                  >
                    <el-option label="北风" value="north" />
                    <el-option label="东北风" value="northeast" />
                    <el-option label="东风" value="east" />
                    <el-option label="东南风" value="southeast" />
                    <el-option label="南风" value="south" />
                    <el-option label="西南风" value="southwest" />
                    <el-option label="西风" value="west" />
                    <el-option label="西北风" value="northwest" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="与障碍物安全距离 (米)">
              <el-slider
                v-model="configForm.safetyDistanceToObstacles"
                :min="1"
                :max="20"
                :step="1"
                show-input
              />
            </el-form-item>
            
            <el-form-item label="与非目标区域安全距离 (米)">
              <el-slider
                v-model="configForm.safetyDistanceToNonTargetAreas"
                :min="1"
                :max="50"
                :step="1"
                show-input
              />
            </el-form-item>
            
            <el-form-item label="紧急制动配置">
              <el-switch
                v-model="configForm.emergencyBraking"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="switch-help-text">检测到障碍物时自动刹车悬停</div>
            </el-form-item>
          </el-form>
          
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" @click="nextStep">下一步</el-button>
          </div>
        </div>
        
        <!-- 步骤4: 配置确认与保存 -->
        <div v-if="currentStep === 4" class="step-container">
          <div class="config-summary">
            <h3>配置摘要</h3>
            <el-descriptions direction="vertical" :column="2" border>
              <el-descriptions-item label="无人机型号">
                {{ getModelLabel(configForm.uavModel) }}
              </el-descriptions-item>
              <el-descriptions-item label="飞行模式">
                {{ getFlightModeLabel(configForm.flightMode) }}
              </el-descriptions-item>
              <el-descriptions-item label="农田区域">
                {{ getFarmAreaName(configForm.farmAreaId) }}
              </el-descriptions-item>
              <el-descriptions-item label="飞行高度">
                {{ configForm.flightHeight }} 米
              </el-descriptions-item>
              <el-descriptions-item label="飞行速度">
                {{ configForm.flightSpeed }} 米/秒
              </el-descriptions-item>
              <el-descriptions-item label="喷洒量">
                {{ configForm.sprayingQuantity }} 升/亩
              </el-descriptions-item>
              <el-descriptions-item label="喷雾粒径">
                {{ getDropletSizeLabel(configForm.dropletSize) }}
              </el-descriptions-item>
              <el-descriptions-item label="风速">
                {{ configForm.windSpeed }} 米/秒
              </el-descriptions-item>
              <el-descriptions-item label="风向">
                {{ configForm.windDirection }}
              </el-descriptions-item>
              <el-descriptions-item label="障碍物安全距离">
                {{ configForm.safetyDistanceToObstacles }} 米
              </el-descriptions-item>
              <el-descriptions-item label="非目标区域安全距离">
                {{ configForm.safetyDistanceToNonTargetAreas }} 米
              </el-descriptions-item>
              <el-descriptions-item label="紧急制动">
                {{ configForm.emergencyBraking ? '开启' : '关闭' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button 
              type="primary" 
              :icon="Document"
              :loading="saving"
              @click="saveConfiguration"
            >
              {{ saving ? '保存中...' : '保存配置' }}
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 右侧实时预览面板 -->
      <div class="preview-panel">
        <div class="panel-header">
          <h3>实时预览</h3>
        </div>
        <div class="panel-content">
          <div class="uav-model">
            <div class="uav-icon">
              <el-icon><DArrowLeft /></el-icon>
              <span>无人机</span>
              <el-icon><DArrowRight /></el-icon>
            </div>
            
            <div class="uav-status">
              <div class="status-item">
                <span class="status-label">飞行高度:</span>
                <span class="status-value">{{ configForm.flightHeight }} 米</span>
              </div>
              <div class="status-item">
                <span class="status-label">飞行速度:</span>
                <span class="status-value">{{ configForm.flightSpeed }} 米/秒</span>
              </div>
              <div class="status-item">
                <span class="status-label">喷洒量:</span>
                <span class="status-value">{{ configForm.sprayingQuantity }} 升/亩</span>
              </div>
              <div class="status-item">
                <span class="status-label">粒径:</span>
                <span class="status-value">{{ getDropletSizeLabel(configForm.dropletSize) }}</span>
              </div>
            </div>
          </div>
          
          <div class="spray-simulation">
            <div class="simulation-label">喷洒范围模拟</div>
            <div 
              class="simulation-area"
              :style="{
                '--spray-width': `${configForm.flightHeight * 2}px`,
                '--spray-density': getSprayDensity()
              }"
            >
              <div class="uav-position"></div>
              <div class="spray-pattern"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 底部状态指示器 -->
  <div class="status-indicators">
    <div class="indicator-group">
      <StatusIndicator type="success" label="参数已配置" />
      <StatusIndicator type="warning" label="喷洒计划中" />
      <StatusIndicator type="normal" label="待执行" />
    </div>
    <div class="refresh-info">
      <span>上次保存时间: {{ formatTime(lastSaveTime) }}</span>
      <el-button type="primary" size="small" plain @click="resetForm">
        <el-icon><RefreshRight /></el-icon>
        重置参数
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection, 
  DArrowLeft, 
  DArrowRight,
  Aim,
  Document,
  Crop,
  RefreshRight
} from '@element-plus/icons-vue'
import { uavSprayingApi } from '@/api/pesticideApi'
import type { UAVSprayingParameters } from '@/types/pesticide'

// 导入自定义组件
import PageHeader from '../DeviceManagement/components/PageHeader.vue'
import StatusIndicator from '../DeviceManagement/components/StatusIndicator.vue'

// 当前步骤
const currentStep = ref(1)

// 步骤标题
const stepTitles = [
  '基础参数设置',
  '喷洒参数配置',
  '环境与安全设置',
  '配置确认与保存'
]

// 无人机型号列表
const uavModels = [
  { label: 'DJI Agras T30', value: 'dji_agras_t30' },
  { label: 'DJI Agras T20P', value: 'dji_agras_t20p' },
  { label: 'DJI Agras T10', value: 'dji_agras_t10' },
  { label: 'XAG P100', value: 'xag_p100' },
  { label: 'XAG P50', value: 'xag_p50' },
  { label: 'XAG P40', value: 'xag_p40' }
]

// 农田区域列表
const farmAreas = ref([
  { id: 'area1', name: '东部水稻田', area: 120 },
  { id: 'area2', name: '西部玉米田', area: 85 },
  { id: 'area3', name: '南部蔬菜地', area: 45 },
  { id: 'area4', name: '北部果园', area: 60 }
])

// 配置表单
const configForm = reactive<Omit<UAVSprayingParameters, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>>({
  uavModel: '',
  flightMode: 'semi_auto',
  farmAreaId: '',
  farmAreaName: '',
  flightHeight: 2.0,
  flightSpeed: 3.0,
  sprayingQuantity: 15,
  dropletSize: 'medium',
  windSpeed: 0,
  windDirection: 'north',
  safetyDistanceToObstacles: 5,
  safetyDistanceToNonTargetAreas: 10,
  emergencyBraking: true
})

// 保存状态
const saving = ref(false)

// 最后保存时间
const lastSaveTime = ref<Date | null>(null)

// 格式化时间
const formatTime = (date: Date | null) => {
  if (!date) return '暂无记录'
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 获取无人机型号显示名称
const getModelLabel = (value: string) => {
  const model = uavModels.find(m => m.value === value)
  return model ? model.label : value
}

// 获取飞行模式显示名称
const getFlightModeLabel = (value: string) => {
  const modes: Record<string, string> = {
    'manual': '手动模式',
    'semi_auto': '半自动模式',
    'full_auto': '全自动模式'
  }
  return modes[value] || value
}

// 获取农田区域名称
const getFarmAreaName = (id: string) => {
  const area = farmAreas.value.find(a => a.id === id)
  return area ? area.name : id
}

// 获取喷雾粒径显示名称
const getDropletSizeLabel = (value: string) => {
  const sizes: Record<string, string> = {
    'fine': '细雾',
    'medium': '中雾',
    'coarse': '粗雾'
  }
  return sizes[value] || value
}

// 获取喷洒密度样式值
const getSprayDensity = () => {
  const densities: Record<string, string> = {
    'fine': 'rgba(59, 130, 246, 0.7)',
    'medium': 'rgba(59, 130, 246, 0.5)',
    'coarse': 'rgba(59, 130, 246, 0.3)'
  }
  return densities[configForm.dropletSize] || 'rgba(59, 130, 246, 0.5)'
}

// 下一步
const nextStep = () => {
  if (validateCurrentStep()) {
    if (currentStep.value < 4) {
      currentStep.value++
    }
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 验证当前步骤
const validateCurrentStep = () => {
  if (currentStep.value === 1) {
    if (!configForm.uavModel) {
      ElMessage.warning('请选择无人机型号')
      return false
    }
    if (!configForm.farmAreaId) {
      ElMessage.warning('请选择农田区域')
      return false
    }
  }
  
  return true
}

// 保存配置
const saveConfiguration = async () => {
  // 设置农田区域名称
  const selectedArea = farmAreas.value.find(a => a.id === configForm.farmAreaId)
  if (selectedArea) {
    configForm.farmAreaName = selectedArea.name
  }
  
  saving.value = true
  try {
    await uavSprayingApi.saveConfiguration(configForm)
    lastSaveTime.value = new Date()
    ElMessage.success('喷洒参数配置保存成功')
  } catch (error) {
    ElMessage.error('保存配置失败')
    console.error('Failed to save configuration:', error)
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  ElMessage.warning('即将重置所有参数')
  
  configForm.uavModel = ''
  configForm.flightMode = 'semi_auto'
  configForm.farmAreaId = ''
  configForm.farmAreaName = ''
  configForm.flightHeight = 2.0
  configForm.flightSpeed = 3.0
  configForm.sprayingQuantity = 15
  configForm.dropletSize = 'medium'
  configForm.windSpeed = 0
  configForm.windDirection = 'north'
  configForm.safetyDistanceToObstacles = 5
  configForm.safetyDistanceToNonTargetAreas = 10
  configForm.emergencyBraking = true
  
  currentStep.value = 1
}

// 获取无人机型号和农田区域数据
const fetchInitialData = async () => {
  try {
    // 获取无人机型号列表
    const models = await uavSprayingApi.getUAVModels()
    if (models.length > 0) {
      // 可以更新 uavModels
    }
    
    // 获取农田区域列表
    const areas = await uavSprayingApi.getFarmAreas()
    if (areas.length > 0) {
      farmAreas.value = areas
    }
  } catch (error) {
    console.error('Failed to fetch initial data:', error)
  }
}

// 加载初始数据
fetchInitialData()
</script>

<style scoped>
.uav-spraying-config {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 步骤进度条 */
.step-progress {
  padding: 16px 20px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
}

.step-indicator {
  font-size: 16px;
  font-weight: 500;
  color: #e5e7eb;
  background-color: rgba(59, 130, 246, 0.2);
  padding: 6px 12px;
  border-radius: 4px;
}

/* 配置内容区域 */
.config-content {
  display: flex;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

.config-form {
  flex: 3;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
}

.step-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 无人机型号选择器 */
.uav-model-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.uav-model-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  background-color: rgba(31, 41, 55, 0.7);
  cursor: pointer;
  transition: all 0.3s;
}

.uav-model-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.uav-model-item.active {
  background-color: rgba(59, 130, 246, 0.2);
  border: 1px solid #3b82f6;
}

.model-icon {
  font-size: 28px;
  color: #e5e7eb;
}

.model-name {
  font-size: 14px;
  font-weight: 500;
  color: #e5e7eb;
}

.switch-help-text {
  margin-top: 8px;
  font-size: 12px;
  color: #9ca3af;
}

/* 配置摘要 */
.config-summary {
  margin-bottom: 20px;
}

.config-summary h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #e5e7eb;
}

/* 步骤操作按钮 */
.step-actions {
  margin-top: auto;
  padding-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 预览面板 */
.preview-panel {
  flex: 2;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  background-color: rgba(59, 130, 246, 0.2);
  padding: 16px;
}

.panel-header h3 {
  margin: 0;
  color: #e5e7eb;
}

.panel-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 无人机状态 */
.uav-model {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.uav-icon {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  color: #e5e7eb;
}

.uav-status {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.status-item {
  display: flex;
  flex-direction: column;
}

.status-label {
  font-size: 12px;
  color: #9ca3af;
}

.status-value {
  font-weight: 500;
  color: #e5e7eb;
}

/* 喷洒模拟 */
.spray-simulation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.simulation-label {
  font-size: 16px;
  font-weight: 500;
  color: #e5e7eb;
}

.simulation-area {
  flex: 1;
  position: relative;
  background-color: rgba(31, 41, 55, 0.8);
  border-radius: 8px;
  overflow: hidden;
}

.uav-position {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background-color: #3b82f6;
  border-radius: 50%;
  z-index: 2;
}

.spray-pattern {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: var(--spray-width, 40px);
  height: calc(100% - 40px);
  background: linear-gradient(to bottom, var(--spray-density, rgba(59, 130, 246, 0.5)) 0%, transparent 100%);
  border-radius: 50% 50% 0 0;
  z-index: 1;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .config-content {
    flex-direction: column;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>

<!--
注意: 此组件需要以下依赖:
1. @/api/pesticideApi.ts - 包含uavSprayingApi对象和相关方法
2. @/types/pesticide.ts - 包含UAVSprayingParameters类型定义
3. ../DeviceManagement/components/PageHeader.vue - 页面标题组件
4. ../DeviceManagement/components/StatusIndicator.vue - 状态指示器组件

如果这些文件不存在，请先创建它们。
--> 