<template>
  <BaseLayout
    title="AI任务调度系统"
    theme="taskScheduling"
    themeColor="#10b981"
    moduleIcon="Van"
    ref="baseLayoutRef"
  >
    <template #menu>
      <TechMenuItem
        title="智能路径规划引擎"
        icon="Location"
        route="/task-scheduling/path-planning"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="周期性巡航任务编排"
        icon="Calendar"
        route="/task-scheduling/periodic-tasks"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="应急消杀任务插队机制"
        icon="Warning"
        route="/task-scheduling/emergency-tasks"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="多机协同工作模式"
        icon="Connection"
        route="/task-scheduling/multi-device-collaboration"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="任务执行进度可视化"
        icon="PieChart"
        route="/task-scheduling/task-progress"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
      <TechMenuItem
        title="历史轨迹回放功能"
        icon="VideoPlay"
        route="/task-scheduling/history-trajectory"
        :collapsed="isAsideCollapsed"
        themeColor="#10b981"
      />
    </template>
    
    <template #header-actions>
      <div class="task-stats">
        <div class="stat-item">
          <el-icon><Clock /></el-icon>
          <span class="stat-value">22</span>
          <span class="stat-label">活跃任务</span>
        </div>
        <div class="stat-item">
          <el-icon><Check /></el-icon>
          <span class="stat-value">108</span>
          <span class="stat-label">已完成</span>
        </div>
      </div>
      
      <TechActionButton
        type="primary"
        size="small"
        icon="Plus"
        text="新建任务"
        @click="createNewTask"
      />
    </template>
    
    <router-view />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Location,
  Calendar,
  Warning,
  Connection,
  PieChart,
  VideoPlay,
  HomeFilled,
  Van,
  Clock,
  Check,
  Plus
} from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'
import BaseLayout from './components/BaseLayout.vue'
import TechMenuItem from './components/TechMenuItem.vue'
import TechActionButton from './components/TechActionButton.vue'
import { getModuleTheme } from './components/layoutConfig'

const route = useRoute()
const router = useRouter()
const isAsideCollapsed = ref(false)
const baseLayoutRef = ref(null)

// 监听侧边栏折叠状态
const handleCollapseChange = (collapsed: boolean) => {
  isAsideCollapsed.value = collapsed
}

// 创建新任务
const createNewTask = () => {
  ElNotification({
    title: '任务创建',
    message: '正在打开任务创建向导...',
    type: 'info',
    duration: 2000
  })
  
  // 这里可以添加实际的任务创建逻辑
  setTimeout(() => {
    router.push('/task-scheduling/create-task')
  }, 500)
}

// 组件挂载
onMounted(() => {
  // 监听BaseLayout事件
  if (baseLayoutRef.value) {
    baseLayoutRef.value.$on('collapse-change', handleCollapseChange)
  }
})
</script>

<style scoped>
.task-stats {
  display: flex;
  gap: 15px;
  margin-right: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(16, 185, 129, 0.1);
  padding: 6px 12px;
  border-radius: 8px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(16, 185, 129, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.2);
}

.stat-value {
  font-weight: 700;
  color: #fff;
  font-size: 16px;
}

.stat-label {
  color: #d1d5db;
  font-size: 14px;
}

@media (max-width: 1024px) {
  .task-stats {
    gap: 10px;
  }
  
  .stat-item {
    padding: 4px 8px;
  }
}

@media (max-width: 768px) {
  .task-stats {
    display: none;
  }
}
</style> 