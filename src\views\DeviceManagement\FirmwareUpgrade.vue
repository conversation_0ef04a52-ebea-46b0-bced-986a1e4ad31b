<!-- 
  FirmwareUpgrade.vue
  固件远程升级系统模块
  管理和更新智能设备的固件版本，提升设备性能和功能
-->
<template>
  <div class="firmware-upgrade">
    <!-- 页面标题 -->
    <PageHeader
      title="固件远程升级系统"
      description="管理和更新智能设备的固件版本，提升设备性能和功能"
      icon="Connection"
    >
      <template #actions>
        <div class="upgrade-summary">
          <div class="summary-item">
            <span class="summary-value">{{ deviceStats.updatable }}</span>
            <span class="summary-label">可升级设备</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ deviceStats.upgrading }}</span>
            <span class="summary-label">升级中</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 主要内容区域 -->
    <div class="upgrade-container">
      <!-- 设备列表区域 -->
      <div class="devices-area">
        <DataPanel title="可升级设备列表">
          <template #actions>
            <div class="panel-actions">
              <el-checkbox 
                v-model="allSelected" 
                :indeterminate="isIndeterminate"
                @change="handleSelectAllChange"
              >
                选择全部
              </el-checkbox>
              <el-button 
                type="primary" 
                size="small"
                :disabled="selectedCount === 0" 
                @click="startUpgrade"
              >
                <el-icon><Download /></el-icon>
                开始升级
              </el-button>
            </div>
          </template>
          
          <div class="devices-list">
            <div v-for="device in devices" :key="device.id" class="device-item" :class="{ upgrading: device.upgrading }">
              <el-checkbox 
                v-model="device.selected" 
                :disabled="device.upgrading || device.upgraded"
                @change="handleDeviceSelectChange"
              />
              
              <div class="device-info">
                <div class="device-name-area">
                  <h4 class="device-name">{{ device.name }}</h4>
                  <div class="device-model">{{ device.model }}</div>
                </div>
                
                <div class="firmware-versions">
                  <div class="current-version">
                    <span class="version-label">当前版本</span>
                    <span class="version-tag">{{ device.currentVersion }}</span>
                  </div>
                  <div class="version-arrow">→</div>
                  <div class="available-version">
                    <span class="version-label">可升级版本</span>
                    <span class="version-tag new">{{ device.availableVersion }}</span>
                  </div>
                </div>
                
                <div v-if="device.upgrading" class="upgrade-progress">
                  <div class="progress-info">
                    <div class="progress-percentage">{{ device.progress }}%</div>
                    <div class="progress-status">{{ getProgressStatus(device.progress) }}</div>
                  </div>
                  <el-progress 
                    :percentage="device.progress" 
                    :status="getProgressType(device.progress)"
                    :stroke-width="8"
                    :show-text="false"
                  />
                </div>
                
                <div v-else-if="device.upgraded" class="upgrade-complete">
                  <div class="complete-icon"></div>
                  <div class="complete-info">
                    <div class="complete-status">升级完成</div>
                    <div class="complete-time">{{ device.upgradeTime }}</div>
                  </div>
                </div>
                
                <div v-else class="device-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="startSingleUpgrade(device)"
                  >
                    升级
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click="viewReleaseNotes(device)"
                  >
                    查看更新日志
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </DataPanel>
      </div>
      
      <!-- 升级信息面板 -->
      <div class="upgrade-info">
        <DataPanel title="固件升级信息">
          <div class="upgrade-stats">
            <div class="stat-card">
              <div class="stat-value">{{ deviceStats.total }}</div>
              <div class="stat-label">设备总数</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ deviceStats.updatable }}</div>
              <div class="stat-label">可升级设备</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ deviceStats.upgrading }}</div>
              <div class="stat-label">升级中</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ deviceStats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
          
          <div class="section-divider">
            <h4 class="section-title">最近升级记录</h4>
          </div>
          <div class="updates-list">
            <div v-for="(update, index) in recentUpdates" :key="index" class="update-item">
              <div class="update-time">{{ update.time }}</div>
              <div class="update-content">
                <div class="update-device">{{ update.deviceName }}</div>
                <div class="update-detail">
                  <span class="old-version">{{ update.oldVersion }}</span>
                  <span class="arrow">→</span>
                  <span class="new-version">{{ update.newVersion }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section-divider">
            <h4 class="section-title">固件存储库</h4>
          </div>
          <div class="repository-list">
            <div v-for="(firmware, index) in firmwareRepository" :key="index" class="repository-item">
              <div class="firmware-info">
                <div class="firmware-name">{{ firmware.name }}</div>
                <div class="firmware-version">v{{ firmware.version }}</div>
              </div>
              <div class="firmware-meta">
                <div class="release-date">发布日期: {{ firmware.releaseDate }}</div>
                <div class="file-size">{{ firmware.fileSize }}</div>
              </div>
              <div class="firmware-actions">
                <el-button 
                  size="small" 
                  @click="viewFirmwareDetails(firmware)"
                >
                  详情
                </el-button>
              </div>
            </div>
          </div>
        </DataPanel>
      </div>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="升级完成" />
        <StatusIndicator type="warning" label="升级中" />
        <StatusIndicator type="error" label="升级失败" />
        <StatusIndicator type="normal" label="可升级" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 更新日志对话框 -->
    <el-dialog
      v-model="dialogState.releaseNotesVisible"
      title="固件更新日志"
      width="600px"
      class="release-notes-dialog"
    >
      <div v-if="dialogState.currentFirmware" class="release-notes">
        <div class="firmware-header">
          <div class="firmware-title">{{ dialogState.currentFirmware.name }} v{{ dialogState.currentFirmware.version }}</div>
          <div class="release-date">发布日期: {{ dialogState.currentFirmware.releaseDate }}</div>
        </div>
        
        <div class="changes-list">
          <div class="changes-category">
            <h4 class="category-title">新功能</h4>
            <ul class="changes">
              <li v-for="(feature, index) in dialogState.currentFirmware.changes.features" :key="index">
                {{ feature }}
              </li>
            </ul>
          </div>
          
          <div class="changes-category">
            <h4 class="category-title">优化改进</h4>
            <ul class="changes">
              <li v-for="(improvement, index) in dialogState.currentFirmware.changes.improvements" :key="index">
                {{ improvement }}
              </li>
            </ul>
          </div>
          
          <div class="changes-category">
            <h4 class="category-title">问题修复</h4>
            <ul class="changes">
              <li v-for="(bugfix, index) in dialogState.currentFirmware.changes.bugfixes" :key="index">
                {{ bugfix }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 升级确认对话框 -->
    <el-dialog
      v-model="dialogState.upgradeConfirmVisible"
      title="确认固件升级"
      width="500px"
      class="upgrade-confirm-dialog"
    >
      <div class="confirm-content">
        <div class="warning-icon"></div>
        <div class="confirm-message">
          您确定要升级以下设备的固件吗？升级过程中设备将暂时无法使用。
        </div>
        
        <div class="devices-to-upgrade">
          <div v-for="device in selectedDevices" :key="device.id" class="upgrade-device-item">
            <div class="device-name">{{ device.name }}</div>
            <div class="version-change">
              <span class="current-version">{{ device.currentVersion }}</span>
              <span class="arrow">→</span>
              <span class="new-version">{{ device.availableVersion }}</span>
            </div>
          </div>
        </div>
        
        <div class="confirm-notes">
          <div class="note-item">
            <div class="note-icon"></div>
            <div class="note-text">升级过程中请勿断开设备电源</div>
          </div>
          <div class="note-item">
            <div class="note-icon"></div>
            <div class="note-text">备份重要数据以防升级失败</div>
          </div>
          <div class="note-item">
            <div class="note-icon"></div>
            <div class="note-text">升级完成后设备将自动重启</div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogState.hideUpgradeConfirm">取消</el-button>
          <el-button type="primary" @click="confirmUpgrade">确认升级</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection,
  Refresh,
  View,
  Setting,
  HomeFilled,
  Download
} from '@element-plus/icons-vue';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import StatusIndicator from './components/StatusIndicator.vue';
import DataPanel from './components/DataPanel.vue';
import DeviceCard from './components/DeviceCard.vue';

// 定义固件版本类型
interface FirmwareVersion {
  name: string;
  version: string;
  releaseDate: string;
  fileSize: string;
  changes: {
    features: string[];
    improvements: string[];
    bugfixes: string[];
  };
}

// 设备列表
const devices = reactive([
  {
    id: 'DEV001',
    name: '智能机器狗Alpha',
    model: 'RoboDog-A1',
    currentVersion: 'v1.2.3',
    availableVersion: 'v1.3.0',
    selected: false,
    upgrading: false,
    upgraded: false,
    progress: 0,
    upgradeTime: ''
  },
  {
    id: 'DEV002',
    name: '无人机 Sky-7',
    model: 'UAV-500',
    currentVersion: 'v2.0.1',
    availableVersion: 'v2.1.0',
    selected: false,
    upgrading: false,
    upgraded: false,
    progress: 0,
    upgradeTime: ''
  },
  {
    id: 'DEV003',
    name: '捕虫灯 LT-200',
    model: 'ITS-Pro',
    currentVersion: 'v3.5.2',
    availableVersion: 'v3.6.0',
    selected: false,
    upgrading: false,
    upgraded: false,
    progress: 0,
    upgradeTime: ''
  },
  {
    id: 'DEV004',
    name: '超声波装置 US-50',
    model: 'USonic-X',
    currentVersion: 'v1.0.8',
    availableVersion: 'v1.1.0',
    selected: false,
    upgrading: false,
    upgraded: false,
    progress: 0,
    upgradeTime: ''
  },
  {
    id: 'DEV005',
    name: '温湿度传感器 TH-100',
    model: 'ENV-2000',
    currentVersion: 'v2.1.5',
    availableVersion: 'v2.2.0',
    selected: false,
    upgrading: false,
    upgraded: true,
    progress: 100,
    upgradeTime: '2023-06-15 14:35'
  }
]);

// 设备统计数据
const deviceStats = computed(() => {
  return {
    total: devices.length,
    updatable: devices.filter(d => !d.upgraded).length,
    upgrading: devices.filter(d => d.upgrading).length,
    completed: devices.filter(d => d.upgraded).length
  }
});

// 最近升级记录
const recentUpdates = reactive([
  {
    deviceName: '温湿度传感器 TH-100',
    oldVersion: 'v2.1.5',
    newVersion: 'v2.2.0',
    time: '2023-06-15 14:35'
  },
  {
    deviceName: '无人机控制器 RC-300',
    oldVersion: 'v1.8.3',
    newVersion: 'v2.0.0',
    time: '2023-06-10 09:12'
  },
  {
    deviceName: '土壤监测器 SM-50',
    oldVersion: 'v3.0.2',
    newVersion: 'v3.1.0',
    time: '2023-06-08 16:40'
  }
]);

// 固件存储库
const firmwareRepository = reactive<FirmwareVersion[]>([
  {
    name: '机器狗固件更新',
    version: '1.3.0',
    releaseDate: '2023-10-05',
    fileSize: '15.8 MB',
    changes: {
      features: [
        '新增光线传感器支持，可在低光环境中自动调整LED亮度',
        '语音命令系统升级，支持更多自定义命令'
      ],
      improvements: [
        '优化电池使用效率，待机时间延长约20%',
        '增强环境识别算法，减少误报率'
      ],
      bugfixes: [
        '修复在某些情况下传感器数据不同步的问题',
        '解决长时间运行后偶发的系统重启问题'
      ]
    }
  },
  {
    name: '无人机固件',
    version: '2.1.0',
    releaseDate: '2023-05-28',
    fileSize: '22.3 MB',
    changes: {
      features: [
        '新增智能返航模式，可自动规避障碍',
        '增加喷洒精度控制，支持变速喷洒',
        '新增低电量紧急着陆保护功能'
      ],
      improvements: [
        '提升飞行稳定性，减少风力干扰',
        '优化喷洒系统控制算法，降低药液浪费',
        '改进电池管理系统，延长单次飞行时间'
      ],
      bugfixes: [
        '修复高速飞行时出现的控制延迟问题',
        '解决药箱余量显示不准确的问题',
        '修复在雨天作业时可能出现的故障'
      ]
    }
  },
  {
    name: '捕虫灯固件',
    version: '3.6.0',
    releaseDate: '2023-06-05',
    fileSize: '8.5 MB',
    changes: {
      features: [
        '新增智能光波频率自适应调节功能',
        '增加虫害类型识别功能，针对不同虫害自动调整参数',
        '新增远程数据分析接口，支持云端评估效果'
      ],
      improvements: [
        '优化功耗控制，延长电池使用寿命',
        '改进光源亮度控制，提高捕获效率',
        '增强防水性能，适应更多恶劣天气条件'
      ],
      bugfixes: [
        '修复定时启动偶尔失效的问题',
        '解决长时间工作后光源衰减严重的问题',
        '修复数据统计不准确的问题'
      ]
    }
  },
  {
    name: '超声波装置固件',
    version: '1.1.0',
    releaseDate: '2023-06-12',
    fileSize: '6.2 MB',
    changes: {
      features: [
        '新增多频段自动切换功能，提高驱虫效果',
        '增加智能功率调节，根据环境自动调整强度',
        '新增虫害干扰模式，通过特定频率干扰虫害通信'
      ],
      improvements: [
        '优化声波传播算法，增大有效覆盖范围',
        '改进功率管理，降低能耗',
        '提高设备防护等级，适应更多环境'
      ],
      bugfixes: [
        '修复在特定频率下的共振问题',
        '解决长时间工作后自动关闭的问题',
        '修复远程控制接口不稳定的问题'
      ]
    }
  }
]);

// 选择状态
const allSelected = ref(false);
const isIndeterminate = computed(() => {
  const availableDevices = devices.filter(d => !d.upgrading && !d.upgraded);
  const selectedAvailable = availableDevices.filter(d => d.selected);
  return selectedAvailable.length > 0 && selectedAvailable.length < availableDevices.length;
});

// 已选中设备计数
const selectedCount = computed(() => devices.filter(device => device.selected).length);

// 已选中设备
const selectedDevices = computed(() => devices.filter(device => device.selected));

// 最后更新时间
const lastUpdateTime = ref(new Date());

// 对话框状态
const dialogState = reactive({
  releaseNotesVisible: false,
  upgradeConfirmVisible: false,
  currentFirmware: null as FirmwareVersion | null,
  
  showReleaseNotes(firmware: FirmwareVersion) {
    this.currentFirmware = firmware;
    this.releaseNotesVisible = true;
  },
  
  hideReleaseNotes() {
    this.releaseNotesVisible = false;
  },
  
  showUpgradeConfirm() {
    this.upgradeConfirmVisible = true;
  },
  
  hideUpgradeConfirm() {
    this.upgradeConfirmVisible = false;
  }
});

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false });
};

// 获取进度状态
const getProgressStatus = (progress: number) => {
  if (progress < 30) return '正在下载固件...';
  if (progress < 60) return '正在验证固件...';
  if (progress < 90) return '正在安装固件...';
  return '即将完成升级...';
};

// 获取进度类型
const getProgressType = (progress: number) => {
  if (progress >= 100) return 'success';
  return '';
};

// 处理全选变化
const handleSelectAllChange = (val: boolean) => {
  devices.forEach(device => {
    if (!device.upgrading && !device.upgraded) {
      device.selected = val;
    }
  });
};

// 处理设备选择变化
const handleDeviceSelectChange = () => {
  const availableDevices = devices.filter(d => !d.upgrading && !d.upgraded);
  const selectedAvailable = availableDevices.filter(d => d.selected);
  
  allSelected.value = availableDevices.length > 0 && 
                       selectedAvailable.length === availableDevices.length;
};

// 查看更新日志
const viewReleaseNotes = (device: any) => {
  // 查找对应的固件信息
  const firmware = firmwareRepository.find(f => 
    f.name.includes(device.name.split(' ')[0]) && 
    f.version === device.availableVersion.replace('v', '')
  );
  
  if (firmware) {
    dialogState.showReleaseNotes(firmware);
  } else {
    ElMessage({
      type: 'warning',
      message: '未找到该设备的更新日志信息'
    });
  }
};

// 查看固件详情
const viewFirmwareDetails = (firmware: FirmwareVersion) => {
  dialogState.showReleaseNotes(firmware);
};

// 启动单个设备升级
const startSingleUpgrade = (device: any) => {
  // 取消其他选择
  devices.forEach(d => d.selected = false);
  // 选中当前设备
  device.selected = true;
  // 显示确认对话框
  dialogState.showUpgradeConfirm();
};

// 启动批量升级
const startUpgrade = () => {
  if (selectedCount.value === 0) {
    ElMessage({
      type: 'warning',
      message: '请先选择要升级的设备'
    });
    return;
  }
  
  dialogState.showUpgradeConfirm();
};

// 确认升级
const confirmUpgrade = () => {
  // 获取选中的设备
  const devicesToUpgrade = devices.filter(device => device.selected);
  
  // 更新设备状态
  devicesToUpgrade.forEach(device => {
    device.selected = false;
    device.upgrading = true;
    device.progress = 0;
    
    // 模拟升级进度
    simulateUpgradeProgress(device);
  });
  
  // 更新全选状态
  handleDeviceSelectChange();
  
  // 关闭确认对话框
  dialogState.hideUpgradeConfirm();
  
  ElMessage({
    type: 'success',
    message: `已开始为 ${devicesToUpgrade.length} 台设备进行固件升级`
  });
};

// 模拟升级进度
const simulateUpgradeProgress = (device: any) => {
  let progress = 0;
  const interval = setInterval(() => {
    progress += Math.floor(Math.random() * 5) + 1;
    
    if (progress >= 100) {
      progress = 100;
      clearInterval(interval);
      
      // 升级完成
      setTimeout(() => {
        device.upgrading = false;
        device.upgraded = true;
        device.progress = 100;
        
        // 设置升级完成时间
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
          year: 'numeric', 
          month: '2-digit', 
          day: '2-digit',
          hour: '2-digit', 
          minute: '2-digit'
        });
        device.upgradeTime = timeString;
        
        // 添加到最近升级记录
        recentUpdates.unshift({
          deviceName: device.name,
          oldVersion: device.currentVersion,
          newVersion: device.availableVersion,
          time: timeString
        });
        
        // 如果记录超过5条，移除最老的记录
        if (recentUpdates.length > 5) {
          recentUpdates.pop();
        }
        
        ElMessage({
          type: 'success',
          message: `${device.name} 固件升级完成`
        });
      }, 1000);
    }
    
    device.progress = progress;
  }, 300);
};

// 刷新数据
const refreshData = () => {
  lastUpdateTime.value = new Date();
  ElMessage.success('数据已更新');
};

onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped>
.firmware-upgrade {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 升级摘要 */
.upgrade-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 主容器 */
.upgrade-container {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 20px;
  flex: 1;
  margin-bottom: 20px;
}

.panel-actions{
  display: flex;
  align-items: center;
  gap: 20px;
}

/* 设备列表区域 */
.devices-list {
  overflow-y: auto;
  padding-right: 10px;
}

.device-item {
  padding: 15px;
  border-radius: 8px;
  background: linear-gradient(145deg, #1a2234, #2a3349);
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
  border: 1px solid #3b4863;
}

.device-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.device-item.upgrading {
  background: linear-gradient(145deg, #1a2e4a, #2a3f5f);
  border-left: 3px solid #3b82f6;
  animation: pulse-bg 2s infinite;
}

.device-info {
  flex: 1;
  margin-left: 15px;
}

.device-name-area {
  margin-bottom: 10px;
}

.device-name {
  margin: 0 0 3px 0;
  font-size: 16px;
  font-weight: 600;
  color: #e5e7eb;
}

.device-model {
  font-size: 12px;
  color: #9ca3af;
}

.firmware-versions {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.current-version, .available-version {
  display: flex;
  flex-direction: column;
}

.version-label {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 3px;
}

.version-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: rgba(75, 85, 99, 0.5);
  color: #e5e7eb;
}

.version-tag.new {
  background-color: rgba(59, 130, 246, 0.3);
  color: #93c5fd;
}

.version-arrow {
  margin: 0 15px;
  color: #9ca3af;
  margin-top: 10px;
}

.upgrade-progress {
  margin-bottom: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

.progress-status {
  font-size: 12px;
  color: #9ca3af;
}

.upgrade-complete {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.complete-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'/%3E%3Cpolyline points='22 4 12 14.01 9 11.01'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.complete-status {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
}

.complete-time {
  font-size: 12px;
  color: #9ca3af;
}

.device-actions {
  display: flex;
  gap: 10px;
}

/* 升级信息面板 */
.upgrade-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.stat-card {
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  border: 1px solid #3b4863;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #9ca3af;
}

.section-divider {
  margin: 20px 0 10px;
  border-bottom: 1px solid #3b4863;
  padding-bottom: 5px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #e5e7eb;
}

.updates-list {
  max-height: 500px;
  overflow-y: auto;
}

.update-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #3b4863;
}

.update-item:last-child {
  border-bottom: none;
}

.update-time {
  width: 120px;
  font-size: 12px;
  color: #9ca3af;
}

.update-content {
  flex: 1;
}

.update-device {
  font-size: 14px;
  font-weight: 500;
  color: #e5e7eb;
  margin-bottom: 3px;
}

.update-detail {
  font-size: 12px;
  color: #9ca3af;
}

.old-version, .new-version {
  display: inline-block;
  padding: 1px 6px;
  border-radius: 4px;
}

.old-version {
  background-color: rgba(75, 85, 99, 0.5);
  color: #e5e7eb;
}

.arrow {
  margin: 0 8px;
}

.new-version {
  background-color: rgba(59, 130, 246, 0.3);
  color: #93c5fd;
}

.repository-list {
  max-height: 500px;
  overflow-y: auto;
}

.repository-item {
  padding: 15px;
  border-radius: 8px;
  background: linear-gradient(145deg, #1a2234, #2a3349);
  margin-bottom: 10px;
  border: 1px solid #3b4863;
}

.firmware-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.firmware-name {
  font-size: 14px;
  font-weight: 600;
  color: #e5e7eb;
}

.firmware-version {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

.firmware-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 12px;
  color: #9ca3af;
}

.firmware-actions {
  display: flex;
  justify-content: flex-end;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 对话框样式 */
.release-notes-dialog {
  color: #e5e7eb;
}

.firmware-header {
  margin-bottom: 20px;
}

.firmware-title {
  font-size: 18px;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 5px;
}

.release-date {
  font-size: 14px;
  color: #9ca3af;
}

.changes-category {
  margin-bottom: 20px;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #e5e7eb;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #3b4863;
}

.changes {
  padding-left: 20px;
  margin: 0;
}

.changes li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #d1d5db;
}

.upgrade-confirm-dialog {
  color: #e5e7eb;
}

.confirm-content {
  text-align: center;
}

.warning-icon {
  width: 50px;
  height: 50px;
  margin: 0 auto 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f59e0b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'/%3E%3Cline x1='12' y1='9' x2='12' y2='13'/%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.confirm-message {
  margin-bottom: 20px;
  color: #d1d5db;
  font-size: 14px;
}

.devices-to-upgrade {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  text-align: left;
}

.upgrade-device-item {
  padding: 10px;
  border-bottom: 1px solid #3b4863;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upgrade-device-item:last-child {
  border-bottom: none;
}

.confirm-notes {
  text-align: left;
}

.note-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.note-item:last-child {
  margin-bottom: 0;
}

.note-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f59e0b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cline x1='12' y1='8' x2='12' y2='12'/%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.note-text {
  font-size: 14px;
  color: #d1d5db;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 动画 */
@keyframes pulse-bg {
  0% {
    background: linear-gradient(145deg, #1a2e4a, #2a3f5f);
  }
  50% {
    background: linear-gradient(145deg, #1e355a, #2e4670);
  }
  100% {
    background: linear-gradient(145deg, #1a2e4a, #2a3f5f);
  }
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .upgrade-container {
    grid-template-columns: 1fr;
  }
  
  .upgrade-stats {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .upgrade-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .firmware-versions {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .version-arrow {
    transform: rotate(90deg);
    margin: 10px 0;
  }
  
  .upgrade-stats {
    grid-template-columns: 1fr;
  }
}
</style> 