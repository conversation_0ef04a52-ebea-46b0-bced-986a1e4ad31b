import type { PesticideLedger } from '@/types/pesticide'

/**
 * 将后端农药数据映射为前端显示所需的格式
 * @param data 后端农药数据
 * @returns 适配前端显示的农药数据
 */
export function mapPesticideData(data: PesticideLedger): PesticideLedger {
  return {
    ...data,
    stockQuantity: data.currentStock,
    unit: data.stockUnit,
    lowStockWarning: data.lowStockThreshold,
    expiryDate: data.validUntil,
    specification: `${data.activeIngredients} ${data.formulation}`,
    inboundTime: data.createdAt ? new Date(data.createdAt).toLocaleDateString('zh-CN') : '-',
    batchNumber: data.id.substring(0, 8).toUpperCase(),
    supplier: data.manufacturer,
    purchaseBatch: 'PB-' + data.id.substring(0, 6).toUpperCase()
  }
}

/**
 * 将农药毒性级别转换为标签类型
 * @param toxicityLevel 毒性级别
 * @returns 标签类型
 */
export function getToxicityTagType(toxicityLevel: string): string {
  const toxicityMap: Record<string, string> = {
    '低毒': 'success',
    '中等毒性': 'warning', 
    '高毒': 'danger',
    '剧毒': 'danger'
  }
  return toxicityMap[toxicityLevel] || 'info'
}

/**
 * 将农药状态转换为状态指示器类型
 * @param stock 库存量 
 * @param threshold 预警阈值
 * @returns 状态指示器类型
 */
export function getStockStatusType(stock: number, threshold: number): string {
  if (stock <= 0) return 'error'
  if (stock <= threshold) return 'warning'
  return 'success'
}

/**
 * 获取农药库存状态描述
 * @param stock 库存量
 * @param threshold 预警阈值
 * @returns 状态描述
 */
export function getStockStatusLabel(stock: number, threshold: number): string {
  if (stock <= 0) return '无库存'
  if (stock <= threshold) return '库存不足'
  return '库存充足'
}

/**
 * 模拟数据，用于在开发阶段提供测试数据
 * @returns 农药数据数组
 */
export function getMockPesticides(): PesticideLedger[] {
  return [
    {
      id: '6a7b8c9d-0e1f-2a3b-4c5d-6e7f8a9b0c1d',
      name: '丙硫菌唑',
      registrationNumber: 'PD20230001',
      activeIngredients: '丙硫菌唑 25%',
      formulation: '悬浮剂',
      manufacturer: '拜耳作物科学有限公司',
      approvalDate: '2023-01-15',
      validUntil: '2025-01-14',
      toxicityLevel: '低毒',
      usageTargets: ['小麦赤霉病', '水稻纹枯病', '玉米大斑病'],
      dosageRange: '25-35克/亩',
      safetyInterval: 14,
      storageRequirements: '避光、通风、干燥',
      notes: '适合用于多种作物的广谱杀菌剂',
      currentStock: 85,
      stockUnit: '升',
      lowStockThreshold: 20,
      createdAt: '2023-03-10T08:30:00Z',
      updatedAt: '2023-05-18T14:45:00Z',
      specification: '丙硫菌唑 25% 悬浮剂',
      stockQuantity: 85,
      unit: '升',
      expiryDate: '2025-01-14',
      batchNumber: '6A7B8C9D',
      supplier: '拜耳作物科学有限公司',
      inboundTime: '2023-03-10',
      purchaseBatch: 'PB-6A7B8C',
      lowStockWarning: 20
    },
    {
      id: '1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d',
      name: '啶虫脒',
      registrationNumber: 'PD20230002',
      activeIngredients: '啶虫脒 70%',
      formulation: '水分散粒剂',
      manufacturer: '先正达农业科技有限公司',
      approvalDate: '2023-02-10',
      validUntil: '2025-02-09',
      toxicityLevel: '中等毒性',
      usageTargets: ['水稻褐飞虱', '棉花蚜虫', '果树红蜘蛛'],
      dosageRange: '15-20克/亩',
      safetyInterval: 21,
      storageRequirements: '密封、避光、防潮',
      notes: '高效、低残留杀虫剂',
      currentStock: 12,
      stockUnit: '千克',
      lowStockThreshold: 15,
      createdAt: '2023-04-05T09:15:00Z',
      updatedAt: '2023-05-20T10:30:00Z',
      specification: '啶虫脒 70% 水分散粒剂',
      stockQuantity: 12,
      unit: '千克',
      expiryDate: '2025-02-09',
      batchNumber: '1A2B3C4D',
      supplier: '先正达农业科技有限公司',
      inboundTime: '2023-04-05',
      purchaseBatch: 'PB-1A2B3C',
      lowStockWarning: 15
    },
    {
      id: '2c3d4e5f-6a7b-8c9d-0e1f-2a3b4c5d6e7f',
      name: '草甘膦',
      registrationNumber: 'PD20230003',
      activeIngredients: '草甘膦铵盐 41%',
      formulation: '可溶液剂',
      manufacturer: '孟山都农业科技有限公司',
      approvalDate: '2023-01-30',
      validUntil: '2025-01-29',
      toxicityLevel: '低毒',
      usageTargets: ['一年生杂草', '多年生杂草', '阔叶杂草'],
      dosageRange: '3-5升/公顷',
      safetyInterval: 7,
      storageRequirements: '阴凉、干燥、通风',
      notes: '非选择性除草剂，适用于多种作物',
      currentStock: 150,
      stockUnit: '升',
      lowStockThreshold: 30,
      createdAt: '2023-03-15T10:45:00Z',
      updatedAt: '2023-05-16T16:20:00Z',
      specification: '草甘膦铵盐 41% 可溶液剂',
      stockQuantity: 150,
      unit: '升',
      expiryDate: '2025-01-29',
      batchNumber: '2C3D4E5F',
      supplier: '孟山都农业科技有限公司',
      inboundTime: '2023-03-15',
      purchaseBatch: 'PB-2C3D4E',
      lowStockWarning: 30
    },
    {
      id: '3e4f5a6b-7c8d-9e0f-1a2b-3c4d5e6f7a8b',
      name: '吡唑醚菌酯',
      registrationNumber: 'PD20230004',
      activeIngredients: '吡唑醚菌酯 25%',
      formulation: '乳油',
      manufacturer: '巴斯夫农业化学品有限公司',
      approvalDate: '2023-02-28',
      validUntil: '2025-02-27',
      toxicityLevel: '低毒',
      usageTargets: ['水稻稻瘟病', '小麦锈病', '黄瓜霜霉病'],
      dosageRange: '30-40毫升/亩',
      safetyInterval: 14,
      storageRequirements: '避光、通风、干燥',
      notes: '高效、广谱、内吸性杀菌剂',
      currentStock: 5,
      stockUnit: '升',
      lowStockThreshold: 10,
      createdAt: '2023-04-01T08:00:00Z',
      updatedAt: '2023-05-10T11:30:00Z',
      specification: '吡唑醚菌酯 25% 乳油',
      stockQuantity: 5,
      unit: '升',
      expiryDate: '2025-02-27',
      batchNumber: '3E4F5A6B',
      supplier: '巴斯夫农业化学品有限公司',
      inboundTime: '2023-04-01',
      purchaseBatch: 'PB-3E4F5A',
      lowStockWarning: 10
    },
    {
      id: '4a5b6c7d-8e9f-0a1b-2c3d-4e5f6a7b8c9d',
      name: '阿维菌素',
      registrationNumber: 'PD20230005',
      activeIngredients: '阿维菌素 1.8%',
      formulation: '乳油',
      manufacturer: '海利尔药业有限公司',
      approvalDate: '2023-03-10',
      validUntil: '2025-03-09',
      toxicityLevel: '高毒',
      usageTargets: ['蔬菜小菜蛾', '果树红蜘蛛', '棉花红铃虫'],
      dosageRange: '15-25毫升/亩',
      safetyInterval: 30,
      storageRequirements: '密封、避光、远离食物',
      notes: '生物源杀虫剂，对害虫有触杀和胃毒作用',
      currentStock: 0,
      stockUnit: '升',
      lowStockThreshold: 5,
      createdAt: '2023-03-25T14:30:00Z',
      updatedAt: '2023-05-15T09:45:00Z',
      specification: '阿维菌素 1.8% 乳油',
      stockQuantity: 0,
      unit: '升',
      expiryDate: '2025-03-09',
      batchNumber: '4A5B6C7D',
      supplier: '海利尔药业有限公司',
      inboundTime: '2023-03-25',
      purchaseBatch: 'PB-4A5B6C',
      lowStockWarning: 5
    }
  ]
} 