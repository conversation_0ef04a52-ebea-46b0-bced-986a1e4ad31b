import { ref, computed } from 'vue';
import { LayoutType } from '../types';
import type { LayoutConfig } from '../types';

export function useLayoutManager() {
  // 当前布局
  const currentLayout = ref<LayoutType>(LayoutType.LAYOUT_2X2);
  
  // 布局配置映射
  const layoutConfigs: Record<LayoutType, LayoutConfig> = {
    [LayoutType.LAYOUT_1X1]: { type: LayoutType.LAYOUT_1X1, columns: 1, rows: 1 },
    [LayoutType.LAYOUT_2X2]: { type: LayoutType.LAYOUT_2X2, columns: 2, rows: 2 },
    [LayoutType.LAYOUT_3X3]: { type: LayoutType.LAYOUT_3X3, columns: 3, rows: 3 },
    [LayoutType.LAYOUT_4X4]: { type: LayoutType.LAYOUT_4X4, columns: 4, rows: 4 }
  };
  
  // 当前布局配置
  const currentLayoutConfig = computed(() => layoutConfigs[currentLayout.value]);
  
  // 切换布局
  const changeLayout = (layout: LayoutType) => {
    currentLayout.value = layout;
  };
  
  // 计算网格样式
  const gridStyle = computed(() => {
    const { columns, rows } = currentLayoutConfig.value;
    return {
      gridTemplateColumns: `repeat(${columns}, 1fr)`,
      gridTemplateRows: `repeat(${rows}, 1fr)`
    };
  });
  
  // 计算可见的摄像头数量
  const visibleCamerasCount = computed(() => {
    const { columns, rows } = currentLayoutConfig.value;
    return columns * rows;
  });
  
  return {
    currentLayout,
    currentLayoutConfig,
    changeLayout,
    gridStyle,
    visibleCamerasCount
  };
} 