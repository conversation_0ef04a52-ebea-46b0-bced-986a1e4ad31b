<template>
  <div class="robot-dog-api-test">
    <div class="test-header">
      <h2>机器狗API测试</h2>
      <p>用于测试机器狗API接口的功能和数据获取</p>
    </div>

    <div class="test-sections">
      <!-- 连接管理测试 -->
      <div class="test-section">
        <h3>连接管理</h3>
        <div class="test-buttons">
          <el-button
            type="primary"
            @click="testConnect"
            :loading="loading.connect"
          >
            连接机器狗
          </el-button>
          <el-button
            @click="testGetStatus"
            :loading="loading.status"
          >
            获取连接状态
          </el-button>
          <el-button
            @click="testGetSystemInfo"
            :loading="loading.systemInfo"
          >
            获取系统信息
          </el-button>
          <el-button
            type="danger"
            @click="testDisconnect"
            :loading="loading.disconnect"
          >
            断开连接
          </el-button>
        </div>
      </div>

      <!-- IMU数据测试 -->
      <div class="test-section">
        <h3>IMU数据获取</h3>
        <div class="test-buttons">
          <el-button
            type="success"
            @click="testGetIMUData"
            :loading="loading.imu"
          >
            获取IMU数据
          </el-button>
          <el-button
            @click="testGetIMURPY"
            :loading="loading.rpy"
          >
            获取姿态角
          </el-button>
          <el-button
            @click="testGetMotorStatus"
            :loading="loading.motor"
          >
            获取电机状态
          </el-button>
          <el-button
            @click="testGetBatteryStatus"
            :loading="loading.battery"
          >
            获取电池状态
          </el-button>
        </div>
      </div>

      <!-- 运动控制测试 -->
      <div class="test-section">
        <h3>运动控制</h3>
        <div class="test-buttons">
          <el-button
            @click="testMovement"
            :loading="loading.movement"
          >
            测试移动
          </el-button>
          <el-button
            type="warning"
            @click="testStop"
            :loading="loading.stop"
          >
            停止移动
          </el-button>
          <el-button
            type="danger"
            @click="testEmergencyStop"
            :loading="loading.emergency"
          >
            紧急停止
          </el-button>
        </div>
      </div>
    </div>

    <!-- 响应数据显示 -->
    <div class="response-display">
      <h3>API响应数据</h3>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="请求信息" name="request">
          <pre class="response-content">{{ requestInfo }}</pre>
        </el-tab-pane>
        <el-tab-pane label="响应数据" name="response">
          <pre class="response-content">{{ responseData }}</pre>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { robotDogApi } from '@/api/robotDog'

// 加载状态
const loading = reactive({
  connect: false,
  disconnect: false,
  status: false,
  systemInfo: false,
  imu: false,
  rpy: false,
  motor: false,
  battery: false,
  movement: false,
  stop: false,
  emergency: false
})

// 响应数据
const requestInfo = ref('')
const responseData = ref('')
const activeTab = ref('request')

// 设置请求信息
const setRequestInfo = (method: string, url: string, data?: any) => {
  requestInfo.value = `请求方法: ${method}\n请求URL: ${url}\n请求时间: ${new Date().toLocaleString()}`
  if (data) {
    requestInfo.value += `\n请求数据: ${JSON.stringify(data, null, 2)}`
  }
}

// 设置响应数据
const setResponseData = (data: any, error?: any) => {
  if (error) {
    responseData.value = `错误信息: ${error.message || error}\n错误时间: ${new Date().toLocaleString()}`
  } else {
    responseData.value = JSON.stringify(data, null, 2)
  }
  activeTab.value = 'response'
}

// 连接管理测试
const testConnect = async () => {
  loading.connect = true
  setRequestInfo('POST', '/dog/connect')

  try {
    const result = await robotDogApi.connect()
    setResponseData(result)
    ElMessage.success('连接成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('连接失败')
  } finally {
    loading.connect = false
  }
}

const testDisconnect = async () => {
  loading.disconnect = true
  setRequestInfo('POST', '/dog/disconnect')

  try {
    const result = await robotDogApi.disconnect()
    setResponseData(result)
    ElMessage.success('断开连接成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('断开连接失败')
  } finally {
    loading.disconnect = false
  }
}

const testGetStatus = async () => {
  loading.status = true
  setRequestInfo('GET', '/dog/status')

  try {
    const result = await robotDogApi.getConnectionStatus()
    setResponseData(result)
    ElMessage.success('获取状态成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('获取状态失败')
  } finally {
    loading.status = false
  }
}

const testGetSystemInfo = async () => {
  loading.systemInfo = true
  setRequestInfo('GET', '/dog/info')

  try {
    const result = await robotDogApi.getSystemInfo()
    setResponseData(result)
    ElMessage.success('获取系统信息成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('获取系统信息失败')
  } finally {
    loading.systemInfo = false
  }
}

// IMU数据测试
const testGetIMUData = async () => {
  loading.imu = true
  setRequestInfo('GET', '/dog/imu')

  try {
    const result = await robotDogApi.getIMUData()
    setResponseData(result)
    ElMessage.success('获取IMU数据成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('获取IMU数据失败')
  } finally {
    loading.imu = false
  }
}

const testGetIMURPY = async () => {
  loading.rpy = true
  setRequestInfo('GET', '/dog/imu/rpy')

  try {
    const result = await robotDogApi.getIMURPY()
    setResponseData(result)
    ElMessage.success('获取姿态角成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('获取姿态角失败')
  } finally {
    loading.rpy = false
  }
}

const testGetMotorStatus = async () => {
  loading.motor = true
  setRequestInfo('GET', '/dog/imu/motors')

  try {
    const result = await robotDogApi.getMotorStatus()
    setResponseData(result)
    ElMessage.success('获取电机状态成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('获取电机状态失败')
  } finally {
    loading.motor = false
  }
}

const testGetBatteryStatus = async () => {
  loading.battery = true
  setRequestInfo('GET', '/dog/imu/battery')

  try {
    const result = await robotDogApi.getBatteryStatus()
    setResponseData(result)
    ElMessage.success('获取电池状态成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('获取电池状态失败')
  } finally {
    loading.battery = false
  }
}

// 运动控制测试
const testMovement = async () => {
  loading.movement = true
  const moveParams = { x: 0.1, y: 0, z: 0 }
  setRequestInfo('POST', '/dog/movement', moveParams)

  try {
    const result = await robotDogApi.movement(moveParams)
    setResponseData(result)
    ElMessage.success('移动命令发送成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('移动命令发送失败')
  } finally {
    loading.movement = false
  }
}

const testStop = async () => {
  loading.stop = true
  setRequestInfo('POST', '/dog/stop')

  try {
    const result = await robotDogApi.stopMovement()
    setResponseData(result)
    ElMessage.success('停止命令发送成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('停止命令发送失败')
  } finally {
    loading.stop = false
  }
}

const testEmergencyStop = async () => {
  loading.emergency = true
  setRequestInfo('POST', '/dog/emergency-stop')

  try {
    const result = await robotDogApi.emergencyStop()
    setResponseData(result)
    ElMessage.success('紧急停止命令发送成功')
  } catch (error) {
    setResponseData(null, error)
    ElMessage.error('紧急停止命令发送失败')
  } finally {
    loading.emergency = false
  }
}
</script>

<style lang="scss" scoped>
.robot-dog-api-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .test-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      color: #2c3e50;
      margin-bottom: 10px;
    }

    p {
      color: #7f8c8d;
      font-size: 14px;
    }
  }

  .test-sections {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;

    .test-section {
      border: 1px solid #e1e8ed;
      border-radius: 8px;
      padding: 20px;
      background: #fff;

      h3 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        font-size: 16px;
      }

      .test-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    }
  }

  .response-display {
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 20px;
    background: #fff;

    h3 {
      margin: 0 0 15px 0;
      color: #2c3e50;
      font-size: 16px;
    }

    .response-content {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      max-height: 400px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}
</style>
