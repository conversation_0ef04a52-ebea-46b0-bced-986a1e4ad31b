<!--
  ResizablePanel.vue
  可调整大小的面板组件
  支持折叠/展开、拖拽调整大小、响应式适配
-->
<template>
  <div
    ref="panelRef"
    class="resizable-panel"
    :class="panelClasses"
    :style="panelStyles"
  >
    <!-- 面板头部 -->
    <div
      class="panel-header"
      @click="toggleCollapse"
      :class="{ 'clickable': collapsible }"
    >
      <div class="header-content">
        <div class="header-title">
          <slot name="title">
            <h3>{{ title }}</h3>
          </slot>
        </div>

        <div class="header-actions">
          <slot name="actions" />

          <!-- 折叠按钮 -->
          <button
            v-if="collapsible"
            class="collapse-btn"
            @click.stop="toggleCollapse"
            :aria-label="isCollapsed ? '展开面板' : '折叠面板'"
          >
            <el-icon>
              <ArrowRight v-if="isCollapsed" />
              <ArrowDown v-else />
            </el-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- 面板内容 -->
    <div
      v-show="!isCollapsed"
      class="panel-content"
      :style="contentStyles"
    >
      <slot />
    </div>

    <!-- 调整大小手柄 -->
    <div
      v-if="resizable && !isMobile"
      class="resize-handle"
      :class="`resize-handle--${resizeDirection}`"
      @mousedown="startResize"
      @touchstart="startResize"
    >
      <div class="resize-indicator"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { ArrowRight, ArrowDown } from '@element-plus/icons-vue';
import { useResponsiveLayout } from '../composables/useResponsiveLayout';

// Props定义
interface Props {
  title?: string;
  collapsible?: boolean;
  collapsed?: boolean;
  resizable?: boolean;
  resizeDirection?: 'horizontal' | 'vertical';
  minWidth?: number;
  maxWidth?: number;
  minHeight?: number;
  maxHeight?: number;
  defaultWidth?: number;
  defaultHeight?: number;
  position?: 'left' | 'right' | 'top' | 'bottom';
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  collapsible: true,
  collapsed: false,
  resizable: true,
  resizeDirection: 'horizontal',
  minWidth: 200,
  maxWidth: 600,
  minHeight: 200,
  maxHeight: 800,
  defaultWidth: 300,
  defaultHeight: 400,
  position: 'right'
});

// Emits定义
const emit = defineEmits<{
  'update:collapsed': [collapsed: boolean];
  'resize': [size: { width: number; height: number }];
  'collapse': [collapsed: boolean];
}>();

// 使用响应式布局
const { isMobile, isTablet, screenSize } = useResponsiveLayout();

// 模板引用
const panelRef = ref<HTMLDivElement>();

// 状态管理
const isCollapsed = ref(props.collapsed);
const currentWidth = ref(props.defaultWidth);
const currentHeight = ref(props.defaultHeight);
const isResizing = ref(false);

// 调整大小相关状态
const resizeStartX = ref(0);
const resizeStartY = ref(0);
const resizeStartWidth = ref(0);
const resizeStartHeight = ref(0);

// 计算属性
const panelClasses = computed(() => ({
  'resizable-panel--collapsed': isCollapsed.value,
  'resizable-panel--mobile': isMobile.value,
  'resizable-panel--tablet': isTablet.value,
  'resizable-panel--resizable': props.resizable && !isMobile.value,
  'resizable-panel--resizing': isResizing.value,
  [`resizable-panel--${props.position}`]: true,
  [`resizable-panel--resize-${props.resizeDirection}`]: props.resizable
}));

const panelStyles = computed(() => {
  const styles: Record<string, string> = {};

  if (!isCollapsed.value) {
    if (props.resizeDirection === 'horizontal') {
      styles.width = `${currentWidth.value}px`;
    } else {
      styles.height = `${currentHeight.value}px`;
    }
  }

  return styles;
});

const contentStyles = computed(() => {
  const styles: Record<string, string> = {};

  if (isCollapsed.value) {
    styles.display = 'none';
  }

  return styles;
});

// 切换折叠状态
const toggleCollapse = () => {
  if (!props.collapsible) return;

  isCollapsed.value = !isCollapsed.value;
  emit('update:collapsed', isCollapsed.value);
  emit('collapse', isCollapsed.value);
};

// 开始调整大小
const startResize = (event: MouseEvent | TouchEvent) => {
  if (!props.resizable || isMobile.value) return;

  event.preventDefault();
  isResizing.value = true;

  const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX;
  const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;

  resizeStartX.value = clientX;
  resizeStartY.value = clientY;
  resizeStartWidth.value = currentWidth.value;
  resizeStartHeight.value = currentHeight.value;

  // 添加全局事件监听器
  document.addEventListener('mousemove', handleResize, { passive: false });
  document.addEventListener('mouseup', stopResize);
  document.addEventListener('touchmove', handleResize, { passive: false });
  document.addEventListener('touchend', stopResize);

  // 防止文本选择
  document.body.style.userSelect = 'none';
  document.body.style.cursor = props.resizeDirection === 'horizontal' ? 'ew-resize' : 'ns-resize';
};

// 处理调整大小
const handleResize = (event: MouseEvent | TouchEvent) => {
  if (!isResizing.value) return;

  event.preventDefault();

  const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX;
  const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;

  if (props.resizeDirection === 'horizontal') {
    let deltaX = clientX - resizeStartX.value;

    // 根据面板位置调整增量方向
    if (props.position === 'right') {
      deltaX = -deltaX;
    }

    const newWidth = Math.max(
      props.minWidth,
      Math.min(props.maxWidth, resizeStartWidth.value + deltaX)
    );

    currentWidth.value = newWidth;
  } else {
    let deltaY = clientY - resizeStartY.value;

    // 根据面板位置调整增量方向
    if (props.position === 'bottom') {
      deltaY = -deltaY;
    }

    const newHeight = Math.max(
      props.minHeight,
      Math.min(props.maxHeight, resizeStartHeight.value + deltaY)
    );

    currentHeight.value = newHeight;
  }

  // 发出调整大小事件
  emit('resize', {
    width: currentWidth.value,
    height: currentHeight.value
  });
};

// 停止调整大小
const stopResize = () => {
  if (!isResizing.value) return;

  isResizing.value = false;

  // 移除全局事件监听器
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchmove', handleResize);
  document.removeEventListener('touchend', stopResize);

  // 恢复默认样式
  document.body.style.userSelect = '';
  document.body.style.cursor = '';
};

// 监听collapsed prop变化
watch(() => props.collapsed, (newValue) => {
  isCollapsed.value = newValue;
});

// 响应式适配
watch(screenSize, (newSize) => {
  if (newSize === 'mobile') {
    // 移动端自动展开并禁用调整大小
    if (isCollapsed.value) {
      isCollapsed.value = false;
      emit('update:collapsed', false);
    }
  }
});

// 生命周期
onMounted(() => {
  // 初始化尺寸
  if (props.resizeDirection === 'horizontal') {
    currentWidth.value = props.defaultWidth;
  } else {
    currentHeight.value = props.defaultHeight;
  }
});

onUnmounted(() => {
  // 清理事件监听器
  if (isResizing.value) {
    stopResize();
  }
});

// 暴露方法给父组件
defineExpose({
  collapse: () => {
    if (props.collapsible) {
      isCollapsed.value = true;
      emit('update:collapsed', true);
      emit('collapse', true);
    }
  },
  expand: () => {
    if (props.collapsible) {
      isCollapsed.value = false;
      emit('update:collapsed', false);
      emit('collapse', false);
    }
  },
  toggle: toggleCollapse,
  resize: (width: number, height: number) => {
    if (props.resizable) {
      currentWidth.value = Math.max(props.minWidth, Math.min(props.maxWidth, width));
      currentHeight.value = Math.max(props.minHeight, Math.min(props.maxHeight, height));
      emit('resize', { width: currentWidth.value, height: currentHeight.value });
    }
  },
  getSize: () => ({
    width: currentWidth.value,
    height: currentHeight.value,
    collapsed: isCollapsed.value
  })
});
</script>

<style lang="scss" scoped>
@use './styles/common.scss' as common;

.resizable-panel {
  position: relative;
  background: common.$bg-panel;
  border-radius: common.$border-radius-md;
  box-shadow: common.$shadow-md;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;

  // 位置样式
  &--left {
    border-right: 3px solid common.$primary-color;
  }

  &--right {
    border-left: 3px solid common.$primary-color;
  }

  &--top {
    border-bottom: 3px solid common.$primary-color;
  }

  &--bottom {
    border-top: 3px solid common.$primary-color;
  }

  // 折叠状态
  &--collapsed {
    .panel-content {
      display: none;
    }

    .resize-handle {
      display: none;
    }
  }

  // 调整大小状态
  &--resizing {
    user-select: none;
    pointer-events: none;

    .panel-content {
      pointer-events: none;
    }
  }

  // 响应式样式
  &--mobile {
    width: 100% !important;
    height: auto !important;
    border-radius: 0;
    border-left: none;
    border-right: none;

    .resize-handle {
      display: none;
    }
  }

  &--tablet {
    .resize-handle {
      width: 6px;
      height: 6px;
    }
  }

  &:hover {
    box-shadow: common.$shadow-lg;
  }
}

.panel-header {
  padding: common.$spacing-sm common.$spacing-md;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);

  &.clickable {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
  }
}

.header-content {
  @include common.flex-between;
  width: 100%;
}

.header-title {
  h3 {
    margin: 0;
    color: common.$text-light;
    font-size: 16px;
    font-weight: 500;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: common.$spacing-sm;
}

.collapse-btn {
  background: none;
  border: none;
  color: common.$text-secondary;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: common.$primary-color;
    background: rgba(59, 130, 246, 0.1);
  }

  .el-icon {
    font-size: 14px;
  }
}

.panel-content {
  padding: common.$spacing-md;
  height: 100%;
  overflow: auto;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(59, 130, 246, 0.5);
    }
  }
}

// 调整大小手柄
.resize-handle {
  position: absolute;
  background: transparent;
  z-index: 10;

  &--horizontal {
    width: 8px;
    height: 100%;
    top: 0;
    cursor: ew-resize;

    &.resizable-panel--left & {
      right: -4px;
    }

    &.resizable-panel--right & {
      left: -4px;
    }
  }

  &--vertical {
    width: 100%;
    height: 8px;
    left: 0;
    cursor: ns-resize;

    &.resizable-panel--top & {
      bottom: -4px;
    }

    &.resizable-panel--bottom & {
      top: -4px;
    }
  }

  &:hover {
    .resize-indicator {
      opacity: 1;
    }
  }
}

.resize-indicator {
  position: absolute;
  background: common.$primary-color;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 2px;

  .resize-handle--horizontal & {
    width: 2px;
    height: 30px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .resize-handle--vertical & {
    width: 30px;
    height: 2px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

// 响应式媒体查询
@media (max-width: common.$breakpoint-sm) {
  .resizable-panel {
    margin: 0;
    border-radius: 0;

    &--left,
    &--right {
      border-left: none;
      border-right: none;
      border-top: 3px solid common.$primary-color;
    }
  }

  .panel-header {
    padding: common.$spacing-sm;
  }

  .panel-content {
    padding: common.$spacing-sm;
  }
}

// 触摸设备优化
@media (hover: none) and (pointer: coarse) {
  .resize-handle {
    width: 12px;
    height: 12px;

    &--horizontal {
      width: 12px;
    }

    &--vertical {
      height: 12px;
    }
  }

  .collapse-btn {
    padding: 8px;
    min-width: 32px;
    min-height: 32px;
  }
}
</style>
