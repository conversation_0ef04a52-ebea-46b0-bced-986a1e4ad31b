<!-- 
  DecisionTree.vue
  智能决策树推荐页面
  基于环境数据和病虫害类型构建决策树，为农业防治提供智能决策支持
-->
<template>
  <div class="decision-tree">
    <!-- 页面标题 -->
    <PageHeader
      title="智能决策树推荐工作室"
      description="基于环境数据和病虫害类型构建决策树，为农业防治提供智能决策支持"
      icon="Share"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ decisionStats.pathCount }}</span>
            <span class="summary-label">决策路径</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ decisionStats.successRate }}%</span>
            <span class="summary-label">预期成功率</span>
          </div>
        </div>
      </template>
    </PageHeader>
    
    <!-- 决策树参数面板 -->
    <div class="decision-panels">
      <DataPanel title="决策树参数配置">
        <template #actions>
          <el-tag type="info" effect="dark" size="small">智能参数推荐</el-tag>
        </template>
        <div class="parameter-form">
          <el-form :model="treeParams" label-position="top" label-width="100px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="病虫害类型">
                  <el-select v-model="treeParams.pestType" placeholder="选择病虫害类型" class="w-full">
                    <el-option-group label="害虫">
                      <el-option label="稻飞虱" value="ricehopper"></el-option>
                      <el-option label="棉铃虫" value="bollworm"></el-option>
                      <el-option label="蝗虫" value="locust"></el-option>
                    </el-option-group>
                    <el-option-group label="病害">
                      <el-option label="小麦条锈病" value="wheatrust"></el-option>
                      <el-option label="水稻稻瘟病" value="riceblast"></el-option>
                      <el-option label="黄瓜霜霉病" value="cucumbermildew"></el-option>
                    </el-option-group>
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item label="环境条件">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-input-number 
                        v-model="treeParams.temperature" 
                        :min="0" 
                        :max="50" 
                        placeholder="温度(°C)" 
                        class="w-full">
                      </el-input-number>
                    </el-col>
                    <el-col :span="12">
                      <el-input-number 
                        v-model="treeParams.humidity" 
                        :min="0" 
                        :max="100" 
                        placeholder="湿度(%)" 
                        class="w-full">
                      </el-input-number>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              
              <el-col :span="8">
                <el-form-item label="决策目标">
                  <el-radio-group v-model="treeParams.objective">
                    <el-radio label="cost">防治成本最低</el-radio>
                    <el-radio label="effect">防治效果最佳</el-radio>
                    <el-radio label="eco">生态友好</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="generateDecisionTree" 
                :disabled="!treeParams.pestType"
                class="generate-btn">
                <el-icon><Share /></el-icon>
                生成决策树
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </DataPanel>
      
      <!-- 决策树可视化区域 -->
      <DataPanel title="决策树可视化" v-if="showTree">
        <template #actions>
          <el-tag type="success" effect="dark" size="small">{{ getDecisionTreeStatusText() }}</el-tag>
        </template>
        <div class="tree-visualization">
          <div ref="treeContainer" class="tree-container"></div>
        </div>
      </DataPanel>
      
      <!-- 决策路径回顾区域 -->
      <DataPanel title="决策路径回顾" v-if="decisionPath.length > 0">
        <template #actions>
          <el-tag type="warning" effect="dark" size="small">推荐决策路径</el-tag>
        </template>
        <div class="decision-path-review">
          <el-timeline>
            <el-timeline-item
              v-for="(step, index) in decisionPath"
              :key="index"
              :type="getTimelineItemType(step.type)"
              :icon="getTimelineItemIcon(step.type)"
              :color="getTimelineItemColor(step.type)"
              :timestamp="step.timestamp"
            >
              <div class="timeline-content">
                <h4>{{ step.title }}</h4>
                <p>{{ step.description }}</p>
                <div class="action-buttons" v-if="index === decisionPath.length - 1">
                  <el-button size="small" type="success" @click="adoptRecommendation">
                    <el-icon><Check /></el-icon>
                    采纳方案
                  </el-button>
                  <el-button size="small" type="danger" @click="provideFeedback">
                    <el-icon><Warning /></el-icon>
                    反馈修正
                  </el-button>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="高成功率路径" />
        <StatusIndicator type="warning" label="中等成功率路径" />
        <StatusIndicator type="error" label="低成功率路径" />
        <StatusIndicator type="normal" label="AI推荐中" />
      </div>
      <div class="decision-stats">
        <div class="stat-item">
          <span class="stat-label">平均防治成本</span>
          <span class="stat-value">{{ decisionStats.avgCost }} 元/亩</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">环保评分</span>
          <span class="stat-value">{{ decisionStats.ecoScore }}/10</span>
        </div>
        <div class="refresh-info">
          <el-button type="primary" size="small" plain @click="resetDecisionTree">
            <el-icon><Refresh /></el-icon>
            重置决策树
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 反馈对话框 -->
    <el-dialog
      v-model="showFeedbackDialog"
      title="决策反馈"
      width="500px"
    >
      <el-form :model="feedbackForm" label-width="120px">
        <el-form-item label="反馈类型">
          <el-select v-model="feedbackForm.type" placeholder="选择反馈类型" class="w-full">
            <el-option label="效果不佳" value="poor_effect"></el-option>
            <el-option label="成本过高" value="high_cost"></el-option>
            <el-option label="操作复杂" value="complex_operation"></el-option>
            <el-option label="不适用场景" value="unsuitable_scenario"></el-option>
            <el-option label="其他问题" value="other"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="详细说明">
          <el-input
            v-model="feedbackForm.details"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您的反馈..."
          ></el-input>
        </el-form-item>
        <el-form-item label="调整建议">
          <el-input
            v-model="feedbackForm.suggestion"
            type="textarea"
            :rows="2"
            placeholder="请提供您的调整建议..."
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showFeedbackDialog = false">取消</el-button>
          <el-button type="primary" @click="submitFeedback">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Share, 
  Connection, 
  Document, 
  Check, 
  Warning, 
  Refresh 
} from '@element-plus/icons-vue';
import * as d3 from 'd3';

// 导入自定义组件
import PageHeader from './components/PageHeader.vue';
import StatusIndicator from './components/StatusIndicator.vue';
import DataPanel from './components/DataPanel.vue';

// 决策树参数
const treeParams = reactive({
  pestType: '',
  temperature: 25,
  humidity: 70,
  objective: 'effect'
});

// UI状态
const showTree = ref(false);
const treeContainer = ref<HTMLElement | null>(null);
const showFeedbackDialog = ref(false);

// 决策路径类型定义
interface DecisionPathStep {
  title: string;
  description: string;
  type: string;
  timestamp: string;
}

// 决策路径
const decisionPath = ref<DecisionPathStep[]>([]);

// 决策统计数据
const decisionStats = reactive({
  pathCount: 5,
  avgCost: 45,
  successRate: 85,
  ecoScore: 7.5
});

// 反馈表单
const feedbackForm = reactive({
  type: '',
  details: '',
  suggestion: ''
});

// 示例决策树数据 (实际实现中会从后端获取)
const treeData = {
  name: "是否大面积发生?",
  children: [
    {
      name: "是",
      children: [
        {
          name: "是否经济作物?",
          children: [
            {
              name: "是",
              children: [
                {
                  name: "化学防治",
                  children: [
                    { 
                      name: "阈值处理",
                      details: {
                        method: "使用吡虫啉等内吸性农药喷施",
                        cost: 60,
                        effectiveness: 92,
                        eco_impact: "中等"
                      }
                    }
                  ]
                }
              ]
            },
            {
              name: "否",
              children: [
                {
                  name: "综合防治",
                  children: [
                    { 
                      name: "生物农药+天敌",
                      details: {
                        method: "释放赤眼蜂+使用Bt生物农药",
                        cost: 45,
                        effectiveness: 82,
                        eco_impact: "低"
                      }
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      name: "否",
      children: [
        {
          name: "作物生长阶段?",
          children: [
            {
              name: "幼苗期",
              children: [
                { 
                  name: "农艺调控",
                  details: {
                    method: "适当延迟播种期、加强水分管理",
                    cost: 20,
                    effectiveness: 75,
                    eco_impact: "无"
                  }
                }
              ]
            },
            {
              name: "成熟期",
              children: [
                { 
                  name: "监测观察",
                  details: {
                    method: "加强田间监测,设立黄板诱捕",
                    cost: 15,
                    effectiveness: 65,
                    eco_impact: "无"
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};

// 方法
const getDecisionTreeStatusText = () => {
  const objective = treeParams.objective;
  if (objective === 'cost') return '成本优先方案';
  if (objective === 'effect') return '效果优先方案';
  if (objective === 'eco') return '生态友好方案';
  return '综合方案';
};

// 生成决策树
const generateDecisionTree = () => {
  if (!treeParams.pestType) {
    ElMessage.warning('请选择病虫害类型');
    return;
  }
  
  showTree.value = true;
  
  // 实际实现中，这里会从后端获取树数据
  nextTick(() => {
    renderDecisionTree();
    generateDecisionPath();
  });
};

// 渲染决策树
const renderDecisionTree = () => {
  if (!treeContainer.value) return;
  
  // 清除之前的树
  const container = treeContainer.value;
  d3.select(container as any).selectAll("*").remove();
  
  // 设置SVG
  const width = container.clientWidth || 800;
  const height = 500;
  const margin = {top: 40, right: 120, bottom: 20, left: 120};
  
  // 创建SVG元素
  const svg = d3.select(container as any)
    .append("svg")
    .attr("width", width)
    .attr("height", height)
    .append("g")
    .attr("transform", `translate(${margin.left},${margin.top})`);
  
  // 设置树布局
  const treeLayout = d3.tree<any>()
    .size([height - margin.top - margin.bottom, width - margin.left - margin.right]);
  
  // 创建根节点
  const root = d3.hierarchy(treeData) as d3.HierarchyNode<any>;
  treeLayout(root);
  
  // 添加连接线
  svg.selectAll(".link")
    .data(root.links())
    .enter()
    .append("path")
    .attr("class", "link")
    .attr("d", (d: any) => {
      return `M${d.target.y},${d.target.x}
              C${(d.source.y + d.target.y) / 2},${d.target.x}
               ${(d.source.y + d.target.y) / 2},${d.source.x}
               ${d.source.y},${d.source.x}`;
    })
    .style("fill", "none")
    .style("stroke", "#60a5fa")
    .style("stroke-width", "2px")
    .style("opacity", 0.7);
  
  // 添加节点
  const nodes = svg.selectAll(".node")
    .data(root.descendants())
    .enter()
    .append("g")
    .attr("class", d => `node ${d.children ? "node-internal" : "node-leaf"}`)
    .attr("transform", d => `translate(${d.y},${d.x})`)
    .on("click", (event, d: any) => handleNodeClick(d));
  
  // 添加节点圆圈
  nodes.append("circle")
    .attr("r", d => d.children ? 15 : 20)
    .style("fill", d => {
      const depth = d.depth;
      if (depth === 0) return "#0f172a";  // 顶层
      if (depth === 1) return "#1e40af";  // 第二层
      if (depth === 2) return "#3b82f6";  // 第三层
      return "#60a5fa";                   // 叶节点
    })
    .style("stroke", "#fff")
    .style("stroke-width", "2px");
  
  // 添加节点标签
  nodes.append("text")
    .attr("dy", d => d.children ? "0.35em" : "-1.5em")
    .attr("x", d => d.children ? 0 : 0)
    .attr("text-anchor", "middle")
    .text(d => {
      const name = d.data.name;
      return name.length > 15 ? name.substring(0, 15) + '...' : name;
    })
    .style("font-size", "12px")
    .style("fill", "#fff");
  
  // 为叶节点添加提示信息
  const leafNodes = nodes.filter(d => !d.children);
  
  leafNodes.append("text")
    .attr("dy", "2em")
    .attr("x", 0)
    .attr("text-anchor", "middle")
    .text(d => d.data.details ? `成功率: ${d.data.details.effectiveness}%` : "")
    .style("font-size", "10px")
    .style("fill", "#fff");
  
  leafNodes.append("text")
    .attr("dy", "3.2em")
    .attr("x", 0)
    .attr("text-anchor", "middle")
    .text(d => d.data.details ? `成本: ${d.data.details.cost}元/亩` : "")
    .style("font-size", "10px")
    .style("fill", "#fff");
};

// 处理节点点击
const handleNodeClick = (node: any) => {
  ElMessage.info(`选择节点: ${node.data.name}`);
  // 实际实现中，这里会更新决策路径
};

// 生成决策路径
const generateDecisionPath = () => {
  // 实际实现中，这将基于用户的选择
  decisionPath.value = [
    {
      title: "初始评估",
      description: "稻飞虱中等程度发生，水稻处于分蘖期，需要制定防治方案。",
      type: "info",
      timestamp: "2023-05-15 09:30"
    },
    {
      title: "危害评估",
      description: "田间调查显示虫口密度约为10头/丛，低于经济阈值，建议采取生态友好型防治措施。",
      type: "warning",
      timestamp: "2023-05-15 09:35"
    },
    {
      title: "推荐方案",
      description: "释放赤眼蜂结合生物农药处理，预期防效80%以上，成本约45元/亩，对环境友好。",
      type: "success",
      timestamp: "2023-05-15 09:40"
    }
  ];
};

// 获取时间线项目类型
const getTimelineItemType = (type: string) => {
  switch(type) {
    case 'info': return 'primary';
    case 'warning': return 'warning';
    case 'success': return 'success';
    default: return 'info';
  }
};

// 获取时间线项目图标
const getTimelineItemIcon = (type: string) => {
  switch(type) {
    case 'info': return Document;
    case 'warning': return Warning;
    case 'success': return Check;
    default: return Document;
  }
};

// 获取时间线项目颜色
const getTimelineItemColor = (type: string) => {
  switch(type) {
    case 'info': return '#3b82f6';
    case 'warning': return '#f59e0b';
    case 'success': return '#10b981';
    default: return '#3b82f6';
  }
};

// 采纳推荐方案
const adoptRecommendation = () => {
  ElMessage.success('已采纳推荐方案');
  // 实际实现中，这里会将决策保存到用户历史记录
};

// 提供反馈
const provideFeedback = () => {
  showFeedbackDialog.value = true;
};

// 提交反馈
const submitFeedback = () => {
  ElMessage.success('感谢您的反馈，我们会持续改进算法');
  showFeedbackDialog.value = false;
  // 实际实现中，这里会将反馈发送给后端
};

// 重置决策树
const resetDecisionTree = () => {
  showTree.value = false;
  decisionPath.value = [];
  treeParams.pestType = '';
  ElMessage.info('已重置决策树');
};

// 窗口调整函数
let resizeTimeout: number | null = null;
const handleResize = () => {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }
  
  resizeTimeout = window.setTimeout(() => {
    if (showTree.value && treeContainer.value) {
      renderDecisionTree();
    }
  }, 200);
};

onMounted(() => {
  // 添加窗口调整事件监听
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 移除窗口调整事件监听
  window.removeEventListener('resize', handleResize);
  
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }
});
</script>

<style scoped>
.decision-tree {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 决策面板区域 */
.decision-panels {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* 参数表单 */
.parameter-form {
  padding: 10px;
}

.w-full {
  width: 100%;
}

.generate-btn {
  background-color: #3b82f6;
  border-color: #3b82f6;
  padding: 8px 20px;
  font-weight: 500;
}

/* 决策树可视化 */
.tree-visualization {
  width: 100%;
  height: 100%;
}

.tree-container {
  width: 100%;
  height: 500px;
  overflow: auto;
}

/* 决策路径回顾 */
.decision-path-review {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.timeline-content {
  background-color: rgba(31, 41, 55, 0.3);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.timeline-content h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #e5e7eb;
}

.timeline-content p {
  margin-bottom: 15px;
  color: #9ca3af;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.decision-stats {
  display: flex;
  align-items: center;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.stat-label {
  font-size: 12px;
  color: #9ca3af;
}

.stat-value {
  font-size: 16px;
  font-weight: 500;
  color: #e5e7eb;
}

.refresh-info {
  display: flex;
  align-items: center;
  margin-left: 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .decision-panels {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .decision-stats {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 深色模式调整 */
:deep(.el-form-item__label) {
  color: #d1d5db !important;
}

:deep(.el-radio__label) {
  color: #d1d5db !important;
}

:deep(.el-timeline-item__node) {
  background-color: transparent !important;
}

:deep(.el-timeline-item__wrapper) {
  margin-left: 10px;
}
</style>

<!--
注意: 此组件依赖d3库，请确保已安装：
npm install d3 --save

该组件需要DeviceManagement组件中的DataPanel组件，如果决策支持模块没有这个组件，
请先从DeviceManagement组件复制DataPanel.vue到DecisionSupport/components目录下。
--> 