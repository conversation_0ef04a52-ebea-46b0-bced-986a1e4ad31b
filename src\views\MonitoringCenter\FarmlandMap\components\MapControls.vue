<template>
  <div class="map-controls map-card card-compact" :class="{ 'minimized': isMinimized }">
    <div v-if="!isMinimized" class="card-header">
      <h3 class="card-title">
        <el-icon class="title-icon"><Setting /></el-icon>
        地图控制
      </h3>
      <div class="card-actions">
        <el-button type="text" size="small" @click="isMinimized = true" title="最小化">
          <el-icon><Minus /></el-icon>
        </el-button>
      </div>
    </div>
    
    <div v-if="!isMinimized" class="card-body">
      <div class="control-section">
        <div class="section-title">图层控制</div>
        <div class="control-options">
          <el-checkbox v-model="showDevices">显示设备</el-checkbox>
          <el-checkbox v-model="showRegions">显示区域</el-checkbox>
          <el-checkbox v-model="showLabels">显示标签</el-checkbox>
        </div>
      </div>
      
      <div class="control-section">
        <div class="section-title">视图切换</div>
        <div class="view-buttons">
          <el-button size="small" :type="currentView === '2d' ? 'primary' : ''" @click="switchView('2d')">2D视图</el-button>
          <el-button size="small" :type="currentView === '3d' ? 'primary' : ''" @click="switchView('3d')">3D视图</el-button>
        </div>
      </div>
      
      <div class="control-section">
        <div class="section-title">地图样式</div>
        <el-select v-model="mapStyle" size="small" placeholder="选择地图样式" style="width: 100%">
          <el-option label="标准样式" value="standard"></el-option>
          <el-option label="卫星影像" value="satellite"></el-option>
          <el-option label="地形样式" value="terrain"></el-option>
          <el-option label="暗色模式" value="dark"></el-option>
        </el-select>
      </div>
    </div>
    
    <!-- 最小化时只显示图标 -->
    <div v-if="isMinimized" class="minimized-card" @click="isMinimized = false">
      <el-icon><Setting /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Setting, Minus } from '@element-plus/icons-vue';
import { threeService } from '../services/threeService';

// 控件状态
const showDevices = ref(true);
const showRegions = ref(true);
const showLabels = ref(true);
const currentView = ref('3d'); // 默认使用3D视图
const mapStyle = ref('standard');
const isMinimized = ref(false);

// 监听图层控制变化
watch(showDevices, (value) => {
  // 通知ThreeService更新设备显示状态
  // 这里假设ThreeService有toggleDevicesVisibility方法
  // 如果没有，需要在ThreeService中添加
  if (typeof threeService.toggleDevicesVisibility === 'function') {
    threeService.toggleDevicesVisibility(value);
  }
});

watch(showLabels, (value) => {
  // 通知ThreeService更新标签显示状态
  // 这里假设ThreeService有toggleLabelsVisibility方法
  // 如果没有，需要在ThreeService中添加
  if (typeof threeService.toggleLabelsVisibility === 'function') {
    threeService.toggleLabelsVisibility(value);
  }
});

// 切换视图
const switchView = (view: '2d' | '3d') => {
  currentView.value = view;
  // 通知ThreeService切换视图模式
  // 这里假设ThreeService有switchViewMode方法
  // 如果没有，需要在ThreeService中添加
  if (typeof threeService.switchViewMode === 'function') {
    threeService.switchViewMode(view);
  }
};

// 监听地图样式变化
watch(mapStyle, (value) => {
  // 通知ThreeService更新地图样式
  // 这里假设ThreeService有changeMapStyle方法
  // 如果没有，需要在ThreeService中添加
  if (typeof threeService.changeMapStyle === 'function') {
    threeService.changeMapStyle(value);
  }
});
</script>

<style scoped lang="scss">
@use '../styles/variables.scss' as vars;

.map-controls {
  width: 280px;
  
  &.minimized {
    width: auto;
    height: auto;
    padding: 0;
  }
  
  .minimized-card {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    .el-icon {
      font-size: 20px;
      color: vars.$primary-color;
    }
  }
  
  .control-section {
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 14px;
      color: vars.$text-secondary;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    .control-options {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    
    .view-buttons {
      display: flex;
      gap: 10px;
    }
  }
}
</style> 