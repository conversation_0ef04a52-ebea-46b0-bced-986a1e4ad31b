<!-- 
  TaskEditForm.vue
  任务编辑表单组件，用于编辑周期性巡航任务的信息
-->
<template>
  <div class="task-edit-form">
    <div class="form-header">
      <h3>{{ isCreate ? '创建新任务' : '编辑任务' }}</h3>
      <div class="header-actions">
        <el-button @click="$emit('cancel')">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </div>
    
    <div class="form-content">
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-position="top"
        status-icon
      >
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          <div class="form-grid">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入任务名称" />
            </el-form-item>
            
            <el-form-item label="任务类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择任务类型" class="full-width">
                <el-option label="巡逻任务" value="patrol" />
                <el-option label="喷洒任务" value="spray" />
                <el-option label="检查任务" value="inspection" />
                <el-option label="其他任务" value="other" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="formData.priority" placeholder="请选择优先级" class="full-width">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="emergency" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="启用状态" prop="enabled">
              <el-switch
                v-model="formData.enabled"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </div>
          
          <el-form-item label="任务描述" prop="description">
            <el-input 
              v-model="formData.description" 
              type="textarea" 
              :rows="4" 
              placeholder="请输入任务描述"
            />
          </el-form-item>
        </div>
        
        <div class="form-section">
          <h4 class="section-title">执行计划</h4>
          <div class="form-grid">
            <el-form-item label="执行周期类型" prop="cycleType">
              <el-select v-model="formData.cycleType" placeholder="请选择周期类型" class="full-width">
                <el-option label="单次执行" value="once" />
                <el-option label="每天执行" value="daily" />
                <el-option label="每周执行" value="weekly" />
                <el-option label="每月执行" value="monthly" />
              </el-select>
            </el-form-item>
            
            <el-form-item 
              label="周期值" 
              prop="cycleValue"
              v-if="formData.cycleType !== 'once'"
            >
              <el-input-number 
                v-model="formData.cycleValue" 
                :min="1" 
                :max="30" 
                class="full-width"
              />
            </el-form-item>
            
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                class="full-width"
              />
            </el-form-item>
          </div>
        </div>
        
        <div class="form-section">
          <h4 class="section-title">执行设备</h4>
          <el-form-item prop="deviceIds">
            <div class="devices-selector">
              <div class="selected-devices">
                <el-tag
                  v-for="deviceId in formData.deviceIds"
                  :key="deviceId"
                  closable
                  @close="removeDevice(deviceId)"
                  class="device-tag"
                  effect="dark"
                >
                  {{ deviceId }}
                </el-tag>
                <el-button 
                  v-if="!isSelectingDevice" 
                  class="add-device-btn" 
                  @click="isSelectingDevice = true"
                  type="primary"
                  plain
                  size="small"
                >
                  <el-icon><Plus /></el-icon>
                  添加设备
                </el-button>
              </div>
              
              <div v-if="isSelectingDevice" class="device-selection">
                <el-input
                  v-model="deviceSearchQuery"
                  placeholder="搜索设备..."
                  clearable
                  @clear="deviceSearchQuery = ''"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                
                <div class="device-list">
                  <el-checkbox-group v-model="formData.deviceIds">
                    <el-checkbox 
                      v-for="device in filteredDevices" 
                      :key="device.id" 
                      :label="device.id"
                    >
                      {{ device.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
                
                <div class="selection-actions">
                  <el-button size="small" @click="isSelectingDevice = false">完成</el-button>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Close, 
  Check, 
  Plus, 
  Search 
} from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import type { PeriodicTask } from '@/types/taskScheduling';

const props = defineProps<{
  task?: PeriodicTask
}>();

const emit = defineEmits<{
  (e: 'save', task: PeriodicTask): void
  (e: 'cancel'): void
}>();

// 判断是否为创建模式
const isCreate = computed(() => !props.task?.id);

// 表单引用
const formRef = ref<FormInstance>();

// 加载状态
const loading = ref(false);

// 默认任务数据
const defaultTaskData = (): any => {
  return {
    name: '',
    description: '',
    type: 'patrol',
    deviceIds: [],
    cycleType: 'daily',
    cycleValue: 1,
    startTime: new Date(),
    priority: 'medium',
    status: 'pending',
    enabled: true
  };
};

// 初始化表单数据
const initFormData = () => {
  if (props.task) {
    return { ...props.task };
  } else {
    return defaultTaskData();
  }
};

const formData = reactive(initFormData());

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  cycleType: [
    { required: true, message: '请选择执行周期类型', trigger: 'change' }
  ],
  cycleValue: [
    { required: true, message: '请输入周期值', trigger: 'blur' },
    { type: 'number', min: 1, message: '周期值必须大于0', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  deviceIds: [
    { type: 'array', required: true, message: '请至少选择一个设备', trigger: 'change' }
  ]
});

// 设备选择相关
const isSelectingDevice = ref(false);
const deviceSearchQuery = ref('');

// 模拟设备列表数据
const devices = [
  { id: 'device-001', name: '机器狗 Alpha-X' },
  { id: 'device-002', name: '机器狗 Beta-Z' },
  { id: 'device-003', name: '无人机 Sky-7' },
  { id: 'device-004', name: '无人机 Aero-9' },
  { id: 'device-005', name: '捕虫灯 LT-200' },
  { id: 'device-006', name: '超声波装置 US-50' }
];

// 过滤后的设备列表
const filteredDevices = computed(() => {
  if (!deviceSearchQuery.value) return devices;
  
  const query = deviceSearchQuery.value.toLowerCase();
  return devices.filter(device => 
    device.id.toLowerCase().includes(query) || 
    device.name.toLowerCase().includes(query)
  );
});

// 移除设备
const removeDevice = (deviceId: string) => {
  const index = formData.deviceIds.indexOf(deviceId);
  if (index !== -1) {
    formData.deviceIds.splice(index, 1);
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true;
      try {
        // 处理日期格式
        const taskData = {
          ...formData,
          startTime: (formData.startTime && typeof formData.startTime.toISOString === 'function')
            ? (formData.startTime as Date).toISOString() 
            : formData.startTime,
          updatedAt: new Date().toISOString()
        };
        
        // 如果是新建，添加创建时间和ID
        if (isCreate.value) {
          taskData.id = 'task-' + Date.now();
          taskData.createdAt = new Date().toISOString();
          
          // 计算下次执行时间（实际项目中应该由后端计算）
          const nextDate = new Date(taskData.startTime);
          nextDate.setDate(nextDate.getDate() + (taskData.cycleType === 'daily' ? taskData.cycleValue : 0));
          taskData.nextExecutionTime = nextDate.toISOString();
        }
        
        emit('save', taskData);
      } catch (error) {
        console.error('提交表单失败:', error);
        ElMessage.error('提交失败，请重试');
      } finally {
        loading.value = false;
      }
    } else {
      console.log('表单验证失败:', fields);
      ElMessage.error('请完善表单信息');
    }
  });
};

onMounted(() => {
  // 如果是编辑模式，确保日期字段是Date对象
  if (props.task && typeof props.task.startTime === 'string') {
    formData.startTime = new Date(props.task.startTime);
  }
});
</script>

<style scoped>
.task-edit-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(145deg, #1a2234, #2a3349);
  border-radius: 12px;
  overflow: hidden;
}

.form-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3b4863;
}

.form-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #e5e7eb;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.form-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #60a5fa;
  border-bottom: 1px solid #3b4863;
  padding-bottom: 8px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

:deep(.el-form-item__label) {
  color: #9ca3af;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  background-color: rgba(31, 41, 55, 0.3);
  border-color: #4b5563;
  color: #e5e7eb;
}

:deep(.el-input__inner:focus),
:deep(.el-textarea__inner:focus) {
  border-color: #3b82f6;
}

:deep(.el-select-dropdown__item) {
  color: #e5e7eb;
}

:deep(.el-select-dropdown__item.selected) {
  color: #3b82f6;
}

.full-width {
  width: 100%;
}

.devices-selector {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.selected-devices {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  min-height: 32px;
  padding: 5px;
  border-radius: 4px;
  background-color: rgba(31, 41, 55, 0.3);
  border: 1px solid #4b5563;
}

.device-tag {
  margin-right: 0;
}

.add-device-btn {
  height: 28px;
  padding: 0 10px;
}

.device-selection {
  padding: 10px;
  background-color: rgba(31, 41, 55, 0.7);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.device-list {
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 4px;
}

:deep(.el-checkbox) {
  margin-right: 30px;
  margin-bottom: 10px;
  color: #e5e7eb;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #3b82f6;
}

.selection-actions {
  display: flex;
  justify-content: flex-end;
}
</style> 