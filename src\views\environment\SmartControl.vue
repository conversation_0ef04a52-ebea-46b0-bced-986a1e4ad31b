<!-- 
  SmartControl.vue
  智能通风/灌溉联动系统模块
  实时监控和控制温室中的通风、灌溉设备的联动系统
-->
<template>
  <div class="smart-control">
    <!-- 页面标题 -->
    <PageHeader
      title="智能通风/灌溉联动系统"
      description="实时监控和控制温室中的通风、灌溉设备的联动系统"
      icon="Connection"
    >
      <template #actions>
        <div class="status-summary">
          <StatusIndicator :type="systemOnline ? 'success' : 'error'" :label="systemOnline ? '系统在线' : '系统离线'" size="large" />
        </div>
      </template>
    </PageHeader>
    
    <!-- 主要内容区域 -->
    <div class="control-panels">
      <!-- 设备状态面板 -->
      <DataPanel title="设备状态">
        <template #actions>
          <el-button type="primary" size="small" @click="refreshDeviceStatus">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </template>
        <div class="device-grid">
          <div v-for="device in devices" :key="device.id" class="device-card">
            <div class="device-header">
              <div class="device-name">{{ device.name }}</div>
              <el-switch
                v-model="device.active"
                @change="toggleDevice(device.id, device.active)"
                :disabled="!systemOnline"
              />
            </div>
            <div class="device-icon">
              <WaterCupIcon v-if="device.icon === 'WaterCupIcon'" :size="32" />
              <el-icon v-else :size="32"><component :is="device.icon" /></el-icon>
            </div>
            <div class="device-details">
              <div class="detail-item">
                <span class="item-label">状态:</span>
                <el-tag :type="device.status === '正常' ? 'success' : 'warning'" size="small">
                  {{ device.status }}
                </el-tag>
              </div>
              <div class="detail-item">
                <span class="item-label">上次激活:</span>
                <span>{{ device.lastActive }}</span>
              </div>
            </div>
          </div>
        </div>
      </DataPanel>

      <!-- 自动化规则面板 -->
      <DataPanel title="自动化规则">
        <template #actions>
          <el-button type="primary" size="small" @click="addNewRule">
            <el-icon><Plus /></el-icon>
            新增规则
          </el-button>
        </template>
        <div class="rules-list">
          <el-collapse v-model="activeRules">
            <el-collapse-item v-for="rule in rules" :key="rule.id" :name="rule.id">
              <template #title>
                <div class="rule-header">
                  <span class="rule-name">{{ rule.name }}</span>
                  <el-tag :type="rule.enabled ? 'success' : 'info'" size="small">
                    {{ rule.enabled ? '已启用' : '已禁用' }}
                  </el-tag>
                </div>
              </template>
              <div class="rule-content">
                <div class="rule-conditions">
                  <div class="section-title">触发条件</div>
                  <div class="condition-list">
                    <div v-for="(condition, index) in rule.conditions" :key="index" class="condition-item">
                      <el-tag type="warning">{{ condition.sensor }}</el-tag>
                      <span class="condition-operator">{{ condition.operator }}</span>
                      <span class="condition-value">{{ condition.value }}</span>
                    </div>
                  </div>
                </div>
                <div class="rule-actions">
                  <div class="section-title">执行动作</div>
                  <div class="action-list">
                    <div v-for="(action, index) in rule.actions" :key="index" class="action-item">
                      <el-tag type="success">{{ action.device }}</el-tag>
                      <span class="action-operation">{{ action.operation }}</span>
                      <span v-if="action.value !== null && action.value !== undefined" class="action-value">{{ action.value }}%</span>
                    </div>
                  </div>
                </div>
                <div class="rule-controls">
                  <el-switch
                    v-model="rule.enabled"
                    @change="toggleRule(rule.id, rule.enabled)"
                    active-text="启用"
                    inactive-text="禁用"
                  />
                  <div class="rule-buttons">
                    <el-button type="primary" size="small" @click="editRule(rule)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button type="danger" size="small" @click="deleteRule(rule.id)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </DataPanel>
      
      <!-- 系统监控面板 -->
      <DataPanel title="系统监控">
        <template #actions>
          <div class="time-selector">
            <span>时间范围: </span>
            <el-select v-model="timeRange" size="small">
              <el-option label="最近6小时" value="6h" />
              <el-option label="最近24小时" value="24h" />
              <el-option label="最近7天" value="7d" />
            </el-select>
          </div>
        </template>
        <div class="chart-panels">
          <div class="chart-panel">
            <div class="chart-title">设备激活次数</div>
            <div class="chart-container" ref="activationChartRef"></div>
          </div>
          <div class="chart-panel">
            <div class="chart-title">系统能耗分析</div>
            <div class="chart-container" ref="energyChartRef"></div>
          </div>
        </div>
      </DataPanel>
    </div>
    
    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="设备健康" />
        <StatusIndicator type="warning" label="需要维护" />
        <StatusIndicator type="error" label="故障设备" />
        <StatusIndicator type="offline" label="离线设备" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 新增/编辑规则对话框 -->
    <el-dialog
      v-model="ruleDialogVisible"
      :title="editingRule ? '编辑规则' : '新增规则'"
      width="600px"
    >
      <el-form :model="ruleForm" label-width="100px">
        <el-form-item label="规则名称">
          <el-input v-model="ruleForm.name" placeholder="输入规则名称" />
        </el-form-item>
        <el-form-item label="触发条件">
          <div v-for="(condition, index) in ruleForm.conditions" :key="index" class="form-condition">
            <el-select v-model="condition.sensor" placeholder="选择传感器">
              <el-option label="温度" value="温度" />
              <el-option label="湿度" value="湿度" />
              <el-option label="土壤湿度" value="土壤湿度" />
              <el-option label="光照" value="光照" />
            </el-select>
            <el-select v-model="condition.operator" placeholder="条件">
              <el-option label="大于" value="大于" />
              <el-option label="小于" value="小于" />
              <el-option label="等于" value="等于" />
            </el-select>
            <el-input-number v-model="condition.value" :min="0" :max="100" />
            <el-button @click="removeCondition(index)" type="danger" circle>
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <el-button @click="addCondition" type="primary" plain size="small">
            <el-icon><Plus /></el-icon> 添加条件
          </el-button>
        </el-form-item>
        <el-form-item label="执行动作">
          <div v-for="(action, index) in ruleForm.actions" :key="index" class="form-action">
            <el-select v-model="action.device" placeholder="选择设备">
              <el-option label="通风系统" value="通风系统" />
              <el-option label="灌溉系统" value="灌溉系统" />
              <el-option label="天窗" value="天窗" />
              <el-option label="遮阳帘" value="遮阳帘" />
            </el-select>
            <el-select v-model="action.operation" placeholder="操作">
              <el-option label="开启" value="开启" />
              <el-option label="关闭" value="关闭" />
              <el-option label="调整至" value="调整至" />
            </el-select>
            <el-input-number 
              v-if="action.operation === '调整至'" 
              v-model="action.value" 
              :min="0" 
              :max="100" 
            />
            <el-button @click="removeAction(index)" type="danger" circle>
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <el-button @click="addAction" type="primary" plain size="small">
            <el-icon><Plus /></el-icon> 添加动作
          </el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRule">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { 
  Refresh, Plus, Edit, Delete, 
  Connection, Setting, Warning,
  WindPower, Umbrella, Cloudy, SwitchButton
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'
import WaterCupIcon from './components/WaterCupIcon.vue'

// 系统状态
const systemOnline = ref(true)

// 最后更新时间
const lastUpdateTime = ref(new Date())

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 设备列表
const devices = reactive([
  { 
    id: 1, 
    name: '温室通风系统', 
    icon: 'WindPower', 
    active: true, 
    status: '正常', 
    lastActive: '2023-05-20 14:30' 
  },
  { 
    id: 2, 
    name: '滴灌系统', 
    icon: 'WaterCupIcon', 
    active: false, 
    status: '正常', 
    lastActive: '2023-05-20 09:15' 
  },
  { 
    id: 3, 
    name: '喷雾系统', 
    icon: 'Umbrella', 
    active: false, 
    status: '正常', 
    lastActive: '2023-05-19 16:45' 
  },
  { 
    id: 4, 
    name: '天窗控制器', 
    icon: 'SwitchButton', 
    active: true, 
    status: '维护中', 
    lastActive: '2023-05-20 11:20' 
  },
  { 
    id: 5, 
    name: '遮阳帘控制器', 
    icon: 'Cloudy', 
    active: false, 
    status: '正常', 
    lastActive: '2023-05-19 14:10' 
  },
  { 
    id: 6, 
    name: '环境传感器集', 
    icon: 'Setting', 
    active: true, 
    status: '正常', 
    lastActive: '持续运行中' 
  }
])

// 自动化规则
const activeRules = ref(['rule1', 'rule3'])
const rules = reactive([
  {
    id: 'rule1',
    name: '高温自动通风',
    enabled: true,
    conditions: [
      { sensor: '温度', operator: '大于', value: 28 }
    ],
    actions: [
      { device: '天窗', operation: '开启' },
      { device: '通风系统', operation: '开启' }
    ]
  },
  {
    id: 'rule2',
    name: '土壤干燥自动灌溉',
    enabled: false,
    conditions: [
      { sensor: '土壤湿度', operator: '小于', value: 40 }
    ],
    actions: [
      { device: '滴灌系统', operation: '开启' }
    ]
  },
  {
    id: 'rule3',
    name: '高温高湿综合调节',
    enabled: true,
    conditions: [
      { sensor: '温度', operator: '大于', value: 30 },
      { sensor: '湿度', operator: '大于', value: 85 }
    ],
    actions: [
      { device: '通风系统', operation: '调整至', value: 80 },
      { device: '天窗', operation: '开启' },
      { device: '遮阳帘', operation: '开启' }
    ]
  }
])

// 图表相关
const activationChartRef = ref<HTMLElement | null>(null)
const energyChartRef = ref<HTMLElement | null>(null)
const timeRange = ref('24h')
let activationChart: echarts.ECharts | null = null
let energyChart: echarts.ECharts | null = null

// 编辑规则相关
const ruleDialogVisible = ref(false)
const editingRule = ref<any>(null)
const ruleForm = reactive({
  name: '',
  conditions: [{ sensor: '温度', operator: '大于', value: 25 }],
  actions: [{ device: '通风系统', operation: '开启', value: null }]
})

// 刷新设备状态
const refreshDeviceStatus = () => {
  // 实际应用中会调用API获取设备状态
  devices.forEach(device => {
    // 模拟随机状态更新
    if (Math.random() > 0.8) {
      device.status = Math.random() > 0.7 ? '正常' : '维护中'
    }
  })
  lastUpdateTime.value = new Date()
  ElMessage.success('设备状态已更新')
}

// 刷新数据
const refreshData = () => {
  refreshDeviceStatus()
  
  // 重新生成图表数据
  initActivationChart()
  initEnergyChart()
  
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
}

// 切换设备开关
const toggleDevice = (deviceId: number, active: boolean) => {
  ElMessage.success(`设备 ${deviceId} 已${active ? '开启' : '关闭'}`)
  // 实际应用中会调用API控制设备
}

// 添加新规则
const addNewRule = () => {
  editingRule.value = null
  ruleForm.name = '新规则'
  ruleForm.conditions = [{ sensor: '温度', operator: '大于', value: 25 }]
  ruleForm.actions = [{ device: '通风系统', operation: '开启', value: null }]
  ruleDialogVisible.value = true
}

// 编辑规则
const editRule = (rule: any) => {
  editingRule.value = rule
  ruleForm.name = rule.name
  ruleForm.conditions = JSON.parse(JSON.stringify(rule.conditions))
  ruleForm.actions = JSON.parse(JSON.stringify(rule.actions))
  ruleDialogVisible.value = true
}

// 删除规则
const deleteRule = (ruleId: string) => {
  // 实际应用中会调用API删除规则
  const index = rules.findIndex(rule => rule.id === ruleId)
  if (index !== -1) {
    rules.splice(index, 1)
  }
  ElMessage.success('规则已删除')
}

// 切换规则启用状态
const toggleRule = (ruleId: string, enabled: boolean) => {
  ElMessage.success(`规则 ${ruleId} 已${enabled ? '启用' : '禁用'}`)
  // 实际应用中会调用API更新规则状态
}

// 添加条件
const addCondition = () => {
  ruleForm.conditions.push({ sensor: '温度', operator: '大于', value: 25 })
}

// 移除条件
const removeCondition = (index: number) => {
  ruleForm.conditions.splice(index, 1)
}

// 添加动作
const addAction = () => {
  ruleForm.actions.push({ device: '通风系统', operation: '开启', value: null })
}

// 移除动作
const removeAction = (index: number) => {
  ruleForm.actions.splice(index, 1)
}

// 保存规则
const saveRule = () => {
  if (editingRule.value) {
    // 更新现有规则
    const rule = rules.find(r => r.id === editingRule.value.id)
    if (rule) {
      rule.name = ruleForm.name
      rule.conditions = JSON.parse(JSON.stringify(ruleForm.conditions))
      rule.actions = JSON.parse(JSON.stringify(ruleForm.actions))
    }
  } else {
    // 创建新规则
    const newRule = {
      id: `rule${Date.now()}`,
      name: ruleForm.name,
      enabled: true,
      conditions: JSON.parse(JSON.stringify(ruleForm.conditions)),
      actions: JSON.parse(JSON.stringify(ruleForm.actions))
    }
    rules.push(newRule)
  }
  ruleDialogVisible.value = false
  ElMessage.success('规则已保存')
}

// 初始化设备激活图表
const initActivationChart = () => {
  if (!activationChartRef.value) return
  
  if (activationChart) {
    activationChart.dispose()
  }
  
  activationChart = echarts.init(activationChartRef.value as HTMLDivElement)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    legend: {
      data: ['通风系统', '灌溉系统', '天窗', '遮阳帘'],
      textStyle: {
        color: '#9ca3af'
      },
      right: 10,
      top: 0,
      icon: 'roundRect'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0:00', '4:00', '8:00', '12:00', '16:00', '20:00'],
      axisLabel: {
        color: '#9ca3af'
      },
      axisLine: {
        lineStyle: {
          color: '#4b5563'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '激活次数',
      nameTextStyle: {
        color: '#9ca3af'
      },
      axisLabel: {
        color: '#9ca3af'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(75, 85, 99, 0.1)'
        }
      }
    },
    series: [
      {
        name: '通风系统',
        type: 'bar',
        data: [0, 1, 3, 4, 2, 1],
        color: '#3b82f6'
      },
      {
        name: '灌溉系统',
        type: 'bar',
        data: [2, 0, 0, 1, 3, 0],
        color: '#10b981'
      },
      {
        name: '天窗',
        type: 'bar',
        data: [0, 2, 4, 3, 2, 0],
        color: '#f59e0b'
      },
      {
        name: '遮阳帘',
        type: 'bar',
        data: [0, 0, 3, 5, 3, 0],
        color: '#8b5cf6'
      }
    ]
  }
  
  activationChart.setOption(option)
}

// 初始化能耗分析图表
const initEnergyChart = () => {
  if (!energyChartRef.value) return
  
  if (energyChart) {
    energyChart.dispose()
  }
  
  energyChart = echarts.init(energyChartRef.value as HTMLDivElement)
  
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(31, 41, 55, 0.8)',
      borderColor: '#3b82f6',
      textStyle: {
        color: '#e5e7eb'
      }
    },
    legend: {
      top: '5%',
      left: 'center',
      textStyle: {
        color: '#9ca3af'
      },
      icon: 'roundRect'
    },
    series: [
      {
        name: '能耗占比',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#1f2937',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            color: '#e5e7eb'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 45, name: '通风系统', itemStyle: { color: '#3b82f6' } },
          { value: 25, name: '灌溉系统', itemStyle: { color: '#10b981' } },
          { value: 15, name: '天窗', itemStyle: { color: '#f59e0b' } },
          { value: 10, name: '遮阳帘', itemStyle: { color: '#8b5cf6' } },
          { value: 5, name: '其他设备', itemStyle: { color: '#ef4444' } }
        ]
      }
    ]
  }
  
  energyChart.setOption(option)
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  activationChart?.resize()
  energyChart?.resize()
}

// 模拟数据自动更新
let dataUpdateInterval: number | null = null

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initActivationChart()
    initEnergyChart()
  })
  
  window.addEventListener('resize', handleResize)
  
  // 设置自动更新间隔
  dataUpdateInterval = window.setInterval(() => {
    // 小幅度随机波动数据
    devices.forEach(device => {
      if (device.active && Math.random() > 0.7) {
        device.lastActive = new Date().toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      }
    })
    
    lastUpdateTime.value = new Date()
  }, 60000) // 每60秒更新一次
})

// 组件销毁
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  activationChart?.dispose()
  energyChart?.dispose()
  
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval)
  }
})
</script>

<style scoped>
.smart-control {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

/* 主要内容面板 */
.control-panels {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  flex: 1;
}

/* 设备卡片网格 */
.device-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 10px;
}

.device-card {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-name {
  font-weight: 500;
  font-size: 14px;
  color: #e5e7eb;
}

.device-icon {
  display: flex;
  justify-content: center;
  padding: 12px 0;
  color: #3b82f6;
}

.device-details {
  font-size: 12px;
  color: #9ca3af;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.item-label {
  color: #9ca3af;
}

/* 规则列表 */
.rules-list {
  margin-top: 10px;
}

.rule-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rule-name {
  font-weight: 500;
}

.rule-content {
  padding: 12px;
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  margin-top: 12px;
}

.rule-conditions,
.rule-actions {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #e5e7eb;
  margin-bottom: 8px;
}

.condition-list,
.action-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.condition-item,
.action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(31, 41, 55, 0.5);
  padding: 8px 12px;
  border-radius: 4px;
}

.condition-operator,
.action-operation,
.action-value,
.condition-value {
  color: #e5e7eb;
}

.rule-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.rule-buttons {
  display: flex;
  gap: 8px;
}

/* 图表面板 */
.chart-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  height: calc(100% - 40px);
}

.chart-panel {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #e5e7eb;
  margin-bottom: 16px;
  text-align: center;
}

.chart-container {
  height: calc(100% - 40px);
}

.time-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #9ca3af;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 表单样式 */
.form-condition,
.form-action {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

/* Element Plus组件样式调整 */
:deep(.el-collapse) {
  border: none;
  background-color: transparent;
}

:deep(.el-collapse-item__header) {
  background-color: rgba(31, 41, 55, 0.3);
  color: #e5e7eb;
  border-color: rgba(59, 130, 246, 0.2);
  padding: 0 12px;
}

:deep(.el-collapse-item__wrap) {
  background-color: transparent;
  border-color: rgba(59, 130, 246, 0.2);
}

:deep(.el-collapse-item__content) {
  padding: 10px 0;
}

:deep(.el-input__inner),
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background-color: rgba(31, 41, 55, 0.8);
  border-color: rgba(59, 130, 246, 0.2);
  color: #e5e7eb;
}

:deep(.el-select .el-input .el-select__caret) {
  color: #e5e7eb;
}

:deep(.el-dialog) {
  background-color: #1f2937;
  border-radius: 8px;
}

:deep(.el-dialog__title) {
  color: #e5e7eb;
}

:deep(.el-dialog__body),
:deep(.el-dialog__footer) {
  border-top-color: rgba(59, 130, 246, 0.2);
}

:deep(.el-form-item__label) {
  color: #e5e7eb;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .control-panels {
    grid-template-columns: 1fr;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .device-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-panels {
    grid-template-columns: 1fr;
  }
}
</style>

<!--
注意: 此组件需要安装echarts依赖：
npm install echarts --save

同时在element-plus中存在一个问题，WaterCup图标需要额外导入：
import { WaterCup } from '@element-plus/icons-svg'
或者自定义一个图标组件替代它。
--> 