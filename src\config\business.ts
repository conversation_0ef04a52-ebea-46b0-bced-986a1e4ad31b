/**
 * 业务逻辑配置
 * 存放业务相关的配置参数和默认值
 */

import envConfig from './env';

// 模拟数据配置
export const SIMULATION_CONFIG = {
  // 模拟API延迟（毫秒）
  API_DELAY: envConfig.simulatedDelay,
  // 模拟数据更新间隔（毫秒）
  DATA_UPDATE_INTERVAL: 5000,
  // 模拟错误概率（0-1）
  ERROR_PROBABILITY: 0.05,
  // 模拟网络延迟范围（毫秒）
  NETWORK_DELAY_RANGE: [100, 500]
} as const;

// 数据记录配置
export const DATA_RECORDING_CONFIG = {
  // 是否启用数据记录
  ENABLED: false,
  // 最大记录数量
  MAX_RECORDS: envConfig.dataRecordingMaxRecords,
  // 是否自动保存
  AUTO_SAVE: true,
  // 自动保存间隔（分钟）
  SAVE_INTERVAL: envConfig.dataRecordingSaveInterval,
  // 数据压缩启用
  COMPRESSION_ENABLED: true
} as const;

// 设备追踪配置
export const DEVICE_TRACKING_CONFIG = {
  // 最大追踪点数量
  MAX_TRACK_POINTS: envConfig.maxTrackPoints,
  // 追踪数据更新间隔（毫秒）
  UPDATE_INTERVAL: 1000,
  // 轨迹平滑处理
  SMOOTH_TRAJECTORY: true,
  // 历史轨迹保留时间（小时）
  HISTORY_RETENTION_HOURS: 24,
  // 轨迹平滑算法配置
  TRAJECTORY_SMOOTHING: {
    // 默认启用状态
    ENABLED: true,
    // 移动平均窗口大小
    MOVING_AVERAGE_WINDOW: 5,
    // 贝塞尔曲线控制点权重
    BEZIER_WEIGHT: 0.3,
    // 异常点检测阈值（标准差倍数）
    OUTLIER_THRESHOLD: 2.0,
    // 最小距离阈值（米）
    MIN_DISTANCE_THRESHOLD: 0.001,
    // 自适应窗口启用
    ADAPTIVE_WINDOW: true,
    // 平滑强度 (0-1)
    SMOOTHING_STRENGTH: 0.7,
    // 实时平滑处理
    REAL_TIME_SMOOTHING: true,
    // 平滑算法类型
    ALGORITHM_TYPE: 'hybrid' as 'moving_average' | 'bezier' | 'hybrid'
  }
} as const;

// 农药管理配置
export const PESTICIDE_CONFIG = {
  // 默认稀释比例
  DEFAULT_DILUTION_RATIO: 1000,
  // 最大稀释比例
  MAX_DILUTION_RATIO: 10000,
  // 最小稀释比例
  MIN_DILUTION_RATIO: 100,
  // 安全间隔期（天）
  SAFETY_INTERVAL_DAYS: 7,
  // 最大单次用药量（kg/ha）
  MAX_SINGLE_DOSE: 5,
  // 推荐用药浓度范围
  RECOMMENDED_CONCENTRATION: {
    MIN: 0.1,
    MAX: 2.0
  }
} as const;

// 环境监测配置
export const ENVIRONMENT_CONFIG = {
  // 温度阈值配置
  TEMPERATURE_THRESHOLDS: {
    LOW: 10,
    HIGH: 35,
    OPTIMAL_MIN: 18,
    OPTIMAL_MAX: 28
  },
  // 湿度阈值配置
  HUMIDITY_THRESHOLDS: {
    LOW: 30,
    HIGH: 90,
    OPTIMAL_MIN: 50,
    OPTIMAL_MAX: 70
  },
  // 土壤湿度阈值配置
  SOIL_MOISTURE_THRESHOLDS: {
    DRY: 30,
    WET: 80,
    OPTIMAL_MIN: 40,
    OPTIMAL_MAX: 70
  },
  // 光照强度阈值配置
  ILLUMINANCE_THRESHOLDS: {
    LOW: 1000,
    HIGH: 50000,
    OPTIMAL_MIN: 10000,
    OPTIMAL_MAX: 30000
  }
} as const;

// 任务调度配置
export const TASK_SCHEDULING_CONFIG = {
  // 任务执行超时时间（分钟）
  TASK_TIMEOUT_MINUTES: 60,
  // 最大并发任务数
  MAX_CONCURRENT_TASKS: 5,
  // 任务重试次数
  MAX_RETRY_ATTEMPTS: 3,
  // 任务优先级级别
  PRIORITY_LEVELS: {
    LOW: 1,
    NORMAL: 2,
    HIGH: 3,
    URGENT: 4,
    CRITICAL: 5
  },
  // 定期任务默认配置
  PERIODIC_TASK_DEFAULTS: {
    ENABLED: true,
    RETRY_ON_FAILURE: true,
    NOTIFICATION_ENABLED: true
  }
} as const;

// 病虫害分析配置
export const PEST_ANALYSIS_CONFIG = {
  // 热力图数据点密度
  HEATMAP_DENSITY: 50,
  // 病虫害严重程度阈值
  SEVERITY_THRESHOLDS: {
    LOW: 0.3,
    MEDIUM: 0.6,
    HIGH: 0.8
  },
  // 预警级别配置
  WARNING_LEVELS: {
    INFO: {
      threshold: 0.2,
      color: '#1890ff',
      label: '信息'
    },
    WARNING: {
      threshold: 0.5,
      color: '#faad14',
      label: '警告'
    },
    CRITICAL: {
      threshold: 0.8,
      color: '#f5222d',
      label: '严重'
    }
  }
} as const;

// 地图配置
export const MAP_CONFIG = {
  // 默认中心点坐标（北京）
  DEFAULT_CENTER: [116.397428, 39.90923],
  // 默认缩放级别
  DEFAULT_ZOOM: 10,
  // 最小缩放级别
  MIN_ZOOM: 3,
  // 最大缩放级别
  MAX_ZOOM: 18,
  // 地图样式
  MAP_STYLES: [
    { label: '标准', value: 'normal' },
    { label: '卫星', value: 'satellite' },
    { label: '混合', value: 'hybrid' },
    { label: '路网', value: 'roadnet' }
  ]
} as const;

// 文件上传配置
export const FILE_UPLOAD_CONFIG = {
  // 最大文件大小（MB）
  MAX_FILE_SIZE: 10,
  // 允许的文件类型
  ALLOWED_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  // 上传超时时间（毫秒）
  UPLOAD_TIMEOUT: 30000,
  // 分片上传大小（MB）
  CHUNK_SIZE: 2
} as const;

// 通知配置
export const NOTIFICATION_CONFIG = {
  // 通知显示时间（毫秒）
  DISPLAY_DURATION: 5000,
  // 最大通知数量
  MAX_NOTIFICATIONS: 5,
  // 通知类型配置
  TYPES: {
    SYSTEM: {
      icon: 'bell',
      color: '#1890ff'
    },
    ALERT: {
      icon: 'warning',
      color: '#faad14'
    },
    ERROR: {
      icon: 'close-circle',
      color: '#f5222d'
    },
    SUCCESS: {
      icon: 'check-circle',
      color: '#52c41a'
    }
  }
} as const;

// 数据导出配置
export const DATA_EXPORT_CONFIG = {
  // 支持的导出格式
  SUPPORTED_FORMATS: [
    { label: 'Excel (.xlsx)', value: 'xlsx' },
    { label: 'CSV (.csv)', value: 'csv' },
    { label: 'PDF (.pdf)', value: 'pdf' },
    { label: 'JSON (.json)', value: 'json' }
  ],
  // 最大导出记录数
  MAX_EXPORT_RECORDS: 10000,
  // 导出超时时间（毫秒）
  EXPORT_TIMEOUT: 60000
} as const;

// 搜索配置
export const SEARCH_CONFIG = {
  // 搜索防抖延迟（毫秒）
  DEBOUNCE_DELAY: 300,
  // 最小搜索字符数
  MIN_SEARCH_LENGTH: 2,
  // 最大搜索结果数
  MAX_SEARCH_RESULTS: 100,
  // 搜索历史保存数量
  SEARCH_HISTORY_SIZE: 10
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  // 缓存过期时间（毫秒）
  EXPIRY_TIME: 5 * 60 * 1000, // 5分钟
  // 最大缓存大小
  MAX_CACHE_SIZE: 100,
  // 缓存键前缀
  KEY_PREFIX: 'smart_agriculture_',
  // 启用本地存储缓存
  LOCAL_STORAGE_ENABLED: true
} as const;

// 性能配置
export const PERFORMANCE_CONFIG = {
  // 虚拟滚动阈值
  VIRTUAL_SCROLL_THRESHOLD: 100,
  // 图片懒加载阈值
  LAZY_LOAD_THRESHOLD: 200,
  // 防抖延迟（毫秒）
  DEBOUNCE_DELAY: 300,
  // 节流延迟（毫秒）
  THROTTLE_DELAY: 100
} as const;
