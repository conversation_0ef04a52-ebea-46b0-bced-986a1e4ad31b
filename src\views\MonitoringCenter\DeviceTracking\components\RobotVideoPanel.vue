<!--
机器狗视频显示面板
功能特性：
1. HTTP视频流显示
2. 连接状态指示
3. 视频流控制
4. 错误处理和重连
-->

<template>
  <div class="robot-video-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <div class="panel-title">
        <el-icon class="title-icon"><VideoCamera /></el-icon>
        <span>机器狗视频</span>
        <el-badge
          :value="connectionStatus"
          :type="connectionBadgeType"
          class="status-badge"
        />
      </div>
      <div class="panel-actions">
        <el-button
          size="small"
          :type="isConnected ? 'danger' : 'primary'"
          @click="toggleConnection"
          :loading="isConnecting"
        >
          {{ isConnected ? '断开' : '连接' }}
        </el-button>
        <el-button size="small" @click="refreshVideo" circle>
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 视频显示区域 -->
    <div class="video-container">
      <!-- MJPEG视频流元素 -->
      <img
        ref="videoElement"
        class="video-stream"
        :src="videoStreamUrl"
        @load="handleVideoLoad"
        @error="handleVideoError"
        @loadstart="handleVideoLoadStart"
      />

      <!-- 加载状态覆盖层 -->
      <div v-if="isLoading" class="loading-overlay">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span class="loading-text">正在加载视频流...</span>
      </div>

      <!-- 错误状态覆盖层 -->
      <div v-if="hasError && !isLoading" class="error-overlay">
        <el-icon class="error-icon"><Warning /></el-icon>
        <div class="error-content">
          <p class="error-title">视频流加载失败</p>
          <p class="error-message">{{ errorMessage }}</p>
          <el-button type="primary" size="small" @click="retryConnection">
            重试连接
          </el-button>
        </div>
      </div>

      <!-- 视频信息覆盖层 -->
      <div class="video-info-overlay">
        <div class="video-info">
          <span class="video-source">实时视频流 • {{ robotIP }}</span>
          <span class="video-status" :class="{ 'status-live': isPlaying }">
            {{ isPlaying ? 'LIVE' : 'OFFLINE' }}
          </span>
        </div>
      </div>

      <!-- 科技感边框效果 -->
      <div class="tech-frame">
        <div class="corner top-left"></div>
        <div class="corner top-right"></div>
        <div class="corner bottom-left"></div>
        <div class="corner bottom-right"></div>
        <div class="scan-line" v-if="isPlaying"></div>
      </div>
    </div>

    <!-- 控制信息 -->
    <div class="control-info">
      <div class="info-item">
        <span class="info-label">视频源：</span>
        <span class="info-value">{{ videoStreamUrl }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">状态：</span>
        <span class="info-value" :class="statusClass">{{ statusText }}</span>
      </div>
      <div class="info-item" v-if="lastUpdateTime">
        <span class="info-label">更新时间：</span>
        <span class="info-value">{{ formatTime(lastUpdateTime) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoCamera, Refresh, Loading, Warning } from '@element-plus/icons-vue'
import { robotWebSocketService, ConnectionStatus } from '@/services/robotWebSocketService'
import envConfig from '@/config/env'

// 组件属性
interface Props {
  autoConnect?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoConnect: true
})

// 组件事件
const emit = defineEmits<{
  'connection-change': [connected: boolean]
  'video-status-change': [status: string]
  'error': [error: Error]
}>()

// 响应式数据
const videoElement = ref<HTMLImageElement | null>(null)
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const isPlaying = ref(false)
const isConnecting = ref(false)
const lastUpdateTime = ref<Date | null>(null)

// MJPEG流监控
let streamMonitorTimer: number | null = null

// 计算属性
const robotIP = computed(() => envConfig.robotIP)
const videoStreamUrl = computed(() =>
  `http://${envConfig.videoStreamHost}:${envConfig.videoStreamPort}/camera/stream`
)

const connectionStatus = computed(() => {
  switch (robotWebSocketService.connectionState.status) {
    case ConnectionStatus.CONNECTED: return '已连接'
    case ConnectionStatus.CONNECTING: return '连接中'
    case ConnectionStatus.RECONNECTING: return '重连中'
    case ConnectionStatus.DISCONNECTED: return '已断开'
    case ConnectionStatus.ERROR: return '错误'
    default: return '未知'
  }
})

const connectionBadgeType = computed(() => {
  switch (robotWebSocketService.connectionState.status) {
    case ConnectionStatus.CONNECTED: return 'success'
    case ConnectionStatus.CONNECTING:
    case ConnectionStatus.RECONNECTING: return 'warning'
    case ConnectionStatus.ERROR: return 'danger'
    default: return 'info'
  }
})

const isConnected = computed(() => robotWebSocketService.connectionState.connected)

const statusText = computed(() => {
  if (hasError.value) return '错误'
  if (isLoading.value) return '加载中'
  if (isPlaying.value) return '播放中'
  return '离线'
})

const statusClass = computed(() => ({
  'status-success': isPlaying.value,
  'status-warning': isLoading.value,
  'status-error': hasError.value,
  'status-offline': !isPlaying.value && !isLoading.value && !hasError.value
}))

// MJPEG视频流事件处理
const handleVideoLoadStart = () => {
  console.log('MJPEG流开始加载')
  isLoading.value = true
  hasError.value = false
  isPlaying.value = false
  emit('video-status-change', 'loading')
}

const handleVideoLoad = () => {
  console.log('MJPEG流加载成功')
  isLoading.value = false
  hasError.value = false
  isPlaying.value = true
  lastUpdateTime.value = new Date()
  emit('video-status-change', 'playing')

  // MJPEG流加载成功后，开始监控流状态
  startStreamMonitoring()
}

const handleVideoError = (event: Event) => {
  console.error('MJPEG流加载错误:', event)
  isLoading.value = false
  hasError.value = true
  isPlaying.value = false

  // 停止流监控
  stopStreamMonitoring()

  // MJPEG流错误处理
  const img = event.target as HTMLImageElement

  // 根据HTTP状态或网络错误判断错误类型
  if (!navigator.onLine) {
    errorMessage.value = '网络连接断开'
  } else if (img.src && img.src.includes('192.168.8.7')) {
    errorMessage.value = '无法连接到视频流服务器'
  } else {
    errorMessage.value = 'MJPEG视频流加载失败'
  }

  emit('video-status-change', 'error')
  emit('error', new Error(errorMessage.value))

  ElMessage.error(`视频流错误: ${errorMessage.value}`)

  // 自动重试机制
  setTimeout(() => {
    if (hasError.value && isConnected.value) {
      console.log('尝试重新加载MJPEG流')
      refreshVideo()
    }
  }, 5000)
}

// 连接控制
const toggleConnection = async () => {
  if (isConnected.value) {
    await disconnect()
  } else {
    await connect()
  }
}

const connect = async () => {
  try {
    isConnecting.value = true
    await robotWebSocketService.connect()
    await robotWebSocketService.sendConnectCommand()

    ElMessage.success('机器狗连接成功')
    emit('connection-change', true)

    // 刷新视频流
    refreshVideo()

  } catch (error) {
    console.error('连接失败:', error)
    ElMessage.error(`连接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    emit('error', error instanceof Error ? error : new Error('连接失败'))
  } finally {
    isConnecting.value = false
  }
}

const disconnect = async () => {
  try {
    robotWebSocketService.disconnect()

    // 停止MJPEG流
    stopStreamMonitoring()
    if (videoElement.value) {
      videoElement.value.src = ''
    }

    isPlaying.value = false
    ElMessage.info('机器狗连接已断开')
    emit('connection-change', false)

  } catch (error) {
    console.error('断开连接失败:', error)
    ElMessage.error('断开连接失败')
  }
}

const refreshVideo = () => {
  if (videoElement.value) {
    // 停止当前监控
    stopStreamMonitoring()

    // 重新加载MJPEG流
    const currentSrc = videoElement.value.src
    videoElement.value.src = ''

    setTimeout(() => {
      if (videoElement.value) {
        videoElement.value.src = currentSrc + '?t=' + Date.now() // 添加时间戳避免缓存
      }
    }, 100)
  }

  lastUpdateTime.value = new Date()
  ElMessage.info('MJPEG视频流已刷新')
}

// 开始流监控
const startStreamMonitoring = () => {
  stopStreamMonitoring()

  streamMonitorTimer = window.setInterval(() => {
    if (videoElement.value && isPlaying.value) {
      // 检查图像是否正在更新
      const now = Date.now()
      if (lastUpdateTime.value && (now - lastUpdateTime.value.getTime()) > 10000) {
        console.warn('MJPEG流可能已停止更新')
        // 可以在这里触发重连逻辑
      }
      lastUpdateTime.value = new Date()
    }
  }, 2000) // 每2秒检查一次
}

// 停止流监控
const stopStreamMonitoring = () => {
  if (streamMonitorTimer) {
    clearInterval(streamMonitorTimer)
    streamMonitorTimer = null
  }
}

const retryConnection = () => {
  hasError.value = false
  errorMessage.value = ''
  refreshVideo()
}

// 工具函数
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 生命周期
onMounted(() => {
  console.log('机器狗视频面板已加载')

  // 设置WebSocket事件监听
  robotWebSocketService.setStatusChangeCallback((status) => {
    console.log('WebSocket状态变化:', status)
  })

  robotWebSocketService.setErrorCallback((error) => {
    console.error('WebSocket错误:', error)
    emit('error', error)
  })

  // 自动连接
  if (props.autoConnect) {
    connect()
  }
})

onUnmounted(() => {
  // 清理资源
  stopStreamMonitoring()
  if (videoElement.value) {
    videoElement.value.src = ''
  }
})
</script>

<style lang="scss" scoped>
.robot-video-panel {
  background: rgba(31, 41, 55, 0.9);
  border-radius: 16px;
  border: 1px solid rgba(75, 85, 99, 0.3);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: rgba(31, 41, 55, 0.8);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);

    .panel-title {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 16px;
      font-weight: 600;
      color: #f3f4f6;

      .title-icon {
        color: #3b82f6;
        font-size: 18px;
      }

      .status-badge {
        margin-left: 8px;
      }
    }

    .panel-actions {
      display: flex;
      gap: 8px;

      .el-button {
        background: rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.3);
        color: #3b82f6;

        &:hover {
          background: rgba(59, 130, 246, 0.3);
        }

        &.el-button--danger {
          background: rgba(239, 68, 68, 0.2);
          border-color: rgba(239, 68, 68, 0.3);
          color: #ef4444;

          &:hover {
            background: rgba(239, 68, 68, 0.3);
          }
        }
      }
    }
  }

  .video-container {
    flex: 1;
    position: relative;
    background: #000;
    min-height: 300px;

    .video-stream {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .loading-overlay,
    .error-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      color: #f3f4f6;
    }

    .loading-overlay {
      .loading-icon {
        font-size: 48px;
        color: #3b82f6;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      .loading-text {
        font-size: 16px;
        color: #9ca3af;
      }
    }

    .error-overlay {
      .error-icon {
        font-size: 48px;
        color: #ef4444;
        margin-bottom: 16px;
      }

      .error-content {
        text-align: center;

        .error-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #f3f4f6;
        }

        .error-message {
          font-size: 14px;
          color: #9ca3af;
          margin-bottom: 16px;
        }
      }
    }

    .video-info-overlay {
      position: absolute;
      top: 16px;
      left: 16px;
      right: 16px;
      pointer-events: none;

      .video-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .video-source {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          background: rgba(0, 0, 0, 0.6);
          padding: 4px 8px;
          border-radius: 4px;
        }

        .video-status {
          font-size: 12px;
          font-weight: 600;
          padding: 4px 8px;
          border-radius: 4px;
          background: rgba(107, 114, 128, 0.8);
          color: #f3f4f6;

          &.status-live {
            background: rgba(239, 68, 68, 0.8);
            color: #fff;
            animation: pulse 2s infinite;
          }
        }
      }
    }

    .tech-frame {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;

      .corner {
        position: absolute;
        width: 20px;
        height: 20px;
        border: 2px solid #3b82f6;

        &.top-left {
          top: 8px;
          left: 8px;
          border-right: none;
          border-bottom: none;
        }

        &.top-right {
          top: 8px;
          right: 8px;
          border-left: none;
          border-bottom: none;
        }

        &.bottom-left {
          bottom: 8px;
          left: 8px;
          border-right: none;
          border-top: none;
        }

        &.bottom-right {
          bottom: 8px;
          right: 8px;
          border-left: none;
          border-top: none;
        }
      }

      .scan-line {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, transparent, #3b82f6, transparent);
        animation: scan 3s linear infinite;
      }
    }
  }

  .control-info {
    padding: 12px 16px;
    background: rgba(31, 41, 55, 0.6);
    border-top: 1px solid rgba(75, 85, 99, 0.3);

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      font-size: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        color: #9ca3af;
      }

      .info-value {
        color: #f3f4f6;
        font-family: monospace;

        &.status-success {
          color: #10b981;
        }

        &.status-warning {
          color: #f59e0b;
        }

        &.status-error {
          color: #ef4444;
        }

        &.status-offline {
          color: #6b7280;
        }
      }
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes scan {
  0% { transform: translateY(0); }
  100% { transform: translateY(300px); }
}
</style>
