<!-- 
  EmergencyPlans.vue
  应急预案库管理模块
  管理和维护农业生产过程中的各类应急预案，提供查看、编辑、演练和启动功能
-->
<template>
  <div class="emergency-plans" :style="{'background-color': '#1f2937'}">
    <!-- 页面标题 -->
    <PageHeader
      title="智慧农场应急预案库"
      description="管理和维护农业生产过程中的各类应急预案，提供查看、编辑、演练和启动功能"
      icon="Operation"
    >
      <template #actions>
        <div class="status-summary">
          <div class="summary-item">
            <span class="summary-value">{{ metrics.unprocessedIncidents }}</span>
            <span class="summary-label">未处理事件</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ metrics.expiringPlans }}</span>
            <span class="summary-label">即将到期预案</span>
          </div>
          <div class="summary-item">
            <span class="summary-value">{{ metrics.resourceAvailability }}%</span>
            <span class="summary-label">资源可用率</span>
          </div>
        </div>
      </template>
    </PageHeader>

    <!-- Emergency Commander Dashboard -->
    <div class="commander-dashboard">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-card alert-high">
            <div class="metric-icon"><el-icon><Warning /></el-icon></div>
            <div class="metric-content">
              <div class="metric-value">{{ metrics.unprocessedIncidents }}</div>
              <div class="metric-label">未处理事件</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card alert-medium">
            <div class="metric-icon"><el-icon><Clock /></el-icon></div>
            <div class="metric-content">
              <div class="metric-value">{{ metrics.expiringPlans }}</div>
              <div class="metric-label">即将到期预案</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon"><el-icon><Box /></el-icon></div>
            <div class="metric-content">
              <div class="metric-value">{{ metrics.resourceAvailability }}%</div>
              <div class="metric-label">设备可用率</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon"><el-icon><List /></el-icon></div>
            <div class="metric-content">
              <div class="metric-value">{{ metrics.chemicalStock }}%</div>
              <div class="metric-label">药剂库存</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选器面板 -->
    <div class="filter-container">
      <div class="filter-panel">
        <el-button :type="filterType === 'all' ? 'primary' : ''" @click="filterType = 'all'">
          <el-icon><Menu /></el-icon>
          全部预案
        </el-button>
        <el-button :type="filterType === 'high' ? 'danger' : ''" @click="filterType = 'high'">
          <el-icon><Warning /></el-icon>
          高紧急度
        </el-button>
        <el-button :type="filterType === 'medium' ? 'warning' : ''" @click="filterType = 'medium'">
          <el-icon><InfoFilled /></el-icon>
          中紧急度
        </el-button>
        <el-button :type="filterType === 'low' ? 'info' : ''" @click="filterType = 'low'">
          <el-icon><Notification /></el-icon>
          低紧急度
        </el-button>
        <el-button :type="filterType === 'summer' ? 'success' : ''" @click="filterType = 'summer'">
          <el-icon><Calendar /></el-icon>
          夏季适用
        </el-button>
      </div>
      <div class="action-panel">
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          新建预案
        </el-button>
      </div>
    </div>

    <!-- Emergency Plans List -->
    <div class="plans-container">
      <el-table
        :data="filteredPlans"
        style="width: 100%"
        @row-click="handleRowClick"
        row-key="id"
        :row-class-name="getRowClass"
        class="plans-table"
      >
        <el-table-column label="紧急程度" width="100">
          <template #default="scope">
            <el-tag 
              :type="getUrgencyTag(scope.row.urgency)" 
              effect="dark" 
              size="small"
            >
              {{ getUrgencyLabel(scope.row.urgency) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="预案名称" width="250" />
        <el-table-column prop="scenario" label="适用场景" />
        <el-table-column prop="updateTime" label="最后更新时间" width="180" />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button 
                size="small" 
                type="danger" 
                @click.stop="activatePlan(scope.row)"
              >
                <el-icon><VideoPause /></el-icon>
                启动
              </el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click.stop="simulateDrill(scope.row)"
              >
                <el-icon><VideoPlay /></el-icon>
                演练
              </el-button>
              <el-button 
                size="small" 
                @click.stop="editPlan(scope.row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 底部状态指示器 -->
    <div class="status-indicators">
      <div class="indicator-group">
        <StatusIndicator type="success" label="运行正常" />
        <StatusIndicator type="warning" label="需要更新" />
        <StatusIndicator type="error" label="过期预案" />
        <StatusIndicator type="normal" label="演练中" />
      </div>
      <div class="refresh-info">
        <span>数据更新时间: {{ formatTime(lastUpdateTime) }}</span>
        <el-button type="primary" size="small" plain @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- Plan Detail Drawer -->
    <el-drawer
      v-model="showDetailDrawer"
      direction="rtl"
      size="80%"
      :title="selectedPlan ? selectedPlan.name : '预案详情'"
      destroy-on-close
    >
      <template v-if="selectedPlan">
        <div class="plan-detail">
          <!-- Plan Flowchart -->
          <DataPanel title="预案流程图">
            <div class="flowchart-container">
              <!-- In a real implementation, this would use a flowchart library -->
              <div class="mock-flowchart">
                <div class="flow-node start">开始</div>
                <div class="flow-arrow">↓</div>
                <div class="flow-node">发现病虫害</div>
                <div class="flow-arrow">↓</div>
                <div class="flow-node">初步评估</div>
                <div class="flow-arrow">↓</div>
                <div class="flow-node decision">超过阈值?</div>
                <div class="flow-branches">
                  <div class="flow-branch">
                    <div class="flow-arrow branch-arrow">←是</div>
                    <div class="flow-node">启动应急响应</div>
                    <div class="flow-arrow">↓</div>
                    <div class="flow-node">组织防治</div>
                  </div>
                  <div class="flow-branch">
                    <div class="flow-arrow branch-arrow">→否</div>
                    <div class="flow-node">继续监测</div>
                  </div>
                </div>
                <div class="flow-arrow">↓</div>
                <div class="flow-node">效果评估</div>
                <div class="flow-arrow">↓</div>
                <div class="flow-node end">结束</div>
              </div>
            </div>
          </DataPanel>

          <!-- Emergency Operation Steps -->
          <DataPanel title="应急操作步骤">
            <el-collapse v-model="activeStepCollapse">
              <el-collapse-item 
                v-for="(step, index) in selectedPlan.steps" 
                :key="index"
                :title="`步骤 ${index + 1}: ${step.name}`"
                :name="index"
              >
                <div class="step-card">
                  <div class="step-guidance">
                    <h4>操作指导</h4>
                    <p>{{ step.guidance }}</p>
                  </div>
                  <div class="step-responsible">
                    <h4>负责人</h4>
                    <p>{{ step.responsible }}</p>
                  </div>
                  <div class="step-timeframe">
                    <h4>时间要求</h4>
                    <p>{{ step.timeframe }}</p>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </DataPanel>

          <!-- Emergency Resource List -->
          <DataPanel title="应急资源清单">
            <el-table :data="selectedPlan.resources" style="width: 100%">
              <el-table-column prop="type" label="资源类型" width="120" />
              <el-table-column prop="name" label="资源名称" width="200" />
              <el-table-column prop="quantity" label="需求数量" width="120" />
              <el-table-column prop="available" label="当前可用" width="120" />
              <el-table-column prop="location" label="存放位置" />
            </el-table>
          </DataPanel>

          <!-- Drill History -->
          <DataPanel title="历史演练记录">
            <el-timeline>
              <el-timeline-item
                v-for="(drill, index) in selectedPlan.drills"
                :key="index"
                :timestamp="drill.date"
                :type="getDrillType(drill.result)"
                :color="getDrillColor(drill.result)"
              >
                <h4>演练结果: {{ drill.result }}</h4>
                <p>{{ drill.improvements }}</p>
              </el-timeline-item>
            </el-timeline>
          </DataPanel>
        </div>
      </template>
    </el-drawer>

    <!-- Activate Plan Dialog -->
    <el-dialog
      v-model="showActivateDialog"
      title="确认启动预案"
      width="30%"
    >
      <div class="activate-warning">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <p>您正在启动 <strong>{{ selectedPlan?.name }}</strong> 预案。请确认是否继续?</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showActivateDialog = false">取消</el-button>
          <el-button type="danger" @click="confirmActivate">确认启动</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Warning, Menu, InfoFilled, Notification, Calendar, 
  Clock, Box, List, Edit, Document, Delete, Plus, VideoPause, VideoPlay, Refresh, Operation
} from '@element-plus/icons-vue'

// 导入自定义组件
import PageHeader from './components/PageHeader.vue'
import StatusIndicator from './components/StatusIndicator.vue'
import DataPanel from './components/DataPanel.vue'

// 定义类型
interface Resource {
  type: string;
  name: string;
  quantity: number | string;
  available: number | string;
  location: string;
}

interface Step {
  name: string;
  guidance: string;
  responsible: string;
  timeframe: string;
}

interface Drill {
  date: string;
  result: string;
  improvements: string;
}

interface EmergencyPlan {
  id: number;
  name: string;
  scenario: string;
  urgency: 'high' | 'medium' | 'low';
  updateTime: string;
  steps: Step[];
  resources: Resource[];
  drills: Drill[];
}

// Dashboard metrics
const metrics = reactive({
  unprocessedIncidents: 5,
  expiringPlans: 3,
  resourceAvailability: 87,
  chemicalStock: 65
})

// Emergency plans data
const emergencyPlans = ref<EmergencyPlan[]>([
  {
    id: 1,
    name: '水稻稻飞虱爆发应急预案',
    scenario: '水稻生长期稻飞虱密度超过阈值时的应急处置',
    urgency: 'high',
    updateTime: '2023-05-20 14:30',
    steps: [
      {
        name: '快速评估',
        guidance: '调查受灾面积，确定虫口密度，评估潜在损失',
        responsible: '技术组长',
        timeframe: '发现后24小时内'
      },
      {
        name: '制定防治方案',
        guidance: '根据稻飞虱种类和密度，结合天气情况，制定药剂和施药方案',
        responsible: '作物保护专家',
        timeframe: '评估后12小时内'
      },
      {
        name: '组织实施',
        guidance: '协调人员、设备和药剂，按照方案实施防治',
        responsible: '执行组长',
        timeframe: '方案确定后48小时内'
      },
      {
        name: '效果评估',
        guidance: '防治后3-5天检查防效，确定是否需要补施药剂',
        responsible: '质量监督员',
        timeframe: '施药后3-5天'
      }
    ],
    resources: [
      { type: '设备', name: '高效喷雾器', quantity: 10, available: 8, location: '设备仓库A区' },
      { type: '药剂', name: '噻虫嗪', quantity: '50kg', available: '30kg', location: '药品库B区' },
      { type: '人员', name: '施药技术员', quantity: 5, available: 5, location: '技术部' },
      { type: '防护', name: '防护服', quantity: 10, available: 10, location: '设备仓库C区' }
    ],
    drills: [
      {
        date: '2023-04-15',
        result: '通过',
        improvements: '响应时间符合要求，药剂调配流程顺畅，但设备准备时间较长，需要改进。'
      },
      {
        date: '2022-10-20',
        result: '部分通过',
        improvements: '资源调度不够及时，需要优化人员分配机制和沟通流程。'
      }
    ]
  },
  {
    id: 2,
    name: '小麦条锈病防治应急预案',
    scenario: '小麦抽穗期发现条锈病局部严重发生',
    urgency: 'medium',
    updateTime: '2023-04-15 09:45',
    steps: [
      {
        name: '发病区域隔离',
        guidance: '迅速标记严重发病区域，防止人员交叉传播',
        responsible: '区域负责人',
        timeframe: '发现后12小时内'
      },
      {
        name: '制定方案',
        guidance: '根据发病程度选择适当的杀菌剂和施药方法',
        responsible: '病害专家',
        timeframe: '24小时内'
      }
    ],
    resources: [
      { type: '设备', name: '背负式喷雾器', quantity: 15, available: 15, location: '设备仓库B区' },
      { type: '药剂', name: '戊唑醇乳油', quantity: '30L', available: '25L', location: '药品库A区' }
    ],
    drills: [
      {
        date: '2023-03-10',
        result: '通过',
        improvements: '预案执行顺畅，各环节衔接良好。'
      }
    ]
  },
  {
    id: 3,
    name: '蔬菜温室蚜虫爆发预案',
    scenario: '蔬菜大棚内蚜虫种群迅速增加，有蔓延趋势',
    urgency: 'low',
    updateTime: '2023-05-10 16:20',
    steps: [],
    resources: [],
    drills: []
  },
  {
    id: 4,
    name: '果园食心虫突发预案',
    scenario: '果园内发现食心虫危害，处于生长季节',
    urgency: 'medium',
    updateTime: '2023-05-05 11:30',
    steps: [],
    resources: [],
    drills: []
  },
  {
    id: 5,
    name: '农田蝗灾应急预案',
    scenario: '大面积农田发生蝗虫灾害',
    urgency: 'high',
    updateTime: '2023-04-28 08:15',
    steps: [],
    resources: [],
    drills: []
  }
])

// UI states
const filterType = ref('all')
const selectedPlan = ref<EmergencyPlan | null>(null)
const showDetailDrawer = ref(false)
const showActivateDialog = ref(false)
const activeStepCollapse = ref([0]) // Default open first step
const lastUpdateTime = ref(new Date())

// Computed properties
const filteredPlans = computed(() => {
  if (filterType.value === 'all') return emergencyPlans.value
  
  if (['high', 'medium', 'low'].includes(filterType.value)) {
    return emergencyPlans.value.filter(plan => plan.urgency === filterType.value)
  }
  
  if (filterType.value === 'summer') {
    // In a real implementation, would filter by season
    return emergencyPlans.value.filter(plan => 
      plan.name.includes('水稻') || plan.name.includes('蔬菜')
    )
  }
  
  return emergencyPlans.value
})

// Methods
function getUrgencyLabel(urgency: string): string {
  switch(urgency) {
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

function getUrgencyTag(urgency: string): string {
  switch(urgency) {
    case 'high': return 'danger'
    case 'medium': return 'warning'
    case 'low': return 'info'
    default: return ''
  }
}

function handleRowClick(row: EmergencyPlan): void {
  selectedPlan.value = row
  showDetailDrawer.value = true
}

function getRowClass({row}: {row: EmergencyPlan}): string {
  return `urgency-${row.urgency}`
}

function activatePlan(plan: EmergencyPlan): void {
  selectedPlan.value = plan
  showActivateDialog.value = true
}

function confirmActivate(): void {
  if (selectedPlan.value) {
    ElMessage.success(`已启动预案: ${selectedPlan.value.name}`)
    showActivateDialog.value = false
    // In real implementation, would trigger the plan activation process
  }
}

function simulateDrill(plan: EmergencyPlan): void {
  ElMessage.info(`正在模拟演练: ${plan.name}`)
  // In real implementation, would launch the drill simulation interface
}

function editPlan(plan: EmergencyPlan): void {
  ElMessage.info(`编辑预案: ${plan.name}`)
  // In real implementation, would open the plan editor
}

function getDrillType(result: string): string {
  switch(result) {
    case '通过': return 'success'
    case '部分通过': return 'warning'
    case '未通过': return 'danger'
    default: return 'info'
  }
}

function getDrillColor(result: string): string {
  switch(result) {
    case '通过': return '#67C23A'
    case '部分通过': return '#E6A23C'
    case '未通过': return '#F56C6C'
    default: return '#909399'
  }
}

function formatTime(date: Date): string {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

function refreshData(): void {
  lastUpdateTime.value = new Date()
  ElMessage.success('数据已更新')
  // In real implementation, would fetch data from backend
}

onMounted(() => {
  // In real implementation, would fetch data from backend
  lastUpdateTime.value = new Date()
})
</script>

<style scoped>
.emergency-plans {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #ffffff;
}

/* 状态摘要 */
.status-summary {
  display: flex;
  gap: 20px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
}

.summary-label {
  font-size: 14px;
  color: #9ca3af;
}

/* Commander Dashboard */
.commander-dashboard {
  margin-bottom: 20px;
}

.metric-card {
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  height: 100px;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.metric-card.alert-high {
  background-color: rgba(239, 68, 68, 0.2);
  border-left: 4px solid #ef4444;
}

.metric-card.alert-medium {
  background-color: rgba(245, 158, 11, 0.2);
  border-left: 4px solid #f59e0b;
}

.metric-icon {
  font-size: 2.5rem;
  margin-right: 15px;
  opacity: 0.7;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 0.9rem;
  color: #d1d5db;
}

/* 筛选器和操作区域 */
.filter-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  background-color: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  padding: 10px;
}

.filter-panel {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-panel {
  display: flex;
  align-items: center;
}

/* 预案表格容器 */
.plans-container {
  background-color: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  flex: 1;
  padding: 0;
}

/* 自定义表格样式 */
:deep(.plans-table) {
  background-color: transparent !important;
  color: #e5e7eb !important;
}

:deep(.plans-table .el-table__header-wrapper) {
  background-color: transparent !important;
}

:deep(.plans-table th.el-table__cell) {
  background-color: rgba(31, 41, 55, 0.8) !important;
  color: #e5e7eb !important;
  font-weight: 600;
  border-bottom: 1px solid #374151;
}

:deep(.plans-table .el-table__row) {
  background-color: transparent !important;
}

:deep(.plans-table td),
:deep(.plans-table th.el-table__cell) {
  border-bottom-color: rgba(75, 85, 99, 0.4) !important;
}

:deep(.plans-table .el-table__row:hover > td.el-table__cell) {
  background-color: rgba(55, 65, 81, 0.5) !important;
}

/* 紧急度行样式 */
:deep(.urgency-high) {
  background-color: rgba(239, 68, 68, 0.1) !important;
}

:deep(.urgency-medium) {
  background-color: rgba(245, 158, 11, 0.1) !important;
}

:deep(.urgency-low) {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

/* 操作按钮 */
.operation-buttons {
  display: flex;
  gap: 5px;
}

/* 状态指示器区域 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  margin-top: auto;
}

.indicator-group {
  display: flex;
  gap: 20px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #9ca3af;
  font-size: 14px;
}

/* 预案详情样式 */
.plan-detail {
  padding: 20px;
}

.plan-detail h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #3b82f6;
  border-bottom: 1px solid #3b82f6;
  padding-bottom: 5px;
}

.flowchart-section,
.steps-section,
.resource-section,
.drill-section {
  margin-bottom: 30px;
}

/* 流程图容器 */
.flowchart-container {
  overflow-x: auto;
  padding: 20px 0;
}

.mock-flowchart {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 600px;
}

.flow-node {
  padding: 10px 20px;
  border-radius: 4px;
  background-color: #3b82f6;
  margin: 10px 0;
  min-width: 150px;
  text-align: center;
  color: #fff;
  transition: transform 0.2s ease;
}

.flow-node:hover {
  transform: scale(1.05);
}

.flow-node.start,
.flow-node.end {
  background-color: #10b981;
}

.flow-node.decision {
  background-color: #f59e0b;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flow-arrow {
  font-size: 24px;
  color: #d1d5db;
}

.flow-branches {
  display: flex;
  justify-content: center;
  gap: 80px;
  width: 100%;
}

.flow-branch {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.branch-arrow {
  margin: 10px 0;
}

/* 步骤卡片 */
.step-card {
  background-color: rgba(55, 65, 81, 0.3);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-card h4 {
  margin-top: 0;
  margin-bottom: 5px;
  color: #3b82f6;
}

.step-guidance,
.step-responsible,
.step-timeframe {
  margin-bottom: 15px;
}

.step-card p {
  margin: 0;
  color: #e5e7eb;
}

/* 资源表格样式 */
:deep(.resource-section .el-table) {
  background-color: transparent !important;
  color: #e5e7eb !important;
}

:deep(.resource-section .el-table th.el-table__cell) {
  background-color: rgba(31, 41, 55, 0.8) !important;
  color: #e5e7eb !important;
}

:deep(.resource-section .el-table__row) {
  background-color: transparent !important;
}

:deep(.resource-section .el-table td),
:deep(.resource-section .el-table th.el-table__cell) {
  border-bottom-color: rgba(75, 85, 99, 0.4) !important;
}

/* 预案激活警告 */
.activate-warning {
  display: flex;
  align-items: center;
  color: #f56c6c;
}

.warning-icon {
  font-size: 2rem;
  margin-right: 15px;
}

/* 自定义抽屉样式 */
:deep(.el-drawer__header) {
  color: #ffffff;
  margin-bottom: 0;
  padding: 15px 20px;
  border-bottom: 1px solid #374151;
  background-color: rgba(31, 41, 55, 0.8);
}

:deep(.el-drawer__body) {
  padding: 0;
  background-color: #1f2937;
  color: #ffffff;
}

/* 自定义折叠面板样式 */
:deep(.el-collapse),
:deep(.el-collapse-item__wrap) {
  background-color: transparent !important;
  border-color: #374151 !important;
}

:deep(.el-collapse-item__header) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #ffffff !important;
  border-color: #374151 !important;
}

:deep(.el-collapse-item__content) {
  color: #e5e7eb !important;
  background-color: transparent !important;
}

/* 自定义时间线样式 */
:deep(.el-timeline-item__node--normal) {
  left: -1px;
}

:deep(.el-timeline-item__content) {
  color: #d1d5db;
}

:deep(.el-timeline-item__timestamp) {
  color: #9ca3af;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 28px;
}

/* 自定义对话框样式 */
:deep(.el-dialog) {
  background-color: #1f2937;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__header) {
  background-color: rgba(31, 41, 55, 0.8);
  color: #ffffff;
  padding: 15px 20px;
  margin-right: 0;
  border-bottom: 1px solid #374151;
}

:deep(.el-dialog__title) {
  color: #ffffff;
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: #3b82f6;
}

:deep(.el-dialog__body) {
  color: #ffffff;
  padding: 20px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #374151;
  padding: 15px 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .filter-panel {
    justify-content: center;
  }
  
  .action-panel {
    justify-content: center;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 15px;
  }
  
  .indicator-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .status-summary {
    flex-wrap: wrap;
    justify-content: center;
  }

  .commander-dashboard .el-col {
    margin-bottom: 10px;
  }
}

@media (max-width: 576px) {
  .plans-container {
    margin-right: -10px;
    margin-left: -10px;
    border-radius: 0;
  }
  
  .flow-branches {
    flex-direction: column;
    gap: 0;
  }
  
  .mock-flowchart {
    min-width: 300px;
  }
}
</style> 