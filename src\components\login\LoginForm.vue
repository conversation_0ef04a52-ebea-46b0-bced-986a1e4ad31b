<template>
  <div class="login-form-container">
    <el-form
      :model="loginForm"
      :rules="rules"
      ref="loginFormRef"
      class="login-form"
    >

      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          :prefix-icon="User"
          placeholder="请输入用户名或手机号"
          class="custom-input"
          @focus="handleInputFocus('username')"
          @blur="handleInputBlur()"
          :class="{ 'input-focused': focusedField === 'username' }"
        />
      </el-form-item>

      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          :prefix-icon="Lock"
          type="password"
          placeholder="请输入密码"
          show-password
          @keyup.enter="submitForm"
          class="custom-input"
          @focus="handleInputFocus('password')"
          @blur="handleInputBlur()"
          :class="{ 'input-focused': focusedField === 'password' }"
        />
      </el-form-item>



      <div class="remember-box">
        <el-checkbox v-model="loginForm.remember" class="remember-check">
          <span class="remember-text">记住我</span>
        </el-checkbox>
      </div>

      <el-button
        type="primary"
        class="login-button"
        @click="submitForm"
        :loading="loading"
      >
        <span class="button-text">{{ loading ? '登录中...' : '登录' }}</span>
      </el-button>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuth } from '@/composables/useAuth'
import { useLoginForm } from '@/composables/useForm'
import { useRouter } from 'vue-router'

// 使用自定义composables
const router = useRouter()
const { loading, login } = useAuth()
const { loginFormRef, loginForm, rules, loadRememberedUser, validateForm } = useLoginForm()

// 输入框焦点状态
const focusedField = ref<string | null>(null)

// 在组件挂载时加载记住的用户信息
onMounted(() => {
  loadRememberedUser()
})

// 提交表单
const submitForm = async () => {
  const valid = await validateForm()
  if (valid) {
    // 调用登录方法
    const success = await login(
      loginForm.username,
      loginForm.password,
      loginForm.remember
    )

    if (success) {
      // 登录成功后跳转到首页
      router.push('/home')
    }
  }
}

// 处理输入框焦点事件
const handleInputFocus = (field: string): void => {
  focusedField.value = field
}

const handleInputBlur = (): void => {
  focusedField.value = null
}
</script>

<style lang="scss" scoped>
@use '@/styles/login.scss' as login;

.login-form-container {
  width: 100%;
  animation: fadeIn 0.8s ease-out 0.3s both;
}

.login-form {
  :deep(.el-input) {
    --el-input-height: 50px;
    margin-bottom: 20px;

    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      box-shadow: none;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(0, 255, 170, 0.3);
        box-shadow: 0 0 0 1px rgba(0, 255, 170, 0.1);
      }

      &.is-focus {
        border-color: #00ffaa;
        box-shadow: 0 0 0 2px rgba(0, 255, 170, 0.2);
      }
    }

    .el-input__inner {
      color: #ffffff;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .el-input__prefix {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .input-focused {
    :deep(.el-input__wrapper) {
      border-color: #00ffaa !important;
      box-shadow: 0 0 0 2px rgba(0, 255, 170, 0.2) !important;

      .el-input__prefix {
        color: #00ffaa;
      }
    }
  }
}



.remember-box {
  margin: 16px 0 24px;

  .remember-check {
    :deep(.el-checkbox__label) {
      color: rgba(255, 255, 255, 0.9);
    }

    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
      background-color: #00ffaa;
      border-color: #00ffaa;
    }

    .remember-text {
      color: rgba(255, 255, 255, 0.9);
      transition: color 0.3s ease;
    }

    &:hover .remember-text {
      color: #ffffff;
    }
  }
}

.login-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(90deg, #00ffaa, #00aa83);
  border: none;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 255, 170, 0.3);

    &::before {
      transform: translateX(100%);
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: transform 0.6s ease;
  }

  .button-text {
    position: relative;
    z-index: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
